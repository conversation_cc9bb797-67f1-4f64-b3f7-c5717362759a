.class public final Landroidx/compose/ui/layout/f$a;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/compose/ui/layout/f;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# static fields
.field public static final synthetic a:Landroidx/compose/ui/layout/f$a;

.field public static final b:Landroidx/compose/ui/layout/f;

.field public static final c:Landroidx/compose/ui/layout/f;

.field public static final d:Landroidx/compose/ui/layout/f;

.field public static final e:Landroidx/compose/ui/layout/f;

.field public static final f:Landroidx/compose/ui/layout/f;

.field public static final g:Landroidx/compose/ui/layout/h;

.field public static final h:Landroidx/compose/ui/layout/f;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Landroidx/compose/ui/layout/f$a;

    invoke-direct {v0}, Landroidx/compose/ui/layout/f$a;-><init>()V

    sput-object v0, Landroidx/compose/ui/layout/f$a;->a:Landroidx/compose/ui/layout/f$a;

    new-instance v0, Landroidx/compose/ui/layout/f$a$a;

    invoke-direct {v0}, Landroidx/compose/ui/layout/f$a$a;-><init>()V

    sput-object v0, Landroidx/compose/ui/layout/f$a;->b:Landroidx/compose/ui/layout/f;

    new-instance v0, Landroidx/compose/ui/layout/f$a$e;

    invoke-direct {v0}, Landroidx/compose/ui/layout/f$a$e;-><init>()V

    sput-object v0, Landroidx/compose/ui/layout/f$a;->c:Landroidx/compose/ui/layout/f;

    new-instance v0, Landroidx/compose/ui/layout/f$a$c;

    invoke-direct {v0}, Landroidx/compose/ui/layout/f$a$c;-><init>()V

    sput-object v0, Landroidx/compose/ui/layout/f$a;->d:Landroidx/compose/ui/layout/f;

    new-instance v0, Landroidx/compose/ui/layout/f$a$d;

    invoke-direct {v0}, Landroidx/compose/ui/layout/f$a$d;-><init>()V

    sput-object v0, Landroidx/compose/ui/layout/f$a;->e:Landroidx/compose/ui/layout/f;

    new-instance v0, Landroidx/compose/ui/layout/f$a$f;

    invoke-direct {v0}, Landroidx/compose/ui/layout/f$a$f;-><init>()V

    sput-object v0, Landroidx/compose/ui/layout/f$a;->f:Landroidx/compose/ui/layout/f;

    new-instance v0, Landroidx/compose/ui/layout/h;

    const/high16 v1, 0x3f800000    # 1.0f

    invoke-direct {v0, v1}, Landroidx/compose/ui/layout/h;-><init>(F)V

    sput-object v0, Landroidx/compose/ui/layout/f$a;->g:Landroidx/compose/ui/layout/h;

    new-instance v0, Landroidx/compose/ui/layout/f$a$b;

    invoke-direct {v0}, Landroidx/compose/ui/layout/f$a$b;-><init>()V

    sput-object v0, Landroidx/compose/ui/layout/f$a;->h:Landroidx/compose/ui/layout/f;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final a()Landroidx/compose/ui/layout/f;
    .locals 1

    sget-object v0, Landroidx/compose/ui/layout/f$a;->c:Landroidx/compose/ui/layout/f;

    return-object v0
.end method

.method public final b()Landroidx/compose/ui/layout/f;
    .locals 1

    sget-object v0, Landroidx/compose/ui/layout/f$a;->f:Landroidx/compose/ui/layout/f;

    return-object v0
.end method
