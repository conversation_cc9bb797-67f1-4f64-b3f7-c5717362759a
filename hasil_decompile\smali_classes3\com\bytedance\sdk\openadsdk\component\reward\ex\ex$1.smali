.class Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex$1;
.super Ljava/lang/Object;

# interfaces
.implements Landroid/view/View$OnClickListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;->dG()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public onClick(Landroid/view/View;)V
    .locals 0

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Obv:Lcom/bytedance/sdk/openadsdk/component/reward/view/Ubf;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/view/Ubf;->Ko()Landroid/view/View;

    move-result-object p1

    invoke-virtual {p1}, Landroid/view/View;->performClick()Z

    return-void
.end method
