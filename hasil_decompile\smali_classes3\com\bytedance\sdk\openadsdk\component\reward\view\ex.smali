.class public Lcom/bytedance/sdk/openadsdk/component/reward/view/ex;
.super Ljava/lang/Object;


# instance fields
.field private final Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

.field private eV:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW;

.field private ex:Lcom/bytedance/sdk/openadsdk/component/reward/view/RFEndCardBackUpLayout;

.field private hjc:Z


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/view/ex;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    return-void
.end method


# virtual methods
.method public Fj()V
    .locals 2

    iget-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/view/ex;->hjc:Z

    if-eqz v0, :cond_0

    return-void

    :cond_0
    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/view/ex;->hjc:Z

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/view/ex;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->HY:Lcom/bytedance/sdk/openadsdk/component/reward/view/RewardFullBaseLayout;

    sget v1, Lcom/bytedance/sdk/openadsdk/utils/Ko;->tyC:I

    invoke-virtual {v0, v1}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Lcom/bytedance/sdk/openadsdk/component/reward/view/RFEndCardBackUpLayout;

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/view/ex;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/view/RFEndCardBackUpLayout;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/view/ex;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/component/reward/view/RFEndCardBackUpLayout;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/view/ex;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->zf()Z

    move-result v0

    if-eqz v0, :cond_1

    new-instance v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/view/ex;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    invoke-direct {v0, v1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;)V

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/view/ex;->eV:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW;

    :cond_1
    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/openadsdk/core/ex/Ubf;)V
    .locals 3

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/view/ex;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/view/RFEndCardBackUpLayout;

    new-instance v1, Lcom/bytedance/sdk/openadsdk/component/reward/view/ex$1;

    invoke-direct {v1, p0}, Lcom/bytedance/sdk/openadsdk/component/reward/view/ex$1;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/view/ex;)V

    const-string v2, "TTBaseVideoActivity#mFLEndCardBackupContainer"

    invoke-static {v0, v1, v2}, Lcom/bytedance/sdk/openadsdk/utils/eh;->Fj(Landroid/view/View;Landroid/view/View$OnClickListener;Ljava/lang/String;)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/view/ex;->eV:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW;

    if-eqz v0, :cond_0

    invoke-virtual {v0, p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW;->Fj(Lcom/bytedance/sdk/openadsdk/core/ex/Ubf;)V

    :cond_0
    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;)Z
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/view/ex;->eV:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW;

    const/4 v1, 0x0

    if-nez v0, :cond_0

    return v1

    :cond_0
    invoke-virtual {v0, p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;)Z

    move-result p1

    if-eqz p1, :cond_1

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/view/ex;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/view/RFEndCardBackUpLayout;

    invoke-static {p1, v1}, Lcom/bytedance/sdk/openadsdk/utils/eh;->Fj(Landroid/view/View;I)V

    const/4 p1, 0x1

    return p1

    :cond_1
    return v1
.end method

.method public Ubf()Z
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/view/ex;->eV:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW;->hjc()Z

    move-result v0

    return v0

    :cond_0
    const/4 v0, 0x0

    return v0
.end method

.method public eV()V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/view/ex;->eV:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW;->ex()V

    :cond_0
    return-void
.end method

.method public ex()V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/view/ex;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/view/RFEndCardBackUpLayout;

    const/4 v1, 0x0

    invoke-static {v0, v1}, Lcom/bytedance/sdk/openadsdk/utils/eh;->Fj(Landroid/view/View;I)V

    return-void
.end method

.method public hjc()V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/view/ex;->eV:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW;->Fj()V

    :cond_0
    return-void
.end method
