.class public Lcom/bytedance/sdk/openadsdk/core/model/vYf;
.super Ljava/lang/Object;


# instance fields
.field public BcC:Lcom/bytedance/sdk/openadsdk/core/model/mC;

.field public final Fj:Ljava/lang/String;

.field public Ubf:Lorg/json/JSONArray;

.field public WR:I
    .annotation build Lcom/bytedance/sdk/openadsdk/core/model/NetExtParams$RenderType;
    .end annotation
.end field

.field public eV:I

.field public ex:I

.field public hjc:I

.field public final svN:Lcom/bytedance/sdk/openadsdk/utils/lv;


# direct methods
.method public constructor <init>()V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/utils/rXP;->eV()Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/vYf;->Fj:Ljava/lang/String;

    const/4 v0, -0x1

    iput v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/vYf;->ex:I

    iput v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/vYf;->hjc:I

    iput v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/vYf;->eV:I

    const/4 v0, 0x0

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/vYf;->Ubf:Lorg/json/JSONArray;

    const/4 v0, 0x1

    iput v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/vYf;->WR:I

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/utils/lv;->Fj()Lcom/bytedance/sdk/openadsdk/utils/lv;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/vYf;->svN:Lcom/bytedance/sdk/openadsdk/utils/lv;

    return-void
.end method
