.class public Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf$Fj;
    }
.end annotation


# static fields
.field private static final Ko:Ljava/util/Set;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Set<",
            "Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field private BcC:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf$Fj;

.field private Fj:Lcom/bytedance/sdk/openadsdk/AdSlot;

.field private final Tc:Lcom/bytedance/sdk/openadsdk/utils/lv;

.field private UYd:Ljava/util/concurrent/ScheduledFuture;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/concurrent/ScheduledFuture<",
            "*>;"
        }
    .end annotation
.end field

.field private final Ubf:Ljava/util/concurrent/atomic/AtomicBoolean;

.field private WR:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/bytedance/sdk/openadsdk/core/model/Ql;",
            ">;"
        }
    .end annotation
.end field

.field private dG:Ljava/util/concurrent/ScheduledFuture;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/concurrent/ScheduledFuture<",
            "*>;"
        }
    .end annotation
.end field

.field private eV:Lcom/bytedance/sdk/openadsdk/api/banner/PAGBannerAdLoadListener;

.field private final ex:Lcom/bytedance/sdk/openadsdk/core/Ql;

.field private final hjc:Landroid/content/Context;

.field private mSE:I

.field private final rAx:Ljava/util/concurrent/ScheduledFuture;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/concurrent/ScheduledFuture<",
            "*>;"
        }
    .end annotation
.end field

.field private svN:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/bytedance/sdk/openadsdk/core/model/Ql;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Ljava/util/HashSet;

    invoke-direct {v0}, Ljava/util/HashSet;-><init>()V

    invoke-static {v0}, Ljava/util/Collections;->synchronizedSet(Ljava/util/Set;)Ljava/util/Set;

    move-result-object v0

    sput-object v0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->Ko:Ljava/util/Set;

    return-void
.end method

.method private constructor <init>(Landroid/content/Context;)V
    .locals 2

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Ljava/util/concurrent/atomic/AtomicBoolean;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Ljava/util/concurrent/atomic/AtomicBoolean;-><init>(Z)V

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->Ubf:Ljava/util/concurrent/atomic/AtomicBoolean;

    const/4 v0, 0x5

    iput v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->mSE:I

    const/4 v0, 0x0

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->rAx:Ljava/util/concurrent/ScheduledFuture;

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->UYd:Ljava/util/concurrent/ScheduledFuture;

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->dG:Ljava/util/concurrent/ScheduledFuture;

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/utils/lv;->ex()Lcom/bytedance/sdk/openadsdk/utils/lv;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->Tc:Lcom/bytedance/sdk/openadsdk/utils/lv;

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/JU;->hjc()Lcom/bytedance/sdk/openadsdk/core/Ql;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->ex:Lcom/bytedance/sdk/openadsdk/core/Ql;

    if-eqz p1, :cond_0

    invoke-virtual {p1}, Landroid/content/Context;->getApplicationContext()Landroid/content/Context;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->hjc:Landroid/content/Context;

    goto :goto_0

    :cond_0
    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/JU;->Fj()Landroid/content/Context;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->hjc:Landroid/content/Context;

    :goto_0
    sget-object p1, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->Ko:Ljava/util/Set;

    invoke-interface {p1, p0}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method private Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Lcom/bytedance/sdk/openadsdk/api/banner/PAGBannerAd;
    .locals 3

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->mSE:I

    const/4 v1, 0x1

    if-ne v0, v1, :cond_1

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->spi()Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

    move-result-object v0

    if-eqz v0, :cond_0

    new-instance v0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/hjc;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->hjc:Landroid/content/Context;

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->Fj:Lcom/bytedance/sdk/openadsdk/AdSlot;

    invoke-direct {v0, v1, p1, v2}, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/hjc;-><init>(Landroid/content/Context;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/AdSlot;)V

    return-object v0

    :cond_0
    new-instance v0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->hjc:Landroid/content/Context;

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->Fj:Lcom/bytedance/sdk/openadsdk/AdSlot;

    invoke-direct {v0, v1, p1, v2}, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;-><init>(Landroid/content/Context;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/AdSlot;)V

    return-object v0

    :cond_1
    const/4 p1, 0x0

    return-object p1
.end method

.method public static Fj(Landroid/content/Context;)Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;
    .locals 1

    new-instance v0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;

    invoke-direct {v0, p0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;-><init>(Landroid/content/Context;)V

    return-object v0
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;)Lcom/bytedance/sdk/openadsdk/utils/lv;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->Tc:Lcom/bytedance/sdk/openadsdk/utils/lv;

    return-object p0
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;Ljava/util/List;)Ljava/util/List;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->WR:Ljava/util/List;

    return-object p1
.end method

.method private Fj()V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->WR:Ljava/util/List;

    if-eqz v0, :cond_0

    invoke-interface {v0}, Ljava/util/List;->clear()V

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->svN:Ljava/util/List;

    if-eqz v0, :cond_1

    invoke-interface {v0}, Ljava/util/List;->clear()V

    :cond_1
    const/4 v0, 0x1

    invoke-direct {p0, v0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->Fj(Z)V

    invoke-direct {p0, v0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->ex(Z)V

    invoke-direct {p0, v0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->hjc(Z)V

    invoke-direct {p0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->ex()V

    return-void
.end method

.method private Fj(I)V
    .locals 3

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->WR:Ljava/util/List;

    if-eqz v0, :cond_0

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    if-lez v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->WR:Ljava/util/List;

    const/4 v1, 0x0

    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->qw()Ljava/lang/String;

    move-result-object v0

    goto :goto_0

    :cond_0
    const-string v0, ""

    :goto_0
    invoke-static {}, Lcom/bytedance/sdk/openadsdk/Ko/Fj/hjc;->ex()Lcom/bytedance/sdk/openadsdk/Ko/Fj/hjc;

    move-result-object v1

    iget v2, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->mSE:I

    invoke-virtual {v1, v2}, Lcom/bytedance/sdk/openadsdk/Ko/Fj/hjc;->Fj(I)Lcom/bytedance/sdk/openadsdk/Ko/Fj/hjc;

    move-result-object v1

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->Fj:Lcom/bytedance/sdk/openadsdk/AdSlot;

    invoke-virtual {v2}, Lcom/bytedance/sdk/openadsdk/AdSlot;->getCodeId()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Lcom/bytedance/sdk/openadsdk/Ko/Fj/hjc;->hjc(Ljava/lang/String;)Lcom/bytedance/sdk/openadsdk/Ko/Fj/hjc;

    move-result-object v1

    invoke-virtual {v1, v0}, Lcom/bytedance/sdk/openadsdk/Ko/Fj/hjc;->Ubf(Ljava/lang/String;)Lcom/bytedance/sdk/openadsdk/Ko/Fj/hjc;

    move-result-object v0

    invoke-virtual {v0, p1}, Lcom/bytedance/sdk/openadsdk/Ko/Fj/hjc;->ex(I)Lcom/bytedance/sdk/openadsdk/Ko/Fj/hjc;

    move-result-object v0

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/core/svN;->Fj(I)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Lcom/bytedance/sdk/openadsdk/Ko/Fj/hjc;->WR(Ljava/lang/String;)Lcom/bytedance/sdk/openadsdk/Ko/Fj/hjc;

    move-result-object p1

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/Ko/hjc;->Fj()Lcom/bytedance/sdk/openadsdk/Ko/hjc;

    move-result-object v0

    invoke-virtual {v0, p1}, Lcom/bytedance/sdk/openadsdk/Ko/hjc;->Fj(Lcom/bytedance/sdk/openadsdk/Ko/Fj/hjc;)V

    return-void
.end method

.method private Fj(ILjava/lang/String;)V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->Ubf:Ljava/util/concurrent/atomic/AtomicBoolean;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/util/concurrent/atomic/AtomicBoolean;->getAndSet(Z)Z

    move-result v0

    if-eqz v0, :cond_2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->eV:Lcom/bytedance/sdk/openadsdk/api/banner/PAGBannerAdLoadListener;

    if-eqz v0, :cond_0

    invoke-interface {v0, p1, p2}, Lcom/bytedance/sdk/openadsdk/api/PAGLoadListener;->onError(ILjava/lang/String;)V

    :cond_0
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->BcC:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf$Fj;

    if-eqz p1, :cond_1

    invoke-interface {p1}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf$Fj;->Fj()V

    :cond_1
    invoke-direct {p0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->Fj()V

    :cond_2
    return-void
.end method

.method private Fj(Lcom/bytedance/sdk/openadsdk/AdSlot;)V
    .locals 4

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->WR:Ljava/util/List;

    if-nez v0, :cond_0

    return-void

    :cond_0
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_1
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_2

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {v1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->Ubf(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v2

    if-eqz v2, :cond_1

    invoke-virtual {v1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->spi()Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

    move-result-object v2

    if-eqz v2, :cond_1

    invoke-virtual {v1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->spi()Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

    move-result-object v2

    invoke-virtual {v2}, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->rAx()Ljava/lang/String;

    move-result-object v2

    if-eqz v2, :cond_1

    invoke-virtual {v1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->aYy()I

    move-result v2

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/JU;->eV()Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;

    move-result-object v3

    invoke-static {v2}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    move-result-object v2

    invoke-interface {v3, v2}, Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;->Ubf(Ljava/lang/String;)Z

    move-result v2

    if-eqz v2, :cond_1

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/JU;->eV()Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;

    move-result-object v2

    invoke-interface {v2}, Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;->cs()Z

    move-result v2

    if-eqz v2, :cond_1

    invoke-virtual {v1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->aGk()I

    move-result v2

    invoke-static {v2}, Lcom/bytedance/sdk/openadsdk/CacheDirFactory;->getICacheDir(I)Lcom/bykv/vk/openvk/component/video/api/Fj/ex;

    move-result-object v2

    invoke-interface {v2}, Lcom/bykv/vk/openvk/component/video/api/Fj/ex;->hjc()Ljava/lang/String;

    move-result-object v2

    invoke-static {v2, v1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->Fj(Ljava/lang/String;Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Lcom/bytedance/sdk/openadsdk/core/video/Fj/ex;

    move-result-object v2

    const-string v3, "material_meta"

    invoke-virtual {v2, v3, v1}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->Fj(Ljava/lang/String;Ljava/lang/Object;)V

    const-string v1, "ad_slot"

    invoke-virtual {v2, v1, p1}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->Fj(Ljava/lang/String;Ljava/lang/Object;)V

    const/4 v1, 0x0

    invoke-static {v2, v1}, Lcom/bytedance/sdk/openadsdk/core/video/eV/Fj;->Fj(Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;Lcom/bykv/vk/openvk/component/video/api/Ubf/Fj$Fj;)V

    goto :goto_0

    :cond_2
    return-void
.end method

.method private Fj(Lcom/bytedance/sdk/openadsdk/AdSlot;Lcom/bytedance/sdk/openadsdk/common/hjc;)V
    .locals 3

    if-nez p1, :cond_0

    return-void

    :cond_0
    new-instance p2, Lcom/bytedance/sdk/openadsdk/core/model/vYf;

    invoke-direct {p2}, Lcom/bytedance/sdk/openadsdk/core/model/vYf;-><init>()V

    const/4 v0, 0x2

    iput v0, p2, Lcom/bytedance/sdk/openadsdk/core/model/vYf;->WR:I

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->ex:Lcom/bytedance/sdk/openadsdk/core/Ql;

    iget v1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->mSE:I

    new-instance v2, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf$1;

    invoke-direct {v2, p0, p1}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf$1;-><init>(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;Lcom/bytedance/sdk/openadsdk/AdSlot;)V

    invoke-interface {v0, p1, p2, v1, v2}, Lcom/bytedance/sdk/openadsdk/core/Ql;->Fj(Lcom/bytedance/sdk/openadsdk/AdSlot;Lcom/bytedance/sdk/openadsdk/core/model/vYf;ILcom/bytedance/sdk/openadsdk/core/Ql$Fj;)V

    return-void
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;ILjava/lang/String;)V
    .locals 0

    invoke-direct {p0, p1, p2}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->Fj(ILjava/lang/String;)V

    return-void
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;Lcom/bytedance/sdk/openadsdk/AdSlot;)V
    .locals 0

    invoke-direct {p0, p1}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->Fj(Lcom/bytedance/sdk/openadsdk/AdSlot;)V

    return-void
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;Lcom/bytedance/sdk/openadsdk/utils/lv;)V
    .locals 0

    invoke-direct {p0, p1}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->Fj(Lcom/bytedance/sdk/openadsdk/utils/lv;)V

    return-void
.end method

.method private Fj(Lcom/bytedance/sdk/openadsdk/utils/lv;)V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->Ubf:Ljava/util/concurrent/atomic/AtomicBoolean;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/util/concurrent/atomic/AtomicBoolean;->getAndSet(Z)Z

    move-result v0

    if-eqz v0, :cond_0

    new-instance v0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf$2;

    invoke-direct {v0, p0, p1}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf$2;-><init>(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;Lcom/bytedance/sdk/openadsdk/utils/lv;)V

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/utils/uy;->Fj(Ljava/lang/Runnable;)V

    :cond_0
    return-void
.end method

.method private Fj(Z)V
    .locals 2

    :try_start_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->UYd:Ljava/util/concurrent/ScheduledFuture;

    if-eqz v0, :cond_0

    invoke-interface {v0}, Ljava/util/concurrent/Future;->isCancelled()Z

    move-result v0

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->UYd:Ljava/util/concurrent/ScheduledFuture;

    invoke-interface {v0, p1}, Ljava/util/concurrent/Future;->cancel(Z)Z

    move-result p1

    const-string v0, "ExpressAdLoadManager"

    const-string v1, "CheckValidFutureTask-->cancel......success="

    invoke-static {p1}, Ljava/lang/String;->valueOf(Z)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v1, p1}, Ljava/lang/String;->concat(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    invoke-static {v0, p1}, Lcom/bytedance/sdk/component/utils/dG;->Fj(Ljava/lang/String;Ljava/lang/String;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :catchall_0
    :cond_0
    return-void
.end method

.method public static synthetic eV(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;)V
    .locals 0

    invoke-direct {p0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->Fj()V

    return-void
.end method

.method public static synthetic ex(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;)Ljava/util/List;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->svN:Ljava/util/List;

    return-object p0
.end method

.method public static synthetic ex(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;Ljava/util/List;)Ljava/util/List;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->svN:Ljava/util/List;

    return-object p1
.end method

.method private ex()V
    .locals 1

    sget-object v0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->Ko:Ljava/util/Set;

    invoke-interface {v0, p0}, Ljava/util/Set;->remove(Ljava/lang/Object;)Z

    return-void
.end method

.method public static synthetic ex(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;Lcom/bytedance/sdk/openadsdk/utils/lv;)V
    .locals 0

    invoke-direct {p0, p1}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->ex(Lcom/bytedance/sdk/openadsdk/utils/lv;)V

    return-void
.end method

.method private ex(Lcom/bytedance/sdk/openadsdk/utils/lv;)V
    .locals 4

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->eV:Lcom/bytedance/sdk/openadsdk/api/banner/PAGBannerAdLoadListener;

    if-eqz p1, :cond_4

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->svN:Ljava/util/List;

    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    const/4 v0, 0x0

    :cond_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-direct {p0, v0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Lcom/bytedance/sdk/openadsdk/api/banner/PAGBannerAd;

    move-result-object v0

    if-eqz v0, :cond_0

    :cond_1
    if-eqz v0, :cond_3

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->Fj:Lcom/bytedance/sdk/openadsdk/AdSlot;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/AdSlot;->getBidAdm()Ljava/lang/String;

    move-result-object p1

    invoke-static {p1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result p1

    if-nez p1, :cond_2

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->Tc:Lcom/bytedance/sdk/openadsdk/utils/lv;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/utils/lv;->hjc()J

    move-result-wide v1

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->svN:Ljava/util/List;

    const/4 v3, 0x0

    invoke-interface {p1, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {p1, v1, v2}, Lcom/bytedance/sdk/openadsdk/Ko/hjc;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;J)V

    :cond_2
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->eV:Lcom/bytedance/sdk/openadsdk/api/banner/PAGBannerAdLoadListener;

    invoke-interface {p1, v0}, Lcom/bytedance/sdk/openadsdk/api/PAGLoadListener;->onAdLoaded(Ljava/lang/Object;)V

    return-void

    :cond_3
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->eV:Lcom/bytedance/sdk/openadsdk/api/banner/PAGBannerAdLoadListener;

    const/16 v0, 0x67

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/svN;->Fj(I)Ljava/lang/String;

    move-result-object v1

    invoke-interface {p1, v0, v1}, Lcom/bytedance/sdk/openadsdk/api/PAGLoadListener;->onError(ILjava/lang/String;)V

    invoke-direct {p0, v0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->Fj(I)V

    :cond_4
    return-void
.end method

.method private ex(Z)V
    .locals 1

    :try_start_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->dG:Ljava/util/concurrent/ScheduledFuture;

    if-eqz v0, :cond_0

    invoke-interface {v0}, Ljava/util/concurrent/Future;->isCancelled()Z

    move-result v0

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->dG:Ljava/util/concurrent/ScheduledFuture;

    invoke-interface {v0, p1}, Ljava/util/concurrent/Future;->cancel(Z)Z
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :catchall_0
    :cond_0
    return-void
.end method

.method public static synthetic hjc(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;)Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf$Fj;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->BcC:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf$Fj;

    return-object p0
.end method

.method private hjc(Z)V
    .locals 2

    :try_start_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->rAx:Ljava/util/concurrent/ScheduledFuture;

    if-eqz v0, :cond_0

    invoke-interface {v0}, Ljava/util/concurrent/Future;->isCancelled()Z

    move-result v0

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->rAx:Ljava/util/concurrent/ScheduledFuture;

    invoke-interface {v0, p1}, Ljava/util/concurrent/Future;->cancel(Z)Z

    move-result p1

    const-string v0, "ExpressAdLoadManager"

    const-string v1, "TimeOutFutureTask-->cancel......success="

    invoke-static {p1}, Ljava/lang/String;->valueOf(Z)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v1, p1}, Ljava/lang/String;->concat(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    invoke-static {v0, p1}, Lcom/bytedance/sdk/component/utils/dG;->Fj(Ljava/lang/String;Ljava/lang/String;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :catchall_0
    :cond_0
    return-void
.end method


# virtual methods
.method public Fj(Lcom/bytedance/sdk/openadsdk/AdSlot;ILcom/bytedance/sdk/openadsdk/common/hjc;I)V
    .locals 6
    .param p3    # Lcom/bytedance/sdk/openadsdk/common/hjc;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    const/4 v4, 0x0

    move-object v0, p0

    move-object v1, p1

    move v2, p2

    move-object v3, p3

    move v5, p4

    invoke-virtual/range {v0 .. v5}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->Fj(Lcom/bytedance/sdk/openadsdk/AdSlot;ILcom/bytedance/sdk/openadsdk/common/hjc;Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf$Fj;I)V

    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/openadsdk/AdSlot;ILcom/bytedance/sdk/openadsdk/common/hjc;Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf$Fj;I)V
    .locals 0
    .param p3    # Lcom/bytedance/sdk/openadsdk/common/hjc;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p4    # Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf$Fj;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    iget-object p5, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->Tc:Lcom/bytedance/sdk/openadsdk/utils/lv;

    invoke-virtual {p5}, Lcom/bytedance/sdk/openadsdk/utils/lv;->eV()V

    iget-object p5, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->Ubf:Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-virtual {p5}, Ljava/util/concurrent/atomic/AtomicBoolean;->get()Z

    move-result p5

    if-eqz p5, :cond_0

    const-string p1, "ExpressAdLoadManager"

    const-string p2, "express ad is loading..."

    invoke-static {p1, p2}, Lcom/bytedance/sdk/component/utils/dG;->Fj(Ljava/lang/String;Ljava/lang/String;)V

    return-void

    :cond_0
    iput p2, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->mSE:I

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->Ubf:Ljava/util/concurrent/atomic/AtomicBoolean;

    const/4 p5, 0x1

    invoke-virtual {p2, p5}, Ljava/util/concurrent/atomic/AtomicBoolean;->set(Z)V

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->Fj:Lcom/bytedance/sdk/openadsdk/AdSlot;

    instance-of p2, p3, Lcom/bytedance/sdk/openadsdk/api/banner/PAGBannerAdLoadListener;

    if-eqz p2, :cond_1

    move-object p2, p3

    check-cast p2, Lcom/bytedance/sdk/openadsdk/api/banner/PAGBannerAdLoadListener;

    iput-object p2, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->eV:Lcom/bytedance/sdk/openadsdk/api/banner/PAGBannerAdLoadListener;

    :cond_1
    iput-object p4, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->BcC:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf$Fj;

    invoke-direct {p0, p1, p3}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->Fj(Lcom/bytedance/sdk/openadsdk/AdSlot;Lcom/bytedance/sdk/openadsdk/common/hjc;)V

    return-void
.end method
