.class Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$7;
.super Lcom/bytedance/sdk/openadsdk/core/widget/Fj/eV;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Fj(Ljava/lang/String;Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$eV;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$eV;

.field final synthetic ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;Lcom/bytedance/sdk/openadsdk/core/Vq;Lcom/bytedance/sdk/openadsdk/ex/Ko;Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$eV;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$7;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;

    iput-object p4, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$7;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$eV;

    invoke-direct {p0, p2, p3}, Lcom/bytedance/sdk/openadsdk/core/widget/Fj/eV;-><init>(Lcom/bytedance/sdk/openadsdk/core/Vq;Lcom/bytedance/sdk/openadsdk/ex/Ko;)V

    return-void
.end method


# virtual methods
.method public onProgressChanged(Landroid/webkit/WebView;I)V
    .locals 1

    invoke-super {p0, p1, p2}, Lcom/bytedance/sdk/openadsdk/core/widget/Fj/eV;->onProgressChanged(Landroid/webkit/WebView;I)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$7;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$eV;

    if-eqz v0, :cond_0

    invoke-interface {v0, p1, p2}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$eV;->Fj(Landroid/webkit/WebView;I)V

    :cond_0
    return-void
.end method
