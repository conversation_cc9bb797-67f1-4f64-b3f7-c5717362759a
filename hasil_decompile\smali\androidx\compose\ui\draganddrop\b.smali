.class public final Landroidx/compose/ui/draganddrop/b;
.super Ljava/lang/Object;


# annotations
.annotation runtime Lkotlin/Metadata;
.end annotation


# instance fields
.field public final a:Landroid/view/DragEvent;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Landroid/view/DragEvent;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Landroidx/compose/ui/draganddrop/b;->a:Landroid/view/DragEvent;

    return-void
.end method


# virtual methods
.method public final a()Landroid/view/DragEvent;
    .locals 1

    iget-object v0, p0, Landroidx/compose/ui/draganddrop/b;->a:Landroid/view/DragEvent;

    return-object v0
.end method
