.class Lcom/bytedance/sdk/openadsdk/component/hjc$4;
.super Ljava/lang/Object;

# interfaces
.implements Landroid/view/View$OnClickListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/hjc;->Fj()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/component/hjc;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/hjc;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/hjc$4;->Fj:Lcom/bytedance/sdk/openadsdk/component/hjc;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public onClick(Landroid/view/View;)V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/hjc$4;->Fj:Lcom/bytedance/sdk/openadsdk/component/hjc;

    iget-object v1, v0, Lcom/bytedance/sdk/openadsdk/component/hjc;->Ubf:Lcom/bytedance/sdk/openadsdk/component/Fj;

    if-eqz v1, :cond_0

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/hjc;->Fj(Lcom/bytedance/sdk/openadsdk/component/hjc;)Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/hjc$4;->Fj:Lcom/bytedance/sdk/openadsdk/component/hjc;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/hjc;->Ubf:Lcom/bytedance/sdk/openadsdk/component/Fj;

    invoke-virtual {v0, p1}, Lcom/bytedance/sdk/openadsdk/component/Fj;->Fj(Landroid/view/View;)V

    :cond_0
    return-void
.end method
