.class public final Lcom/transsion/ninegridview/R$styleable;
.super Ljava/lang/Object;


# static fields
.field public static NineGridView:[I = null

.field public static NineGridView_enable_click:I = 0x0

.field public static NineGridView_ngv_gridSpacing:I = 0x1

.field public static NineGridView_ngv_maxSize:I = 0x2

.field public static NineGridView_ngv_mode:I = 0x3

.field public static NineGridView_ngv_singleImageRatio:I = 0x4

.field public static NineGridView_ngv_singleImageSize:I = 0x5


# direct methods
.method public static constructor <clinit>()V
    .locals 1

    const/4 v0, 0x6

    new-array v0, v0, [I

    fill-array-data v0, :array_0

    sput-object v0, Lcom/transsion/ninegridview/R$styleable;->NineGridView:[I

    return-void

    nop

    :array_0
    .array-data 4
        0x7f04032e
        0x7f040583
        0x7f040584
        0x7f040585
        0x7f040586
        0x7f040587
    .end array-data
.end method

.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
