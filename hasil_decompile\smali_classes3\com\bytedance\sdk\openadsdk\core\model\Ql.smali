.class public abstract Lcom/bytedance/sdk/openadsdk/core/model/Ql;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;
    }
.end annotation


# instance fields
.field private Fj:J


# direct methods
.method public constructor <init>()V
    .locals 2

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const-wide/16 v0, 0x0

    iput-wide v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->Fj:J

    return-void
.end method

.method public static Fj(Lorg/json/JSONObject;)I
    .locals 2

    const/4 v0, 0x0

    if-eqz p0, :cond_0

    const-string v1, "ut"

    invoke-virtual {p0, v1, v0}, Lorg/json/JSONObject;->optInt(Ljava/lang/String;I)I

    move-result p0

    return p0

    :cond_0
    return v0
.end method

.method public static Fj(Ljava/lang/String;)J
    .locals 2

    invoke-static {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->hjc(Ljava/lang/String;)Lorg/json/JSONObject;

    move-result-object p0

    invoke-static {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->Ubf(Lorg/json/JSONObject;)J

    move-result-wide v0

    return-wide v0
.end method

.method public static Fj(Ljava/lang/String;Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Lcom/bytedance/sdk/openadsdk/core/video/Fj/ex;
    .locals 9

    const/4 v0, 0x0

    invoke-virtual {p1, v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->eh(I)V

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->Im()I

    move-result v0

    const/4 v1, 0x3

    if-eq v0, v1, :cond_2

    const/4 v2, 0x7

    if-eq v0, v2, :cond_1

    const/16 v2, 0x8

    if-eq v0, v2, :cond_0

    const/4 v8, 0x3

    goto :goto_0

    :cond_0
    const/4 v1, 0x2

    const/4 v8, 0x2

    goto :goto_0

    :cond_1
    const/4 v1, 0x1

    const/4 v8, 0x1

    goto :goto_0

    :cond_2
    const/4 v1, 0x4

    const/4 v8, 0x4

    :goto_0
    new-instance v0, Lcom/bytedance/sdk/openadsdk/core/video/Fj/ex;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->GZ()Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

    move-result-object v4

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->Wx()Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

    move-result-object v5

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->aGk()I

    move-result v6

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->xx()I

    move-result v7

    move-object v2, v0

    move-object v3, p0

    invoke-direct/range {v2 .. v8}, Lcom/bytedance/sdk/openadsdk/core/video/Fj/ex;-><init>(Ljava/lang/String;Lcom/bykv/vk/openvk/component/video/api/hjc/ex;Lcom/bykv/vk/openvk/component/video/api/hjc/ex;III)V

    return-object v0
.end method

.method public static Fj(Landroid/content/Context;Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Ljava/lang/String;
    .locals 3

    const/4 v0, 0x0

    if-eqz p0, :cond_3

    if-nez p1, :cond_0

    goto :goto_0

    :cond_0
    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->Im()I

    move-result v1

    const/16 v2, 0x8

    if-eq v1, v2, :cond_1

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->Im()I

    move-result v1

    const/4 v2, 0x7

    if-ne v1, v2, :cond_3

    :cond_1
    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->eh()Z

    move-result v1

    if-nez v1, :cond_2

    return-object v0

    :cond_2
    invoke-static {p0, p1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->ex(Landroid/content/Context;Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Ljava/lang/String;

    move-result-object p0

    invoke-static {p0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result p1

    if-nez p1, :cond_3

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/act/Fj;->Fj()I

    move-result p1

    const/4 v1, 0x1

    if-ne p1, v1, :cond_3

    return-object p0

    :cond_3
    :goto_0
    return-object v0
.end method

.method public static Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z
    .locals 1

    if-eqz p0, :cond_0

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->spi()Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

    move-result-object v0

    if-eqz v0, :cond_0

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->spi()Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

    move-result-object p0

    invoke-virtual {p0}, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->eV()I

    move-result p0

    const/4 v0, 0x1

    if-ne p0, v0, :cond_0

    return v0

    :cond_0
    const/4 p0, 0x0

    return p0
.end method

.method public static Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;ZZZZ)Z
    .locals 2

    invoke-static {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v0

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    return v1

    :cond_0
    if-nez p4, :cond_4

    if-eqz p0, :cond_4

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->spi()Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

    move-result-object p4

    if-eqz p4, :cond_4

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->spi()Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

    move-result-object p4

    invoke-virtual {p4}, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->UYd()Ljava/lang/String;

    move-result-object p4

    invoke-static {p4}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result p4

    if-eqz p4, :cond_1

    goto :goto_0

    :cond_1
    invoke-static {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->hjc(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result p4

    if-eqz p4, :cond_2

    return p3

    :cond_2
    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->spi()Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

    move-result-object p3

    if-eqz p3, :cond_3

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->spi()Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

    move-result-object p0

    invoke-virtual {p0}, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->Fj()I

    move-result p0

    const/4 p3, 0x1

    if-ne p0, p3, :cond_3

    return p2

    :cond_3
    return p1

    :cond_4
    :goto_0
    return v1
.end method

.method private static Ubf(Lorg/json/JSONObject;)J
    .locals 3

    const-wide/16 v0, 0x0

    if-eqz p0, :cond_0

    const-string v2, "uid"

    invoke-virtual {p0, v2, v0, v1}, Lorg/json/JSONObject;->optLong(Ljava/lang/String;J)J

    move-result-wide v0

    :cond_0
    return-wide v0
.end method

.method public static Ubf(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z
    .locals 2

    const/4 v0, 0x0

    if-nez p0, :cond_0

    return v0

    :cond_0
    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->haP()I

    move-result v1

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->zf()Z

    move-result p0

    if-nez p0, :cond_2

    const/4 p0, 0x5

    if-eq v1, p0, :cond_2

    const/16 p0, 0xf

    if-eq v1, p0, :cond_2

    const/16 p0, 0x32

    if-ne v1, p0, :cond_1

    goto :goto_0

    :cond_1
    return v0

    :cond_2
    :goto_0
    const/4 p0, 0x1

    return p0
.end method

.method private static WR(Lorg/json/JSONObject;)D
    .locals 3

    const-wide/16 v0, 0x0

    if-eqz p0, :cond_0

    const-string v2, "pack_time"

    invoke-virtual {p0, v2, v0, v1}, Lorg/json/JSONObject;->optDouble(Ljava/lang/String;D)D

    move-result-wide v0

    :cond_0
    return-wide v0
.end method

.method public static eV(Ljava/lang/String;)I
    .locals 0

    invoke-static {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->hjc(Ljava/lang/String;)Lorg/json/JSONObject;

    move-result-object p0

    invoke-static {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->Fj(Lorg/json/JSONObject;)I

    move-result p0

    return p0
.end method

.method public static eV(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z
    .locals 2

    const/4 v0, 0x0

    if-eqz p0, :cond_1

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->spi()Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

    move-result-object v1

    if-nez v1, :cond_0

    goto :goto_0

    :cond_0
    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->spi()Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

    move-result-object p0

    invoke-virtual {p0}, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->Fj()I

    move-result p0

    const/4 v1, 0x1

    if-ne p0, v1, :cond_1

    return v1

    :cond_1
    :goto_0
    return v0
.end method

.method public static ex(Ljava/lang/String;)D
    .locals 2

    invoke-static {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->hjc(Ljava/lang/String;)Lorg/json/JSONObject;

    move-result-object p0

    invoke-static {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->WR(Lorg/json/JSONObject;)D

    move-result-wide v0

    return-wide v0
.end method

.method public static ex()Lcom/bytedance/sdk/openadsdk/core/model/Ql;
    .locals 1

    new-instance v0, Lcom/bytedance/sdk/openadsdk/core/model/Af;

    invoke-direct {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;-><init>()V

    return-object v0
.end method

.method public static ex(Landroid/content/Context;Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Ljava/lang/String;
    .locals 2

    const/4 v0, 0x0

    if-eqz p0, :cond_2

    if-nez p1, :cond_0

    goto :goto_0

    :cond_0
    :try_start_0
    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->efV()I

    move-result p1

    const/16 v1, 0x8

    if-ne p1, v1, :cond_2

    invoke-static {p0}, Lcom/bytedance/sdk/openadsdk/core/act/Fj;->Fj(Landroid/content/Context;)Ljava/lang/String;

    move-result-object p0

    invoke-static {p0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result p1
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-nez p1, :cond_1

    return-object p0

    :cond_1
    return-object v0

    :catchall_0
    move-exception p0

    const-string p1, "MaterialMeta"

    invoke-virtual {p0}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    move-result-object p0

    invoke-static {p1, p0}, Lcom/bytedance/sdk/component/utils/dG;->Fj(Ljava/lang/String;Ljava/lang/String;)V

    :cond_2
    :goto_0
    return-object v0
.end method

.method public static ex(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z
    .locals 1

    invoke-static {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v0

    if-nez v0, :cond_0

    invoke-static {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->hjc(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result p0

    if-nez p0, :cond_0

    const/4 p0, 0x1

    return p0

    :cond_0
    const/4 p0, 0x0

    return p0
.end method

.method public static hjc(Ljava/lang/String;)Lorg/json/JSONObject;
    .locals 1

    invoke-static {p0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-nez v0, :cond_0

    :try_start_0
    new-instance v0, Lorg/json/JSONObject;

    invoke-direct {v0, p0}, Lorg/json/JSONObject;-><init>(Ljava/lang/String;)V
    :try_end_0
    .catch Lorg/json/JSONException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception p0

    const-string v0, "MaterialMeta"

    invoke-virtual {p0}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    move-result-object p0

    invoke-static {v0, p0}, Lcom/bytedance/sdk/component/utils/dG;->Fj(Ljava/lang/String;Ljava/lang/String;)V

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return-object v0
.end method

.method public static hjc(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z
    .locals 2

    if-eqz p0, :cond_0

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->spi()Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

    move-result-object v0

    if-eqz v0, :cond_0

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->spi()Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->eV()I

    move-result v0

    const/4 v1, 0x7

    if-ne v0, v1, :cond_0

    invoke-static {p0}, Lcom/bytedance/sdk/openadsdk/core/model/mE;->ex(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result p0

    if-nez p0, :cond_0

    const/4 p0, 0x1

    return p0

    :cond_0
    const/4 p0, 0x0

    return p0
.end method


# virtual methods
.method public abstract AY()[Ljava/lang/String;
.end method

.method public abstract Af(I)V
.end method

.method public abstract Af(Ljava/lang/String;)V
.end method

.method public abstract Af()Z
.end method

.method public abstract At()Lcom/bytedance/sdk/openadsdk/core/model/hjc;
.end method

.method public abstract Bb()V
.end method

.method public abstract BcC()Lcom/bytedance/sdk/openadsdk/core/model/JU;
.end method

.method public abstract BcC(I)V
.end method

.method public abstract BcC(Ljava/lang/String;)V
.end method

.method public abstract Bzy()Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end method

.method public abstract CML()I
.end method

.method public abstract ES()Z
.end method

.method public abstract Eev()Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end method

.method public abstract Fi()I
.end method

.method public Fj()J
    .locals 2

    iget-wide v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->Fj:J

    return-wide v0
.end method

.method public abstract Fj(D)V
.end method

.method public abstract Fj(F)V
.end method

.method public abstract Fj(I)V
.end method

.method public abstract Fj(II)V
.end method

.method public Fj(J)V
    .locals 0

    iput-wide p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->Fj:J

    return-void
.end method

.method public abstract Fj(Lcom/bykv/vk/openvk/component/video/api/hjc/ex;)V
.end method

.method public abstract Fj(Lcom/bytedance/sdk/openadsdk/AdSlot;)V
.end method

.method public abstract Fj(Lcom/bytedance/sdk/openadsdk/FilterWord;)V
.end method

.method public abstract Fj(Lcom/bytedance/sdk/openadsdk/core/model/BcC;)V
.end method

.method public abstract Fj(Lcom/bytedance/sdk/openadsdk/core/model/JU;)V
.end method

.method public abstract Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ko;)V
.end method

.method public abstract Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;)V
.end method

.method public abstract Fj(Lcom/bytedance/sdk/openadsdk/core/model/Tc;)V
.end method

.method public abstract Fj(Lcom/bytedance/sdk/openadsdk/core/model/UYd;)V
.end method

.method public abstract Fj(Lcom/bytedance/sdk/openadsdk/core/model/eV;)V
.end method

.method public abstract Fj(Lcom/bytedance/sdk/openadsdk/core/model/hjc;)V
.end method

.method public abstract Fj(Lcom/bytedance/sdk/openadsdk/core/model/mE;)V
.end method

.method public abstract Fj(Lcom/bytedance/sdk/openadsdk/core/model/rAx;)V
.end method

.method public abstract Fj(Lcom/bytedance/sdk/openadsdk/core/svN/Fj;)V
.end method

.method public abstract Fj(Lcom/bytedance/sdk/openadsdk/core/ugen/Ubf/Fj;)V
.end method

.method public abstract Fj(Ljava/util/Map;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/Object;",
            ">;)V"
        }
    .end annotation
.end method

.method public abstract Fj(Z)V
.end method

.method public abstract Fj([Ljava/lang/String;)V
.end method

.method public abstract GZ()Lcom/bykv/vk/openvk/component/video/api/hjc/ex;
.end method

.method public abstract Gv()Lorg/json/JSONObject;
.end method

.method public abstract HQ()F
.end method

.method public abstract HY()Ljava/lang/String;
.end method

.method public abstract Ic()Z
.end method

.method public abstract IgC()I
.end method

.method public abstract Im()I
.end method

.method public abstract JU()I
.end method

.method public abstract JU(I)V
.end method

.method public abstract JU(Ljava/lang/String;)V
.end method

.method public abstract JW()I
.end method

.method public abstract JW(I)V
.end method

.method public abstract JW(Ljava/lang/String;)V
.end method

.method public abstract JZ()Z
.end method

.method public abstract Jq()Ljava/lang/String;
.end method

.method public abstract KV()D
.end method

.method public abstract KZ()Lcom/bytedance/sdk/openadsdk/core/model/Tc;
.end method

.method public abstract Kf()Ljava/lang/String;
.end method

.method public abstract Kk()Ljava/lang/String;
.end method

.method public abstract Kkq()Ljava/lang/String;
.end method

.method public abstract Ko()I
.end method

.method public abstract Ko(I)V
.end method

.method public abstract Ko(Ljava/lang/String;)V
.end method

.method public abstract LEp()Z
.end method

.method public abstract LL()I
.end method

.method public abstract LZ()Z
.end method

.method public abstract LwI()Lcom/bytedance/sdk/openadsdk/core/ugen/Ubf/Fj;
.end method

.method public abstract Moo()I
.end method

.method public abstract Moo(I)V
.end method

.method public abstract Moo(Ljava/lang/String;)V
.end method

.method public abstract NgJ()Z
.end method

.method public abstract Nyg()I
.end method

.method public abstract OK()Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end method

.method public abstract OQa()Z
.end method

.method public abstract OXv()Lcom/bytedance/sdk/openadsdk/core/svN/Fj;
.end method

.method public abstract Obv()Ljava/lang/String;
.end method

.method public abstract PpV()Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/bytedance/sdk/openadsdk/core/model/Tc;",
            ">;"
        }
    .end annotation
.end method

.method public abstract QR()Z
.end method

.method public abstract QV()Lorg/json/JSONObject;
.end method

.method public abstract Ql()I
.end method

.method public abstract Ql(I)V
.end method

.method public abstract Ql(Ljava/lang/String;)V
.end method

.method public abstract Rgn()V
.end method

.method public abstract Rwq()Z
.end method

.method public abstract Tc()I
.end method

.method public abstract Tc(I)V
.end method

.method public abstract Tc(Ljava/lang/String;)V
.end method

.method public abstract Tyd()Lcom/bytedance/sdk/openadsdk/utils/lv;
.end method

.method public abstract UF()Z
.end method

.method public abstract UYd()J
.end method

.method public abstract UYd(I)V
.end method

.method public abstract UYd(Ljava/lang/String;)V
.end method

.method public abstract Ubf()Ljava/lang/String;
.end method

.method public abstract Ubf(I)V
.end method

.method public abstract Ubf(Ljava/lang/String;)V
.end method

.method public abstract Ubf(Z)V
.end method

.method public abstract Ud()Z
.end method

.method public abstract VXW()I
.end method

.method public abstract Vq()Ljava/lang/String;
.end method

.method public abstract Vq(Ljava/lang/String;)Ljava/lang/String;
.end method

.method public abstract Vq(I)V
.end method

.method public abstract WR()Ljava/lang/String;
.end method

.method public abstract WR(I)V
.end method

.method public abstract WR(Ljava/lang/String;)V
.end method

.method public abstract WR(Z)V
.end method

.method public abstract We()Z
.end method

.method public abstract Wx()Lcom/bykv/vk/openvk/component/video/api/hjc/ex;
.end method

.method public abstract Xl()Z
.end method

.method public abstract Xnz()J
.end method

.method public abstract YH()Ljava/lang/String;
.end method

.method public abstract YP()Ljava/lang/String;
.end method

.method public abstract Yh()Z
.end method

.method public abstract aGk()I
.end method

.method public abstract aYy()I
.end method

.method public abstract cB()I
.end method

.method public abstract cB(I)V
.end method

.method public abstract cB(Ljava/lang/String;)V
.end method

.method public abstract cHy()V
.end method

.method public abstract cP()Z
.end method

.method public abstract cs()Ljava/lang/String;
.end method

.method public abstract dG()Lcom/bytedance/sdk/openadsdk/core/model/mE;
.end method

.method public abstract dG(I)V
.end method

.method public abstract dG(Ljava/lang/String;)V
.end method

.method public abstract eV(I)V
.end method

.method public abstract eV(Lorg/json/JSONObject;)V
.end method

.method public abstract eV(Z)V
.end method

.method public abstract eV()Z
.end method

.method public abstract efV()I
.end method

.method public abstract eh(I)V
.end method

.method public abstract eh()Z
.end method

.method public abstract ei()Ljava/lang/String;
.end method

.method public abstract ex(D)V
.end method

.method public abstract ex(I)V
.end method

.method public abstract ex(J)V
.end method

.method public abstract ex(Lcom/bykv/vk/openvk/component/video/api/hjc/ex;)V
.end method

.method public abstract ex(Lcom/bytedance/sdk/openadsdk/core/model/Tc;)V
.end method

.method public abstract ex(Lorg/json/JSONObject;)V
.end method

.method public abstract ex(Z)V
.end method

.method public abstract fHV()Z
.end method

.method public abstract fU()Lcom/bytedance/sdk/openadsdk/core/model/UYd;
.end method

.method public abstract fj()Ljava/lang/String;
.end method

.method public abstract fj(I)V
.end method

.method public abstract fjZ()Lorg/json/JSONObject;
.end method

.method public abstract flF()Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/bytedance/sdk/openadsdk/FilterWord;",
            ">;"
        }
    .end annotation
.end method

.method public abstract gXF()I
.end method

.method public abstract gXF(I)V
.end method

.method public abstract gci()Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end method

.method public abstract hE()Z
.end method

.method public abstract haP()I
.end method

.method public abstract hjc(I)V
.end method

.method public abstract hjc(J)V
.end method

.method public abstract hjc(Lcom/bykv/vk/openvk/component/video/api/hjc/ex;)V
.end method

.method public abstract hjc(Lcom/bytedance/sdk/openadsdk/core/model/Tc;)V
.end method

.method public abstract hjc(Lorg/json/JSONObject;)V
.end method

.method public abstract hjc(Z)V
.end method

.method public abstract hjc()Z
.end method

.method public abstract iT()Ljava/lang/String;
.end method

.method public abstract iT(I)V
.end method

.method public abstract iq()Ljava/lang/String;
.end method

.method public abstract jID()Lcom/bytedance/sdk/component/widget/ex/Fj;
.end method

.method public abstract jsD()I
.end method

.method public abstract kF()I
.end method

.method public abstract kF(I)V
.end method

.method public abstract kOD()V
.end method

.method public abstract kl()Z
.end method

.method public abstract ks()J
.end method

.method public abstract lc()Z
.end method

.method public abstract lv()Lcom/bytedance/sdk/openadsdk/AdSlot;
.end method

.method public abstract lv(I)V
.end method

.method public abstract mC()I
.end method

.method public abstract mC(I)V
.end method

.method public abstract mC(Ljava/lang/String;)V
.end method

.method public abstract mE()I
.end method

.method public abstract mE(I)V
.end method

.method public abstract mE(Ljava/lang/String;)V
.end method

.method public abstract mSE()Lcom/bytedance/sdk/openadsdk/core/model/eV;
.end method

.method public abstract mSE(I)V
.end method

.method public abstract mSE(Ljava/lang/String;)V
.end method

.method public abstract mj()Ljava/lang/String;
.end method

.method public abstract mt()I
.end method

.method public abstract nsB()I
.end method

.method public abstract nsB(I)V
.end method

.method public abstract nsB(Ljava/lang/String;)V
.end method

.method public abstract oX()Lcom/bytedance/sdk/openadsdk/core/model/Tc;
.end method

.method public abstract qPr()Ljava/lang/String;
.end method

.method public abstract qg()Lcom/bytedance/sdk/openadsdk/core/model/Ko;
.end method

.method public abstract qj()Ljava/lang/String;
.end method

.method public abstract qw()Ljava/lang/String;
.end method

.method public abstract rAx()I
.end method

.method public abstract rAx(I)V
.end method

.method public abstract rAx(Ljava/lang/String;)V
.end method

.method public abstract rS()Ljava/lang/String;
.end method

.method public abstract rS(I)V
.end method

.method public abstract rS(Ljava/lang/String;)V
.end method

.method public abstract rXP()I
.end method

.method public abstract rXP(I)V
.end method

.method public abstract rf()I
.end method

.method public abstract rf(I)V
.end method

.method public abstract rf(Ljava/lang/String;)V
.end method

.method public abstract rqT()I
.end method

.method public abstract spi()Lcom/bykv/vk/openvk/component/video/api/hjc/ex;
.end method

.method public abstract spi(I)V
.end method

.method public abstract ss()I
.end method

.method public abstract sse()Lorg/json/JSONObject;
.end method

.method public abstract svN(I)V
.end method

.method public abstract svN(Ljava/lang/String;)V
.end method

.method public abstract svN(Z)V
.end method

.method public svN()Z
    .locals 1

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->Ubf()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-nez v0, :cond_0

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->WR()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-nez v0, :cond_0

    const/4 v0, 0x1

    return v0

    :cond_0
    const/4 v0, 0x0

    return v0
.end method

.method public abstract tS()Z
.end method

.method public abstract tc()Ljava/lang/String;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end method

.method public abstract tr()I
.end method

.method public abstract ttV()Ljava/lang/String;
.end method

.method public abstract tyC()I
.end method

.method public abstract uJB()Z
.end method

.method public abstract uM()Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;
.end method

.method public abstract uM(I)V
.end method

.method public abstract uv()V
.end method

.method public abstract uy()Lcom/bytedance/sdk/openadsdk/core/model/BcC;
.end method

.method public abstract uy(I)V
.end method

.method public abstract vLw()Z
.end method

.method public abstract vYf()I
.end method

.method public abstract vYf(I)V
.end method

.method public abstract vYf(Ljava/lang/String;)V
.end method

.method public abstract vg()I
.end method

.method public abstract vv()J
.end method

.method public abstract xEF()I
.end method

.method public abstract xx()I
.end method

.method public abstract yR()Z
.end method

.method public abstract yo()Z
.end method

.method public abstract zDD()I
.end method

.method public abstract zX()Z
.end method

.method public abstract zYh()Z
.end method

.method public abstract zf()Z
.end method

.method public abstract zj()V
.end method
