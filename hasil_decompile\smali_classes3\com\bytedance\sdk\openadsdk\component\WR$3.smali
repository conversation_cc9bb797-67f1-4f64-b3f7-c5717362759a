.class Lcom/bytedance/sdk/openadsdk/component/WR$3;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/openadsdk/core/Ql$Fj;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/WR;->Fj(Lcom/bytedance/sdk/openadsdk/AdSlot;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/AdSlot;

.field final synthetic ex:Lcom/bytedance/sdk/openadsdk/core/model/mC;

.field final synthetic hjc:Lcom/bytedance/sdk/openadsdk/component/WR;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/WR;Lcom/bytedance/sdk/openadsdk/AdSlot;Lcom/bytedance/sdk/openadsdk/core/model/mC;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/WR$3;->hjc:Lcom/bytedance/sdk/openadsdk/component/WR;

    iput-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/WR$3;->Fj:Lcom/bytedance/sdk/openadsdk/AdSlot;

    iput-object p3, p0, Lcom/bytedance/sdk/openadsdk/component/WR$3;->ex:Lcom/bytedance/sdk/openadsdk/core/model/mC;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj(ILjava/lang/String;)V
    .locals 0

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/WR$3;->hjc:Lcom/bytedance/sdk/openadsdk/component/WR;

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/WR$3;->Fj:Lcom/bytedance/sdk/openadsdk/AdSlot;

    invoke-static {p1, p2}, Lcom/bytedance/sdk/openadsdk/component/WR;->Fj(Lcom/bytedance/sdk/openadsdk/component/WR;Lcom/bytedance/sdk/openadsdk/AdSlot;)V

    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/openadsdk/core/model/Fj;Lcom/bytedance/sdk/openadsdk/core/model/ex;)V
    .locals 2

    if-eqz p1, :cond_3

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/model/Fj;->hjc()Ljava/util/List;

    move-result-object v0

    if-eqz v0, :cond_3

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/model/Fj;->hjc()Ljava/util/List;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    if-nez v0, :cond_0

    goto :goto_0

    :cond_0
    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/model/Fj;->hjc()Ljava/util/List;

    move-result-object p1

    const/4 p2, 0x0

    invoke-interface {p1, p2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    if-eqz p1, :cond_1

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->ES()Z

    move-result p2

    if-eqz p2, :cond_1

    new-instance p2, Lcom/bytedance/sdk/openadsdk/component/Ubf/Fj;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->aYy()I

    move-result v0

    invoke-direct {p2, v0, p1}, Lcom/bytedance/sdk/openadsdk/component/Ubf/Fj;-><init>(ILcom/bytedance/sdk/openadsdk/core/model/Ql;)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/WR$3;->hjc:Lcom/bytedance/sdk/openadsdk/component/WR;

    invoke-virtual {p1, p2}, Lcom/bytedance/sdk/openadsdk/component/WR;->Fj(Lcom/bytedance/sdk/openadsdk/component/Ubf/Fj;)V

    invoke-virtual {p2}, Lcom/bytedance/sdk/openadsdk/component/Ubf/Fj;->ex()Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    move-result-object p1

    const/4 p2, 0x1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/WR$3;->ex:Lcom/bytedance/sdk/openadsdk/core/model/mC;

    invoke-static {p1, p2, v0}, Lcom/bytedance/sdk/openadsdk/component/eV/Fj;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;ILcom/bytedance/sdk/openadsdk/core/model/mC;)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/WR$3;->hjc:Lcom/bytedance/sdk/openadsdk/component/WR;

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/WR$3;->Fj:Lcom/bytedance/sdk/openadsdk/AdSlot;

    invoke-static {p1, p2}, Lcom/bytedance/sdk/openadsdk/component/WR;->Fj(Lcom/bytedance/sdk/openadsdk/component/WR;Lcom/bytedance/sdk/openadsdk/AdSlot;)V

    return-void

    :cond_1
    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->Ubf(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result p2

    if-eqz p2, :cond_2

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/WR$3;->hjc:Lcom/bytedance/sdk/openadsdk/component/WR;

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/WR$3;->Fj:Lcom/bytedance/sdk/openadsdk/AdSlot;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/WR$3;->ex:Lcom/bytedance/sdk/openadsdk/core/model/mC;

    invoke-static {p2, p1, v0, v1}, Lcom/bytedance/sdk/openadsdk/component/WR;->Fj(Lcom/bytedance/sdk/openadsdk/component/WR;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/AdSlot;Lcom/bytedance/sdk/openadsdk/core/model/mC;)V

    return-void

    :cond_2
    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/WR$3;->hjc:Lcom/bytedance/sdk/openadsdk/component/WR;

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/WR$3;->Fj:Lcom/bytedance/sdk/openadsdk/AdSlot;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/WR$3;->ex:Lcom/bytedance/sdk/openadsdk/core/model/mC;

    invoke-static {p2, p1, v0, v1}, Lcom/bytedance/sdk/openadsdk/component/WR;->ex(Lcom/bytedance/sdk/openadsdk/component/WR;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/AdSlot;Lcom/bytedance/sdk/openadsdk/core/model/mC;)V

    return-void

    :cond_3
    :goto_0
    const/4 p1, -0x3

    invoke-virtual {p2, p1}, Lcom/bytedance/sdk/openadsdk/core/model/ex;->Fj(I)V

    invoke-static {p2}, Lcom/bytedance/sdk/openadsdk/core/model/ex;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/ex;)V

    return-void
.end method
