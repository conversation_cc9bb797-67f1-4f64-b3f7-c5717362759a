.class Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$4;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/openadsdk/utils/kF$ex;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;->Fj(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/NativeExpressView;Lcom/bytedance/sdk/openadsdk/core/model/Ql;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

.field final synthetic Ubf:Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;

.field final synthetic eV:Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$Fj;

.field final synthetic ex:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/NativeExpressView;

.field final synthetic hjc:Ljava/lang/String;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/core/nativeexpress/NativeExpressView;Ljava/lang/String;Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$Fj;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$4;->Ubf:Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;

    iput-object p2, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$4;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    iput-object p3, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$4;->ex:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/NativeExpressView;

    iput-object p4, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$4;->hjc:Ljava/lang/String;

    iput-object p5, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$4;->eV:Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$Fj;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj()V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$4;->Ubf:Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;->Ubf(Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;)V

    return-void
.end method

.method public Fj(Landroid/view/View;Z)V
    .locals 6

    if-nez p2, :cond_0

    return-void

    :cond_0
    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$4;->Ubf:Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;

    invoke-static {p2}, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;->WR(Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;)Ljava/util/concurrent/atomic/AtomicBoolean;

    move-result-object p2

    const/4 v0, 0x0

    const/4 v1, 0x1

    invoke-virtual {p2, v0, v1}, Ljava/util/concurrent/atomic/AtomicBoolean;->compareAndSet(ZZ)Z

    move-result p2

    if-eqz p2, :cond_1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$4;->Ubf:Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$4;->ex:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/NativeExpressView;

    iget-object v3, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$4;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    iget-object v4, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$4;->hjc:Ljava/lang/String;

    iget-object v5, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$4;->eV:Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$Fj;

    move-object v1, p1

    invoke-static/range {v0 .. v5}, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;->Fj(Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;Landroid/view/View;Lcom/bytedance/sdk/openadsdk/core/nativeexpress/NativeExpressView;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Ljava/lang/String;Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$Fj;)V

    :cond_1
    return-void
.end method

.method public Fj(Z)V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$4;->Ubf:Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$4;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {v0, p1, v1}, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;->Fj(Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;ZLcom/bytedance/sdk/openadsdk/core/model/Ql;)V

    return-void
.end method

.method public ex()V
    .locals 4

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$4;->Ubf:Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;

    const/4 v1, 0x1

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$4;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    const/4 v3, 0x0

    invoke-static {v0, v3, v1, v2}, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;->Fj(Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;Lcom/bytedance/sdk/openadsdk/core/EmptyView;ZLcom/bytedance/sdk/openadsdk/core/model/Ql;)V

    return-void
.end method
