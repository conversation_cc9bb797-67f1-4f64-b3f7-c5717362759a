.class Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG$1;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->ex(JJ)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 3

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Vq()V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->hjc(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object v0

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->spi:Lcom/bytedance/sdk/openadsdk/component/reward/view/hjc;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;

    invoke-static {v1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;)I

    move-result v1

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;

    invoke-static {v2}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->ex(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v1, v2}, Lcom/bytedance/sdk/openadsdk/component/reward/view/hjc;->Fj(ILjava/lang/String;)V

    return-void
.end method
