.class Lcom/bytedance/sdk/openadsdk/core/eV/Fj$Fj;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/sdk/openadsdk/core/eV/Fj;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "Fj"
.end annotation


# instance fields
.field private final Fj:J

.field private final ex:Ljava/lang/String;


# direct methods
.method private constructor <init>(JLjava/lang/String;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-wide p1, p0, Lcom/bytedance/sdk/openadsdk/core/eV/Fj$Fj;->Fj:J

    iput-object p3, p0, Lcom/bytedance/sdk/openadsdk/core/eV/Fj$Fj;->ex:Ljava/lang/String;

    return-void
.end method

.method public synthetic constructor <init>(JLjava/lang/String;Lcom/bytedance/sdk/openadsdk/core/eV/Fj$1;)V
    .locals 0

    invoke-direct {p0, p1, p2, p3}, Lcom/bytedance/sdk/openadsdk/core/eV/Fj$Fj;-><init>(JLjava/lang/String;)V

    return-void
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/openadsdk/core/eV/Fj$Fj;)J
    .locals 2

    iget-wide v0, p0, Lcom/bytedance/sdk/openadsdk/core/eV/Fj$Fj;->Fj:J

    return-wide v0
.end method

.method public static synthetic ex(Lcom/bytedance/sdk/openadsdk/core/eV/Fj$Fj;)Ljava/lang/String;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/openadsdk/core/eV/Fj$Fj;->ex:Ljava/lang/String;

    return-object p0
.end method
