.class public Lcom/bytedance/sdk/openadsdk/component/Ubf/Fj;
.super Ljava/lang/Object;


# instance fields
.field private Fj:I

.field private ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;


# direct methods
.method public constructor <init>(ILcom/bytedance/sdk/openadsdk/core/model/Ql;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/component/Ubf/Fj;->Fj:I

    iput-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/Ubf/Fj;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    return-void
.end method


# virtual methods
.method public Fj()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/component/Ubf/Fj;->Fj:I

    return v0
.end method

.method public ex()Lcom/bytedance/sdk/openadsdk/core/model/Ql;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/Ubf/Fj;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    return-object v0
.end method
