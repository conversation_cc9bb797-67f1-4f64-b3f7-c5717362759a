.class public Lcom/bytedance/sdk/openadsdk/component/ex;
.super Lcom/bytedance/sdk/openadsdk/component/hjc;


# instance fields
.field private Ko:Lcom/bytedance/sdk/openadsdk/component/view/PAGAppOpenAdExpressView;

.field private rAx:Z


# direct methods
.method public constructor <init>(Landroid/app/Activity;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Landroid/widget/FrameLayout;Lcom/bytedance/sdk/openadsdk/component/Fj;IZLcom/bytedance/sdk/openadsdk/component/BcC/Fj;)V
    .locals 0

    invoke-direct/range {p0 .. p7}, Lcom/bytedance/sdk/openadsdk/component/hjc;-><init>(Landroid/app/Activity;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Landroid/widget/FrameLayout;Lcom/bytedance/sdk/openadsdk/component/Fj;IZLcom/bytedance/sdk/openadsdk/component/BcC/Fj;)V

    return-void
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/openadsdk/component/ex;)Lcom/bytedance/sdk/openadsdk/component/view/PAGAppOpenAdExpressView;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/openadsdk/component/ex;->Ko:Lcom/bytedance/sdk/openadsdk/component/view/PAGAppOpenAdExpressView;

    return-object p0
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/openadsdk/component/ex;Landroid/view/ViewGroup;)V
    .locals 0

    invoke-super {p0, p1}, Lcom/bytedance/sdk/openadsdk/component/hjc;->Fj(Landroid/view/ViewGroup;)V

    return-void
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/openadsdk/component/ex;Z)Z
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/sdk/openadsdk/component/ex;->rAx:Z

    return p1
.end method

.method public static synthetic ex(Lcom/bytedance/sdk/openadsdk/component/ex;)V
    .locals 0

    invoke-super {p0}, Lcom/bytedance/sdk/openadsdk/component/hjc;->Fj()V

    return-void
.end method

.method public static synthetic hjc(Lcom/bytedance/sdk/openadsdk/component/ex;)V
    .locals 0

    invoke-super {p0}, Lcom/bytedance/sdk/openadsdk/component/hjc;->ex()V

    return-void
.end method


# virtual methods
.method public Fj(Lorg/json/JSONObject;)Lorg/json/JSONObject;
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/ex;->Ko:Lcom/bytedance/sdk/openadsdk/component/view/PAGAppOpenAdExpressView;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v0, p1, v1}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/NativeExpressView;->Fj(Lorg/json/JSONObject;Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Lorg/json/JSONObject;

    move-result-object p1

    return-object p1
.end method

.method public Fj()V
    .locals 4

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->Fj:Landroid/app/Activity;

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->mSE:Lcom/bytedance/sdk/openadsdk/component/BcC/Fj;

    iget-object v3, p0, Lcom/bytedance/sdk/openadsdk/component/ex;->Ko:Lcom/bytedance/sdk/openadsdk/component/view/PAGAppOpenAdExpressView;

    invoke-static {v0, v1, v2, v3}, Lcom/bytedance/sdk/openadsdk/component/Fj/ex;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Landroid/app/Activity;Lcom/bytedance/sdk/openadsdk/component/BcC/Fj;Lcom/bytedance/sdk/openadsdk/component/view/PAGAppOpenAdExpressView;)Lcom/bytedance/sdk/openadsdk/core/nativeexpress/svN;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/ex;->Ko:Lcom/bytedance/sdk/openadsdk/component/view/PAGAppOpenAdExpressView;

    invoke-virtual {v1, v0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/NativeExpressView;->setClickListener(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/svN;)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->Fj:Landroid/app/Activity;

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->mSE:Lcom/bytedance/sdk/openadsdk/component/BcC/Fj;

    iget-object v3, p0, Lcom/bytedance/sdk/openadsdk/component/ex;->Ko:Lcom/bytedance/sdk/openadsdk/component/view/PAGAppOpenAdExpressView;

    invoke-static {v0, v1, v2, v3}, Lcom/bytedance/sdk/openadsdk/component/Fj/ex;->ex(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Landroid/app/Activity;Lcom/bytedance/sdk/openadsdk/component/BcC/Fj;Lcom/bytedance/sdk/openadsdk/component/view/PAGAppOpenAdExpressView;)Lcom/bytedance/sdk/openadsdk/core/nativeexpress/WR;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/ex;->Ko:Lcom/bytedance/sdk/openadsdk/component/view/PAGAppOpenAdExpressView;

    invoke-virtual {v1, v0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/NativeExpressView;->setClickCreativeListener(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/WR;)V

    new-instance v1, Lcom/bytedance/sdk/openadsdk/component/ex$2;

    invoke-direct {v1, p0}, Lcom/bytedance/sdk/openadsdk/component/ex$2;-><init>(Lcom/bytedance/sdk/openadsdk/component/ex;)V

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->Fj(Lcom/bytedance/sdk/openadsdk/core/ex/ex$Fj;)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/ex;->Ko:Lcom/bytedance/sdk/openadsdk/component/view/PAGAppOpenAdExpressView;

    new-instance v1, Lcom/bytedance/sdk/openadsdk/component/ex$3;

    invoke-direct {v1, p0}, Lcom/bytedance/sdk/openadsdk/component/ex$3;-><init>(Lcom/bytedance/sdk/openadsdk/component/ex;)V

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/NativeExpressView;->setBackupListener(Lcom/bytedance/sdk/component/adexpress/ex/hjc;)V

    return-void
.end method

.method public Fj(IIZ)V
    .locals 2

    iget-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/component/ex;->rAx:Z

    if-eqz v0, :cond_0

    invoke-super {p0, p1, p2, p3}, Lcom/bytedance/sdk/openadsdk/component/hjc;->Fj(IIZ)V

    return-void

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/ex;->Ko:Lcom/bytedance/sdk/openadsdk/component/view/PAGAppOpenAdExpressView;

    invoke-static {p1}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    move-result-object p1

    const/4 v1, 0x0

    invoke-virtual {v0, p1, p2, v1, p3}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/NativeExpressView;->Fj(Ljava/lang/CharSequence;IIZ)V

    return-void
.end method

.method public Fj(Landroid/view/ViewGroup;)V
    .locals 4

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->Fj:Landroid/app/Activity;

    invoke-virtual {p1}, Landroid/app/Activity;->getWindow()Landroid/view/Window;

    move-result-object p1

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->svN:I

    invoke-static {p1, v0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Fj/Fj;->Fj(Landroid/view/Window;I)Landroid/util/Pair;

    move-result-object p1

    new-instance v0, Lcom/bytedance/sdk/openadsdk/AdSlot$Builder;

    invoke-direct {v0}, Lcom/bytedance/sdk/openadsdk/AdSlot$Builder;-><init>()V

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->aYy()I

    move-result v1

    invoke-static {v1}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/AdSlot$Builder;->setCodeId(Ljava/lang/String;)Lcom/bytedance/sdk/openadsdk/AdSlot$Builder;

    move-result-object v0

    iget-object v1, p1, Landroid/util/Pair;->first:Ljava/lang/Object;

    check-cast v1, Ljava/lang/Float;

    invoke-virtual {v1}, Ljava/lang/Float;->floatValue()F

    move-result v1

    iget-object p1, p1, Landroid/util/Pair;->second:Ljava/lang/Object;

    check-cast p1, Ljava/lang/Float;

    invoke-virtual {p1}, Ljava/lang/Float;->floatValue()F

    move-result p1

    invoke-virtual {v0, v1, p1}, Lcom/bytedance/sdk/openadsdk/AdSlot$Builder;->setExpressViewAcceptedSize(FF)Lcom/bytedance/sdk/openadsdk/AdSlot$Builder;

    move-result-object p1

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/AdSlot$Builder;->build()Lcom/bytedance/sdk/openadsdk/AdSlot;

    move-result-object p1

    invoke-static {}, Lcom/bytedance/sdk/component/utils/dG;->eV()Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->uM()Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;

    move-result-object v0

    if-eqz v0, :cond_0

    new-instance v1, Ljava/lang/StringBuilder;

    const-string v2, "tryDynamicNative: id is "

    invoke-direct {v1, v2}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;->Ubf()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    :cond_0
    new-instance v0, Lcom/bytedance/sdk/openadsdk/component/view/PAGAppOpenAdExpressView;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->Fj:Landroid/app/Activity;

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    const-string v3, "open_ad"

    invoke-direct {v0, v1, v2, p1, v3}, Lcom/bytedance/sdk/openadsdk/component/view/PAGAppOpenAdExpressView;-><init>(Landroid/content/Context;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/AdSlot;Ljava/lang/String;)V

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/ex;->Ko:Lcom/bytedance/sdk/openadsdk/component/view/PAGAppOpenAdExpressView;

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->Ubf:Lcom/bytedance/sdk/openadsdk/component/Fj;

    invoke-virtual {v0, p1}, Lcom/bytedance/sdk/openadsdk/component/view/PAGAppOpenAdExpressView;->setTopListener(Lcom/bytedance/sdk/openadsdk/component/WR/Fj;)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/ex;->Ko:Lcom/bytedance/sdk/openadsdk/component/view/PAGAppOpenAdExpressView;

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->Ubf:Lcom/bytedance/sdk/openadsdk/component/Fj;

    invoke-virtual {p1, v0}, Lcom/bytedance/sdk/openadsdk/component/view/PAGAppOpenAdExpressView;->setExpressVideoListenerProxy(Lcom/bykv/vk/openvk/component/video/api/eV/hjc$Fj;)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/ex;->Ko:Lcom/bytedance/sdk/openadsdk/component/view/PAGAppOpenAdExpressView;

    new-instance v0, Lcom/bytedance/sdk/openadsdk/component/ex$1;

    invoke-direct {v0, p0}, Lcom/bytedance/sdk/openadsdk/component/ex$1;-><init>(Lcom/bytedance/sdk/openadsdk/component/ex;)V

    invoke-virtual {p1, v0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/NativeExpressView;->setExpressInteractionListener(Lcom/bytedance/sdk/openadsdk/api/PAGExpressAdWrapperListener;)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    const/4 v0, 0x1

    invoke-virtual {p1, v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->BcC(I)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->eV:Landroid/widget/FrameLayout;

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/ex;->Ko:Lcom/bytedance/sdk/openadsdk/component/view/PAGAppOpenAdExpressView;

    new-instance v1, Landroid/widget/FrameLayout$LayoutParams;

    const/4 v2, -0x1

    invoke-direct {v1, v2, v2}, Landroid/widget/FrameLayout$LayoutParams;-><init>(II)V

    invoke-virtual {p1, v0, v1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;Landroid/view/ViewGroup$LayoutParams;)V

    return-void
.end method

.method public eV()I
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/ex;->Ko:Lcom/bytedance/sdk/openadsdk/component/view/PAGAppOpenAdExpressView;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/view/PAGAppOpenAdExpressView;->getDynamicShowType()I

    move-result v0

    return v0
.end method

.method public ex()V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/ex;->Ko:Lcom/bytedance/sdk/openadsdk/component/view/PAGAppOpenAdExpressView;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/NativeExpressView;->mSE()V

    return-void
.end method

.method public hjc()V
    .locals 1

    invoke-super {p0}, Lcom/bytedance/sdk/openadsdk/component/hjc;->hjc()V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/ex;->Ko:Lcom/bytedance/sdk/openadsdk/component/view/PAGAppOpenAdExpressView;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/NativeExpressView;->rAx()V

    :cond_0
    return-void
.end method
