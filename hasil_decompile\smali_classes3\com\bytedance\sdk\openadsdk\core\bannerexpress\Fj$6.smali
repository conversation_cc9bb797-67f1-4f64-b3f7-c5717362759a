.class Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$6;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf$Fj;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;->eV()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$6;->Fj:Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj()V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$6;->Fj:Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;->UYd(Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;)V

    return-void
.end method

.method public Fj(Ljava/util/List;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/bytedance/sdk/openadsdk/core/model/Ql;",
            ">;)V"
        }
    .end annotation

    if-eqz p1, :cond_1

    invoke-interface {p1}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    invoke-interface {p1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$6;->Fj:Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;

    iget-object v1, v0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/core/bannerexpress/BannerExpressView;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;->eV:Lcom/bytedance/sdk/openadsdk/AdSlot;

    invoke-virtual {v1, p1, v0}, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/BannerExpressView;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/AdSlot;)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$6;->Fj:Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;

    invoke-static {v0, p1}, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;->Fj(Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;Lcom/bytedance/sdk/openadsdk/core/model/Ql;)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$6;->Fj:Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/core/bannerexpress/BannerExpressView;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/BannerExpressView;->eV()V

    return-void

    :cond_1
    :goto_0
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$6;->Fj:Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;->UYd(Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;)V

    return-void
.end method
