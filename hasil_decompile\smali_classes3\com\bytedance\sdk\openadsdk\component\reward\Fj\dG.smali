.class public Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;
.super Ljava/lang/Object;


# instance fields
.field private final BcC:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

.field protected Fj:Z

.field private JU:Z

.field private JW:Ljava/util/HashSet;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/HashSet<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field private final Ko:Ljava/lang/String;

.field private final Ql:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

.field private Tc:Ljava/lang/String;

.field private UYd:J

.field protected Ubf:Z

.field WR:Lcom/bykv/vk/openvk/component/video/api/eV/hjc;

.field private dG:I

.field final eV:Z

.field ex:Z

.field hjc:Z

.field private mSE:Landroid/widget/FrameLayout;

.field private rAx:J

.field private rS:Z

.field private final svN:Landroid/app/Activity;

.field private vYf:Lcom/bytedance/sdk/openadsdk/ex/svN;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;)V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    iput-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Fj:Z

    iput-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->ex:Z

    iput-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Ubf:Z

    const/4 v0, -0x1

    iput v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->dG:I

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Ql:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Kk:Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->svN:Landroid/app/Activity;

    iget-object v0, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->BcC:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    iget-boolean v0, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->WR:Z

    iput-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->eV:Z

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->svN:Ljava/lang/String;

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Ko:Ljava/lang/String;

    new-instance p1, Ljava/util/HashSet;

    invoke-direct {p1}, Ljava/util/HashSet;-><init>()V

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->JW:Ljava/util/HashSet;

    return-void
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;)I
    .locals 0

    iget p0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->dG:I

    return p0
.end method

.method private Fj(JZ)Z
    .locals 7

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->WR:Lcom/bykv/vk/openvk/component/video/api/eV/hjc;

    if-eqz v0, :cond_3

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->BcC:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->spi()Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

    move-result-object v0

    if-nez v0, :cond_0

    goto/16 :goto_0

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->BcC:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->aGk()I

    move-result v0

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/CacheDirFactory;->getICacheDir(I)Lcom/bykv/vk/openvk/component/video/api/Fj/ex;

    move-result-object v0

    invoke-interface {v0}, Lcom/bykv/vk/openvk/component/video/api/Fj/ex;->Fj()Ljava/lang/String;

    move-result-object v0

    new-instance v1, Ljava/io/File;

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->BcC:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v2}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->spi()Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

    move-result-object v2

    invoke-virtual {v2}, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->Tc()Ljava/lang/String;

    move-result-object v2

    invoke-direct {v1, v0, v2}, Ljava/io/File;-><init>(Ljava/lang/String;Ljava/lang/String;)V

    invoke-virtual {v1}, Ljava/io/File;->exists()Z

    move-result v2

    const/4 v3, 0x1

    if-eqz v2, :cond_1

    invoke-virtual {v1}, Ljava/io/File;->length()J

    move-result-wide v1

    const-wide/16 v4, 0x0

    cmp-long v6, v1, v4

    if-lez v6, :cond_1

    iput-boolean v3, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->ex:Z

    :cond_1
    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->BcC:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {v0, v1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->Fj(Ljava/lang/String;Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Lcom/bytedance/sdk/openadsdk/core/video/Fj/ex;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->BcC:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->cs()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->ex(Ljava/lang/String;)V

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->mSE:Landroid/widget/FrameLayout;

    invoke-virtual {v1}, Landroid/view/View;->getWidth()I

    move-result v1

    invoke-virtual {v0, v1}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->Fj(I)V

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->mSE:Landroid/widget/FrameLayout;

    invoke-virtual {v1}, Landroid/view/View;->getHeight()I

    move-result v1

    invoke-virtual {v0, v1}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->ex(I)V

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->BcC:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->Jq()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->hjc(Ljava/lang/String;)V

    invoke-virtual {v0, p1, p2}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->Fj(J)V

    invoke-virtual {v0, p3}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->Fj(Z)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Ql:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Kk:Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->JU()Z

    move-result p1

    if-eqz p1, :cond_2

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Ql:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->spi:Lcom/bytedance/sdk/openadsdk/component/reward/view/hjc;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/view/hjc;->BcC()Z

    move-result p1

    if-nez p1, :cond_2

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->BcC:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->Ubf(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result p1

    if-eqz p1, :cond_2

    iput v3, v0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->eV:I

    :cond_2
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->WR:Lcom/bykv/vk/openvk/component/video/api/eV/hjc;

    invoke-interface {p1, v0}, Lcom/bykv/vk/openvk/component/video/api/eV/hjc;->Fj(Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;)Z

    move-result p1

    return p1

    :cond_3
    :goto_0
    const/4 p1, 0x0

    return p1
.end method

.method public static synthetic eV(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;)Lcom/bytedance/sdk/openadsdk/core/model/Ql;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->BcC:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    return-object p0
.end method

.method public static synthetic ex(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;)Ljava/lang/String;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Tc:Ljava/lang/String;

    return-object p0
.end method

.method private ex(JJ)V
    .locals 5

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->dG:I

    int-to-long v0, v0

    sub-long/2addr v0, p1

    invoke-static {v0, v1}, Ljava/lang/Math;->abs(J)J

    move-result-wide v0

    long-to-int v1, v0

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->dG:I

    if-ltz v0, :cond_2

    const/16 v2, 0x1f4

    if-gt v1, v2, :cond_2

    int-to-long v3, v0

    cmp-long v0, v3, p3

    if-lez v0, :cond_0

    goto :goto_1

    :cond_0
    if-ge v1, v2, :cond_2

    iget-object p3, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->JW:Ljava/util/HashSet;

    iget-object p4, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Tc:Ljava/lang/String;

    invoke-virtual {p3, p4}, Ljava/util/HashSet;->contains(Ljava/lang/Object;)Z

    move-result p3

    if-nez p3, :cond_2

    iget p3, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->dG:I

    int-to-long p3, p3

    cmp-long v0, p3, p1

    if-lez v0, :cond_1

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/dG;->hjc()Landroid/os/Handler;

    move-result-object p1

    new-instance p2, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG$1;

    invoke-direct {p2, p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG$1;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;)V

    int-to-long p3, v1

    invoke-virtual {p1, p2, p3, p4}, Landroid/os/Handler;->postDelayed(Ljava/lang/Runnable;J)Z

    goto :goto_0

    :cond_1
    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Vq()V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Ql:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->spi:Lcom/bytedance/sdk/openadsdk/component/reward/view/hjc;

    iget p2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->dG:I

    iget-object p3, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Tc:Ljava/lang/String;

    invoke-virtual {p1, p2, p3}, Lcom/bytedance/sdk/openadsdk/component/reward/view/hjc;->Fj(ILjava/lang/String;)V

    :goto_0
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->JW:Ljava/util/HashSet;

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Tc:Ljava/lang/String;

    invoke-virtual {p1, p2}, Ljava/util/HashSet;->add(Ljava/lang/Object;)Z

    :cond_2
    :goto_1
    return-void
.end method

.method private fj()V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->WR:Lcom/bykv/vk/openvk/component/video/api/eV/hjc;

    if-eqz v0, :cond_2

    invoke-interface {v0}, Lcom/bykv/vk/openvk/component/video/api/eV/hjc;->UYd()Lcom/bykv/vk/openvk/component/video/api/Fj;

    move-result-object v0

    if-nez v0, :cond_0

    goto :goto_0

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->WR:Lcom/bykv/vk/openvk/component/video/api/eV/hjc;

    invoke-interface {v0}, Lcom/bykv/vk/openvk/component/video/api/eV/hjc;->Ubf()J

    move-result-wide v0

    iput-wide v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->rAx:J

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->WR:Lcom/bykv/vk/openvk/component/video/api/eV/hjc;

    invoke-interface {v0}, Lcom/bykv/vk/openvk/component/video/api/eV/hjc;->UYd()Lcom/bykv/vk/openvk/component/video/api/Fj;

    move-result-object v0

    invoke-interface {v0}, Lcom/bykv/vk/openvk/component/video/api/Fj;->hjc()Z

    move-result v0

    if-nez v0, :cond_1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->WR:Lcom/bykv/vk/openvk/component/video/api/eV/hjc;

    invoke-interface {v0}, Lcom/bykv/vk/openvk/component/video/api/eV/hjc;->UYd()Lcom/bykv/vk/openvk/component/video/api/Fj;

    move-result-object v0

    invoke-interface {v0}, Lcom/bykv/vk/openvk/component/video/api/Fj;->ex()Z

    move-result v0

    if-nez v0, :cond_2

    :cond_1
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->WR:Lcom/bykv/vk/openvk/component/video/api/eV/hjc;

    invoke-interface {v0}, Lcom/bykv/vk/openvk/component/video/api/eV/hjc;->Fj()V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->WR:Lcom/bykv/vk/openvk/component/video/api/eV/hjc;

    invoke-interface {v0}, Lcom/bykv/vk/openvk/component/video/api/eV/hjc;->hjc()V

    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Fj:Z

    :cond_2
    :goto_0
    return-void
.end method

.method public static synthetic hjc(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Ql:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    return-object p0
.end method


# virtual methods
.method public Af()Z
    .locals 4

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->WR:Lcom/bykv/vk/openvk/component/video/api/eV/hjc;

    const/4 v1, 0x0

    if-eqz v0, :cond_4

    invoke-interface {v0}, Lcom/bykv/vk/openvk/component/video/api/eV/hjc;->UYd()Lcom/bykv/vk/openvk/component/video/api/Fj;

    move-result-object v0

    const/4 v2, 0x1

    if-eqz v0, :cond_2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->WR:Lcom/bykv/vk/openvk/component/video/api/eV/hjc;

    invoke-interface {v0}, Lcom/bykv/vk/openvk/component/video/api/eV/hjc;->UYd()Lcom/bykv/vk/openvk/component/video/api/Fj;

    move-result-object v0

    invoke-interface {v0}, Lcom/bykv/vk/openvk/component/video/api/Fj;->svN()Z

    move-result v3

    if-nez v3, :cond_0

    invoke-interface {v0}, Lcom/bykv/vk/openvk/component/video/api/Fj;->BcC()Z

    move-result v0

    if-eqz v0, :cond_4

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->WR:Lcom/bykv/vk/openvk/component/video/api/eV/hjc;

    instance-of v1, v0, Lcom/bytedance/sdk/openadsdk/core/video/hjc/Fj;

    if-eqz v1, :cond_1

    check-cast v0, Lcom/bytedance/sdk/openadsdk/core/video/hjc/Fj;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/video/hjc/Fj;->kF()V

    :cond_1
    return v2

    :cond_2
    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->WR()Z

    move-result v0

    if-eqz v0, :cond_4

    invoke-virtual {p0, v1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Fj(Z)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->WR:Lcom/bykv/vk/openvk/component/video/api/eV/hjc;

    instance-of v1, v0, Lcom/bytedance/sdk/openadsdk/core/video/hjc/Fj;

    if-eqz v1, :cond_3

    check-cast v0, Lcom/bytedance/sdk/openadsdk/core/video/hjc/Fj;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/video/hjc/Fj;->kF()V

    :cond_3
    return v2

    :cond_4
    return v1
.end method

.method public BcC()J
    .locals 2

    iget-wide v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->UYd:J

    return-wide v0
.end method

.method public Fj()Lcom/bytedance/sdk/openadsdk/ex/svN;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->vYf:Lcom/bytedance/sdk/openadsdk/ex/svN;

    return-object v0
.end method

.method public Fj(II)V
    .locals 3

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->WR:Lcom/bykv/vk/openvk/component/video/api/eV/hjc;

    if-eqz v0, :cond_0

    new-instance v0, Lcom/bytedance/sdk/openadsdk/ex/eV/ex/JW$Fj;

    invoke-direct {v0}, Lcom/bytedance/sdk/openadsdk/ex/eV/ex/JW$Fj;-><init>()V

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->rAx()J

    move-result-wide v1

    invoke-virtual {v0, v1, v2}, Lcom/bytedance/sdk/openadsdk/ex/eV/ex/JW$Fj;->ex(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->vYf()J

    move-result-wide v1

    invoke-virtual {v0, v1, v2}, Lcom/bytedance/sdk/openadsdk/ex/eV/ex/JW$Fj;->hjc(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->svN()J

    move-result-wide v1

    invoke-virtual {v0, v1, v2}, Lcom/bytedance/sdk/openadsdk/ex/eV/ex/JW$Fj;->Fj(J)V

    invoke-virtual {v0, p1}, Lcom/bytedance/sdk/openadsdk/ex/eV/ex/JW$Fj;->Fj(I)V

    invoke-virtual {v0, p2}, Lcom/bytedance/sdk/openadsdk/ex/eV/ex/JW$Fj;->ex(I)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->WR:Lcom/bykv/vk/openvk/component/video/api/eV/hjc;

    invoke-interface {p1}, Lcom/bykv/vk/openvk/component/video/api/eV/hjc;->dG()Lcom/bykv/vk/openvk/component/video/api/eV/ex;

    move-result-object p1

    invoke-static {p1, v0}, Lcom/bytedance/sdk/openadsdk/ex/eV/Fj/Fj;->eV(Lcom/bykv/vk/openvk/component/video/api/ex/Fj;Lcom/bytedance/sdk/openadsdk/ex/eV/ex/JW$Fj;)V

    :cond_0
    return-void
.end method

.method public Fj(ILjava/lang/String;)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->dG:I

    iput-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Tc:Ljava/lang/String;

    return-void
.end method

.method public Fj(J)V
    .locals 0

    iput-wide p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->rAx:J

    return-void
.end method

.method public Fj(JJ)V
    .locals 0

    iput-wide p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->UYd:J

    invoke-direct {p0, p1, p2, p3, p4}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->ex(JJ)V

    return-void
.end method

.method public Fj(Landroid/widget/FrameLayout;Lcom/bytedance/sdk/openadsdk/ex/svN;)V
    .locals 3

    iget-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->JU:Z

    if-eqz v0, :cond_0

    return-void

    :cond_0
    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->JU:Z

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->mSE:Landroid/widget/FrameLayout;

    iput-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->vYf:Lcom/bytedance/sdk/openadsdk/ex/svN;

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->BcC:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->Ubf(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result p1

    if-eqz p1, :cond_1

    new-instance p1, Lcom/bytedance/sdk/openadsdk/core/video/hjc/Fj;

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->svN:Landroid/app/Activity;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->mSE:Landroid/widget/FrameLayout;

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->BcC:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-direct {p1, v0, v1, v2, p2}, Lcom/bytedance/sdk/openadsdk/core/video/hjc/Fj;-><init>(Landroid/content/Context;Landroid/view/ViewGroup;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/ex/svN;)V

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->WR:Lcom/bykv/vk/openvk/component/video/api/eV/hjc;

    iget-boolean p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->rS:Z

    invoke-virtual {p0, p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Ubf(Z)V

    return-void

    :cond_1
    new-instance p1, Lcom/bytedance/sdk/openadsdk/component/reward/hjc;

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->BcC:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-direct {p1, v0, p2}, Lcom/bytedance/sdk/openadsdk/component/reward/hjc;-><init>(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/ex/svN;)V

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->WR:Lcom/bykv/vk/openvk/component/video/api/eV/hjc;

    return-void
.end method

.method public Fj(Lcom/bykv/vk/openvk/component/video/api/eV/hjc$Fj;)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->WR:Lcom/bykv/vk/openvk/component/video/api/eV/hjc;

    if-eqz v0, :cond_0

    invoke-interface {v0, p1}, Lcom/bykv/vk/openvk/component/video/api/eV/hjc;->Fj(Lcom/bykv/vk/openvk/component/video/api/eV/hjc$Fj;)V

    :cond_0
    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;)V
    .locals 3

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Ql:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Af:Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-virtual {v0}, Ljava/util/concurrent/atomic/AtomicBoolean;->get()Z

    move-result v0

    if-nez v0, :cond_4

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Ql:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-boolean v1, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Ubf:Z

    if-eqz v1, :cond_4

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/model/mE;->rAx(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v0

    if-nez v0, :cond_4

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Ql:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->Ubf(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v0

    if-nez v0, :cond_0

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/JU;->eV()Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Ql:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget v1, v1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->JW:I

    invoke-static {v1}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    move-result-object v1

    invoke-interface {v0, v1}, Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;->UYd(Ljava/lang/String;)I

    move-result v0

    const/4 v1, 0x1

    if-ne v0, v1, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Ql:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->spi:Lcom/bytedance/sdk/openadsdk/component/reward/view/hjc;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/view/hjc;->eV()Z

    move-result v0

    if-nez v0, :cond_4

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Ql:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/model/JW;->hjc(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v0

    if-eqz v0, :cond_1

    goto :goto_0

    :cond_1
    iget-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Ubf:Z

    if-eqz v0, :cond_2

    return-void

    :cond_2
    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;->WR()Z

    move-result p1

    if-nez p1, :cond_3

    return-void

    :cond_3
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Ql:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->YH:Lcom/bytedance/sdk/component/utils/Vq;

    const/16 v0, 0x12c

    invoke-virtual {p1, v0}, Landroid/os/Handler;->removeMessages(I)V

    invoke-static {}, Landroid/os/Message;->obtain()Landroid/os/Message;

    move-result-object p1

    iput v0, p1, Landroid/os/Message;->what:I

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Ql:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->YH:Lcom/bytedance/sdk/component/utils/Vq;

    const-wide/16 v1, 0x1388

    invoke-virtual {v0, p1, v1, v2}, Landroid/os/Handler;->sendMessageDelayed(Landroid/os/Message;J)Z

    :cond_4
    :goto_0
    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/openadsdk/core/video/hjc/ex;)V
    .locals 2

    const/4 v0, 0x0

    :try_start_0
    iput-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Ubf:Z

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->WR()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-direct {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->fj()V

    invoke-virtual {p0, p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->ex(Lcom/bytedance/sdk/openadsdk/core/video/hjc/ex;)V

    return-void

    :catchall_0
    move-exception p1

    goto :goto_0

    :cond_0
    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->eV()Z

    move-result p1

    if-eqz p1, :cond_1

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Tc()V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :cond_1
    return-void

    :goto_0
    new-instance v0, Ljava/lang/StringBuilder;

    const-string v1, "onContinue throw Exception :"

    invoke-direct {v0, v1}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    invoke-virtual {p1}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    const-string v0, "TTAD.RFVideoPlayerMag"

    invoke-static {v0, p1}, Lcom/bytedance/sdk/component/utils/dG;->Fj(Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public Fj(Ljava/lang/String;)V
    .locals 12

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->WR:Lcom/bykv/vk/openvk/component/video/api/eV/hjc;

    if-eqz v0, :cond_0

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->BcC:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-interface {v0}, Lcom/bykv/vk/openvk/component/video/api/eV/hjc;->WR()J

    move-result-wide v2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->WR:Lcom/bykv/vk/openvk/component/video/api/eV/hjc;

    invoke-interface {v0}, Lcom/bykv/vk/openvk/component/video/api/eV/hjc;->UYd()Lcom/bykv/vk/openvk/component/video/api/Fj;

    move-result-object v0

    invoke-static {v1, v2, v3, v0}, Lcom/bytedance/sdk/openadsdk/utils/rXP;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;JLcom/bykv/vk/openvk/component/video/api/Fj;)Lorg/json/JSONObject;

    move-result-object v10

    iget-object v4, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->BcC:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    iget-object v5, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Ko:Ljava/lang/String;

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->svN()J

    move-result-wide v7

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Ql()I

    move-result v9

    iget-object v11, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->vYf:Lcom/bytedance/sdk/openadsdk/ex/svN;

    move-object v6, p1

    invoke-static/range {v4 .. v11}, Lcom/bytedance/sdk/openadsdk/ex/hjc;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Ljava/lang/String;Ljava/lang/String;JILorg/json/JSONObject;Lcom/bytedance/sdk/openadsdk/ex/svN;)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->vYf()J

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Ql()I

    :cond_0
    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->rXP()V

    return-void
.end method

.method public Fj(Z)V
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Fj:Z

    return-void
.end method

.method public Fj(ZLcom/bytedance/sdk/openadsdk/core/video/hjc/ex;Z)V
    .locals 0

    if-eqz p3, :cond_2

    if-nez p1, :cond_2

    iget-boolean p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Ubf:Z

    if-eqz p1, :cond_0

    goto :goto_0

    :cond_0
    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->eV()Z

    move-result p1

    if-eqz p1, :cond_1

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Tc()V

    return-void

    :cond_1
    invoke-direct {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->fj()V

    invoke-virtual {p0, p2}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->ex(Lcom/bytedance/sdk/openadsdk/core/video/hjc/ex;)V

    :cond_2
    :goto_0
    return-void
.end method

.method public Fj(JZLjava/util/Map;Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;)Z
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(JZ",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/Object;",
            ">;",
            "Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;",
            ")Z"
        }
    .end annotation

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->mC()Z

    move-result v0

    const/4 v1, 0x0

    if-nez v0, :cond_0

    return v1

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Ql:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/model/JW;->hjc(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v0

    if-eqz v0, :cond_1

    const/4 p1, 0x1

    return p1

    :cond_1
    if-eqz p3, :cond_2

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->cB()Z

    move-result v0

    if-nez v0, :cond_3

    :cond_2
    invoke-virtual {p0, p5}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;)V

    :cond_3
    :try_start_0
    iget-object p5, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Ql:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-boolean p5, p5, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->eV:Z

    invoke-direct {p0, p1, p2, p5}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Fj(JZ)Z

    move-result v1
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception p1

    const-string p2, "TTAD.RFVideoPlayerMag"

    const-string p5, "playVideo: "

    invoke-static {p2, p5, p1}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)I

    :goto_0
    if-eqz v1, :cond_4

    if-nez p3, :cond_4

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Ql:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->efV:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC;

    invoke-virtual {p1, p4}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC;->Fj(Ljava/util/Map;)V

    :cond_4
    return v1
.end method

.method public JU()J
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->WR:Lcom/bykv/vk/openvk/component/video/api/eV/hjc;

    if-eqz v0, :cond_0

    invoke-interface {v0}, Lcom/bykv/vk/openvk/component/video/api/eV/hjc;->BcC()J

    move-result-wide v0

    return-wide v0

    :cond_0
    const-wide/16 v0, 0x0

    return-wide v0
.end method

.method public JW()V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->WR:Lcom/bykv/vk/openvk/component/video/api/eV/hjc;

    if-eqz v0, :cond_0

    invoke-interface {v0}, Lcom/bykv/vk/openvk/component/video/api/eV/hjc;->Fj()V

    :cond_0
    return-void
.end method

.method public Ko()V
    .locals 3

    :try_start_0
    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->ex()Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->WR:Lcom/bykv/vk/openvk/component/video/api/eV/hjc;

    invoke-interface {v0}, Lcom/bykv/vk/openvk/component/video/api/eV/hjc;->Fj()V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_0

    :catchall_0
    move-exception v0

    goto :goto_1

    :cond_0
    :goto_0
    return-void

    :goto_1
    new-instance v1, Ljava/lang/StringBuilder;

    const-string v2, "RewardFullVideoPlayerManager onPause throw Exception :"

    invoke-direct {v1, v2}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    invoke-virtual {v0}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/bytedance/sdk/component/utils/dG;->ex(Ljava/lang/String;)V

    return-void
.end method

.method public Moo()Z
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->WR:Lcom/bykv/vk/openvk/component/video/api/eV/hjc;

    if-eqz v0, :cond_0

    invoke-interface {v0}, Lcom/bykv/vk/openvk/component/video/api/eV/hjc;->UYd()Lcom/bykv/vk/openvk/component/video/api/Fj;

    move-result-object v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->WR:Lcom/bykv/vk/openvk/component/video/api/eV/hjc;

    invoke-interface {v0}, Lcom/bykv/vk/openvk/component/video/api/eV/hjc;->UYd()Lcom/bykv/vk/openvk/component/video/api/Fj;

    move-result-object v0

    invoke-interface {v0}, Lcom/bykv/vk/openvk/component/video/api/Fj;->Fj()Z

    move-result v0

    return v0

    :cond_0
    const/4 v0, 0x0

    return v0
.end method

.method public Ql()I
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->WR:Lcom/bykv/vk/openvk/component/video/api/eV/hjc;

    if-eqz v0, :cond_0

    invoke-interface {v0}, Lcom/bykv/vk/openvk/component/video/api/eV/hjc;->Ko()I

    move-result v0

    return v0

    :cond_0
    const/4 v0, 0x0

    return v0
.end method

.method public Tc()V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->WR:Lcom/bykv/vk/openvk/component/video/api/eV/hjc;

    if-eqz v0, :cond_0

    invoke-interface {v0}, Lcom/bykv/vk/openvk/component/video/api/eV/hjc;->ex()V

    :cond_0
    return-void
.end method

.method public UYd()V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->WR:Lcom/bykv/vk/openvk/component/video/api/eV/hjc;

    if-nez v0, :cond_0

    return-void

    :cond_0
    invoke-interface {v0}, Lcom/bykv/vk/openvk/component/video/api/eV/hjc;->hjc()V

    const/4 v0, 0x0

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->WR:Lcom/bykv/vk/openvk/component/video/api/eV/hjc;

    return-void
.end method

.method public Ubf(Z)V
    .locals 1

    iput-boolean p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->rS:Z

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->WR:Lcom/bykv/vk/openvk/component/video/api/eV/hjc;

    instance-of v0, v0, Lcom/bytedance/sdk/openadsdk/core/video/hjc/Fj;

    if-nez v0, :cond_0

    return-void

    :cond_0
    if-eqz p1, :cond_1

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->BcC:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->spi()Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

    move-result-object p1

    invoke-virtual {p1}, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->mC()I

    move-result p1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->WR:Lcom/bykv/vk/openvk/component/video/api/eV/hjc;

    check-cast v0, Lcom/bytedance/sdk/openadsdk/core/video/hjc/Fj;

    invoke-virtual {v0, p1}, Lcom/bytedance/sdk/openadsdk/core/video/Fj/Fj;->ex(I)V

    return-void

    :cond_1
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->BcC:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->spi()Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

    move-result-object p1

    const/4 v0, 0x1

    invoke-virtual {p1, v0}, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->Ko(I)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->WR:Lcom/bykv/vk/openvk/component/video/api/eV/hjc;

    check-cast p1, Lcom/bytedance/sdk/openadsdk/core/video/hjc/Fj;

    invoke-virtual {p1, v0}, Lcom/bytedance/sdk/openadsdk/core/video/Fj/Fj;->ex(I)V

    return-void
.end method

.method public Ubf()Z
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->WR:Lcom/bykv/vk/openvk/component/video/api/eV/hjc;

    if-eqz v0, :cond_0

    invoke-interface {v0}, Lcom/bykv/vk/openvk/component/video/api/eV/hjc;->JU()Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    return v0

    :cond_0
    const/4 v0, 0x0

    return v0
.end method

.method public Vq()V
    .locals 3

    :try_start_0
    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->ex()Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Ubf:Z

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->JW()V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception v0

    goto :goto_1

    :cond_0
    :goto_0
    return-void

    :goto_1
    new-instance v1, Ljava/lang/StringBuilder;

    const-string v2, "onPause throw Exception :"

    invoke-direct {v1, v2}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    invoke-virtual {v0}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v1, "TTAD.RFVideoPlayerMag"

    invoke-static {v1, v0}, Lcom/bytedance/sdk/component/utils/dG;->Fj(Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public WR()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Fj:Z

    return v0
.end method

.method public cB()Z
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->WR:Lcom/bykv/vk/openvk/component/video/api/eV/hjc;

    if-eqz v0, :cond_0

    invoke-interface {v0}, Lcom/bykv/vk/openvk/component/video/api/eV/hjc;->UYd()Lcom/bykv/vk/openvk/component/video/api/Fj;

    move-result-object v0

    if-nez v0, :cond_0

    const/4 v0, 0x1

    return v0

    :cond_0
    const/4 v0, 0x0

    return v0
.end method

.method public dG()V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->WR:Lcom/bykv/vk/openvk/component/video/api/eV/hjc;

    if-eqz v0, :cond_0

    invoke-interface {v0}, Lcom/bykv/vk/openvk/component/video/api/eV/hjc;->eV()V

    :cond_0
    return-void
.end method

.method public eV(Z)V
    .locals 2

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->UYd()V

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/dG;->ex()Landroid/os/Handler;

    move-result-object v0

    new-instance v1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG$2;

    invoke-direct {v1, p0, p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG$2;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;Z)V

    invoke-virtual {v0, v1}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    return-void
.end method

.method public eV()Z
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->WR:Lcom/bykv/vk/openvk/component/video/api/eV/hjc;

    if-eqz v0, :cond_0

    invoke-interface {v0}, Lcom/bykv/vk/openvk/component/video/api/eV/hjc;->UYd()Lcom/bykv/vk/openvk/component/video/api/Fj;

    move-result-object v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->WR:Lcom/bykv/vk/openvk/component/video/api/eV/hjc;

    invoke-interface {v0}, Lcom/bykv/vk/openvk/component/video/api/eV/hjc;->UYd()Lcom/bykv/vk/openvk/component/video/api/Fj;

    move-result-object v0

    invoke-interface {v0}, Lcom/bykv/vk/openvk/component/video/api/Fj;->svN()Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    return v0

    :cond_0
    const/4 v0, 0x0

    return v0
.end method

.method public ex(Lcom/bytedance/sdk/openadsdk/core/video/hjc/ex;)V
    .locals 3

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Af()Z

    move-result v0

    if-eqz v0, :cond_0

    if-eqz p1, :cond_0

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->mSE()J

    move-result-wide v0

    const/4 v2, 0x1

    invoke-interface {p1, v0, v1, v2}, Lcom/bytedance/sdk/openadsdk/core/video/hjc/ex;->Fj(JZ)Z

    :cond_0
    return-void
.end method

.method public ex(Z)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->WR:Lcom/bykv/vk/openvk/component/video/api/eV/hjc;

    if-eqz v0, :cond_0

    invoke-interface {v0, p1}, Lcom/bykv/vk/openvk/component/video/api/eV/hjc;->ex(Z)V

    :cond_0
    return-void
.end method

.method public ex()Z
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->WR:Lcom/bykv/vk/openvk/component/video/api/eV/hjc;

    if-eqz v0, :cond_0

    invoke-interface {v0}, Lcom/bykv/vk/openvk/component/video/api/eV/hjc;->UYd()Lcom/bykv/vk/openvk/component/video/api/Fj;

    move-result-object v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->WR:Lcom/bykv/vk/openvk/component/video/api/eV/hjc;

    invoke-interface {v0}, Lcom/bykv/vk/openvk/component/video/api/eV/hjc;->UYd()Lcom/bykv/vk/openvk/component/video/api/Fj;

    move-result-object v0

    invoke-interface {v0}, Lcom/bykv/vk/openvk/component/video/api/Fj;->WR()Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    return v0

    :cond_0
    const/4 v0, 0x0

    return v0
.end method

.method public hjc()Lcom/bykv/vk/openvk/component/video/api/ex/Fj;
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->WR:Lcom/bykv/vk/openvk/component/video/api/eV/hjc;

    if-eqz v0, :cond_1

    instance-of v1, v0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc;

    if-eqz v1, :cond_0

    check-cast v0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/hjc;->rS()Lcom/bykv/vk/openvk/component/video/api/ex/Fj;

    move-result-object v0

    return-object v0

    :cond_0
    invoke-interface {v0}, Lcom/bykv/vk/openvk/component/video/api/eV/hjc;->dG()Lcom/bykv/vk/openvk/component/video/api/eV/ex;

    move-result-object v0

    return-object v0

    :cond_1
    const/4 v0, 0x0

    return-object v0
.end method

.method public hjc(Z)V
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->hjc:Z

    return-void
.end method

.method public lv()Landroid/view/View;
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->WR:Lcom/bykv/vk/openvk/component/video/api/eV/hjc;

    instance-of v1, v0, Lcom/bytedance/sdk/openadsdk/core/video/hjc/Fj;

    if-eqz v1, :cond_0

    check-cast v0, Lcom/bytedance/sdk/openadsdk/core/video/hjc/Fj;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/video/hjc/Fj;->uM()Lcom/bykv/vk/openvk/component/video/api/renderview/ex;

    move-result-object v0

    check-cast v0, Landroid/view/View;

    return-object v0

    :cond_0
    const/4 v0, 0x0

    return-object v0
.end method

.method public mC()Z
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->WR:Lcom/bykv/vk/openvk/component/video/api/eV/hjc;

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    return v0

    :cond_0
    const/4 v0, 0x0

    return v0
.end method

.method public mE()J
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->WR:Lcom/bykv/vk/openvk/component/video/api/eV/hjc;

    if-eqz v0, :cond_0

    invoke-interface {v0}, Lcom/bykv/vk/openvk/component/video/api/eV/hjc;->BcC()J

    move-result-wide v0

    return-wide v0

    :cond_0
    const-wide/16 v0, 0x0

    return-wide v0
.end method

.method public mSE()J
    .locals 2

    iget-wide v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->rAx:J

    return-wide v0
.end method

.method public nsB()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->hjc:Z

    return v0
.end method

.method public rAx()J
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->WR:Lcom/bykv/vk/openvk/component/video/api/eV/hjc;

    if-eqz v0, :cond_0

    invoke-interface {v0}, Lcom/bykv/vk/openvk/component/video/api/eV/hjc;->WR()J

    move-result-wide v0

    return-wide v0

    :cond_0
    const-wide/16 v0, 0x0

    return-wide v0
.end method

.method public rS()I
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->WR:Lcom/bykv/vk/openvk/component/video/api/eV/hjc;

    if-eqz v0, :cond_0

    invoke-interface {v0}, Lcom/bykv/vk/openvk/component/video/api/eV/hjc;->svN()I

    move-result v0

    return v0

    :cond_0
    const/4 v0, 0x0

    return v0
.end method

.method public rXP()V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->WR:Lcom/bykv/vk/openvk/component/video/api/eV/hjc;

    instance-of v1, v0, Lcom/bytedance/sdk/openadsdk/core/video/hjc/Fj;

    if-eqz v1, :cond_0

    check-cast v0, Lcom/bytedance/sdk/openadsdk/core/video/hjc/Fj;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/video/hjc/Fj;->gXF()V

    :cond_0
    return-void
.end method

.method public rf()D
    .locals 5

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->BcC:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/model/JW;->hjc(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->BcC:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->BcC()Lcom/bytedance/sdk/openadsdk/core/model/JU;

    move-result-object v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->BcC:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->BcC()Lcom/bytedance/sdk/openadsdk/core/model/JU;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/JU;->ex()J

    move-result-wide v0

    long-to-double v0, v0

    return-wide v0

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->BcC:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->spi()Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

    move-result-object v0

    if-eqz v0, :cond_1

    invoke-virtual {v0}, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->WR()D

    move-result-wide v1

    invoke-virtual {v0}, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->mC()I

    move-result v0

    int-to-double v3, v0

    mul-double v1, v1, v3

    return-wide v1

    :cond_1
    const-wide/16 v0, 0x0

    return-wide v0
.end method

.method public svN()J
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->WR:Lcom/bykv/vk/openvk/component/video/api/eV/hjc;

    if-eqz v0, :cond_0

    invoke-interface {v0}, Lcom/bykv/vk/openvk/component/video/api/eV/hjc;->Ubf()J

    move-result-wide v0

    return-wide v0

    :cond_0
    iget-wide v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->rAx:J

    return-wide v0
.end method

.method public uy()V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->WR:Lcom/bykv/vk/openvk/component/video/api/eV/hjc;

    instance-of v1, v0, Lcom/bytedance/sdk/openadsdk/core/video/hjc/Fj;

    if-eqz v1, :cond_0

    check-cast v0, Lcom/bytedance/sdk/openadsdk/core/video/hjc/Fj;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/video/hjc/Fj;->eh()V

    :cond_0
    return-void
.end method

.method public vYf()J
    .locals 4

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->WR:Lcom/bykv/vk/openvk/component/video/api/eV/hjc;

    if-eqz v0, :cond_0

    invoke-interface {v0}, Lcom/bykv/vk/openvk/component/video/api/eV/hjc;->BcC()J

    move-result-wide v0

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->WR:Lcom/bykv/vk/openvk/component/video/api/eV/hjc;

    invoke-interface {v2}, Lcom/bykv/vk/openvk/component/video/api/eV/hjc;->WR()J

    move-result-wide v2

    add-long/2addr v0, v2

    return-wide v0

    :cond_0
    const-wide/16 v0, 0x0

    return-wide v0
.end method
