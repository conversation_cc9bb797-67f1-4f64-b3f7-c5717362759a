.class public interface abstract Landroidx/compose/runtime/a1;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/compose/runtime/f3;
.implements Landroidx/compose/runtime/i1;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Landroidx/compose/runtime/f3;",
        "Landroidx/compose/runtime/i1<",
        "Ljava/lang/Double;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# virtual methods
.method public abstract B()D
.end method

.method public abstract getValue()Ljava/lang/Double;
.end method

.method public abstract n(D)V
.end method

.method public abstract o(D)V
.end method
