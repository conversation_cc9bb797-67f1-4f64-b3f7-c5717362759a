.class public abstract Lcom/bytedance/sdk/openadsdk/core/ex/svN;
.super Lcom/bytedance/sdk/openadsdk/core/ex/hjc;


# instance fields
.field private final Fj:Ljava/lang/String;

.field private final ex:Lcom/bytedance/sdk/openadsdk/core/svN/Fj;

.field private hjc:Lcom/bytedance/sdk/openadsdk/core/ex/hjc;


# direct methods
.method public constructor <init>(Ljava/lang/String;Lcom/bytedance/sdk/openadsdk/core/svN/Fj;)V
    .locals 1

    const/4 v0, 0x0

    invoke-direct {p0, p1, p2, v0}, Lcom/bytedance/sdk/openadsdk/core/ex/svN;-><init>(Ljava/lang/String;Lcom/bytedance/sdk/openadsdk/core/svN/Fj;Lcom/bytedance/sdk/openadsdk/core/ex/hjc;)V

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;Lcom/bytedance/sdk/openadsdk/core/svN/Fj;Lcom/bytedance/sdk/openadsdk/core/ex/hjc;)V
    .locals 0

    invoke-direct {p0}, Lcom/bytedance/sdk/openadsdk/core/ex/hjc;-><init>()V

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/ex/svN;->Fj:Ljava/lang/String;

    iput-object p2, p0, Lcom/bytedance/sdk/openadsdk/core/ex/svN;->ex:Lcom/bytedance/sdk/openadsdk/core/svN/Fj;

    iput-object p3, p0, Lcom/bytedance/sdk/openadsdk/core/ex/svN;->hjc:Lcom/bytedance/sdk/openadsdk/core/ex/hjc;

    return-void
.end method


# virtual methods
.method public Fj(Landroid/view/View;FFFFLandroid/util/SparseArray;Z)V
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/view/View;",
            "FFFF",
            "Landroid/util/SparseArray<",
            "Lcom/bytedance/sdk/openadsdk/core/ex/hjc$Fj;",
            ">;Z)V"
        }
    .end annotation

    move-object v0, p0

    move-object v2, p1

    iget-object v1, v0, Lcom/bytedance/sdk/openadsdk/core/ex/svN;->ex:Lcom/bytedance/sdk/openadsdk/core/svN/Fj;

    if-eqz v1, :cond_0

    iget-object v3, v0, Lcom/bytedance/sdk/openadsdk/core/ex/svN;->Fj:Ljava/lang/String;

    invoke-virtual {v1, v3}, Lcom/bytedance/sdk/openadsdk/core/svN/Fj;->Ubf(Ljava/lang/String;)V

    :cond_0
    if-eqz v2, :cond_3

    invoke-virtual {p1}, Landroid/view/View;->getId()I

    move-result v1

    sget v3, Lcom/bytedance/sdk/openadsdk/utils/Ko;->hjc:I

    const v4, 0x22000001

    if-ne v1, v3, :cond_1

    const-string v1, "VAST_TITLE"

    invoke-virtual {p1, v4, v1}, Landroid/view/View;->setTag(ILjava/lang/Object;)V

    goto :goto_0

    :cond_1
    invoke-virtual {p1}, Landroid/view/View;->getId()I

    move-result v1

    sget v3, Lcom/bytedance/sdk/openadsdk/utils/Ko;->svN:I

    if-ne v1, v3, :cond_2

    const-string v1, "VAST_DESCRIPTION"

    invoke-virtual {p1, v4, v1}, Landroid/view/View;->setTag(ILjava/lang/Object;)V

    goto :goto_0

    :cond_2
    iget-object v1, v0, Lcom/bytedance/sdk/openadsdk/core/ex/svN;->Fj:Ljava/lang/String;

    invoke-virtual {p1, v4, v1}, Landroid/view/View;->setTag(ILjava/lang/Object;)V

    :cond_3
    :goto_0
    iget-object v1, v0, Lcom/bytedance/sdk/openadsdk/core/ex/svN;->hjc:Lcom/bytedance/sdk/openadsdk/core/ex/hjc;

    if-eqz v1, :cond_4

    iget-wide v3, v0, Lcom/bytedance/sdk/openadsdk/core/ex/hjc;->nsB:J

    iput-wide v3, v1, Lcom/bytedance/sdk/openadsdk/core/ex/hjc;->nsB:J

    iget-wide v3, v0, Lcom/bytedance/sdk/openadsdk/core/ex/hjc;->Vq:J

    iput-wide v3, v1, Lcom/bytedance/sdk/openadsdk/core/ex/hjc;->Vq:J

    iget v3, v0, Lcom/bytedance/sdk/openadsdk/core/ex/hjc;->Moo:I

    iput v3, v1, Lcom/bytedance/sdk/openadsdk/core/ex/hjc;->Moo:I

    iget v3, v0, Lcom/bytedance/sdk/openadsdk/core/ex/hjc;->Moo:I

    iput v3, v1, Lcom/bytedance/sdk/openadsdk/core/ex/hjc;->rf:I

    iput v3, v1, Lcom/bytedance/sdk/openadsdk/core/ex/hjc;->uy:I

    move-object v2, p1

    move v3, p2

    move v4, p3

    move v5, p4

    move v6, p5

    move-object v7, p6

    move/from16 v8, p7

    invoke-virtual/range {v1 .. v8}, Lcom/bytedance/sdk/openadsdk/core/ex/hjc;->Fj(Landroid/view/View;FFFFLandroid/util/SparseArray;Z)V

    :cond_4
    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/openadsdk/core/ex/hjc;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/ex/svN;->hjc:Lcom/bytedance/sdk/openadsdk/core/ex/hjc;

    return-void
.end method

.method public onTouch(Landroid/view/View;Landroid/view/MotionEvent;)Z
    .locals 0

    invoke-super {p0, p1, p2}, Lcom/bytedance/sdk/openadsdk/core/ex/hjc;->onTouch(Landroid/view/View;Landroid/view/MotionEvent;)Z

    move-result p1

    return p1
.end method
