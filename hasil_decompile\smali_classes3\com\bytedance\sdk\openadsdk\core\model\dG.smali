.class public Lcom/bytedance/sdk/openadsdk/core/model/dG;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/component/adexpress/hjc;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;
    }
.end annotation


# instance fields
.field public final BcC:I

.field public final Fj:F

.field public JU:I

.field public final JW:Z

.field public final Ko:I

.field public Tc:Landroid/util/SparseArray;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Landroid/util/SparseArray<",
            "Lcom/bytedance/sdk/openadsdk/core/ex/hjc$Fj;",
            ">;"
        }
    .end annotation
.end field

.field public UYd:I

.field public final Ubf:J

.field public final WR:J

.field public dG:Lorg/json/JSONObject;

.field public final eV:F

.field public final ex:F

.field public final hjc:F

.field public final mSE:I

.field public final rAx:Ljava/lang/String;

.field public final svN:I


# direct methods
.method private constructor <init>(Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;)V
    .locals 2
    .param p1    # Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;)F

    move-result v0

    iput v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/dG;->Fj:F

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->ex(Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;)F

    move-result v0

    iput v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/dG;->ex:F

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->hjc(Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;)F

    move-result v0

    iput v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/dG;->hjc:F

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->eV(Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;)F

    move-result v0

    iput v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/dG;->eV:F

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->Ubf(Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;)J

    move-result-wide v0

    iput-wide v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/dG;->Ubf:J

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->WR(Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;)J

    move-result-wide v0

    iput-wide v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/dG;->WR:J

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->svN(Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;)I

    move-result v0

    iput v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/dG;->svN:I

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->BcC(Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;)I

    move-result v0

    iput v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/dG;->BcC:I

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->mSE(Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;)I

    move-result v0

    iput v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/dG;->mSE:I

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->Ko(Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;)I

    move-result v0

    iput v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/dG;->Ko:I

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->rAx(Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;)Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/dG;->rAx:Ljava/lang/String;

    iget-object v0, p1, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->Fj:Landroid/util/SparseArray;

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/dG;->Tc:Landroid/util/SparseArray;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->UYd(Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;)Z

    move-result v0

    iput-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/dG;->JW:Z

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->dG(Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;)I

    move-result v0

    iput v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/dG;->UYd:I

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->Tc(Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;)Lorg/json/JSONObject;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/dG;->dG:Lorg/json/JSONObject;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->JW(Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;)I

    move-result p1

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/dG;->JU:I

    return-void
.end method

.method public synthetic constructor <init>(Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;Lcom/bytedance/sdk/openadsdk/core/model/dG$1;)V
    .locals 0

    invoke-direct {p0, p1}, Lcom/bytedance/sdk/openadsdk/core/model/dG;-><init>(Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;)V

    return-void
.end method
