.class Lcom/bytedance/sdk/openadsdk/component/ex$2;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/openadsdk/core/ex/ex$Fj;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/ex;->Fj()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/component/ex;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/ex;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/ex$2;->Fj:Lcom/bytedance/sdk/openadsdk/component/ex;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj(Landroid/view/View;I)V
    .locals 0

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/ex$2;->Fj:Lcom/bytedance/sdk/openadsdk/component/ex;

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/hjc;->Ubf:Lcom/bytedance/sdk/openadsdk/component/Fj;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/component/Fj;->Ubf()V

    return-void
.end method
