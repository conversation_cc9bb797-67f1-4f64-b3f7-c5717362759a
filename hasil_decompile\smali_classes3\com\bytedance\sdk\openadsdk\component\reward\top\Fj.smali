.class public interface abstract Lcom/bytedance/sdk/openadsdk/component/reward/top/Fj;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Landroid/view/View;",
        ">",
        "Ljava/lang/Object;"
    }
.end annotation


# virtual methods
.method public abstract Fj()V
.end method

.method public abstract Fj(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)V
.end method

.method public abstract Ubf()V
.end method

.method public abstract eV()V
.end method

.method public abstract ex()V
.end method

.method public abstract hjc()V
.end method

.method public abstract setListener(Lcom/bytedance/sdk/openadsdk/component/reward/top/ex;)V
.end method

.method public abstract setShowDislike(Z)V
.end method

.method public abstract setShowSkip(Z)V
.end method

.method public abstract setShowSound(Z)V
.end method

.method public abstract setSkipEnable(Z)V
.end method

.method public abstract setSkipText(Ljava/lang/CharSequence;)V
.end method

.method public abstract setSoundMute(Z)V
.end method
