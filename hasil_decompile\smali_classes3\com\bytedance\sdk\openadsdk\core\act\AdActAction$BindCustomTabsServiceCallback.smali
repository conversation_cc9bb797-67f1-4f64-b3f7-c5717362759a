.class public interface abstract Lcom/bytedance/sdk/openadsdk/core/act/AdActAction$BindCustomTabsServiceCallback;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "BindCustomTabsServiceCallback"
.end annotation


# virtual methods
.method public abstract onBindFail(ILjava/lang/String;)V
.end method

.method public abstract onBindSuccess(Landroidx/browser/customtabs/j;)V
.end method
