.class public Lcom/bytedance/sdk/openadsdk/core/nativeexpress/JW;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/openadsdk/core/ugen/eV/ex;


# instance fields
.field private Fj:J

.field private Ubf:Ljava/lang/String;

.field private final WR:Ljava/util/concurrent/atomic/AtomicBoolean;

.field private eV:Ljava/lang/String;

.field private ex:J

.field private hjc:I


# direct methods
.method public constructor <init>()V
    .locals 2

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const-wide/16 v0, 0x0

    iput-wide v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/JW;->Fj:J

    iput-wide v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/JW;->ex:J

    const/4 v0, 0x0

    iput v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/JW;->hjc:I

    const/4 v1, 0x0

    iput-object v1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/JW;->eV:Ljava/lang/String;

    iput-object v1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/JW;->Ubf:Ljava/lang/String;

    new-instance v1, Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-direct {v1, v0}, Ljava/util/concurrent/atomic/AtomicBoolean;-><init>(Z)V

    iput-object v1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/JW;->WR:Ljava/util/concurrent/atomic/AtomicBoolean;

    return-void
.end method


# virtual methods
.method public Fj()V
    .locals 2

    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    move-result-wide v0

    iput-wide v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/JW;->Fj:J

    return-void
.end method

.method public Fj(ILjava/lang/String;Ljava/lang/String;)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/JW;->hjc:I

    iput-object p2, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/JW;->eV:Ljava/lang/String;

    iput-object p3, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/JW;->Ubf:Ljava/lang/String;

    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    move-result-wide p1

    iput-wide p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/JW;->ex:J

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/JW;->WR:Ljava/util/concurrent/atomic/AtomicBoolean;

    const/4 p2, 0x0

    invoke-virtual {p1, p2}, Ljava/util/concurrent/atomic/AtomicBoolean;->set(Z)V

    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Ljava/lang/String;)V
    .locals 20

    move-object/from16 v0, p0

    iget-object v1, v0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/JW;->WR:Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-virtual {v1}, Ljava/util/concurrent/atomic/AtomicBoolean;->get()Z

    move-result v1

    if-eqz v1, :cond_0

    const-string v4, "success"

    iget-wide v1, v0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/JW;->ex:J

    iget-wide v5, v0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/JW;->Fj:J

    sub-long v5, v1, v5

    iget-object v7, v0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/JW;->Ubf:Ljava/lang/String;

    const-string v8, "ad"

    const/4 v9, 0x0

    const/4 v10, 0x0

    move-object/from16 v2, p1

    move-object/from16 v3, p2

    invoke-static/range {v2 .. v10}, Lcom/bytedance/sdk/openadsdk/ex/hjc;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Ljava/lang/String;Ljava/lang/String;JLjava/lang/String;Ljava/lang/String;ILjava/lang/String;)V

    return-void

    :cond_0
    const-string v13, "fail"

    iget-wide v1, v0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/JW;->ex:J

    iget-wide v3, v0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/JW;->Fj:J

    sub-long v14, v1, v3

    iget-object v1, v0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/JW;->Ubf:Ljava/lang/String;

    const-string v17, "ad"

    iget v2, v0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/JW;->hjc:I

    iget-object v3, v0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/JW;->eV:Ljava/lang/String;

    move-object/from16 v11, p1

    move-object/from16 v12, p2

    move-object/from16 v16, v1

    move/from16 v18, v2

    move-object/from16 v19, v3

    invoke-static/range {v11 .. v19}, Lcom/bytedance/sdk/openadsdk/ex/hjc;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Ljava/lang/String;Ljava/lang/String;JLjava/lang/String;Ljava/lang/String;ILjava/lang/String;)V

    return-void
.end method

.method public Fj(Ljava/lang/String;)V
    .locals 2

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/JW;->Ubf:Ljava/lang/String;

    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    move-result-wide v0

    iput-wide v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/JW;->ex:J

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/JW;->WR:Ljava/util/concurrent/atomic/AtomicBoolean;

    const/4 v0, 0x1

    invoke-virtual {p1, v0}, Ljava/util/concurrent/atomic/AtomicBoolean;->set(Z)V

    return-void
.end method
