.class public final enum Lcom/transsion/player/enum/PlayMimeType;
.super Ljava/lang/Enum;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lcom/transsion/player/enum/PlayMimeType;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# static fields
.field private static final synthetic $VALUES:[Lcom/transsion/player/enum/PlayMimeType;

.field public static final enum DASH:Lcom/transsion/player/enum/PlayMimeType;

.field public static final enum HLS:Lcom/transsion/player/enum/PlayMimeType;

.field public static final enum MP4:Lcom/transsion/player/enum/PlayMimeType;


# direct methods
.method private static final synthetic $values()[Lcom/transsion/player/enum/PlayMimeType;
    .locals 3

    const/4 v0, 0x3

    new-array v0, v0, [Lcom/transsion/player/enum/PlayMimeType;

    const/4 v1, 0x0

    sget-object v2, Lcom/transsion/player/enum/PlayMimeType;->MP4:Lcom/transsion/player/enum/PlayMimeType;

    aput-object v2, v0, v1

    const/4 v1, 0x1

    sget-object v2, Lcom/transsion/player/enum/PlayMimeType;->DASH:Lcom/transsion/player/enum/PlayMimeType;

    aput-object v2, v0, v1

    const/4 v1, 0x2

    sget-object v2, Lcom/transsion/player/enum/PlayMimeType;->HLS:Lcom/transsion/player/enum/PlayMimeType;

    aput-object v2, v0, v1

    return-object v0
.end method

.method static constructor <clinit>()V
    .locals 3

    new-instance v0, Lcom/transsion/player/enum/PlayMimeType;

    const-string v1, "MP4"

    const/4 v2, 0x0

    invoke-direct {v0, v1, v2}, Lcom/transsion/player/enum/PlayMimeType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/transsion/player/enum/PlayMimeType;->MP4:Lcom/transsion/player/enum/PlayMimeType;

    new-instance v0, Lcom/transsion/player/enum/PlayMimeType;

    const-string v1, "DASH"

    const/4 v2, 0x1

    invoke-direct {v0, v1, v2}, Lcom/transsion/player/enum/PlayMimeType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/transsion/player/enum/PlayMimeType;->DASH:Lcom/transsion/player/enum/PlayMimeType;

    new-instance v0, Lcom/transsion/player/enum/PlayMimeType;

    const-string v1, "HLS"

    const/4 v2, 0x2

    invoke-direct {v0, v1, v2}, Lcom/transsion/player/enum/PlayMimeType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/transsion/player/enum/PlayMimeType;->HLS:Lcom/transsion/player/enum/PlayMimeType;

    invoke-static {}, Lcom/transsion/player/enum/PlayMimeType;->$values()[Lcom/transsion/player/enum/PlayMimeType;

    move-result-object v0

    sput-object v0, Lcom/transsion/player/enum/PlayMimeType;->$VALUES:[Lcom/transsion/player/enum/PlayMimeType;

    return-void
.end method

.method private constructor <init>(Ljava/lang/String;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    return-void
.end method

.method public static valueOf(Ljava/lang/String;)Lcom/transsion/player/enum/PlayMimeType;
    .locals 1

    const-class v0, Lcom/transsion/player/enum/PlayMimeType;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object p0

    check-cast p0, Lcom/transsion/player/enum/PlayMimeType;

    return-object p0
.end method

.method public static values()[Lcom/transsion/player/enum/PlayMimeType;
    .locals 1

    sget-object v0, Lcom/transsion/player/enum/PlayMimeType;->$VALUES:[Lcom/transsion/player/enum/PlayMimeType;

    invoke-virtual {v0}, [Ljava/lang/Object;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lcom/transsion/player/enum/PlayMimeType;

    return-object v0
.end method
