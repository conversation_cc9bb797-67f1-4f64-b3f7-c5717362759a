.class Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$15;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/openadsdk/mSE/ex;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Fj(Lcom/bytedance/sdk/openadsdk/mSE/eV;Ljava/lang/String;Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$15;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj()V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$15;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;

    const/4 v1, 0x1

    invoke-static {v0, v1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->eV(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;Z)Z

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$15;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->ex(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object v0

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Obv:Lcom/bytedance/sdk/openadsdk/component/reward/view/Ubf;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/view/Ubf;->Ko()Landroid/view/View;

    move-result-object v0

    invoke-virtual {v0}, Landroid/view/View;->performClick()Z

    return-void
.end method

.method public Fj(ZILjava/lang/String;)V
    .locals 0

    return-void
.end method
