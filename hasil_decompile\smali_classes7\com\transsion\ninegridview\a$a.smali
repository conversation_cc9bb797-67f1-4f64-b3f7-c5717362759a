.class public final Lcom/transsion/ninegridview/a$a;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/transsion/ninegridview/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    invoke-direct {p0}, Lcom/transsion/ninegridview/a$a;-><init>()V

    return-void
.end method


# virtual methods
.method public final a()Llo/d;
    .locals 1

    invoke-static {}, Lcom/transsion/ninegridview/a;->d()Llo/d;

    move-result-object v0

    return-object v0
.end method
