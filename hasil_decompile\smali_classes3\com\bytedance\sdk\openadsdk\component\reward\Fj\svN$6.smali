.class Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN$6;
.super Lcom/bytedance/sdk/openadsdk/core/widget/Fj/Ubf;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;->Fj(Landroid/webkit/DownloadListener;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;Landroid/content/Context;Lcom/bytedance/sdk/openadsdk/core/Vq;Ljava/lang/String;Lcom/bytedance/sdk/openadsdk/ex/Ko;Z)V
    .locals 6

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN$6;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;

    move-object v0, p0

    move-object v1, p2

    move-object v2, p3

    move-object v3, p4

    move-object v4, p5

    move v5, p6

    invoke-direct/range {v0 .. v5}, Lcom/bytedance/sdk/openadsdk/core/widget/Fj/Ubf;-><init>(Landroid/content/Context;Lcom/bytedance/sdk/openadsdk/core/Vq;Ljava/lang/String;Lcom/bytedance/sdk/openadsdk/ex/Ko;Z)V

    return-void
.end method


# virtual methods
.method public onPageFinished(Landroid/webkit/WebView;Ljava/lang/String;)V
    .locals 4

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN$6;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;->WR(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;)Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN$6;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;->eV(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;)Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN$6;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;

    invoke-static {v1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;->Ubf(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;)Ljava/lang/String;

    move-result-object v1

    const-string v2, "loading_h5_success"

    const/4 v3, 0x0

    invoke-static {v0, v1, v2, v3}, Lcom/bytedance/sdk/openadsdk/ex/hjc;->hjc(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Ljava/lang/String;Ljava/lang/String;Lorg/json/JSONObject;)V

    :cond_0
    invoke-super {p0, p1, p2}, Lcom/bytedance/sdk/openadsdk/core/widget/Fj/Ubf;->onPageFinished(Landroid/webkit/WebView;Ljava/lang/String;)V

    return-void
.end method

.method public onReceivedError(Landroid/webkit/WebView;ILjava/lang/String;Ljava/lang/String;)V
    .locals 0

    invoke-super {p0, p1, p2, p3, p4}, Lcom/bytedance/sdk/openadsdk/core/widget/Fj/Ubf;->onReceivedError(Landroid/webkit/WebView;ILjava/lang/String;Ljava/lang/String;)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN$6;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;

    const/4 p2, 0x0

    invoke-static {p1, p2}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;Z)Z

    return-void
.end method

.method public onReceivedError(Landroid/webkit/WebView;Landroid/webkit/WebResourceRequest;Landroid/webkit/WebResourceError;)V
    .locals 0

    invoke-super {p0, p1, p2, p3}, Lcom/bytedance/sdk/openadsdk/core/widget/Fj/Ubf;->onReceivedError(Landroid/webkit/WebView;Landroid/webkit/WebResourceRequest;Landroid/webkit/WebResourceError;)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN$6;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;

    const/4 p2, 0x0

    invoke-static {p1, p2}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;Z)Z

    return-void
.end method

.method public onReceivedHttpError(Landroid/webkit/WebView;Landroid/webkit/WebResourceRequest;Landroid/webkit/WebResourceResponse;)V
    .locals 0

    invoke-super {p0, p1, p2, p3}, Lcom/bytedance/sdk/openadsdk/core/widget/Fj/Ubf;->onReceivedHttpError(Landroid/webkit/WebView;Landroid/webkit/WebResourceRequest;Landroid/webkit/WebResourceResponse;)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN$6;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;

    const/4 p2, 0x0

    invoke-static {p1, p2}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;Z)Z

    return-void
.end method
