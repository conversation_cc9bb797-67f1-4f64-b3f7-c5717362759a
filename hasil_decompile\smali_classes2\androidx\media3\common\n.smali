.class public interface abstract Landroidx/media3/common/n;
.super Ljava/lang/Object;


# static fields
.field public static final a:Landroidx/media3/common/n;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Landroidx/media3/common/m;

    invoke-direct {v0}, Landroidx/media3/common/m;-><init>()V

    sput-object v0, Landroidx/media3/common/n;->a:Landroidx/media3/common/n;

    return-void
.end method
