.class public Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;
.super Ljava/lang/Object;


# instance fields
.field public final Af:Ljava/util/concurrent/atomic/AtomicBoolean;

.field public At:Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;

.field public BcC:I

.field public final Bzy:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Ubf;

.field public final Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

.field public final HY:Lcom/bytedance/sdk/openadsdk/component/reward/view/RewardFullBaseLayout;

.field public JU:Z

.field public final JW:I

.field public final KZ:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc;

.field public final Kk:Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public Ko:I

.field public final Moo:Ljava/util/concurrent/atomic/AtomicBoolean;

.field public final OK:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;

.field public final Obv:Lcom/bytedance/sdk/openadsdk/component/reward/view/Ubf;

.field public PpV:Lcom/bytedance/sdk/openadsdk/common/svN;

.field public Ql:Z

.field public Tc:Z

.field public UYd:I

.field public Ubf:Z

.field public final Vq:Ljava/util/concurrent/atomic/AtomicBoolean;

.field public final WR:Z

.field public final YH:Lcom/bytedance/sdk/component/utils/Vq;

.field public final cB:Ljava/util/concurrent/atomic/AtomicBoolean;

.field public final cs:Landroid/content/Context;

.field public dG:I

.field public eV:Z

.field public final efV:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC;

.field public final eh:Ljava/util/concurrent/atomic/AtomicBoolean;

.field public final ex:Z

.field public final fj:Ljava/util/concurrent/atomic/AtomicBoolean;

.field public final gXF:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;

.field public final gci:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Ko;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public hjc:Ljava/lang/String;

.field public final iT:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/mSE;

.field public final kF:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final lv:Ljava/util/concurrent/atomic/AtomicBoolean;

.field public final mC:Ljava/util/concurrent/atomic/AtomicBoolean;

.field public mE:I

.field public mSE:Z

.field public final mj:Lcom/bytedance/sdk/openadsdk/mSE/svN;

.field public final nsB:Ljava/util/concurrent/atomic/AtomicBoolean;

.field public final oX:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;

.field public final qPr:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;

.field public rAx:F

.field public rS:J

.field public final rXP:Ljava/util/concurrent/atomic/AtomicBoolean;

.field public final rf:Ljava/util/concurrent/atomic/AtomicBoolean;

.field public final spi:Lcom/bytedance/sdk/openadsdk/component/reward/view/hjc;

.field public final svN:Ljava/lang/String;

.field public final uM:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/eV;

.field public final uy:Ljava/util/concurrent/atomic/AtomicBoolean;

.field public final vYf:Z


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;Lcom/bytedance/sdk/component/utils/Vq;Lcom/bytedance/sdk/openadsdk/core/model/Ql;)V
    .locals 3
    .param p1    # Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p3    # Lcom/bytedance/sdk/openadsdk/core/model/Ql;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    iput-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Ubf:Z

    const/4 v1, 0x1

    iput v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Ko:I

    const-wide/16 v1, 0x0

    iput-wide v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->rS:J

    iput v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->mE:I

    new-instance v1, Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-direct {v1, v0}, Ljava/util/concurrent/atomic/AtomicBoolean;-><init>(Z)V

    iput-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Af:Ljava/util/concurrent/atomic/AtomicBoolean;

    new-instance v1, Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-direct {v1, v0}, Ljava/util/concurrent/atomic/AtomicBoolean;-><init>(Z)V

    iput-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->mC:Ljava/util/concurrent/atomic/AtomicBoolean;

    new-instance v1, Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-direct {v1, v0}, Ljava/util/concurrent/atomic/AtomicBoolean;-><init>(Z)V

    iput-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->cB:Ljava/util/concurrent/atomic/AtomicBoolean;

    new-instance v1, Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-direct {v1, v0}, Ljava/util/concurrent/atomic/AtomicBoolean;-><init>(Z)V

    iput-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->nsB:Ljava/util/concurrent/atomic/AtomicBoolean;

    new-instance v1, Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-direct {v1, v0}, Ljava/util/concurrent/atomic/AtomicBoolean;-><init>(Z)V

    iput-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Vq:Ljava/util/concurrent/atomic/AtomicBoolean;

    new-instance v1, Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-direct {v1, v0}, Ljava/util/concurrent/atomic/AtomicBoolean;-><init>(Z)V

    iput-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Moo:Ljava/util/concurrent/atomic/AtomicBoolean;

    new-instance v1, Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-direct {v1, v0}, Ljava/util/concurrent/atomic/AtomicBoolean;-><init>(Z)V

    iput-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->rf:Ljava/util/concurrent/atomic/AtomicBoolean;

    new-instance v1, Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-direct {v1, v0}, Ljava/util/concurrent/atomic/AtomicBoolean;-><init>(Z)V

    iput-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->uy:Ljava/util/concurrent/atomic/AtomicBoolean;

    new-instance v1, Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-direct {v1, v0}, Ljava/util/concurrent/atomic/AtomicBoolean;-><init>(Z)V

    iput-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->lv:Ljava/util/concurrent/atomic/AtomicBoolean;

    new-instance v1, Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-direct {v1, v0}, Ljava/util/concurrent/atomic/AtomicBoolean;-><init>(Z)V

    iput-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->rXP:Ljava/util/concurrent/atomic/AtomicBoolean;

    new-instance v1, Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-direct {v1, v0}, Ljava/util/concurrent/atomic/AtomicBoolean;-><init>(Z)V

    iput-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->fj:Ljava/util/concurrent/atomic/AtomicBoolean;

    new-instance v1, Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-direct {v1, v0}, Ljava/util/concurrent/atomic/AtomicBoolean;-><init>(Z)V

    iput-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->eh:Ljava/util/concurrent/atomic/AtomicBoolean;

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Kk:Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;

    invoke-virtual {p1}, Landroid/content/Context;->getApplicationContext()Landroid/content/Context;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->cs:Landroid/content/Context;

    iput-object p3, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    iget-object v1, p1, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->Fj:Ljava/lang/String;

    iput-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->svN:Ljava/lang/String;

    iput-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->YH:Lcom/bytedance/sdk/component/utils/Vq;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->JW()Z

    move-result p2

    iput-boolean p2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->WR:Z

    invoke-static {p3}, Lcom/bytedance/sdk/openadsdk/core/model/JW;->BcC(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result p2

    iput-boolean p2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->vYf:Z

    invoke-virtual {p3}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->aYy()I

    move-result p2

    iput p2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->JW:I

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/JU;->eV()Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;

    move-result-object p3

    invoke-static {p2}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    move-result-object v1

    invoke-interface {p3, v1}, Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;->JU(Ljava/lang/String;)Z

    move-result p3

    iput-boolean p3, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->eV:Z

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/settings/JW;->Ud()Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;

    move-result-object p3

    invoke-static {p2}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    move-result-object p2

    invoke-interface {p3, p2}, Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;->dG(Ljava/lang/String;)Z

    move-result p2

    iput-boolean p2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->ex:Z

    new-instance p2, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/mSE;

    invoke-direct {p2, p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/mSE;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;)V

    iput-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->iT:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/mSE;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->JU()Z

    move-result p2

    if-eqz p2, :cond_0

    new-instance p2, Lcom/bytedance/sdk/openadsdk/component/reward/view/Ubf;

    invoke-direct {p2, p0}, Lcom/bytedance/sdk/openadsdk/component/reward/view/Ubf;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;)V

    goto :goto_0

    :cond_0
    new-instance p2, Lcom/bytedance/sdk/openadsdk/component/reward/view/eV;

    invoke-direct {p2, p0}, Lcom/bytedance/sdk/openadsdk/component/reward/view/eV;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;)V

    :goto_0
    iput-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Obv:Lcom/bytedance/sdk/openadsdk/component/reward/view/Ubf;

    new-instance p2, Lcom/bytedance/sdk/openadsdk/component/reward/view/RewardFullBaseLayout;

    invoke-direct {p2, p1}, Lcom/bytedance/sdk/openadsdk/component/reward/view/RewardFullBaseLayout;-><init>(Landroid/content/Context;)V

    iput-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->HY:Lcom/bytedance/sdk/openadsdk/component/reward/view/RewardFullBaseLayout;

    new-instance p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;

    invoke-direct {p1, p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;)V

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->kF:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;

    new-instance p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/eV;

    invoke-direct {p1, p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/eV;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;)V

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->uM:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/eV;

    new-instance p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;

    invoke-direct {p1, p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;)V

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->gXF:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;

    new-instance p1, Lcom/bytedance/sdk/openadsdk/component/reward/view/hjc;

    invoke-direct {p1, p0}, Lcom/bytedance/sdk/openadsdk/component/reward/view/hjc;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;)V

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->spi:Lcom/bytedance/sdk/openadsdk/component/reward/view/hjc;

    new-instance p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;

    invoke-direct {p1, p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;)V

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->qPr:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;

    new-instance p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;

    invoke-direct {p1, p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;)V

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->OK:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;

    new-instance p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Ko;

    invoke-direct {p1, p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Ko;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;)V

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->gci:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Ko;

    new-instance p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Ubf;

    invoke-direct {p1, p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Ubf;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;)V

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Bzy:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Ubf;

    new-instance p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC;

    invoke-direct {p1, p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;)V

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->efV:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC;

    new-instance p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;

    invoke-direct {p1, p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;)V

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->oX:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;

    new-instance p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc;

    invoke-direct {p1, p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;)V

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->KZ:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc;

    new-instance p1, Lcom/bytedance/sdk/openadsdk/mSE/svN;

    invoke-direct {p1, v0}, Lcom/bytedance/sdk/openadsdk/mSE/svN;-><init>(Landroid/content/Context;)V

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->mj:Lcom/bytedance/sdk/openadsdk/mSE/svN;

    return-void
.end method


# virtual methods
.method public Fj(Z)V
    .locals 1

    iput-boolean p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->JU:Z

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->gci:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Ko;

    invoke-virtual {v0, p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Ko;->eV(Z)V

    return-void
.end method
