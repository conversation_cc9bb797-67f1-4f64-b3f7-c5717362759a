.class Lcom/bytedance/sdk/openadsdk/component/reward/rAx$3;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/openadsdk/Ko/ex;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/reward/rAx;->Fj(ZLcom/bytedance/sdk/openadsdk/core/model/Ql;JLjava/lang/String;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Z

.field final synthetic Ubf:Ljava/lang/String;

.field final synthetic WR:Ljava/lang/String;

.field final synthetic eV:J

.field final synthetic ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

.field final synthetic hjc:J

.field final synthetic svN:Lcom/bytedance/sdk/openadsdk/component/reward/rAx;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/reward/rAx;ZLcom/bytedance/sdk/openadsdk/core/model/Ql;JJLjava/lang/String;Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/rAx$3;->svN:Lcom/bytedance/sdk/openadsdk/component/reward/rAx;

    iput-boolean p2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/rAx$3;->Fj:Z

    iput-object p3, p0, Lcom/bytedance/sdk/openadsdk/component/reward/rAx$3;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    iput-wide p4, p0, Lcom/bytedance/sdk/openadsdk/component/reward/rAx$3;->hjc:J

    iput-wide p6, p0, Lcom/bytedance/sdk/openadsdk/component/reward/rAx$3;->eV:J

    iput-object p8, p0, Lcom/bytedance/sdk/openadsdk/component/reward/rAx$3;->Ubf:Ljava/lang/String;

    iput-object p9, p0, Lcom/bytedance/sdk/openadsdk/component/reward/rAx$3;->WR:Ljava/lang/String;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public getLogStats()Lcom/bytedance/sdk/openadsdk/Ko/Fj/ex;
    .locals 7
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Exception;
        }
    .end annotation

    iget-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/rAx$3;->Fj:Z

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/rAx$3;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    iget-wide v2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/rAx$3;->hjc:J

    iget-wide v4, p0, Lcom/bytedance/sdk/openadsdk/component/reward/rAx$3;->eV:J

    iget-object v6, p0, Lcom/bytedance/sdk/openadsdk/component/reward/rAx$3;->Ubf:Ljava/lang/String;

    invoke-static/range {v0 .. v6}, Lcom/bytedance/sdk/openadsdk/utils/rXP;->Fj(ZLcom/bytedance/sdk/openadsdk/core/model/Ql;JJLjava/lang/String;)Lorg/json/JSONObject;

    move-result-object v0

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/Ko/Fj/hjc;->ex()Lcom/bytedance/sdk/openadsdk/Ko/Fj/hjc;

    move-result-object v1

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/rAx$3;->WR:Ljava/lang/String;

    invoke-virtual {v1, v2}, Lcom/bytedance/sdk/openadsdk/Ko/Fj/hjc;->Fj(Ljava/lang/String;)Lcom/bytedance/sdk/openadsdk/Ko/Fj/hjc;

    move-result-object v1

    invoke-virtual {v0}, Lorg/json/JSONObject;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v1, v0}, Lcom/bytedance/sdk/openadsdk/Ko/Fj/hjc;->ex(Ljava/lang/String;)Lcom/bytedance/sdk/openadsdk/Ko/Fj/hjc;

    move-result-object v0

    return-object v0
.end method
