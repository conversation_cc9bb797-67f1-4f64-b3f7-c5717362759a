.class public Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;
.super Ljava/lang/Object;


# instance fields
.field private Fj:I

.field private Ubf:Ljava/lang/String;

.field private WR:Z

.field private eV:I

.field private ex:I

.field private hjc:Lcom/bytedance/sdk/openadsdk/core/model/Ql;


# direct methods
.method public constructor <init>(IIILjava/lang/String;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;->Fj:I

    iput p2, p0, Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;->ex:I

    iput p3, p0, Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;->eV:I

    iput-object p4, p0, Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;->Ubf:Ljava/lang/String;

    return-void
.end method

.method public constructor <init>(IILcom/bytedance/sdk/openadsdk/core/model/Ql;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;->Fj:I

    iput p2, p0, Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;->ex:I

    iput-object p3, p0, Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;->hjc:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    return-void
.end method


# virtual methods
.method public Fj(Z)V
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;->WR:Z

    return-void
.end method

.method public Fj()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;->WR:Z

    return v0
.end method

.method public Ubf()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;->eV:I

    return v0
.end method

.method public WR()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;->Ubf:Ljava/lang/String;

    return-object v0
.end method

.method public eV()Lcom/bytedance/sdk/openadsdk/core/model/Ql;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;->hjc:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    return-object v0
.end method

.method public ex()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;->Fj:I

    return v0
.end method

.method public hjc()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;->ex:I

    return v0
.end method
