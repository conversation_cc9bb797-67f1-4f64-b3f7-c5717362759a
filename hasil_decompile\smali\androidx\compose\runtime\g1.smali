.class public interface abstract Landroidx/compose/runtime/g1;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/compose/runtime/f3;
.implements Landroidx/compose/runtime/i1;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Landroidx/compose/runtime/f3;",
        "Landroidx/compose/runtime/i1<",
        "Ljava/lang/Long;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# virtual methods
.method public abstract C(J)V
.end method

.method public abstract getValue()Ljava/lang/Long;
.end method

.method public abstract h(J)V
.end method

.method public abstract r()J
.end method
