.class public Lcom/bytedance/sdk/openadsdk/core/model/cB;
.super Ljava/lang/Object;


# instance fields
.field private Fj:I

.field private eV:Ljava/lang/String;

.field private ex:I

.field private hjc:I


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/cB;->hjc:I

    return v0
.end method

.method public Fj(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/cB;->Fj:I

    return-void
.end method

.method public Fj(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/cB;->eV:Ljava/lang/String;

    return-void
.end method

.method public ex()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/cB;->eV:Ljava/lang/String;

    return-object v0
.end method

.method public ex(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/cB;->ex:I

    return-void
.end method

.method public hjc(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/cB;->hjc:I

    return-void
.end method
