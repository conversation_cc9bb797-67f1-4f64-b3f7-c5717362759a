.class public final Lcom/transsion/ninegridview/R$id;
.super Ljava/lang/Object;


# static fields
.field public static bg_download:I = 0x7f0a00ca

.field public static bg_share:I = 0x7f0a00cd

.field public static fill:I = 0x7f0a025a

.field public static grid:I = 0x7f0a02cb

.field public static iv_back:I = 0x7f0a03dc

.field public static iv_download:I = 0x7f0a03fc

.field public static label_gif:I = 0x7f0a04ad

.field public static nine_grid:I = 0x7f0a06a4

.field public static pb:I = 0x7f0a0706

.field public static player_view:I = 0x7f0a072b

.field public static pv:I = 0x7f0a0754

.field public static root:I = 0x7f0a07ad

.field public static rootView:I = 0x7f0a07af

.field public static statusSpace:I = 0x7f0a0865

.field public static thumb:I = 0x7f0a0900

.field public static tv_download:I = 0x7f0a0a2a

.field public static tv_pager:I = 0x7f0a0a9d

.field public static tv_share:I = 0x7f0a0af0

.field public static video_container:I = 0x7f0a0bd2

.field public static video_cover:I = 0x7f0a0bd3

.field public static viewPager:I = 0x7f0a0bf7


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
