.class final Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ko$2;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/component/adexpress/Fj/Fj/hjc;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ko;->Fj()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = null
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method private Fj(Landroid/os/MessageQueue;)V
    .locals 1

    if-eqz p1, :cond_0

    new-instance v0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ko$2$2;

    invoke-direct {v0, p0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ko$2$2;-><init>(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ko$2;)V

    invoke-virtual {p1, v0}, Landroid/os/MessageQueue;->addIdleHandler(Landroid/os/MessageQueue$IdleHandler;)V

    :cond_0
    return-void
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ko$2;Landroid/os/MessageQueue;)V
    .locals 0

    invoke-direct {p0, p1}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ko$2;->Fj(Landroid/os/MessageQueue;)V

    return-void
.end method


# virtual methods
.method public BcC()Ljava/lang/String;
    .locals 1

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/BcC;->ex()Lcom/bytedance/sdk/openadsdk/core/BcC;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/BcC;->eV()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public Fj()I
    .locals 1

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/JU;->eV()Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;

    move-result-object v0

    if-nez v0, :cond_0

    const/4 v0, 0x0

    return v0

    :cond_0
    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/JU;->eV()Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;

    move-result-object v0

    invoke-interface {v0}, Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;->eV()I

    move-result v0

    return v0
.end method

.method public Ko()I
    .locals 1

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/JU;->eV()Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;

    move-result-object v0

    invoke-interface {v0}, Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;->YH()I

    move-result v0

    return v0
.end method

.method public UYd()I
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public Ubf()Lcom/bytedance/sdk/component/adexpress/Fj/hjc/Fj;
    .locals 1

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/JU;->hjc()Lcom/bytedance/sdk/openadsdk/core/Ql;

    move-result-object v0

    invoke-interface {v0}, Lcom/bytedance/sdk/openadsdk/core/Ql;->Fj()Lcom/bytedance/sdk/component/adexpress/Fj/hjc/Fj;

    move-result-object v0

    return-object v0
.end method

.method public WR()Lcom/bytedance/sdk/component/WR/ex/Fj;
    .locals 1

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/rAx/hjc;->Fj()Lcom/bytedance/sdk/openadsdk/rAx/hjc;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/rAx/hjc;->ex()Lcom/bytedance/sdk/component/WR/Fj;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/WR/Fj;->eV()Lcom/bytedance/sdk/component/WR/ex/Fj;

    move-result-object v0

    return-object v0
.end method

.method public eV()V
    .locals 4

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/JU;->eV()Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;

    move-result-object v0

    invoke-interface {v0}, Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;->YH()I

    move-result v0

    if-eqz v0, :cond_0

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/JU;->eV()Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;

    move-result-object v0

    invoke-interface {v0}, Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;->qg()I

    move-result v0

    if-nez v0, :cond_1

    :cond_0
    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/JU;->eV()Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;

    move-result-object v0

    invoke-interface {v0}, Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;->At()I

    move-result v0

    if-eqz v0, :cond_4

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/JU;->eV()Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;

    move-result-object v0

    invoke-interface {v0}, Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;->Jq()I

    move-result v0

    if-nez v0, :cond_1

    goto :goto_0

    :cond_1
    invoke-static {}, Lcom/bytedance/sdk/component/adexpress/Ubf/Ubf;->Fj()Lcom/bytedance/sdk/component/adexpress/Ubf/Ubf;

    move-result-object v0

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/JU;->eV()Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;

    move-result-object v1

    invoke-interface {v1}, Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;->YH()I

    move-result v1

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/adexpress/Ubf/Ubf;->Fj(I)V

    invoke-static {}, Lcom/bytedance/sdk/component/adexpress/Ubf/Ubf;->Fj()Lcom/bytedance/sdk/component/adexpress/Ubf/Ubf;

    move-result-object v0

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/JU;->eV()Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;

    move-result-object v1

    invoke-interface {v1}, Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;->Jq()I

    move-result v1

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/adexpress/Ubf/Ubf;->ex(I)V

    const/4 v0, 0x1

    new-array v0, v0, [Landroid/os/MessageQueue;

    sget v1, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v2, 0x17

    const/4 v3, 0x0

    if-lt v1, v2, :cond_2

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/dG;->hjc()Landroid/os/Handler;

    move-result-object v1

    invoke-virtual {v1}, Landroid/os/Handler;->getLooper()Landroid/os/Looper;

    move-result-object v1

    invoke-static {v1}, Lcom/apm/insight/b/l;->a(Landroid/os/Looper;)Landroid/os/MessageQueue;

    move-result-object v1

    aput-object v1, v0, v3

    invoke-direct {p0, v1}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ko$2;->Fj(Landroid/os/MessageQueue;)V

    return-void

    :cond_2
    invoke-static {}, Landroid/os/Looper;->getMainLooper()Landroid/os/Looper;

    move-result-object v1

    invoke-static {}, Landroid/os/Looper;->myLooper()Landroid/os/Looper;

    move-result-object v2

    if-ne v1, v2, :cond_3

    invoke-static {}, Landroid/os/Looper;->myQueue()Landroid/os/MessageQueue;

    move-result-object v1

    aput-object v1, v0, v3

    invoke-direct {p0, v1}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ko$2;->Fj(Landroid/os/MessageQueue;)V

    return-void

    :cond_3
    new-instance v1, Landroid/os/Handler;

    invoke-static {}, Landroid/os/Looper;->getMainLooper()Landroid/os/Looper;

    move-result-object v2

    invoke-direct {v1, v2}, Landroid/os/Handler;-><init>(Landroid/os/Looper;)V

    new-instance v2, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ko$2$1;

    invoke-direct {v2, p0, v0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ko$2$1;-><init>(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ko$2;[Landroid/os/MessageQueue;)V

    invoke-virtual {v1, v2}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    :cond_4
    :goto_0
    return-void
.end method

.method public ex()Landroid/content/Context;
    .locals 1

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/JU;->Fj()Landroid/content/Context;

    move-result-object v0

    return-object v0
.end method

.method public hjc()Landroid/os/Handler;
    .locals 1

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/dG;->hjc()Landroid/os/Handler;

    move-result-object v0

    return-object v0
.end method

.method public mSE()I
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public rAx()I
    .locals 1

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/JU;->eV()Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;

    move-result-object v0

    invoke-interface {v0}, Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;->At()I

    move-result v0

    return v0
.end method

.method public svN()Lcom/bytedance/sdk/component/WR/ex/ex;
    .locals 1

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/rAx/hjc;->Fj()Lcom/bytedance/sdk/openadsdk/rAx/hjc;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/rAx/hjc;->ex()Lcom/bytedance/sdk/component/WR/Fj;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/WR/Fj;->hjc()Lcom/bytedance/sdk/component/WR/ex/ex;

    move-result-object v0

    return-object v0
.end method
