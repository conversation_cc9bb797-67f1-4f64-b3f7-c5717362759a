.class public Lcom/bytedance/sdk/openadsdk/core/eV/ex;
.super Ljava/lang/Object;


# direct methods
.method public static Fj()Z
    .locals 1

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/multipro/ex;->hjc()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/multipro/hjc/Fj;->ex()Z

    move-result v0

    return v0

    :cond_0
    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/eV/Fj;->Fj()Lcom/bytedance/sdk/openadsdk/core/eV/Fj;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/eV/Fj;->ex()Z

    move-result v0

    return v0
.end method

.method public static Fj(Ljava/lang/String;)Z
    .locals 1

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/multipro/ex;->hjc()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-static {p0}, Lcom/bytedance/sdk/openadsdk/multipro/hjc/Fj;->Fj(Ljava/lang/String;)Z

    move-result p0

    return p0

    :cond_0
    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/eV/Fj;->Fj()Lcom/bytedance/sdk/openadsdk/core/eV/Fj;

    move-result-object v0

    invoke-virtual {v0, p0}, Lcom/bytedance/sdk/openadsdk/core/eV/Fj;->Fj(Ljava/lang/String;)Z

    move-result p0

    return p0
.end method

.method public static ex()Ljava/lang/String;
    .locals 1

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/multipro/ex;->hjc()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/multipro/hjc/Fj;->hjc()Ljava/lang/String;

    move-result-object v0

    return-object v0

    :cond_0
    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/eV/Fj;->Fj()Lcom/bytedance/sdk/openadsdk/core/eV/Fj;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/eV/Fj;->hjc()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
