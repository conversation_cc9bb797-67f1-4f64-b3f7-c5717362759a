.class Lcom/bytedance/sdk/openadsdk/core/model/JW$3;
.super Ljava/lang/Object;

# interfaces
.implements Landroid/view/View$OnClickListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/core/model/JW;->Tc()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/core/model/JW;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/core/model/JW;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/JW$3;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/JW;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public onClick(Landroid/view/View;)V
    .locals 1

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/JW$3;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/JW;

    iget-object v0, p1, Lcom/bytedance/sdk/openadsdk/core/model/JW;->svN:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/core/model/JW;->eV(Lcom/bytedance/sdk/openadsdk/core/model/JW;)Ljava/lang/String;

    move-result-object p1

    invoke-static {v0, p1}, Lcom/bytedance/sdk/openadsdk/ex/hjc;->ex(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Ljava/lang/String;)V

    return-void
.end method
