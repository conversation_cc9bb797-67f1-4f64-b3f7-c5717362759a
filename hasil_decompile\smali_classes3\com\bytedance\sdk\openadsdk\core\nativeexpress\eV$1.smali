.class Lcom/bytedance/sdk/openadsdk/core/nativeexpress/eV$1;
.super Lcom/bytedance/sdk/component/svN/BcC;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/sdk/openadsdk/core/nativeexpress/eV;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/eV;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/eV;Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/eV$1;->Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/eV;

    invoke-direct {p0, p2}, Lcom/bytedance/sdk/component/svN/BcC;-><init>(Ljava/lang/String;)V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/eV$1;->Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/eV;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/eV;->Fj(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/eV;)Lcom/bytedance/sdk/component/adexpress/ex/dG;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/adexpress/ex/dG;->hjc()Lorg/json/JSONObject;

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/dG;->hjc()Landroid/os/Handler;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/eV$1;->Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/eV;

    invoke-static {v1}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/eV;->ex(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/eV;)Ljava/lang/Runnable;

    move-result-object v1

    invoke-virtual {v0, v1}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    return-void
.end method
