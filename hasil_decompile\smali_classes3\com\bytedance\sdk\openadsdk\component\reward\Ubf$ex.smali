.class Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$ex;
.super Lcom/bytedance/sdk/component/svN/BcC;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "ex"
.end annotation


# instance fields
.field Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

.field ex:Lcom/bytedance/sdk/openadsdk/AdSlot;

.field hjc:Lcom/bytedance/sdk/openadsdk/core/model/Fj;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/AdSlot;)V
    .locals 1

    const-string v0, "Fullscreen Task"

    invoke-direct {p0, v0}, Lcom/bytedance/sdk/component/svN/BcC;-><init>(Ljava/lang/String;)V

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$ex;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    iput-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$ex;->ex:Lcom/bytedance/sdk/openadsdk/AdSlot;

    return-void
.end method


# virtual methods
.method public run()V
    .locals 3

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$ex;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    if-nez v0, :cond_0

    return-void

    :cond_0
    sget v1, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v2, 0x17

    if-lt v1, v2, :cond_2

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->spi()Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

    move-result-object v0

    if-eqz v0, :cond_1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$ex;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->aGk()I

    move-result v0

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/CacheDirFactory;->getICacheDir(I)Lcom/bykv/vk/openvk/component/video/api/Fj/ex;

    move-result-object v0

    invoke-interface {v0}, Lcom/bykv/vk/openvk/component/video/api/Fj/ex;->Fj()Ljava/lang/String;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$ex;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {v0, v1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->Fj(Ljava/lang/String;Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Lcom/bytedance/sdk/openadsdk/core/video/Fj/ex;

    move-result-object v0

    const-string v1, "material_meta"

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$ex;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v0, v1, v2}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->Fj(Ljava/lang/String;Ljava/lang/Object;)V

    const-string v1, "ad_slot"

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$ex;->ex:Lcom/bytedance/sdk/openadsdk/AdSlot;

    invoke-virtual {v0, v1, v2}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->Fj(Ljava/lang/String;Ljava/lang/Object;)V

    new-instance v1, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$ex$1;

    invoke-direct {v1, p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$ex$1;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$ex;)V

    invoke-static {v0, v1}, Lcom/bytedance/sdk/openadsdk/core/video/eV/Fj;->Fj(Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;Lcom/bykv/vk/openvk/component/video/api/Ubf/Fj$Fj;)V

    :cond_1
    return-void

    :cond_2
    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/JU;->Fj()Landroid/content/Context;

    move-result-object v0

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/eV;->Fj(Landroid/content/Context;)Lcom/bytedance/sdk/openadsdk/component/reward/eV;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$ex;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    new-instance v2, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$ex$2;

    invoke-direct {v2, p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$ex$2;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$ex;)V

    invoke-virtual {v0, v1, v2}, Lcom/bytedance/sdk/openadsdk/component/reward/eV;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/component/reward/eV$Fj;)V

    return-void
.end method
