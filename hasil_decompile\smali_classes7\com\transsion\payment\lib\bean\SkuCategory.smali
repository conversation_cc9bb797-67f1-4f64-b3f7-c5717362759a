.class public final enum Lcom/transsion/payment/lib/bean/SkuCategory;
.super Ljava/lang/Enum;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lcom/transsion/payment/lib/bean/SkuCategory;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# static fields
.field private static final synthetic $VALUES:[Lcom/transsion/payment/lib/bean/SkuCategory;

.field public static final enum SkuCategoryAutoRenew:Lcom/transsion/payment/lib/bean/SkuCategory;

.field public static final enum SkuCategoryCoin:Lcom/transsion/payment/lib/bean/SkuCategory;

.field public static final enum SkuCategoryOnceRecharge:Lcom/transsion/payment/lib/bean/SkuCategory;

.field public static final enum _SkuCategory:Lcom/transsion/payment/lib/bean/SkuCategory;


# direct methods
.method private static final synthetic $values()[Lcom/transsion/payment/lib/bean/SkuCategory;
    .locals 3

    const/4 v0, 0x4

    new-array v0, v0, [Lcom/transsion/payment/lib/bean/SkuCategory;

    const/4 v1, 0x0

    sget-object v2, Lcom/transsion/payment/lib/bean/SkuCategory;->_SkuCategory:Lcom/transsion/payment/lib/bean/SkuCategory;

    aput-object v2, v0, v1

    const/4 v1, 0x1

    sget-object v2, Lcom/transsion/payment/lib/bean/SkuCategory;->SkuCategoryCoin:Lcom/transsion/payment/lib/bean/SkuCategory;

    aput-object v2, v0, v1

    const/4 v1, 0x2

    sget-object v2, Lcom/transsion/payment/lib/bean/SkuCategory;->SkuCategoryOnceRecharge:Lcom/transsion/payment/lib/bean/SkuCategory;

    aput-object v2, v0, v1

    const/4 v1, 0x3

    sget-object v2, Lcom/transsion/payment/lib/bean/SkuCategory;->SkuCategoryAutoRenew:Lcom/transsion/payment/lib/bean/SkuCategory;

    aput-object v2, v0, v1

    return-object v0
.end method

.method static constructor <clinit>()V
    .locals 3

    new-instance v0, Lcom/transsion/payment/lib/bean/SkuCategory;

    const-string v1, "_SkuCategory"

    const/4 v2, 0x0

    invoke-direct {v0, v1, v2}, Lcom/transsion/payment/lib/bean/SkuCategory;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/transsion/payment/lib/bean/SkuCategory;->_SkuCategory:Lcom/transsion/payment/lib/bean/SkuCategory;

    new-instance v0, Lcom/transsion/payment/lib/bean/SkuCategory;

    const-string v1, "SkuCategoryCoin"

    const/4 v2, 0x1

    invoke-direct {v0, v1, v2}, Lcom/transsion/payment/lib/bean/SkuCategory;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/transsion/payment/lib/bean/SkuCategory;->SkuCategoryCoin:Lcom/transsion/payment/lib/bean/SkuCategory;

    new-instance v0, Lcom/transsion/payment/lib/bean/SkuCategory;

    const-string v1, "SkuCategoryOnceRecharge"

    const/4 v2, 0x2

    invoke-direct {v0, v1, v2}, Lcom/transsion/payment/lib/bean/SkuCategory;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/transsion/payment/lib/bean/SkuCategory;->SkuCategoryOnceRecharge:Lcom/transsion/payment/lib/bean/SkuCategory;

    new-instance v0, Lcom/transsion/payment/lib/bean/SkuCategory;

    const-string v1, "SkuCategoryAutoRenew"

    const/4 v2, 0x3

    invoke-direct {v0, v1, v2}, Lcom/transsion/payment/lib/bean/SkuCategory;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/transsion/payment/lib/bean/SkuCategory;->SkuCategoryAutoRenew:Lcom/transsion/payment/lib/bean/SkuCategory;

    invoke-static {}, Lcom/transsion/payment/lib/bean/SkuCategory;->$values()[Lcom/transsion/payment/lib/bean/SkuCategory;

    move-result-object v0

    sput-object v0, Lcom/transsion/payment/lib/bean/SkuCategory;->$VALUES:[Lcom/transsion/payment/lib/bean/SkuCategory;

    return-void
.end method

.method private constructor <init>(Ljava/lang/String;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    return-void
.end method

.method public static valueOf(Ljava/lang/String;)Lcom/transsion/payment/lib/bean/SkuCategory;
    .locals 1

    const-class v0, Lcom/transsion/payment/lib/bean/SkuCategory;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object p0

    check-cast p0, Lcom/transsion/payment/lib/bean/SkuCategory;

    return-object p0
.end method

.method public static values()[Lcom/transsion/payment/lib/bean/SkuCategory;
    .locals 1

    sget-object v0, Lcom/transsion/payment/lib/bean/SkuCategory;->$VALUES:[Lcom/transsion/payment/lib/bean/SkuCategory;

    invoke-virtual {v0}, [Ljava/lang/Object;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lcom/transsion/payment/lib/bean/SkuCategory;

    return-object v0
.end method
