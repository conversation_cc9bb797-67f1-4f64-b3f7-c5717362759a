.class Lcom/bytedance/sdk/openadsdk/component/reward/ex/Ubf$1;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/reward/ex/Ubf;->Fj(IJ)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/component/reward/ex/Ubf;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/reward/ex/Ubf;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Ubf$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/ex/Ubf;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Ubf$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/ex/Ubf;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Ubf;->rf()V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Ubf$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/ex/Ubf;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;->uy()V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Ubf$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/ex/Ubf;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Bzy:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Ubf;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Ubf;->ex()V

    return-void
.end method
