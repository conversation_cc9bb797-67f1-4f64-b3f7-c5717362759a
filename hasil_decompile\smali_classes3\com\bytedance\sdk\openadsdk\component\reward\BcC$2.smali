.class Lcom/bytedance/sdk/openadsdk/component/reward/BcC$2;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/reward/BcC;->show(Landroid/app/Activity;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

.field final synthetic ex:Lcom/bytedance/sdk/openadsdk/component/reward/BcC;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/reward/BcC;Lcom/bytedance/sdk/openadsdk/core/model/Ql;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/BcC$2;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/BcC;

    iput-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/BcC$2;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 3

    :try_start_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/BcC$2;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->lv()Lcom/bytedance/sdk/openadsdk/AdSlot;

    move-result-object v0

    if-eqz v0, :cond_0

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/BcC$2;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/BcC;

    invoke-static {v1}, Lcom/bytedance/sdk/openadsdk/component/reward/BcC;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/BcC;)Landroid/content/Context;

    move-result-object v1

    invoke-static {v1}, Lcom/bytedance/sdk/openadsdk/component/reward/UYd;->Fj(Landroid/content/Context;)Lcom/bytedance/sdk/openadsdk/component/reward/UYd;

    move-result-object v1

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/AdSlot;->getCodeId()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Lcom/bytedance/sdk/openadsdk/component/reward/UYd;->Fj(Ljava/lang/String;)V

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/BcC$2;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/BcC;

    invoke-static {v1}, Lcom/bytedance/sdk/openadsdk/component/reward/BcC;->ex(Lcom/bytedance/sdk/openadsdk/component/reward/BcC;)Z

    move-result v1

    if-eqz v1, :cond_0

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/BcC$2;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/BcC;

    invoke-static {v1}, Lcom/bytedance/sdk/openadsdk/component/reward/BcC;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/BcC;)Landroid/content/Context;

    move-result-object v1

    invoke-static {v1}, Lcom/bytedance/sdk/openadsdk/component/reward/UYd;->Fj(Landroid/content/Context;)Lcom/bytedance/sdk/openadsdk/component/reward/UYd;

    move-result-object v1

    invoke-virtual {v1, v0}, Lcom/bytedance/sdk/openadsdk/component/reward/UYd;->ex(Lcom/bytedance/sdk/openadsdk/AdSlot;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :catchall_0
    :cond_0
    return-void
.end method
