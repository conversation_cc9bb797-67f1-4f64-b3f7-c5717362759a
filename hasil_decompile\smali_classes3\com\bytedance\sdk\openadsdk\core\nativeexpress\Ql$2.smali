.class Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql$2;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql$2;->Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql$2;->Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->svN(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;)Ljava/util/concurrent/atomic/AtomicBoolean;

    move-result-object v0

    invoke-virtual {v0}, Ljava/util/concurrent/atomic/AtomicBoolean;->get()Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql$2;->Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->BcC(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;)Lcom/bytedance/sdk/component/adexpress/ex/svN;

    move-result-object v0

    if-eqz v0, :cond_1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql$2;->Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->UYd()V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql$2;->Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->BcC(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;)Lcom/bytedance/sdk/component/adexpress/ex/svN;

    move-result-object v1

    invoke-static {v0, v1}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->Fj(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;Lcom/bytedance/sdk/component/adexpress/ex/svN;)V

    :cond_1
    return-void
.end method
