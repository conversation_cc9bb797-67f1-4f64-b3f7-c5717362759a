.class public Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/sdk/openadsdk/core/model/Ql;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "Fj"
.end annotation


# instance fields
.field private BcC:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation
.end field

.field private Fj:Ljava/lang/String;

.field private Ko:Ljava/lang/String;

.field private UYd:Ljava/lang/String;

.field private Ubf:Ljava/lang/String;

.field private WR:Ljava/lang/String;

.field private dG:Ljava/lang/String;

.field private eV:Ljava/lang/String;

.field private ex:Ljava/lang/String;

.field private hjc:Ljava/lang/String;

.field private mSE:Ljava/lang/String;

.field private rAx:Ljava/lang/String;

.field private svN:Ljava/lang/String;


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;Ljava/lang/String;)Lcom/bytedance/sdk/component/adexpress/Fj/hjc/eV;
    .locals 2
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    invoke-static {}, Lcom/bytedance/sdk/component/adexpress/Fj/hjc/eV;->Fj()Lcom/bytedance/sdk/component/adexpress/Fj/hjc/eV;

    move-result-object v0

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;->Ubf()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/adexpress/Fj/hjc/eV;->Fj(Ljava/lang/String;)Lcom/bytedance/sdk/component/adexpress/Fj/hjc/eV;

    move-result-object v0

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;->WR()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/adexpress/Fj/hjc/eV;->ex(Ljava/lang/String;)Lcom/bytedance/sdk/component/adexpress/Fj/hjc/eV;

    move-result-object v0

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;->svN()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/adexpress/Fj/hjc/eV;->hjc(Ljava/lang/String;)Lcom/bytedance/sdk/component/adexpress/Fj/hjc/eV;

    move-result-object v0

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;->BcC()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/adexpress/Fj/hjc/eV;->eV(Ljava/lang/String;)Lcom/bytedance/sdk/component/adexpress/Fj/hjc/eV;

    move-result-object v0

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;->eV()Ljava/lang/String;

    move-result-object p0

    invoke-virtual {v0, p0}, Lcom/bytedance/sdk/component/adexpress/Fj/hjc/eV;->Ubf(Ljava/lang/String;)Lcom/bytedance/sdk/component/adexpress/Fj/hjc/eV;

    move-result-object p0

    invoke-virtual {p0, p1}, Lcom/bytedance/sdk/component/adexpress/Fj/hjc/eV;->WR(Ljava/lang/String;)Lcom/bytedance/sdk/component/adexpress/Fj/hjc/eV;

    move-result-object p0

    return-object p0
.end method

.method public static ex(Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;Ljava/lang/String;)Lcom/bytedance/sdk/openadsdk/core/ugen/Ubf/Fj;
    .locals 2
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    new-instance v0, Lcom/bytedance/sdk/openadsdk/core/ugen/Ubf/Fj;

    invoke-direct {v0}, Lcom/bytedance/sdk/openadsdk/core/ugen/Ubf/Fj;-><init>()V

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;->Ubf()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/core/ugen/Ubf/Fj;->Fj(Ljava/lang/String;)Lcom/bytedance/sdk/openadsdk/core/ugen/Ubf/Fj;

    move-result-object v0

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;->JW()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/core/ugen/Ubf/Fj;->ex(Ljava/lang/String;)Lcom/bytedance/sdk/openadsdk/core/ugen/Ubf/Fj;

    move-result-object v0

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;->Tc()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/core/ugen/Ubf/Fj;->hjc(Ljava/lang/String;)Lcom/bytedance/sdk/openadsdk/core/ugen/Ubf/Fj;

    move-result-object v0

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;->JU()Ljava/lang/String;

    move-result-object p0

    invoke-virtual {v0, p0}, Lcom/bytedance/sdk/openadsdk/core/ugen/Ubf/Fj;->eV(Ljava/lang/String;)Lcom/bytedance/sdk/openadsdk/core/ugen/Ubf/Fj;

    move-result-object p0

    invoke-virtual {p0, p1}, Lcom/bytedance/sdk/openadsdk/core/ugen/Ubf/Fj;->Ubf(Ljava/lang/String;)Lcom/bytedance/sdk/openadsdk/core/ugen/Ubf/Fj;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public BcC()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;->eV:Ljava/lang/String;

    return-object v0
.end method

.method public BcC(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;->Ko:Ljava/lang/String;

    return-void
.end method

.method public Fj(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;->WR:Ljava/lang/String;

    return-void
.end method

.method public Fj(Ljava/util/List;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Ljava/lang/Integer;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;->BcC:Ljava/util/List;

    return-void
.end method

.method public JU()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;->dG:Ljava/lang/String;

    return-object v0
.end method

.method public JW()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;->UYd:Ljava/lang/String;

    return-object v0
.end method

.method public Ko()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;->svN:Ljava/lang/String;

    return-object v0
.end method

.method public Ko(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;->rAx:Ljava/lang/String;

    return-void
.end method

.method public Tc()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;->rAx:Ljava/lang/String;

    return-object v0
.end method

.method public UYd()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;->mSE:Ljava/lang/String;

    return-object v0
.end method

.method public UYd(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;->dG:Ljava/lang/String;

    return-void
.end method

.method public Ubf()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;->Fj:Ljava/lang/String;

    return-object v0
.end method

.method public Ubf(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;->eV:Ljava/lang/String;

    return-void
.end method

.method public WR()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;->ex:Ljava/lang/String;

    return-object v0
.end method

.method public WR(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;->Ubf:Ljava/lang/String;

    return-void
.end method

.method public dG()Z
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;->mSE:Ljava/lang/String;

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;->mSE:Ljava/lang/String;

    const-string v1, "v3"

    invoke-virtual {v0, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    return v0

    :cond_0
    const/4 v0, 0x0

    return v0
.end method

.method public eV()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;->WR:Ljava/lang/String;

    return-object v0
.end method

.method public eV(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;->hjc:Ljava/lang/String;

    return-void
.end method

.method public ex(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;->Fj:Ljava/lang/String;

    return-void
.end method

.method public hjc()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;->BcC:Ljava/util/List;

    return-object v0
.end method

.method public hjc(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;->ex:Ljava/lang/String;

    return-void
.end method

.method public mSE()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;->Ubf:Ljava/lang/String;

    return-object v0
.end method

.method public mSE(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;->mSE:Ljava/lang/String;

    return-void
.end method

.method public rAx()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;->Ko:Ljava/lang/String;

    return-object v0
.end method

.method public rAx(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;->UYd:Ljava/lang/String;

    return-void
.end method

.method public svN()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;->hjc:Ljava/lang/String;

    return-object v0
.end method

.method public svN(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;->svN:Ljava/lang/String;

    return-void
.end method
