.class public interface abstract Lcom/bytedance/sdk/openadsdk/component/WR$ex;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/sdk/openadsdk/component/WR;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "ex"
.end annotation


# virtual methods
.method public abstract Fj()V
.end method

.method public abstract Fj(Lcom/bytedance/sdk/openadsdk/rAx/Fj/ex;)V
    .param p1    # Lcom/bytedance/sdk/openadsdk/rAx/Fj/ex;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
.end method
