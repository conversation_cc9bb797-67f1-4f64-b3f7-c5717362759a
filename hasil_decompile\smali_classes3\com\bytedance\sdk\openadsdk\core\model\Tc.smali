.class public Lcom/bytedance/sdk/openadsdk/core/model/Tc;
.super Ljava/lang/Object;


# instance fields
.field private Fj:Ljava/lang/String;

.field private Ubf:Z

.field private WR:Ljava/lang/String;

.field private eV:D

.field private ex:I

.field private hjc:I


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Tc;->Fj:Ljava/lang/String;

    return-object v0
.end method

.method public Fj(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Tc;->ex:I

    return-void
.end method

.method public Fj(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Tc;->Fj:Ljava/lang/String;

    return-void
.end method

.method public Fj(Z)V
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Tc;->Ubf:Z

    return-void
.end method

.method public Ubf()Z
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Tc;->Fj:Ljava/lang/String;

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-nez v0, :cond_0

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Tc;->ex:I

    if-lez v0, :cond_0

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Tc;->hjc:I

    if-lez v0, :cond_0

    const/4 v0, 0x1

    return v0

    :cond_0
    const/4 v0, 0x0

    return v0
.end method

.method public WR()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Tc;->Ubf:Z

    return v0
.end method

.method public eV()D
    .locals 2

    iget-wide v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Tc;->eV:D

    return-wide v0
.end method

.method public ex()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Tc;->ex:I

    return v0
.end method

.method public ex(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Tc;->hjc:I

    return-void
.end method

.method public ex(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Tc;->WR:Ljava/lang/String;

    return-void
.end method

.method public hjc()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Tc;->hjc:I

    return v0
.end method

.method public svN()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Tc;->WR:Ljava/lang/String;

    return-object v0
.end method
