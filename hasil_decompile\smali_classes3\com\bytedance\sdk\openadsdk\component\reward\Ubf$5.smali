.class Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$5;
.super Lcom/bykv/vk/openvk/component/video/api/Ubf/ex;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Fj;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/component/reward/Tc;Lcom/bytedance/sdk/openadsdk/AdSlot;ZLcom/bytedance/sdk/openadsdk/utils/lv;Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$Fj;Z)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Z

.field final synthetic Ubf:Lcom/bytedance/sdk/openadsdk/AdSlot;

.field final synthetic WR:Lcom/bytedance/sdk/openadsdk/core/model/Fj;

.field final synthetic eV:Lcom/bytedance/sdk/openadsdk/component/reward/Tc;

.field final synthetic ex:Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$Fj;

.field final synthetic hjc:Z

.field final synthetic svN:Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;ZLcom/bytedance/sdk/openadsdk/component/reward/Ubf$Fj;ZLcom/bytedance/sdk/openadsdk/component/reward/Tc;Lcom/bytedance/sdk/openadsdk/AdSlot;Lcom/bytedance/sdk/openadsdk/core/model/Fj;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$5;->svN:Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;

    iput-boolean p2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$5;->Fj:Z

    iput-object p3, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$5;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$Fj;

    iput-boolean p4, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$5;->hjc:Z

    iput-object p5, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$5;->eV:Lcom/bytedance/sdk/openadsdk/component/reward/Tc;

    iput-object p6, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$5;->Ubf:Lcom/bytedance/sdk/openadsdk/AdSlot;

    iput-object p7, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$5;->WR:Lcom/bytedance/sdk/openadsdk/core/model/Fj;

    invoke-direct {p0}, Lcom/bykv/vk/openvk/component/video/api/Ubf/ex;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj(Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;I)V
    .locals 1

    iget-boolean p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$5;->Fj:Z

    if-nez p1, :cond_1

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$5;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$Fj;

    if-eqz p1, :cond_2

    iget-boolean p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$5;->hjc:Z

    if-nez p1, :cond_0

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/JU;->eV()Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;

    move-result-object p1

    invoke-interface {p1}, Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;->mE()I

    move-result p1

    const/4 p2, 0x1

    if-ne p1, p2, :cond_2

    :cond_0
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$5;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$Fj;

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$5;->eV:Lcom/bytedance/sdk/openadsdk/component/reward/Tc;

    invoke-virtual {p2}, Lcom/bytedance/sdk/openadsdk/component/reward/Tc;->Fj()Lcom/bytedance/sdk/openadsdk/component/reward/WR;

    move-result-object p2

    invoke-virtual {p1, p2}, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$Fj;->Fj(Lcom/bytedance/sdk/openadsdk/api/interstitial/PAGInterstitialAd;)V

    return-void

    :cond_1
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$5;->svN:Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;)Landroid/content/Context;

    move-result-object p1

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/eV;->Fj(Landroid/content/Context;)Lcom/bytedance/sdk/openadsdk/component/reward/eV;

    move-result-object p1

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$5;->Ubf:Lcom/bytedance/sdk/openadsdk/AdSlot;

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$5;->WR:Lcom/bytedance/sdk/openadsdk/core/model/Fj;

    invoke-virtual {p1, p2, v0}, Lcom/bytedance/sdk/openadsdk/component/reward/eV;->Fj(Lcom/bytedance/sdk/openadsdk/AdSlot;Lcom/bytedance/sdk/openadsdk/core/model/Fj;)V

    :cond_2
    return-void
.end method

.method public Fj(Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;ILjava/lang/String;)V
    .locals 1

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$5;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$Fj;

    if-eqz p1, :cond_1

    iget-boolean p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$5;->hjc:Z

    if-nez p1, :cond_0

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/JU;->eV()Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;

    move-result-object p1

    invoke-interface {p1}, Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;->mE()I

    move-result p1

    const/4 v0, 0x1

    if-ne p1, v0, :cond_1

    :cond_0
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$5;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$Fj;

    invoke-virtual {p1, p2, p3}, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$Fj;->onError(ILjava/lang/String;)V

    :cond_1
    return-void
.end method
