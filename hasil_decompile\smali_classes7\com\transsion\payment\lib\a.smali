.class public interface abstract Lcom/transsion/payment/lib/a;
.super Ljava/lang/Object;


# annotations
.annotation runtime Lkotlin/Metadata;
.end annotation


# virtual methods
.method public abstract a(Ljava/lang/String;Lokhttp3/x;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
    .param p1    # Ljava/lang/String;
        .annotation runtime Lfx/t;
            value = "host"
        .end annotation
    .end param
    .param p2    # Lokhttp3/x;
        .annotation runtime Lfx/a;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Lokhttp3/x;",
            "Lkotlin/coroutines/Continuation<",
            "-",
            "Lcom/tn/lib/net/bean/BaseDto<",
            "Lcom/transsion/payment/lib/bean/CheckPaymentBean;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .annotation runtime Lfx/o;
        value = "/wefeed-mobile-bff/money/paynicorn-purchase-result/polling"
    .end annotation
.end method

.method public abstract b(Ljava/lang/String;Lokhttp3/x;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
    .param p1    # Ljava/lang/String;
        .annotation runtime Lfx/t;
            value = "host"
        .end annotation
    .end param
    .param p2    # Lokhttp3/x;
        .annotation runtime Lfx/a;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Lokhttp3/x;",
            "Lkotlin/coroutines/Continuation<",
            "-",
            "Lcom/tn/lib/net/bean/BaseDto<",
            "Lcom/transsion/payment/lib/bean/CreateOrderRes;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .annotation runtime Lfx/o;
        value = "/wefeed-mobile-bff/money/gp-trading-order/create"
    .end annotation
.end method

.method public abstract c(Ljava/lang/String;Lokhttp3/x;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
    .param p1    # Ljava/lang/String;
        .annotation runtime Lfx/t;
            value = "host"
        .end annotation
    .end param
    .param p2    # Lokhttp3/x;
        .annotation runtime Lfx/a;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Lokhttp3/x;",
            "Lkotlin/coroutines/Continuation<",
            "-",
            "Lcom/tn/lib/net/bean/BaseDto<",
            "Lcom/transsion/payment/lib/bean/CheckPaymentBean;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .annotation runtime Lfx/o;
        value = "/wefeed-mobile-bff/money/gp-purchase-result/polling"
    .end annotation
.end method

.method public abstract d(Ljava/lang/String;Lokhttp3/x;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
    .param p1    # Ljava/lang/String;
        .annotation runtime Lfx/t;
            value = "host"
        .end annotation
    .end param
    .param p2    # Lokhttp3/x;
        .annotation runtime Lfx/a;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Lokhttp3/x;",
            "Lkotlin/coroutines/Continuation<",
            "-",
            "Lcom/tn/lib/net/bean/BaseDto<",
            "Lcom/transsion/payment/lib/bean/CreateOrderRes;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .annotation runtime Lfx/o;
        value = "/wefeed-mobile-bff/money/paynicorn-trading-order/create"
    .end annotation
.end method

.method public abstract e(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
    .param p1    # Ljava/lang/String;
        .annotation runtime Lfx/t;
            value = "host"
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/Continuation<",
            "-",
            "Lcom/tn/lib/net/bean/BaseDto<",
            "Lcom/transsion/payment/lib/bean/SkuData;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .annotation runtime Lfx/f;
        value = "/wefeed-mobile-bff/money/sku-list/get"
    .end annotation
.end method
