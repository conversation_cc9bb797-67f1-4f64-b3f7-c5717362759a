.class Lcom/bytedance/sdk/openadsdk/component/reward/ex/svN$2;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/reward/ex/svN;->eh()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/component/reward/ex/svN;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/reward/ex/svN;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/svN$2;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/ex/svN;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 3

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/svN$2;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/ex/svN;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/ex/svN;->ex(Lcom/bytedance/sdk/openadsdk/component/reward/ex/svN;)Lcom/bytedance/sdk/openadsdk/core/customview/PAGRelativeLayout;

    move-result-object v0

    invoke-virtual {v0}, Landroid/view/View;->getWidth()I

    move-result v0

    div-int/lit8 v0, v0, 0x2

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/JU;->Fj()Landroid/content/Context;

    move-result-object v1

    const/high16 v2, 0x42b40000    # 90.0f

    invoke-static {v1, v2}, Lcom/bytedance/sdk/openadsdk/utils/eh;->hjc(Landroid/content/Context;F)I

    move-result v1

    if-lt v0, v1, :cond_0

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/svN$2;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/ex/svN;

    invoke-static {v1}, Lcom/bytedance/sdk/openadsdk/component/reward/ex/svN;->hjc(Lcom/bytedance/sdk/openadsdk/component/reward/ex/svN;)Lcom/bytedance/sdk/openadsdk/core/customview/PAGTextView;

    move-result-object v1

    invoke-virtual {v1}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object v1

    iget v2, v1, Landroid/view/ViewGroup$LayoutParams;->width:I

    if-lez v2, :cond_0

    invoke-static {v0, v2}, Ljava/lang/Math;->min(II)I

    move-result v0

    iput v0, v1, Landroid/view/ViewGroup$LayoutParams;->width:I

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/svN$2;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/ex/svN;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/ex/svN;->hjc(Lcom/bytedance/sdk/openadsdk/component/reward/ex/svN;)Lcom/bytedance/sdk/openadsdk/core/customview/PAGTextView;

    move-result-object v0

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/core/customview/PAGTextView;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    :cond_0
    return-void
.end method
