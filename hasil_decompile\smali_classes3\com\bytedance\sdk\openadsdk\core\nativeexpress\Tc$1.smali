.class Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Tc$1;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Tc;->Fj(Lcom/bytedance/sdk/component/adexpress/ex/svN;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Tc;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Tc;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Tc$1;->Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Tc;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Tc$1;->Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Tc;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Tc;->Fj(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Tc;)V

    return-void
.end method
