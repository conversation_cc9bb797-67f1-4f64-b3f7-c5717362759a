.class public Lcom/bytedance/sdk/openadsdk/component/reward/ex/mSE;
.super Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;)V
    .locals 0

    invoke-direct {p0, p1}, Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;)V

    return-void
.end method

.method public static Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z
    .locals 1

    invoke-static {p0}, Lcom/bytedance/sdk/openadsdk/core/model/mE;->ex(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v0

    if-nez v0, :cond_1

    invoke-static {p0}, Lcom/bytedance/sdk/openadsdk/core/model/mE;->rAx(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result p0

    if-eqz p0, :cond_0

    goto :goto_0

    :cond_0
    const/4 p0, 0x0

    return p0

    :cond_1
    :goto_0
    const/4 p0, 0x1

    return p0
.end method


# virtual methods
.method public Af()V
    .locals 2

    invoke-super {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;->Af()V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/model/mE;->rAx(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Fj;->Ko:Lcom/bytedance/sdk/component/utils/Vq;

    const/16 v1, 0x258

    invoke-virtual {v0, v1}, Landroid/os/Handler;->removeMessages(I)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->gXF:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;

    const-string v1, "go_background"

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;->Fj(Ljava/lang/String;)V

    :cond_0
    return-void
.end method

.method public Fj(Landroid/widget/FrameLayout;)V
    .locals 0

    return-void
.end method

.method public Ubf()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public WR()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public mC()V
    .locals 2

    invoke-super {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;->mC()V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/model/mE;->rAx(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Fj;->Ko:Lcom/bytedance/sdk/component/utils/Vq;

    const/16 v1, 0x258

    invoke-virtual {v0, v1}, Landroid/os/Handler;->removeMessages(I)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->gXF:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;

    const-string v1, "go_background"

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;->Fj(Ljava/lang/String;)V

    :cond_0
    return-void
.end method

.method public mE()V
    .locals 3

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->gXF:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;->Ko()Z

    move-result v0

    if-eqz v0, :cond_1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/model/mE;->rAx(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v0

    if-eqz v0, :cond_1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->gXF:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;

    const-string v1, "return_foreground"

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;->Fj(Ljava/lang/String;)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;->dG:Lcom/bytedance/sdk/openadsdk/core/widget/Fj;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Landroid/app/Dialog;->isShowing()Z

    move-result v0

    if-nez v0, :cond_1

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->gXF:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;->UYd()I

    move-result v0

    if-ltz v0, :cond_1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->gXF:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;

    const-wide/16 v1, 0x0

    invoke-virtual {v0, v1, v2}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;->Fj(J)V

    :cond_1
    return-void
.end method

.method public rS()V
    .locals 3

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Fj;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/model/mE;->hjc(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->qPr:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->gXF()Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->qPr:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->efV()Z

    move-result v0

    if-nez v0, :cond_0

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;->vYf()V

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/model/mE;->ex(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v0

    if-eqz v0, :cond_2

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/utils/DeviceUtils;->svN()I

    move-result v0

    const/4 v1, 0x1

    if-nez v0, :cond_1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iput-boolean v1, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->eV:Z

    :cond_1
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-boolean v2, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->eV:Z

    if-eqz v2, :cond_2

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->gci:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Ko;

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Ko;->ex(Z)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->mj:Lcom/bytedance/sdk/openadsdk/mSE/svN;

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/mSE/svN;->Fj(Z)V

    :cond_2
    return-void
.end method

.method public svN()V
    .locals 0

    return-void
.end method
