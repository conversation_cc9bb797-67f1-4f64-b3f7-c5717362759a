.class Lcom/bytedance/sdk/openadsdk/core/model/JW$1;
.super Lcom/bytedance/sdk/openadsdk/core/ex/ex;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/core/model/JW;-><init>(Landroid/app/Activity;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Ljava/lang/String;Landroid/widget/FrameLayout;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

.field final synthetic ex:Lcom/bytedance/sdk/openadsdk/core/model/JW;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/core/model/JW;Landroid/content/Context;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Ljava/lang/String;IZLcom/bytedance/sdk/openadsdk/core/model/Ql;)V
    .locals 6

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/JW$1;->ex:Lcom/bytedance/sdk/openadsdk/core/model/JW;

    iput-object p7, p0, Lcom/bytedance/sdk/openadsdk/core/model/JW$1;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    move-object v0, p0

    move-object v1, p2

    move-object v2, p3

    move-object v3, p4

    move v4, p5

    move v5, p6

    invoke-direct/range {v0 .. v5}, Lcom/bytedance/sdk/openadsdk/core/ex/ex;-><init>(Landroid/content/Context;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Ljava/lang/String;IZ)V

    return-void
.end method


# virtual methods
.method public Fj(Lcom/bytedance/sdk/openadsdk/core/model/mSE;Ljava/util/Map;)Z
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/bytedance/sdk/openadsdk/core/model/mSE;",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/Object;",
            ">;)Z"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/JW$1;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/model/JW;->WR(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v0

    if-eqz v0, :cond_1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/JW$1;->ex:Lcom/bytedance/sdk/openadsdk/core/model/JW;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/model/JW;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/JW;)Lcom/bytedance/sdk/openadsdk/core/widget/Fj/Ubf;

    move-result-object v0

    if-eqz v0, :cond_1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/JW$1;->ex:Lcom/bytedance/sdk/openadsdk/core/model/JW;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/model/JW;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/JW;)Lcom/bytedance/sdk/openadsdk/core/widget/Fj/Ubf;

    move-result-object v0

    invoke-virtual {v0, p1}, Lcom/bytedance/sdk/openadsdk/core/widget/Fj/Ubf;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/mSE;)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/JW$1;->ex:Lcom/bytedance/sdk/openadsdk/core/model/JW;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/model/JW;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/JW;)Lcom/bytedance/sdk/openadsdk/core/widget/Fj/Ubf;

    move-result-object v0

    invoke-virtual {v0, p2}, Lcom/bytedance/sdk/openadsdk/core/widget/Fj/Ubf;->Fj(Ljava/util/Map;)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/JW$1;->ex:Lcom/bytedance/sdk/openadsdk/core/model/JW;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/core/model/JW;->svN:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/model/JW;->Ubf(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v0

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/JW$1;->ex:Lcom/bytedance/sdk/openadsdk/core/model/JW;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/model/JW;->ex(Lcom/bytedance/sdk/openadsdk/core/model/JW;)Z

    move-result v0

    if-eqz v0, :cond_1

    :cond_0
    const/4 p1, 0x1

    return p1

    :cond_1
    invoke-super {p0, p1, p2}, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/mSE;Ljava/util/Map;)Z

    move-result p1

    return p1
.end method
