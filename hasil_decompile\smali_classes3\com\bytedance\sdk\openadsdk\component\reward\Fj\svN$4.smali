.class Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN$4;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/openadsdk/Ko/ex;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;->Fj(Lcom/bytedance/sdk/openadsdk/mSE/eV;Z)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN$4;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public getLogStats()Lcom/bytedance/sdk/openadsdk/Ko/Fj/ex;
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Exception;
        }
    .end annotation

    new-instance v0, Lorg/json/JSONObject;

    invoke-direct {v0}, Lorg/json/JSONObject;-><init>()V

    const-string v1, "PlayablePlugin_is_null"

    const/4 v2, 0x1

    invoke-virtual {v0, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;Z)Lorg/json/JSONObject;

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/Ko/Fj/hjc;->ex()Lcom/bytedance/sdk/openadsdk/Ko/Fj/hjc;

    move-result-object v1

    const-string v2, "PlayablePlugin_init"

    invoke-virtual {v1, v2}, Lcom/bytedance/sdk/openadsdk/Ko/Fj/hjc;->Fj(Ljava/lang/String;)Lcom/bytedance/sdk/openadsdk/Ko/Fj/hjc;

    move-result-object v1

    invoke-virtual {v0}, Lorg/json/JSONObject;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v1, v0}, Lcom/bytedance/sdk/openadsdk/Ko/Fj/hjc;->ex(Ljava/lang/String;)Lcom/bytedance/sdk/openadsdk/Ko/Fj/hjc;

    move-result-object v0

    return-object v0
.end method
