.class Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx$1;
.super Lcom/bytedance/sdk/component/svN/BcC;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;->WR(I)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Ljava/lang/String;

.field final synthetic ex:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;Ljava/lang/String;Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx$1;->ex:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;

    iput-object p3, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx$1;->Fj:Ljava/lang/String;

    invoke-direct {p0, p2}, Lcom/bytedance/sdk/component/svN/BcC;-><init>(Ljava/lang/String;)V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 4

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx$1;->ex:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;->Fj(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;)Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx$1;->ex:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;

    invoke-static {v1}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;->ex(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;)Ljava/lang/String;

    move-result-object v1

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx$1;->Fj:Ljava/lang/String;

    const/4 v3, 0x0

    invoke-static {v0, v1, v2, v3}, Lcom/bytedance/sdk/openadsdk/ex/hjc;->ex(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Ljava/lang/String;Ljava/lang/String;Lorg/json/JSONObject;)V

    return-void
.end method
