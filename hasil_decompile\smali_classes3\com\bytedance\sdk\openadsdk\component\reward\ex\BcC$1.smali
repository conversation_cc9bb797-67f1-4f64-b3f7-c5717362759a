.class Lcom/bytedance/sdk/openadsdk/component/reward/ex/BcC$1;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex$Fj;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/reward/ex/BcC;->eV()Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex$Fj;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/component/reward/ex/BcC;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/reward/ex/BcC;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/BcC$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/ex/BcC;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj(Z)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/BcC$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/ex/BcC;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/ex/BcC;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/ex/BcC;)Lcom/bytedance/sdk/openadsdk/component/reward/view/FullInteractionStyleView;

    move-result-object v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/BcC$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/ex/BcC;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/ex/BcC;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/ex/BcC;)Lcom/bytedance/sdk/openadsdk/component/reward/view/FullInteractionStyleView;

    move-result-object v0

    invoke-virtual {v0, p1}, Lcom/bytedance/sdk/openadsdk/component/reward/view/FullInteractionStyleView;->setIsMute(Z)V

    :cond_0
    return-void
.end method
