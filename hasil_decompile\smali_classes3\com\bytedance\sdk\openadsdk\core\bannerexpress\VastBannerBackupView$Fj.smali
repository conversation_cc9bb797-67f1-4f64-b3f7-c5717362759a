.class Lcom/bytedance/sdk/openadsdk/core/bannerexpress/VastBannerBackupView$Fj;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/sdk/openadsdk/core/bannerexpress/VastBannerBackupView;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "Fj"
.end annotation


# instance fields
.field public Fj:Landroid/widget/FrameLayout;

.field public Ubf:Lcom/bytedance/sdk/openadsdk/core/widget/PAGLogoView;

.field public WR:Lcom/bytedance/sdk/openadsdk/core/customview/PAGImageView;

.field public eV:Lcom/bytedance/sdk/openadsdk/core/widget/ShadowImageView;

.field public ex:Lcom/bytedance/sdk/openadsdk/core/widget/RatioFrameLayout;

.field public hjc:Lcom/bytedance/sdk/openadsdk/core/widget/ShadowImageView;


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Lcom/bytedance/sdk/openadsdk/core/bannerexpress/VastBannerBackupView$1;)V
    .locals 0

    invoke-direct {p0}, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/VastBannerBackupView$Fj;-><init>()V

    return-void
.end method
