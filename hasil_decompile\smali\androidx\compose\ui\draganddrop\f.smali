.class public interface abstract Landroidx/compose/ui/draganddrop/f;
.super Ljava/lang/Object;


# annotations
.annotation runtime L<PERSON>lin/Metadata;
.end annotation


# virtual methods
.method public abstract G(Landroidx/compose/ui/draganddrop/b;)V
.end method

.method public abstract H(Landroidx/compose/ui/draganddrop/b;)V
.end method

.method public abstract Q(Landroidx/compose/ui/draganddrop/b;)V
.end method

.method public abstract d1(Landroidx/compose/ui/draganddrop/b;)V
.end method

.method public abstract h0(Landroidx/compose/ui/draganddrop/b;)V
.end method

.method public abstract s0(Landroidx/compose/ui/draganddrop/b;)Z
.end method
