.class public interface abstract Landroidx/compose/ui/graphics/g4;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/compose/ui/graphics/g4$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# static fields
.field public static final a:Landroidx/compose/ui/graphics/g4$a;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    sget-object v0, Landroidx/compose/ui/graphics/g4$a;->a:Landroidx/compose/ui/graphics/g4$a;

    sput-object v0, Landroidx/compose/ui/graphics/g4;->a:Landroidx/compose/ui/graphics/g4$a;

    return-void
.end method


# virtual methods
.method public abstract a()V
.end method

.method public abstract b()I
.end method

.method public abstract getHeight()I
.end method

.method public abstract getWidth()I
.end method
