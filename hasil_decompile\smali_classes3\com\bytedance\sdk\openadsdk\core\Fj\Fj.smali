.class public Lcom/bytedance/sdk/openadsdk/core/Fj/Fj;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/openadsdk/core/Ql$Fj;


# instance fields
.field private final Fj:Lcom/bytedance/sdk/openadsdk/core/Ql$Fj;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/core/Ql$Fj;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/Fj/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/core/Ql$Fj;

    return-void
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/openadsdk/core/Fj/Fj;)Lcom/bytedance/sdk/openadsdk/core/Ql$Fj;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/openadsdk/core/Fj/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/core/Ql$Fj;

    return-object p0
.end method


# virtual methods
.method public Fj(ILjava/lang/String;)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/Fj/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/core/Ql$Fj;

    if-eqz v0, :cond_1

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/utils/uy;->Ubf()Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/Fj/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/core/Ql$Fj;

    invoke-interface {v0, p1, p2}, Lcom/bytedance/sdk/openadsdk/core/Ql$Fj;->Fj(ILjava/lang/String;)V

    return-void

    :cond_0
    new-instance v0, Lcom/bytedance/sdk/openadsdk/core/Fj/Fj$1;

    invoke-direct {v0, p0, p1, p2}, Lcom/bytedance/sdk/openadsdk/core/Fj/Fj$1;-><init>(Lcom/bytedance/sdk/openadsdk/core/Fj/Fj;ILjava/lang/String;)V

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/utils/uy;->Fj(Ljava/lang/Runnable;)V

    :cond_1
    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/openadsdk/core/model/Fj;Lcom/bytedance/sdk/openadsdk/core/model/ex;)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/Fj/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/core/Ql$Fj;

    if-eqz v0, :cond_1

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/utils/uy;->Ubf()Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/Fj/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/core/Ql$Fj;

    invoke-interface {v0, p1, p2}, Lcom/bytedance/sdk/openadsdk/core/Ql$Fj;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Fj;Lcom/bytedance/sdk/openadsdk/core/model/ex;)V

    return-void

    :cond_0
    new-instance v0, Lcom/bytedance/sdk/openadsdk/core/Fj/Fj$2;

    invoke-direct {v0, p0, p1, p2}, Lcom/bytedance/sdk/openadsdk/core/Fj/Fj$2;-><init>(Lcom/bytedance/sdk/openadsdk/core/Fj/Fj;Lcom/bytedance/sdk/openadsdk/core/model/Fj;Lcom/bytedance/sdk/openadsdk/core/model/ex;)V

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/utils/uy;->Fj(Ljava/lang/Runnable;)V

    :cond_1
    return-void
.end method
