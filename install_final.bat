@echo off
echo ========================================
echo   MOVIEBOX MEHHO MODDER - INSTALLER
echo ========================================
echo.
echo APK yang sudah siap install:
echo - MovieBox_MEHHO_MODDER_signed.apk
echo.
echo CARA INSTALL KE EMULATOR:
echo.
echo 1. DRAG & DROP (TERMUDAH):
echo    - Drag file "MovieBox_MEHHO_MODDER_signed.apk"
echo    - Drop ke window emulator VSCode
echo    - Tunggu instalasi selesai
echo.
echo 2. COPY KE EMULATOR:
echo    - Copy APK ke Downloads folder emulator
echo    - Buka File Manager di emulator
echo    - Tap APK untuk install
echo.
echo 3. JIKA MASIH ERROR:
echo    - Pastikan "Unknown Sources" diaktifkan
echo    - Coba uninstall aplikasi lama dulu
echo    - Restart emulator
echo.
echo PERUBAHAN YANG SUDAH DIBUAT:
echo - Nama modder: MEHHO MODDER
echo - Tombol Facebook: https://www.facebook.com/JVGTEAM
echo - Tombol Donasi: https://pesayangan.netlify.app/
echo.
echo ========================================
pause