.class public Lcom/bytedance/sdk/openadsdk/component/reward/ex/eV;
.super Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;)V
    .locals 0

    invoke-direct {p0, p1}, Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;)V

    return-void
.end method


# virtual methods
.method public Fj(Landroid/widget/FrameLayout;)V
    .locals 0

    return-void
.end method

.method public Ubf()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public WR()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public svN()V
    .locals 0

    return-void
.end method
