.class public final Lcom/transsion/ninegridview/NineGridViewNew;
.super Landroid/view/ViewGroup;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/transsion/ninegridview/NineGridViewNew$a;
    }
.end annotation

.annotation runtime L<PERSON>lin/Metadata;
.end annotation


# static fields
.field public static final Companion:Lcom/transsion/ninegridview/NineGridViewNew$a;

.field public static u:Leo/b;


# instance fields
.field public final a:I

.field public final b:I

.field public c:I

.field public d:F

.field public e:I

.field public f:I

.field public g:I

.field public h:I

.field public i:I

.field public j:I

.field public k:I

.field public l:I

.field public m:I

.field public n:I

.field public o:I

.field public p:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Landroid/widget/ImageView;",
            ">;"
        }
    .end annotation
.end field

.field public q:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "+",
            "Lcom/transsion/ninegridview/ImageInfo;",
            ">;"
        }
    .end annotation
.end field

.field public r:Lcom/transsion/ninegridview/NineGridViewAdapter;

.field public final s:F

.field public final t:F


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lcom/transsion/ninegridview/NineGridViewNew$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lcom/transsion/ninegridview/NineGridViewNew$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lcom/transsion/ninegridview/NineGridViewNew;->Companion:Lcom/transsion/ninegridview/NineGridViewNew$a;

    new-instance v0, Lcom/transsion/ninegridview/c;

    invoke-direct {v0}, Lcom/transsion/ninegridview/c;-><init>()V

    sput-object v0, Lcom/transsion/ninegridview/NineGridViewNew;->u:Leo/b;

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 1

    const-string v0, "context"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    const/4 v0, 0x0

    invoke-direct {p0, p1, v0}, Lcom/transsion/ninegridview/NineGridViewNew;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 1

    const-string v0, "context"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    const/4 v0, 0x0

    invoke-direct {p0, p1, p2, v0}, Lcom/transsion/ninegridview/NineGridViewNew;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 2

    const-string v0, "context"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-direct {p0, p1, p2, p3}, Landroid/view/ViewGroup;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    const/4 p3, 0x1

    iput p3, p0, Lcom/transsion/ninegridview/NineGridViewNew;->b:I

    const/16 v0, 0xfa

    iput v0, p0, Lcom/transsion/ninegridview/NineGridViewNew;->c:I

    const/high16 v0, 0x3f800000    # 1.0f

    iput v0, p0, Lcom/transsion/ninegridview/NineGridViewNew;->d:F

    const/16 v0, 0x9

    iput v0, p0, Lcom/transsion/ninegridview/NineGridViewNew;->e:I

    const/4 v0, 0x2

    iput v0, p0, Lcom/transsion/ninegridview/NineGridViewNew;->f:I

    iget v0, p0, Lcom/transsion/ninegridview/NineGridViewNew;->a:I

    iput v0, p0, Lcom/transsion/ninegridview/NineGridViewNew;->g:I

    const v0, 0x40071c72

    iput v0, p0, Lcom/transsion/ninegridview/NineGridViewNew;->s:F

    const/high16 v0, 0x3f400000    # 0.75f

    iput v0, p0, Lcom/transsion/ninegridview/NineGridViewNew;->t:F

    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    invoke-virtual {v0}, Landroid/content/res/Resources;->getDisplayMetrics()Landroid/util/DisplayMetrics;

    move-result-object v0

    iget v1, p0, Lcom/transsion/ninegridview/NineGridViewNew;->f:I

    int-to-float v1, v1

    invoke-static {p3, v1, v0}, Landroid/util/TypedValue;->applyDimension(IFLandroid/util/DisplayMetrics;)F

    move-result v1

    float-to-int v1, v1

    iput v1, p0, Lcom/transsion/ninegridview/NineGridViewNew;->f:I

    iget v1, p0, Lcom/transsion/ninegridview/NineGridViewNew;->c:I

    int-to-float v1, v1

    invoke-static {p3, v1, v0}, Landroid/util/TypedValue;->applyDimension(IFLandroid/util/DisplayMetrics;)F

    move-result p3

    float-to-int p3, p3

    iput p3, p0, Lcom/transsion/ninegridview/NineGridViewNew;->c:I

    sget-object p3, Lcom/transsion/ninegridview/R$styleable;->NineGridView:[I

    invoke-virtual {p1, p2, p3}, Landroid/content/Context;->obtainStyledAttributes(Landroid/util/AttributeSet;[I)Landroid/content/res/TypedArray;

    move-result-object p1

    const-string p2, "context.obtainStyledAttr\u2026R.styleable.NineGridView)"

    invoke-static {p1, p2}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    sget p2, Lcom/transsion/ninegridview/R$styleable;->NineGridView_ngv_gridSpacing:I

    iget p3, p0, Lcom/transsion/ninegridview/NineGridViewNew;->f:I

    int-to-float p3, p3

    invoke-virtual {p1, p2, p3}, Landroid/content/res/TypedArray;->getDimension(IF)F

    move-result p2

    float-to-int p2, p2

    iput p2, p0, Lcom/transsion/ninegridview/NineGridViewNew;->f:I

    sget p2, Lcom/transsion/ninegridview/R$styleable;->NineGridView_ngv_singleImageSize:I

    iget p3, p0, Lcom/transsion/ninegridview/NineGridViewNew;->c:I

    invoke-virtual {p1, p2, p3}, Landroid/content/res/TypedArray;->getDimensionPixelSize(II)I

    move-result p2

    iput p2, p0, Lcom/transsion/ninegridview/NineGridViewNew;->c:I

    sget p2, Lcom/transsion/ninegridview/R$styleable;->NineGridView_ngv_singleImageRatio:I

    iget p3, p0, Lcom/transsion/ninegridview/NineGridViewNew;->d:F

    invoke-virtual {p1, p2, p3}, Landroid/content/res/TypedArray;->getFloat(IF)F

    move-result p2

    iput p2, p0, Lcom/transsion/ninegridview/NineGridViewNew;->d:F

    sget p2, Lcom/transsion/ninegridview/R$styleable;->NineGridView_ngv_maxSize:I

    iget p3, p0, Lcom/transsion/ninegridview/NineGridViewNew;->e:I

    invoke-virtual {p1, p2, p3}, Landroid/content/res/TypedArray;->getInt(II)I

    move-result p2

    iput p2, p0, Lcom/transsion/ninegridview/NineGridViewNew;->e:I

    sget p2, Lcom/transsion/ninegridview/R$styleable;->NineGridView_ngv_mode:I

    iget p3, p0, Lcom/transsion/ninegridview/NineGridViewNew;->g:I

    invoke-virtual {p1, p2, p3}, Landroid/content/res/TypedArray;->getInt(II)I

    move-result p2

    iput p2, p0, Lcom/transsion/ninegridview/NineGridViewNew;->g:I

    invoke-virtual {p1}, Landroid/content/res/TypedArray;->recycle()V

    new-instance p1, Ljava/util/ArrayList;

    invoke-direct {p1}, Ljava/util/ArrayList;-><init>()V

    iput-object p1, p0, Lcom/transsion/ninegridview/NineGridViewNew;->p:Ljava/util/List;

    return-void
.end method

.method public static synthetic a(Lcom/transsion/ninegridview/NineGridViewNew;ILandroid/view/View;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lcom/transsion/ninegridview/NineGridViewNew;->h(Lcom/transsion/ninegridview/NineGridViewNew;ILandroid/view/View;)V

    return-void
.end method

.method public static final synthetic access$getMImageLoader$cp()Leo/b;
    .locals 1

    sget-object v0, Lcom/transsion/ninegridview/NineGridViewNew;->u:Leo/b;

    return-object v0
.end method

.method public static final synthetic access$setMImageLoader$cp(Leo/b;)V
    .locals 0

    sput-object p0, Lcom/transsion/ninegridview/NineGridViewNew;->u:Leo/b;

    return-void
.end method

.method public static final h(Lcom/transsion/ninegridview/NineGridViewNew;ILandroid/view/View;)V
    .locals 2

    const-string p2, "this$0"

    invoke-static {p0, p2}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    iget-object p2, p0, Lcom/transsion/ninegridview/NineGridViewNew;->r:Lcom/transsion/ninegridview/NineGridViewAdapter;

    invoke-static {p2}, Lkotlin/jvm/internal/Intrinsics;->d(Ljava/lang/Object;)V

    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    iget-object v1, p0, Lcom/transsion/ninegridview/NineGridViewNew;->r:Lcom/transsion/ninegridview/NineGridViewAdapter;

    invoke-static {v1}, Lkotlin/jvm/internal/Intrinsics;->d(Ljava/lang/Object;)V

    invoke-virtual {v1}, Lcom/transsion/ninegridview/NineGridViewAdapter;->getImageInfo()Ljava/util/List;

    move-result-object v1

    invoke-virtual {p2, v0, p0, p1, v1}, Lcom/transsion/ninegridview/NineGridViewAdapter;->onImageItemClick(Landroid/content/Context;Lcom/transsion/ninegridview/NineGridViewNew;ILjava/util/List;)V

    return-void
.end method


# virtual methods
.method public final b(Lcom/transsion/ninegridview/ImageInfo;II)V
    .locals 2

    iget v0, p1, Lcom/transsion/ninegridview/ImageInfo;->imageViewWidth:F

    const/4 v1, 0x0

    cmpl-float v0, v0, v1

    if-lez v0, :cond_2

    iget v0, p1, Lcom/transsion/ninegridview/ImageInfo;->imageViewHeight:F

    cmpl-float v0, v0, v1

    if-lez v0, :cond_2

    invoke-virtual {p0, p2, p3}, Lcom/transsion/ninegridview/NineGridViewNew;->e(II)I

    move-result p2

    iput p2, p0, Lcom/transsion/ninegridview/NineGridViewNew;->j:I

    iget p3, p1, Lcom/transsion/ninegridview/ImageInfo;->imageViewWidth:F

    iget p1, p1, Lcom/transsion/ninegridview/ImageInfo;->imageViewHeight:F

    div-float v0, p3, p1

    iget v1, p0, Lcom/transsion/ninegridview/NineGridViewNew;->s:F

    cmpl-float v0, v0, v1

    if-lez v0, :cond_0

    :goto_0
    int-to-float p1, p2

    div-float/2addr p1, v1

    float-to-int p1, p1

    goto :goto_1

    :cond_0
    div-float v0, p3, p1

    iget v1, p0, Lcom/transsion/ninegridview/NineGridViewNew;->t:F

    cmpg-float v0, v0, v1

    if-gez v0, :cond_1

    goto :goto_0

    :cond_1
    int-to-float p2, p2

    const/high16 v0, 0x3f800000    # 1.0f

    mul-float p2, p2, v0

    div-float/2addr p2, p3

    mul-float p2, p2, p1

    float-to-int p1, p2

    :goto_1
    iput p1, p0, Lcom/transsion/ninegridview/NineGridViewNew;->k:I

    const/4 p1, 0x0

    iput p1, p0, Lcom/transsion/ninegridview/NineGridViewNew;->m:I

    iput p1, p0, Lcom/transsion/ninegridview/NineGridViewNew;->o:I

    :cond_2
    return-void
.end method

.method public final c(Lcom/transsion/ninegridview/ImageInfo;II)V
    .locals 2

    iget v0, p1, Lcom/transsion/ninegridview/ImageInfo;->imageViewWidth:F

    const/4 v1, 0x0

    cmpl-float v0, v0, v1

    if-lez v0, :cond_2

    iget v0, p1, Lcom/transsion/ninegridview/ImageInfo;->imageViewHeight:F

    cmpl-float v0, v0, v1

    if-lez v0, :cond_2

    invoke-virtual {p0, p2, p3}, Lcom/transsion/ninegridview/NineGridViewNew;->e(II)I

    move-result p2

    iput p2, p0, Lcom/transsion/ninegridview/NineGridViewNew;->l:I

    iget p3, p1, Lcom/transsion/ninegridview/ImageInfo;->imageViewWidth:F

    iget p1, p1, Lcom/transsion/ninegridview/ImageInfo;->imageViewHeight:F

    div-float v0, p3, p1

    iget v1, p0, Lcom/transsion/ninegridview/NineGridViewNew;->s:F

    cmpl-float v0, v0, v1

    if-lez v0, :cond_0

    :goto_0
    int-to-float p1, p2

    div-float/2addr p1, v1

    float-to-int p1, p1

    goto :goto_1

    :cond_0
    div-float v0, p3, p1

    iget v1, p0, Lcom/transsion/ninegridview/NineGridViewNew;->t:F

    cmpg-float v0, v0, v1

    if-gez v0, :cond_1

    goto :goto_0

    :cond_1
    int-to-float p2, p2

    const/high16 v0, 0x3f800000    # 1.0f

    mul-float p2, p2, v0

    div-float/2addr p2, p3

    mul-float p2, p2, p1

    float-to-int p1, p2

    :goto_1
    iput p1, p0, Lcom/transsion/ninegridview/NineGridViewNew;->m:I

    const/4 p1, 0x0

    iput p1, p0, Lcom/transsion/ninegridview/NineGridViewNew;->o:I

    :cond_2
    return-void
.end method

.method public final d(Lcom/transsion/ninegridview/ImageInfo;II)V
    .locals 2

    iget v0, p1, Lcom/transsion/ninegridview/ImageInfo;->imageViewWidth:F

    const/4 v1, 0x0

    cmpl-float v0, v0, v1

    if-lez v0, :cond_2

    iget v0, p1, Lcom/transsion/ninegridview/ImageInfo;->imageViewHeight:F

    cmpl-float v0, v0, v1

    if-lez v0, :cond_2

    invoke-virtual {p0, p2, p3}, Lcom/transsion/ninegridview/NineGridViewNew;->e(II)I

    move-result p2

    iput p2, p0, Lcom/transsion/ninegridview/NineGridViewNew;->n:I

    iget p3, p1, Lcom/transsion/ninegridview/ImageInfo;->imageViewWidth:F

    iget p1, p1, Lcom/transsion/ninegridview/ImageInfo;->imageViewHeight:F

    div-float v0, p3, p1

    iget v1, p0, Lcom/transsion/ninegridview/NineGridViewNew;->s:F

    cmpl-float v0, v0, v1

    if-lez v0, :cond_0

    :goto_0
    int-to-float p1, p2

    div-float/2addr p1, v1

    float-to-int p1, p1

    goto :goto_1

    :cond_0
    div-float v0, p3, p1

    iget v1, p0, Lcom/transsion/ninegridview/NineGridViewNew;->t:F

    cmpg-float v0, v0, v1

    if-gez v0, :cond_1

    goto :goto_0

    :cond_1
    int-to-float p2, p2

    const/high16 v0, 0x3f800000    # 1.0f

    mul-float p2, p2, v0

    div-float/2addr p2, p3

    mul-float p2, p2, p1

    float-to-int p1, p2

    :goto_1
    iput p1, p0, Lcom/transsion/ninegridview/NineGridViewNew;->o:I

    :cond_2
    return-void
.end method

.method public final e(II)I
    .locals 2

    iget v0, p0, Lcom/transsion/ninegridview/NineGridViewNew;->f:I

    add-int/lit8 v1, p2, -0x1

    mul-int v0, v0, v1

    sub-int/2addr p1, v0

    div-int/2addr p1, p2

    return p1
.end method

.method public final f(I)I
    .locals 4

    iget-object v0, p0, Lcom/transsion/ninegridview/NineGridViewNew;->q:Ljava/util/List;

    const/4 v1, 0x0

    if-eqz v0, :cond_4

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    const/4 v2, 0x1

    const/4 v3, 0x3

    packed-switch v0, :pswitch_data_0

    goto :goto_4

    :pswitch_0
    const/4 v0, 0x5

    if-ge p1, v0, :cond_0

    rem-int/2addr p1, v3

    goto :goto_0

    :cond_0
    add-int/lit8 p1, p1, -0x2

    rem-int/2addr p1, v3

    :goto_0
    return p1

    :pswitch_1
    const/4 v0, 0x4

    if-ge p1, v0, :cond_1

    rem-int/2addr p1, v3

    goto :goto_1

    :cond_1
    sub-int/2addr p1, v2

    rem-int/2addr p1, v3

    :goto_1
    return p1

    :pswitch_2
    if-ge p1, v3, :cond_2

    rem-int/lit8 p1, p1, 0x2

    goto :goto_2

    :cond_2
    sub-int/2addr p1, v2

    rem-int/lit8 p1, p1, 0x2

    :goto_2
    return p1

    :pswitch_3
    rem-int/lit8 p1, p1, 0x2

    if-nez p1, :cond_3

    goto :goto_3

    :cond_3
    const/4 v1, 0x1

    :goto_3
    return v1

    :pswitch_4
    rem-int/2addr p1, v3

    return p1

    :cond_4
    :goto_4
    return v1

    nop

    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_4
        :pswitch_4
        :pswitch_3
        :pswitch_3
        :pswitch_2
        :pswitch_4
        :pswitch_1
        :pswitch_0
        :pswitch_4
    .end packed-switch
.end method

.method public final g(I)Landroid/widget/ImageView;
    .locals 2

    iget-object v0, p0, Lcom/transsion/ninegridview/NineGridViewNew;->p:Ljava/util/List;

    invoke-static {v0}, Lkotlin/jvm/internal/Intrinsics;->d(Ljava/lang/Object;)V

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    if-ge p1, v0, :cond_0

    iget-object v0, p0, Lcom/transsion/ninegridview/NineGridViewNew;->p:Ljava/util/List;

    invoke-static {v0}, Lkotlin/jvm/internal/Intrinsics;->d(Ljava/lang/Object;)V

    invoke-interface {v0, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Landroid/widget/ImageView;

    goto :goto_0

    :cond_0
    iget-object v0, p0, Lcom/transsion/ninegridview/NineGridViewNew;->r:Lcom/transsion/ninegridview/NineGridViewAdapter;

    invoke-static {v0}, Lkotlin/jvm/internal/Intrinsics;->d(Ljava/lang/Object;)V

    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/transsion/ninegridview/NineGridViewAdapter;->generateImageView(Landroid/content/Context;)Landroid/widget/ImageView;

    move-result-object v0

    const-string v1, "mAdapter!!.generateImageView(context)"

    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    new-instance v1, Lcom/transsion/ninegridview/b;

    invoke-direct {v1, p0, p1}, Lcom/transsion/ninegridview/b;-><init>(Lcom/transsion/ninegridview/NineGridViewNew;I)V

    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    iget-object p1, p0, Lcom/transsion/ninegridview/NineGridViewNew;->p:Ljava/util/List;

    if-eqz p1, :cond_1

    invoke-interface {p1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :cond_1
    move-object p1, v0

    :goto_0
    return-object p1
.end method

.method public final getMODE_FILL()I
    .locals 1

    iget v0, p0, Lcom/transsion/ninegridview/NineGridViewNew;->a:I

    return v0
.end method

.method public final getMODE_GRID()I
    .locals 1

    iget v0, p0, Lcom/transsion/ninegridview/NineGridViewNew;->b:I

    return v0
.end method

.method public final getMaxSize()I
    .locals 1

    iget v0, p0, Lcom/transsion/ninegridview/NineGridViewNew;->e:I

    return v0
.end method

.method public final i(I)I
    .locals 4

    iget-object v0, p0, Lcom/transsion/ninegridview/NineGridViewNew;->q:Ljava/util/List;

    if-eqz v0, :cond_a

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    const/4 v1, 0x1

    const/4 v2, 0x2

    const/4 v3, 0x3

    packed-switch v0, :pswitch_data_0

    goto :goto_6

    :pswitch_0
    if-ge p1, v3, :cond_0

    goto :goto_0

    :cond_0
    if-gt v3, p1, :cond_1

    const/4 v0, 0x6

    if-ge p1, v0, :cond_1

    const/4 v1, 0x2

    goto :goto_0

    :cond_1
    const/4 v1, 0x3

    :goto_0
    return v1

    :pswitch_1
    if-ge p1, v3, :cond_2

    goto :goto_1

    :cond_2
    if-gt v3, p1, :cond_3

    const/4 v0, 0x5

    if-ge p1, v0, :cond_3

    const/4 v1, 0x2

    goto :goto_1

    :cond_3
    const/4 v1, 0x3

    :goto_1
    return v1

    :pswitch_2
    if-ge p1, v3, :cond_4

    goto :goto_2

    :cond_4
    if-ne p1, v3, :cond_5

    const/4 v1, 0x2

    goto :goto_2

    :cond_5
    const/4 v1, 0x3

    :goto_2
    return v1

    :pswitch_3
    if-ge p1, v3, :cond_6

    goto :goto_3

    :cond_6
    const/4 v1, 0x2

    :goto_3
    return v1

    :pswitch_4
    if-ge p1, v2, :cond_7

    goto :goto_4

    :cond_7
    if-ne p1, v2, :cond_8

    const/4 v1, 0x2

    goto :goto_4

    :cond_8
    const/4 v1, 0x3

    :goto_4
    return v1

    :pswitch_5
    if-ge p1, v2, :cond_9

    goto :goto_5

    :cond_9
    const/4 v1, 0x2

    :goto_5
    :pswitch_6
    return v1

    :cond_a
    :goto_6
    const/4 p1, 0x0

    return p1

    nop

    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_6
        :pswitch_6
        :pswitch_5
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method

.method public final j(Lcom/transsion/ninegridview/ImageInfo;Lcom/transsion/ninegridview/ImageInfo;)Lcom/transsion/ninegridview/ImageInfo;
    .locals 2

    iget v0, p1, Lcom/transsion/ninegridview/ImageInfo;->imageViewHeight:F

    iget v1, p2, Lcom/transsion/ninegridview/ImageInfo;->imageViewHeight:F

    cmpg-float v0, v0, v1

    if-gtz v0, :cond_0

    return-object p1

    :cond_0
    return-object p2
.end method

.method public final k(Lcom/transsion/ninegridview/ImageInfo;Lcom/transsion/ninegridview/ImageInfo;Lcom/transsion/ninegridview/ImageInfo;)Lcom/transsion/ninegridview/ImageInfo;
    .locals 0

    invoke-virtual {p0, p1, p2}, Lcom/transsion/ninegridview/NineGridViewNew;->j(Lcom/transsion/ninegridview/ImageInfo;Lcom/transsion/ninegridview/ImageInfo;)Lcom/transsion/ninegridview/ImageInfo;

    move-result-object p1

    invoke-virtual {p0, p1, p3}, Lcom/transsion/ninegridview/NineGridViewNew;->j(Lcom/transsion/ninegridview/ImageInfo;Lcom/transsion/ninegridview/ImageInfo;)Lcom/transsion/ninegridview/ImageInfo;

    move-result-object p1

    return-object p1
.end method

.method public onDetachedFromWindow()V
    .locals 2

    invoke-super {p0}, Landroid/view/ViewGroup;->onDetachedFromWindow()V

    iget-object v0, p0, Lcom/transsion/ninegridview/NineGridViewNew;->q:Ljava/util/List;

    if-nez v0, :cond_0

    return-void

    :cond_0
    invoke-static {v0}, Lkotlin/jvm/internal/Intrinsics;->d(Ljava/lang/Object;)V

    const/4 v1, 0x0

    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/transsion/ninegridview/ImageInfo;

    iput v1, v0, Lcom/transsion/ninegridview/ImageInfo;->alreadyShow:I

    return-void
.end method

.method public onLayout(ZIIII)V
    .locals 7

    iget-object p1, p0, Lcom/transsion/ninegridview/NineGridViewNew;->q:Ljava/util/List;

    if-nez p1, :cond_0

    return-void

    :cond_0
    const/4 p2, 0x0

    if-eqz p1, :cond_1

    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result p1

    goto :goto_0

    :cond_1
    const/4 p1, 0x0

    :goto_0
    iget-object p3, p0, Lcom/transsion/ninegridview/NineGridViewNew;->q:Ljava/util/List;

    invoke-static {p3}, Lkotlin/jvm/internal/Intrinsics;->d(Ljava/lang/Object;)V

    invoke-interface {p3, p2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p3

    check-cast p3, Lcom/transsion/ninegridview/ImageInfo;

    iget p3, p3, Lcom/transsion/ninegridview/ImageInfo;->alreadyShow:I

    const/4 p4, 0x1

    if-ne p3, p4, :cond_2

    return-void

    :cond_2
    iget-object p3, p0, Lcom/transsion/ninegridview/NineGridViewNew;->q:Ljava/util/List;

    invoke-static {p3}, Lkotlin/jvm/internal/Intrinsics;->d(Ljava/lang/Object;)V

    invoke-interface {p3, p2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p3

    check-cast p3, Lcom/transsion/ninegridview/ImageInfo;

    iput p4, p3, Lcom/transsion/ninegridview/ImageInfo;->alreadyShow:I

    :goto_1
    if-ge p2, p1, :cond_d

    invoke-virtual {p0, p2}, Landroid/view/ViewGroup;->getChildAt(I)Landroid/view/View;

    move-result-object p3

    const-string p5, "null cannot be cast to non-null type android.widget.ImageView"

    invoke-static {p3, p5}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/String;)V

    move-object v2, p3

    check-cast v2, Landroid/widget/ImageView;

    invoke-virtual {p0, p2}, Lcom/transsion/ninegridview/NineGridViewNew;->i(I)I

    move-result p3

    invoke-virtual {p0, p2}, Lcom/transsion/ninegridview/NineGridViewNew;->f(I)I

    move-result p5

    iget-object v0, p0, Lcom/transsion/ninegridview/NineGridViewNew;->q:Ljava/util/List;

    const/4 v1, 0x0

    if-eqz v0, :cond_3

    invoke-interface {v0, p2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/transsion/ninegridview/ImageInfo;

    goto :goto_2

    :cond_3
    move-object v0, v1

    :goto_2
    if-eq p3, p4, :cond_9

    const/4 v3, 0x2

    if-eq p3, v3, :cond_6

    iget v3, p0, Lcom/transsion/ninegridview/NineGridViewNew;->n:I

    iget v4, p0, Lcom/transsion/ninegridview/NineGridViewNew;->f:I

    add-int/2addr v3, v4

    mul-int v3, v3, p5

    invoke-virtual {p0}, Landroid/view/View;->getPaddingLeft()I

    move-result p5

    add-int/2addr v3, p5

    iget p5, p0, Lcom/transsion/ninegridview/NineGridViewNew;->f:I

    add-int/lit8 p3, p3, -0x1

    mul-int p5, p5, p3

    iget p3, p0, Lcom/transsion/ninegridview/NineGridViewNew;->k:I

    add-int/2addr p5, p3

    iget p3, p0, Lcom/transsion/ninegridview/NineGridViewNew;->m:I

    add-int/2addr p5, p3

    invoke-virtual {p0}, Landroid/view/View;->getPaddingTop()I

    move-result p3

    add-int/2addr p5, p3

    iget p3, p0, Lcom/transsion/ninegridview/NineGridViewNew;->n:I

    add-int/2addr p3, v3

    iget v4, p0, Lcom/transsion/ninegridview/NineGridViewNew;->o:I

    add-int/2addr v4, p5

    invoke-virtual {v2, v3, p5, p3, v4}, Landroid/view/View;->layout(IIII)V

    sget-object p3, Lcom/transsion/ninegridview/NineGridViewNew;->u:Leo/b;

    if-eqz p3, :cond_c

    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object p5

    if-eqz v0, :cond_4

    iget-object v3, v0, Lcom/transsion/ninegridview/ImageInfo;->bigImageUrl:Ljava/lang/String;

    goto :goto_3

    :cond_4
    move-object v3, v1

    :goto_3
    iget v4, p0, Lcom/transsion/ninegridview/NineGridViewNew;->n:I

    iget v5, p0, Lcom/transsion/ninegridview/NineGridViewNew;->o:I

    if-eqz v0, :cond_5

    iget-object v0, v0, Lcom/transsion/ninegridview/ImageInfo;->thumbnailUrl:Ljava/lang/String;

    move-object v6, v0

    goto :goto_4

    :cond_5
    move-object v6, v1

    :goto_4
    move-object v0, p3

    move-object v1, p5

    invoke-interface/range {v0 .. v6}, Leo/b;->a(Landroid/content/Context;Landroid/widget/ImageView;Ljava/lang/String;IILjava/lang/String;)V

    goto/16 :goto_9

    :cond_6
    iget v3, p0, Lcom/transsion/ninegridview/NineGridViewNew;->l:I

    iget v4, p0, Lcom/transsion/ninegridview/NineGridViewNew;->f:I

    add-int/2addr v3, v4

    mul-int v3, v3, p5

    invoke-virtual {p0}, Landroid/view/View;->getPaddingLeft()I

    move-result p5

    add-int/2addr v3, p5

    iget p5, p0, Lcom/transsion/ninegridview/NineGridViewNew;->f:I

    add-int/lit8 p3, p3, -0x1

    mul-int p5, p5, p3

    iget p3, p0, Lcom/transsion/ninegridview/NineGridViewNew;->k:I

    add-int/2addr p5, p3

    invoke-virtual {p0}, Landroid/view/View;->getPaddingTop()I

    move-result p3

    add-int/2addr p5, p3

    iget p3, p0, Lcom/transsion/ninegridview/NineGridViewNew;->l:I

    add-int/2addr p3, v3

    iget v4, p0, Lcom/transsion/ninegridview/NineGridViewNew;->m:I

    add-int/2addr v4, p5

    invoke-virtual {v2, v3, p5, p3, v4}, Landroid/view/View;->layout(IIII)V

    sget-object p3, Lcom/transsion/ninegridview/NineGridViewNew;->u:Leo/b;

    if-eqz p3, :cond_c

    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object p5

    if-eqz v0, :cond_7

    iget-object v3, v0, Lcom/transsion/ninegridview/ImageInfo;->bigImageUrl:Ljava/lang/String;

    goto :goto_5

    :cond_7
    move-object v3, v1

    :goto_5
    iget v4, p0, Lcom/transsion/ninegridview/NineGridViewNew;->l:I

    iget v5, p0, Lcom/transsion/ninegridview/NineGridViewNew;->m:I

    if-eqz v0, :cond_8

    iget-object v0, v0, Lcom/transsion/ninegridview/ImageInfo;->thumbnailUrl:Ljava/lang/String;

    move-object v6, v0

    goto :goto_6

    :cond_8
    move-object v6, v1

    :goto_6
    move-object v0, p3

    move-object v1, p5

    invoke-interface/range {v0 .. v6}, Leo/b;->a(Landroid/content/Context;Landroid/widget/ImageView;Ljava/lang/String;IILjava/lang/String;)V

    goto :goto_9

    :cond_9
    iget p3, p0, Lcom/transsion/ninegridview/NineGridViewNew;->j:I

    iget v3, p0, Lcom/transsion/ninegridview/NineGridViewNew;->f:I

    add-int/2addr p3, v3

    mul-int p3, p3, p5

    invoke-virtual {p0}, Landroid/view/View;->getPaddingLeft()I

    move-result p5

    add-int/2addr p3, p5

    invoke-virtual {p0}, Landroid/view/View;->getPaddingTop()I

    move-result p5

    iget v3, p0, Lcom/transsion/ninegridview/NineGridViewNew;->j:I

    add-int/2addr v3, p3

    iget v4, p0, Lcom/transsion/ninegridview/NineGridViewNew;->k:I

    add-int/2addr v4, p5

    invoke-virtual {v2, p3, p5, v3, v4}, Landroid/view/View;->layout(IIII)V

    sget-object p3, Lcom/transsion/ninegridview/NineGridViewNew;->u:Leo/b;

    if-eqz p3, :cond_c

    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object p5

    if-eqz v0, :cond_a

    iget-object v3, v0, Lcom/transsion/ninegridview/ImageInfo;->bigImageUrl:Ljava/lang/String;

    goto :goto_7

    :cond_a
    move-object v3, v1

    :goto_7
    iget v4, p0, Lcom/transsion/ninegridview/NineGridViewNew;->j:I

    iget v5, p0, Lcom/transsion/ninegridview/NineGridViewNew;->k:I

    if-eqz v0, :cond_b

    iget-object v0, v0, Lcom/transsion/ninegridview/ImageInfo;->thumbnailUrl:Ljava/lang/String;

    move-object v6, v0

    goto :goto_8

    :cond_b
    move-object v6, v1

    :goto_8
    move-object v0, p3

    move-object v1, p5

    invoke-interface/range {v0 .. v6}, Leo/b;->a(Landroid/content/Context;Landroid/widget/ImageView;Ljava/lang/String;IILjava/lang/String;)V

    :cond_c
    :goto_9
    add-int/lit8 p2, p2, 0x1

    goto/16 :goto_1

    :cond_d
    return-void
.end method

.method public onMeasure(II)V
    .locals 10

    invoke-super {p0, p1, p2}, Landroid/view/ViewGroup;->onMeasure(II)V

    invoke-static {p1}, Landroid/view/View$MeasureSpec;->getSize(I)I

    move-result p1

    invoke-virtual {p0}, Landroid/view/View;->getPaddingLeft()I

    move-result p2

    sub-int p2, p1, p2

    invoke-virtual {p0}, Landroid/view/View;->getPaddingRight()I

    move-result v0

    sub-int/2addr p2, v0

    iget-object v0, p0, Lcom/transsion/ninegridview/NineGridViewNew;->q:Ljava/util/List;

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    move-object v2, v0

    check-cast v2, Ljava/util/Collection;

    invoke-interface {v2}, Ljava/util/Collection;->isEmpty()Z

    move-result v2

    const/4 v3, 0x1

    xor-int/2addr v2, v3

    if-eqz v2, :cond_0

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result p1

    const/4 v2, 0x7

    const/4 v4, 0x6

    const/4 v5, 0x5

    const/4 v6, 0x4

    const/4 v7, 0x2

    const/4 v8, 0x3

    packed-switch p1, :pswitch_data_0

    goto/16 :goto_0

    :pswitch_0
    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/transsion/ninegridview/ImageInfo;

    invoke-interface {v0, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/transsion/ninegridview/ImageInfo;

    invoke-interface {v0, v7}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v7

    check-cast v7, Lcom/transsion/ninegridview/ImageInfo;

    invoke-virtual {p0, p1, v1, v7}, Lcom/transsion/ninegridview/NineGridViewNew;->k(Lcom/transsion/ninegridview/ImageInfo;Lcom/transsion/ninegridview/ImageInfo;Lcom/transsion/ninegridview/ImageInfo;)Lcom/transsion/ninegridview/ImageInfo;

    move-result-object p1

    invoke-virtual {p0, p1, p2, v8}, Lcom/transsion/ninegridview/NineGridViewNew;->b(Lcom/transsion/ninegridview/ImageInfo;II)V

    invoke-interface {v0, v8}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/transsion/ninegridview/ImageInfo;

    invoke-interface {v0, v6}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/transsion/ninegridview/ImageInfo;

    invoke-interface {v0, v5}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lcom/transsion/ninegridview/ImageInfo;

    invoke-virtual {p0, p1, v1, v5}, Lcom/transsion/ninegridview/NineGridViewNew;->k(Lcom/transsion/ninegridview/ImageInfo;Lcom/transsion/ninegridview/ImageInfo;Lcom/transsion/ninegridview/ImageInfo;)Lcom/transsion/ninegridview/ImageInfo;

    move-result-object p1

    invoke-virtual {p0, p1, p2, v8}, Lcom/transsion/ninegridview/NineGridViewNew;->c(Lcom/transsion/ninegridview/ImageInfo;II)V

    invoke-interface {v0, v4}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/transsion/ninegridview/ImageInfo;

    invoke-interface {v0, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/transsion/ninegridview/ImageInfo;

    const/16 v2, 0x8

    invoke-interface {v0, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/transsion/ninegridview/ImageInfo;

    invoke-virtual {p0, p1, v1, v0}, Lcom/transsion/ninegridview/NineGridViewNew;->k(Lcom/transsion/ninegridview/ImageInfo;Lcom/transsion/ninegridview/ImageInfo;Lcom/transsion/ninegridview/ImageInfo;)Lcom/transsion/ninegridview/ImageInfo;

    move-result-object p1

    invoke-virtual {p0, p1, p2, v8}, Lcom/transsion/ninegridview/NineGridViewNew;->d(Lcom/transsion/ninegridview/ImageInfo;II)V

    goto/16 :goto_0

    :pswitch_1
    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/transsion/ninegridview/ImageInfo;

    invoke-interface {v0, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/transsion/ninegridview/ImageInfo;

    invoke-interface {v0, v7}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v9

    check-cast v9, Lcom/transsion/ninegridview/ImageInfo;

    invoke-virtual {p0, p1, v1, v9}, Lcom/transsion/ninegridview/NineGridViewNew;->k(Lcom/transsion/ninegridview/ImageInfo;Lcom/transsion/ninegridview/ImageInfo;Lcom/transsion/ninegridview/ImageInfo;)Lcom/transsion/ninegridview/ImageInfo;

    move-result-object p1

    invoke-virtual {p0, p1, p2, v8}, Lcom/transsion/ninegridview/NineGridViewNew;->b(Lcom/transsion/ninegridview/ImageInfo;II)V

    invoke-interface {v0, v8}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/transsion/ninegridview/ImageInfo;

    invoke-interface {v0, v6}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/transsion/ninegridview/ImageInfo;

    invoke-virtual {p0, p1, v1}, Lcom/transsion/ninegridview/NineGridViewNew;->j(Lcom/transsion/ninegridview/ImageInfo;Lcom/transsion/ninegridview/ImageInfo;)Lcom/transsion/ninegridview/ImageInfo;

    move-result-object p1

    invoke-virtual {p0, p1, p2, v7}, Lcom/transsion/ninegridview/NineGridViewNew;->c(Lcom/transsion/ninegridview/ImageInfo;II)V

    invoke-interface {v0, v5}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/transsion/ninegridview/ImageInfo;

    invoke-interface {v0, v4}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/transsion/ninegridview/ImageInfo;

    invoke-interface {v0, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/transsion/ninegridview/ImageInfo;

    invoke-virtual {p0, p1, v1, v0}, Lcom/transsion/ninegridview/NineGridViewNew;->k(Lcom/transsion/ninegridview/ImageInfo;Lcom/transsion/ninegridview/ImageInfo;Lcom/transsion/ninegridview/ImageInfo;)Lcom/transsion/ninegridview/ImageInfo;

    move-result-object p1

    invoke-virtual {p0, p1, p2, v8}, Lcom/transsion/ninegridview/NineGridViewNew;->d(Lcom/transsion/ninegridview/ImageInfo;II)V

    goto/16 :goto_0

    :pswitch_2
    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/transsion/ninegridview/ImageInfo;

    invoke-interface {v0, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/transsion/ninegridview/ImageInfo;

    invoke-interface {v0, v7}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/transsion/ninegridview/ImageInfo;

    invoke-virtual {p0, p1, v1, v2}, Lcom/transsion/ninegridview/NineGridViewNew;->k(Lcom/transsion/ninegridview/ImageInfo;Lcom/transsion/ninegridview/ImageInfo;Lcom/transsion/ninegridview/ImageInfo;)Lcom/transsion/ninegridview/ImageInfo;

    move-result-object p1

    invoke-virtual {p0, p1, p2, v8}, Lcom/transsion/ninegridview/NineGridViewNew;->b(Lcom/transsion/ninegridview/ImageInfo;II)V

    invoke-interface {v0, v8}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/transsion/ninegridview/ImageInfo;

    invoke-virtual {p0, p1, p2, v3}, Lcom/transsion/ninegridview/NineGridViewNew;->c(Lcom/transsion/ninegridview/ImageInfo;II)V

    invoke-interface {v0, v6}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/transsion/ninegridview/ImageInfo;

    invoke-interface {v0, v5}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/transsion/ninegridview/ImageInfo;

    invoke-interface {v0, v4}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/transsion/ninegridview/ImageInfo;

    invoke-virtual {p0, p1, v1, v0}, Lcom/transsion/ninegridview/NineGridViewNew;->k(Lcom/transsion/ninegridview/ImageInfo;Lcom/transsion/ninegridview/ImageInfo;Lcom/transsion/ninegridview/ImageInfo;)Lcom/transsion/ninegridview/ImageInfo;

    move-result-object p1

    invoke-virtual {p0, p1, p2, v8}, Lcom/transsion/ninegridview/NineGridViewNew;->d(Lcom/transsion/ninegridview/ImageInfo;II)V

    goto/16 :goto_0

    :pswitch_3
    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/transsion/ninegridview/ImageInfo;

    invoke-interface {v0, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/transsion/ninegridview/ImageInfo;

    invoke-interface {v0, v7}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/transsion/ninegridview/ImageInfo;

    invoke-virtual {p0, p1, v1, v2}, Lcom/transsion/ninegridview/NineGridViewNew;->k(Lcom/transsion/ninegridview/ImageInfo;Lcom/transsion/ninegridview/ImageInfo;Lcom/transsion/ninegridview/ImageInfo;)Lcom/transsion/ninegridview/ImageInfo;

    move-result-object p1

    invoke-virtual {p0, p1, p2, v8}, Lcom/transsion/ninegridview/NineGridViewNew;->b(Lcom/transsion/ninegridview/ImageInfo;II)V

    invoke-interface {v0, v8}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/transsion/ninegridview/ImageInfo;

    invoke-interface {v0, v6}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/transsion/ninegridview/ImageInfo;

    invoke-interface {v0, v5}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/transsion/ninegridview/ImageInfo;

    invoke-virtual {p0, p1, v1, v0}, Lcom/transsion/ninegridview/NineGridViewNew;->k(Lcom/transsion/ninegridview/ImageInfo;Lcom/transsion/ninegridview/ImageInfo;Lcom/transsion/ninegridview/ImageInfo;)Lcom/transsion/ninegridview/ImageInfo;

    move-result-object p1

    invoke-virtual {p0, p1, p2, v8}, Lcom/transsion/ninegridview/NineGridViewNew;->c(Lcom/transsion/ninegridview/ImageInfo;II)V

    goto/16 :goto_0

    :pswitch_4
    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/transsion/ninegridview/ImageInfo;

    invoke-interface {v0, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/transsion/ninegridview/ImageInfo;

    invoke-virtual {p0, p1, v1}, Lcom/transsion/ninegridview/NineGridViewNew;->j(Lcom/transsion/ninegridview/ImageInfo;Lcom/transsion/ninegridview/ImageInfo;)Lcom/transsion/ninegridview/ImageInfo;

    move-result-object p1

    invoke-virtual {p0, p1, p2, v7}, Lcom/transsion/ninegridview/NineGridViewNew;->b(Lcom/transsion/ninegridview/ImageInfo;II)V

    invoke-interface {v0, v7}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/transsion/ninegridview/ImageInfo;

    invoke-virtual {p0, p1, p2, v3}, Lcom/transsion/ninegridview/NineGridViewNew;->c(Lcom/transsion/ninegridview/ImageInfo;II)V

    invoke-interface {v0, v8}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/transsion/ninegridview/ImageInfo;

    invoke-interface {v0, v6}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/transsion/ninegridview/ImageInfo;

    invoke-virtual {p0, p1, v0}, Lcom/transsion/ninegridview/NineGridViewNew;->j(Lcom/transsion/ninegridview/ImageInfo;Lcom/transsion/ninegridview/ImageInfo;)Lcom/transsion/ninegridview/ImageInfo;

    move-result-object p1

    invoke-virtual {p0, p1, p2, v7}, Lcom/transsion/ninegridview/NineGridViewNew;->d(Lcom/transsion/ninegridview/ImageInfo;II)V

    goto :goto_0

    :pswitch_5
    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/transsion/ninegridview/ImageInfo;

    invoke-interface {v0, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/transsion/ninegridview/ImageInfo;

    invoke-virtual {p0, p1, v1}, Lcom/transsion/ninegridview/NineGridViewNew;->j(Lcom/transsion/ninegridview/ImageInfo;Lcom/transsion/ninegridview/ImageInfo;)Lcom/transsion/ninegridview/ImageInfo;

    move-result-object p1

    invoke-virtual {p0, p1, p2, v7}, Lcom/transsion/ninegridview/NineGridViewNew;->b(Lcom/transsion/ninegridview/ImageInfo;II)V

    invoke-interface {v0, v7}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/transsion/ninegridview/ImageInfo;

    invoke-interface {v0, v8}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/transsion/ninegridview/ImageInfo;

    invoke-virtual {p0, p1, v0}, Lcom/transsion/ninegridview/NineGridViewNew;->j(Lcom/transsion/ninegridview/ImageInfo;Lcom/transsion/ninegridview/ImageInfo;)Lcom/transsion/ninegridview/ImageInfo;

    move-result-object p1

    invoke-virtual {p0, p1, p2, v7}, Lcom/transsion/ninegridview/NineGridViewNew;->c(Lcom/transsion/ninegridview/ImageInfo;II)V

    goto :goto_0

    :pswitch_6
    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/transsion/ninegridview/ImageInfo;

    invoke-interface {v0, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/transsion/ninegridview/ImageInfo;

    invoke-virtual {p0, p1, v1}, Lcom/transsion/ninegridview/NineGridViewNew;->j(Lcom/transsion/ninegridview/ImageInfo;Lcom/transsion/ninegridview/ImageInfo;)Lcom/transsion/ninegridview/ImageInfo;

    move-result-object p1

    invoke-virtual {p0, p1, p2, v7}, Lcom/transsion/ninegridview/NineGridViewNew;->b(Lcom/transsion/ninegridview/ImageInfo;II)V

    invoke-interface {v0, v7}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/transsion/ninegridview/ImageInfo;

    invoke-virtual {p0, p1, p2, v3}, Lcom/transsion/ninegridview/NineGridViewNew;->c(Lcom/transsion/ninegridview/ImageInfo;II)V

    goto :goto_0

    :pswitch_7
    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/transsion/ninegridview/ImageInfo;

    invoke-interface {v0, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/transsion/ninegridview/ImageInfo;

    invoke-virtual {p0, p1, v0}, Lcom/transsion/ninegridview/NineGridViewNew;->j(Lcom/transsion/ninegridview/ImageInfo;Lcom/transsion/ninegridview/ImageInfo;)Lcom/transsion/ninegridview/ImageInfo;

    move-result-object p1

    invoke-virtual {p0, p1, p2, v7}, Lcom/transsion/ninegridview/NineGridViewNew;->b(Lcom/transsion/ninegridview/ImageInfo;II)V

    goto :goto_0

    :pswitch_8
    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/transsion/ninegridview/ImageInfo;

    invoke-virtual {p0, p1, p2, v3}, Lcom/transsion/ninegridview/NineGridViewNew;->b(Lcom/transsion/ninegridview/ImageInfo;II)V

    :goto_0
    invoke-virtual {p0}, Landroid/view/View;->getPaddingLeft()I

    move-result p1

    add-int/2addr p2, p1

    invoke-virtual {p0}, Landroid/view/View;->getPaddingRight()I

    move-result p1

    add-int/2addr p1, p2

    iget p2, p0, Lcom/transsion/ninegridview/NineGridViewNew;->k:I

    iget v0, p0, Lcom/transsion/ninegridview/NineGridViewNew;->m:I

    add-int/2addr p2, v0

    iget v0, p0, Lcom/transsion/ninegridview/NineGridViewNew;->o:I

    add-int/2addr p2, v0

    iget v0, p0, Lcom/transsion/ninegridview/NineGridViewNew;->f:I

    iget v1, p0, Lcom/transsion/ninegridview/NineGridViewNew;->i:I

    sub-int/2addr v1, v3

    mul-int v0, v0, v1

    add-int/2addr p2, v0

    invoke-virtual {p0}, Landroid/view/View;->getPaddingTop()I

    move-result v0

    add-int/2addr p2, v0

    invoke-virtual {p0}, Landroid/view/View;->getPaddingBottom()I

    move-result v0

    add-int v1, p2, v0

    :cond_0
    invoke-virtual {p0, p1, v1}, Landroid/view/View;->setMeasuredDimension(II)V

    return-void

    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_8
        :pswitch_7
        :pswitch_6
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method

.method public final setAdapter(Lcom/transsion/ninegridview/NineGridViewAdapter;)V
    .locals 6

    const-string v0, "adapter"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    iput-object p1, p0, Lcom/transsion/ninegridview/NineGridViewNew;->r:Lcom/transsion/ninegridview/NineGridViewAdapter;

    invoke-virtual {p1}, Lcom/transsion/ninegridview/NineGridViewAdapter;->getImageInfo()Ljava/util/List;

    move-result-object v0

    if-eqz v0, :cond_c

    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v1

    if-eqz v1, :cond_0

    goto/16 :goto_6

    :cond_0
    const/4 v1, 0x0

    invoke-virtual {p0, v1}, Landroid/view/View;->setVisibility(I)V

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v2

    iget v3, p0, Lcom/transsion/ninegridview/NineGridViewNew;->e:I

    const/4 v4, 0x1

    if-gt v4, v3, :cond_1

    if-ge v3, v2, :cond_1

    invoke-interface {v0, v1, v3}, Ljava/util/List;->subList(II)Ljava/util/List;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v2

    :cond_1
    if-ne v2, v4, :cond_2

    iput v4, p0, Lcom/transsion/ninegridview/NineGridViewNew;->h:I

    iput v4, p0, Lcom/transsion/ninegridview/NineGridViewNew;->i:I

    goto :goto_2

    :cond_2
    const/4 v3, 0x6

    const/4 v5, 0x2

    if-gt v5, v2, :cond_4

    if-ge v2, v3, :cond_4

    iput v5, p0, Lcom/transsion/ninegridview/NineGridViewNew;->h:I

    div-int/lit8 v3, v2, 0x2

    rem-int/lit8 v5, v2, 0x2

    if-nez v5, :cond_3

    const/4 v5, 0x0

    goto :goto_0

    :cond_3
    const/4 v5, 0x1

    :goto_0
    add-int/2addr v3, v5

    iput v3, p0, Lcom/transsion/ninegridview/NineGridViewNew;->i:I

    goto :goto_2

    :cond_4
    if-gt v3, v2, :cond_6

    const/16 v3, 0xa

    if-ge v2, v3, :cond_6

    const/4 v3, 0x3

    iput v3, p0, Lcom/transsion/ninegridview/NineGridViewNew;->h:I

    div-int/lit8 v3, v2, 0x3

    rem-int/lit8 v5, v2, 0x3

    if-nez v5, :cond_5

    const/4 v5, 0x0

    goto :goto_1

    :cond_5
    const/4 v5, 0x1

    :goto_1
    add-int/2addr v3, v5

    iput v3, p0, Lcom/transsion/ninegridview/NineGridViewNew;->i:I

    :cond_6
    :goto_2
    iget-object v3, p0, Lcom/transsion/ninegridview/NineGridViewNew;->q:Ljava/util/List;

    if-nez v3, :cond_7

    :goto_3
    if-ge v1, v2, :cond_a

    invoke-virtual {p0, v1}, Lcom/transsion/ninegridview/NineGridViewNew;->g(I)Landroid/widget/ImageView;

    move-result-object v3

    invoke-virtual {p0}, Landroid/view/ViewGroup;->generateDefaultLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object v5

    invoke-virtual {p0, v3, v5}, Landroid/view/ViewGroup;->addView(Landroid/view/View;Landroid/view/ViewGroup$LayoutParams;)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_3

    :cond_7
    if-eqz v3, :cond_8

    invoke-interface {v3}, Ljava/util/List;->size()I

    move-result v1

    :cond_8
    if-le v1, v2, :cond_9

    sub-int/2addr v1, v2

    invoke-virtual {p0, v2, v1}, Landroid/view/ViewGroup;->removeViews(II)V

    goto :goto_5

    :cond_9
    if-ge v1, v2, :cond_a

    :goto_4
    if-ge v1, v2, :cond_a

    invoke-virtual {p0, v1}, Lcom/transsion/ninegridview/NineGridViewNew;->g(I)Landroid/widget/ImageView;

    move-result-object v3

    invoke-virtual {p0}, Landroid/view/ViewGroup;->generateDefaultLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object v5

    invoke-virtual {p0, v3, v5}, Landroid/view/ViewGroup;->addView(Landroid/view/View;Landroid/view/ViewGroup$LayoutParams;)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_4

    :cond_a
    :goto_5
    invoke-virtual {p1}, Lcom/transsion/ninegridview/NineGridViewAdapter;->getImageInfo()Ljava/util/List;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    iget v2, p0, Lcom/transsion/ninegridview/NineGridViewNew;->e:I

    if-le v1, v2, :cond_b

    sub-int/2addr v2, v4

    invoke-virtual {p0, v2}, Landroid/view/ViewGroup;->getChildAt(I)Landroid/view/View;

    move-result-object v1

    instance-of v2, v1, Lcom/transsion/ninegridview/NineGridViewWrapper;

    if-eqz v2, :cond_b

    check-cast v1, Lcom/transsion/ninegridview/NineGridViewWrapper;

    invoke-virtual {p1}, Lcom/transsion/ninegridview/NineGridViewAdapter;->getImageInfo()Ljava/util/List;

    move-result-object p1

    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result p1

    iget v2, p0, Lcom/transsion/ninegridview/NineGridViewNew;->e:I

    sub-int/2addr p1, v2

    invoke-virtual {v1, p1}, Lcom/transsion/ninegridview/NineGridViewWrapper;->setMoreNum(I)V

    :cond_b
    iput-object v0, p0, Lcom/transsion/ninegridview/NineGridViewNew;->q:Ljava/util/List;

    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    return-void

    :cond_c
    :goto_6
    const/16 p1, 0x8

    invoke-virtual {p0, p1}, Landroid/view/View;->setVisibility(I)V

    return-void
.end method

.method public final setGridSpacing(I)V
    .locals 0

    iput p1, p0, Lcom/transsion/ninegridview/NineGridViewNew;->f:I

    return-void
.end method

.method public final setMaxSize(I)V
    .locals 0

    iput p1, p0, Lcom/transsion/ninegridview/NineGridViewNew;->e:I

    return-void
.end method

.method public final setSingleImageRatio(F)V
    .locals 0

    iput p1, p0, Lcom/transsion/ninegridview/NineGridViewNew;->d:F

    return-void
.end method

.method public final setSingleImageSize(I)V
    .locals 0

    iput p1, p0, Lcom/transsion/ninegridview/NineGridViewNew;->c:I

    return-void
.end method
