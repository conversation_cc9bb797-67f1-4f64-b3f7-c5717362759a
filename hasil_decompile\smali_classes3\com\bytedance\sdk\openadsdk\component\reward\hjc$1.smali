.class Lcom/bytedance/sdk/openadsdk/component/reward/hjc$1;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bykv/vk/openvk/component/video/api/ex/Fj;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/reward/hjc;-><init>(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/ex/svN;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/component/reward/hjc;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/reward/hjc;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/hjc;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
