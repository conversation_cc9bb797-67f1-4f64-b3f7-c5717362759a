.class Lcom/bytedance/sdk/openadsdk/common/Ko$1;
.super Ljava/lang/Object;

# interfaces
.implements Landroid/view/View$OnClickListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/common/Ko;->Ubf()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/common/Ko;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/common/Ko;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/common/Ko$1;->Fj:Lcom/bytedance/sdk/openadsdk/common/Ko;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public onClick(Landroid/view/View;)V
    .locals 0

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/common/Ko$1;->Fj:Lcom/bytedance/sdk/openadsdk/common/Ko;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/common/Ko;->eV()V

    return-void
.end method
