.class public final synthetic Lcom/transsion/ninegridview/preview/d;
.super Ljava/lang/Object;

# interfaces
.implements Landroid/view/View$OnClickListener;


# instance fields
.field public final synthetic a:Lcom/transsion/ninegridview/preview/GifImagePreviewFragment;


# direct methods
.method public synthetic constructor <init>(Lcom/transsion/ninegridview/preview/GifImagePreviewFragment;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/transsion/ninegridview/preview/d;->a:Lcom/transsion/ninegridview/preview/GifImagePreviewFragment;

    return-void
.end method


# virtual methods
.method public final onClick(Landroid/view/View;)V
    .locals 1

    iget-object v0, p0, Lcom/transsion/ninegridview/preview/d;->a:Lcom/transsion/ninegridview/preview/GifImagePreviewFragment;

    invoke-static {v0, p1}, Lcom/transsion/ninegridview/preview/GifImagePreviewFragment;->Z(Lcom/transsion/ninegridview/preview/GifImagePreviewFragment;Landroid/view/View;)V

    return-void
.end method
