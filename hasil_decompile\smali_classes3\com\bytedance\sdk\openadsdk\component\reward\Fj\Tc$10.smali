.class Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$10;
.super Ljava/lang/Object;

# interfaces
.implements Landroid/webkit/DownloadListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Fj(Ljava/lang/String;Lcom/bytedance/sdk/openadsdk/core/video/hjc/ex;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/core/video/hjc/ex;

.field final synthetic ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;Lcom/bytedance/sdk/openadsdk/core/video/hjc/ex;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$10;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;

    iput-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$10;->Fj:Lcom/bytedance/sdk/openadsdk/core/video/hjc/ex;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public onDownloadStart(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;J)V
    .locals 0

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$10;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->ex(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object p1

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->uM:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/eV;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/eV;->ex()V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$10;->Fj:Lcom/bytedance/sdk/openadsdk/core/video/hjc/ex;

    if-eqz p1, :cond_0

    invoke-interface {p1}, Lcom/bytedance/sdk/openadsdk/core/video/hjc/ex;->vYf()V

    :cond_0
    return-void
.end method
