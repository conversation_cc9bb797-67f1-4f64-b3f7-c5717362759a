.class Lcom/bytedance/sdk/openadsdk/component/WR$6;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bykv/vk/openvk/component/video/api/Ubf/Fj$Fj;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/WR;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/AdSlot;Lcom/bytedance/sdk/openadsdk/core/model/mC;Lcom/bytedance/sdk/openadsdk/component/WR$hjc;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:I

.field final synthetic Ubf:Lcom/bytedance/sdk/openadsdk/component/WR$hjc;

.field final synthetic WR:Ljava/io/File;

.field final synthetic eV:Lcom/bytedance/sdk/openadsdk/core/model/mC;

.field final synthetic ex:Lcom/bytedance/sdk/openadsdk/utils/lv;

.field final synthetic hjc:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

.field final synthetic svN:Lcom/bytedance/sdk/openadsdk/component/WR;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/WR;ILcom/bytedance/sdk/openadsdk/utils/lv;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/core/model/mC;Lcom/bytedance/sdk/openadsdk/component/WR$hjc;Ljava/io/File;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/WR$6;->svN:Lcom/bytedance/sdk/openadsdk/component/WR;

    iput p2, p0, Lcom/bytedance/sdk/openadsdk/component/WR$6;->Fj:I

    iput-object p3, p0, Lcom/bytedance/sdk/openadsdk/component/WR$6;->ex:Lcom/bytedance/sdk/openadsdk/utils/lv;

    iput-object p4, p0, Lcom/bytedance/sdk/openadsdk/component/WR$6;->hjc:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    iput-object p5, p0, Lcom/bytedance/sdk/openadsdk/component/WR$6;->eV:Lcom/bytedance/sdk/openadsdk/core/model/mC;

    iput-object p6, p0, Lcom/bytedance/sdk/openadsdk/component/WR$6;->Ubf:Lcom/bytedance/sdk/openadsdk/component/WR$hjc;

    iput-object p7, p0, Lcom/bytedance/sdk/openadsdk/component/WR$6;->WR:Ljava/io/File;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj(Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;I)V
    .locals 2

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/WR$6;->svN:Lcom/bytedance/sdk/openadsdk/component/WR;

    iget p2, p0, Lcom/bytedance/sdk/openadsdk/component/WR$6;->Fj:I

    invoke-virtual {p1, p2}, Lcom/bytedance/sdk/openadsdk/component/WR;->Fj(I)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/WR$6;->ex:Lcom/bytedance/sdk/openadsdk/utils/lv;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/utils/lv;->hjc()J

    move-result-wide p1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/WR$6;->hjc:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    const/4 v1, 0x1

    invoke-static {v0, p1, p2, v1}, Lcom/bytedance/sdk/openadsdk/component/eV/Fj;->ex(Lcom/bytedance/sdk/openadsdk/core/model/Ql;JZ)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/WR$6;->eV:Lcom/bytedance/sdk/openadsdk/core/model/mC;

    if-eqz v0, :cond_0

    invoke-virtual {v0, p1, p2}, Lcom/bytedance/sdk/openadsdk/core/model/mC;->Fj(J)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/WR$6;->eV:Lcom/bytedance/sdk/openadsdk/core/model/mC;

    const/4 p2, 0x2

    invoke-virtual {p1, p2}, Lcom/bytedance/sdk/openadsdk/core/model/mC;->Fj(I)V

    :cond_0
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/WR$6;->Ubf:Lcom/bytedance/sdk/openadsdk/component/WR$hjc;

    invoke-interface {p1}, Lcom/bytedance/sdk/openadsdk/component/WR$hjc;->Fj()V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/WR$6;->hjc:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    const/4 p2, 0x0

    invoke-static {p1, p2}, Lcom/bytedance/sdk/openadsdk/component/WR;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/component/WR$eV;)V

    return-void
.end method

.method public Fj(Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;ILjava/lang/String;)V
    .locals 3

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/WR$6;->ex:Lcom/bytedance/sdk/openadsdk/utils/lv;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/utils/lv;->hjc()J

    move-result-wide v0

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/WR$6;->hjc:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    const/4 v2, 0x0

    invoke-static {p1, v0, v1, v2}, Lcom/bytedance/sdk/openadsdk/component/eV/Fj;->ex(Lcom/bytedance/sdk/openadsdk/core/model/Ql;JZ)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/WR$6;->eV:Lcom/bytedance/sdk/openadsdk/core/model/mC;

    if-eqz p1, :cond_0

    invoke-virtual {p1, v0, v1}, Lcom/bytedance/sdk/openadsdk/core/model/mC;->Fj(J)V

    :cond_0
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/WR$6;->Ubf:Lcom/bytedance/sdk/openadsdk/component/WR$hjc;

    invoke-interface {p1, p2, p3}, Lcom/bytedance/sdk/openadsdk/component/WR$hjc;->Fj(ILjava/lang/String;)V

    :try_start_0
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/WR$6;->WR:Ljava/io/File;

    invoke-virtual {p1}, Ljava/io/File;->exists()Z

    move-result p1

    if-eqz p1, :cond_1

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/WR$6;->WR:Ljava/io/File;

    invoke-virtual {p1}, Ljava/io/File;->isFile()Z

    move-result p1

    if-eqz p1, :cond_1

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/WR$6;->WR:Ljava/io/File;

    invoke-static {p1}, Lcom/bytedance/sdk/component/utils/WR;->hjc(Ljava/io/File;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :catchall_0
    :cond_1
    return-void
.end method

.method public ex(Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;I)V
    .locals 0

    return-void
.end method
