.class Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN$2;
.super Lcom/bytedance/sdk/openadsdk/UYd/Fj;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;->Fj(Lcom/bytedance/sdk/openadsdk/mSE/eV;Z)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/mSE/eV;

.field final synthetic ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;Lcom/bytedance/sdk/openadsdk/mSE/eV;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN$2;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;

    iput-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN$2;->Fj:Lcom/bytedance/sdk/openadsdk/mSE/eV;

    invoke-direct {p0}, Lcom/bytedance/sdk/openadsdk/UYd/Fj;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj()Lcom/bytedance/sdk/openadsdk/UYd/eV;
    .locals 1

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;->mE()Lcom/bytedance/sdk/openadsdk/UYd/eV;

    move-result-object v0

    return-object v0
.end method

.method public Fj(ILjava/lang/String;)V
    .locals 1

    invoke-super {p0, p1, p2}, Lcom/bytedance/sdk/openadsdk/UYd/Fj;->Fj(ILjava/lang/String;)V

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN$2;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;

    invoke-static {p2}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;)Lcom/bytedance/sdk/openadsdk/core/widget/PlayableLoadingView;

    move-result-object p2

    if-eqz p2, :cond_0

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN$2;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;

    invoke-static {p2}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;)Lcom/bytedance/sdk/openadsdk/core/widget/PlayableLoadingView;

    move-result-object p2

    invoke-virtual {p2}, Landroid/view/View;->isShown()Z

    move-result p2

    if-eqz p2, :cond_0

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN$2;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;

    invoke-static {p2}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;->ex(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;)Landroid/os/Handler;

    move-result-object p2

    const/4 v0, 0x3

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/core/model/mE;->Fj(I)I

    move-result p1

    invoke-static {v0, p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;->Fj(II)Landroid/os/Message;

    move-result-object p1

    invoke-virtual {p2, p1}, Landroid/os/Handler;->sendMessage(Landroid/os/Message;)Z

    :cond_0
    return-void
.end method

.method public Fj(Lorg/json/JSONObject;)V
    .locals 3

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN$2;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;->eV(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;)Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN$2;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;

    invoke-static {v1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;->Ubf(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;)Ljava/lang/String;

    move-result-object v1

    const-string v2, "playable_track"

    invoke-static {v0, v1, v2, p1}, Lcom/bytedance/sdk/openadsdk/ex/hjc;->ex(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Ljava/lang/String;Ljava/lang/String;Lorg/json/JSONObject;)V

    return-void
.end method

.method public ex()V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN$2;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;->hjc(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object v0

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->qPr:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ko()Lcom/bytedance/sdk/openadsdk/core/Vq;

    move-result-object v0

    const/4 v1, 0x1

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/core/Vq;->hjc(Z)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN$2;->Fj:Lcom/bytedance/sdk/openadsdk/mSE/eV;

    if-eqz v0, :cond_0

    invoke-interface {v0}, Lcom/bytedance/sdk/openadsdk/mSE/eV;->Fj()V

    :cond_0
    return-void
.end method
