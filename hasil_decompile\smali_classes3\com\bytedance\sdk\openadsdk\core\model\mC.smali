.class public Lcom/bytedance/sdk/openadsdk/core/model/mC;
.super Ljava/lang/Object;


# instance fields
.field private BcC:J

.field public Fj:Z

.field private Ko:J

.field private Ubf:J

.field private WR:J

.field private eV:Lcom/bytedance/sdk/openadsdk/utils/lv;

.field public ex:J

.field private hjc:Lcom/bytedance/sdk/openadsdk/utils/lv;

.field private mSE:J

.field private rAx:I

.field private svN:J


# direct methods
.method public constructor <init>()V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/utils/lv;->ex()Lcom/bytedance/sdk/openadsdk/utils/lv;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/mC;->hjc:Lcom/bytedance/sdk/openadsdk/utils/lv;

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/utils/lv;->ex()Lcom/bytedance/sdk/openadsdk/utils/lv;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/mC;->eV:Lcom/bytedance/sdk/openadsdk/utils/lv;

    return-void
.end method


# virtual methods
.method public BcC()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/mC;->rAx:I

    return v0
.end method

.method public Fj()Lcom/bytedance/sdk/openadsdk/utils/lv;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/mC;->hjc:Lcom/bytedance/sdk/openadsdk/utils/lv;

    return-object v0
.end method

.method public Fj(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/mC;->rAx:I

    return-void
.end method

.method public Fj(J)V
    .locals 0

    iput-wide p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/mC;->Ko:J

    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/openadsdk/utils/lv;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/mC;->hjc:Lcom/bytedance/sdk/openadsdk/utils/lv;

    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/openadsdk/utils/lv;Lcom/bytedance/sdk/openadsdk/utils/lv;ILcom/bytedance/sdk/openadsdk/utils/lv;)V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/mC;->hjc:Lcom/bytedance/sdk/openadsdk/utils/lv;

    invoke-virtual {p1, v0}, Lcom/bytedance/sdk/openadsdk/utils/lv;->Fj(Lcom/bytedance/sdk/openadsdk/utils/lv;)J

    move-result-wide v0

    iput-wide v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/mC;->Ubf:J

    invoke-virtual {p2, p1}, Lcom/bytedance/sdk/openadsdk/utils/lv;->Fj(Lcom/bytedance/sdk/openadsdk/utils/lv;)J

    move-result-wide v0

    iput-wide v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/mC;->WR:J

    int-to-long v0, p3

    iput-wide v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/mC;->svN:J

    invoke-virtual {p4, p2}, Lcom/bytedance/sdk/openadsdk/utils/lv;->Fj(Lcom/bytedance/sdk/openadsdk/utils/lv;)J

    move-result-wide p1

    iput-wide p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/mC;->BcC:J

    return-void
.end method

.method public Ubf()J
    .locals 2

    iget-wide v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/mC;->BcC:J

    return-wide v0
.end method

.method public WR()J
    .locals 2

    iget-wide v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/mC;->mSE:J

    return-wide v0
.end method

.method public eV()J
    .locals 2

    iget-wide v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/mC;->svN:J

    return-wide v0
.end method

.method public ex()J
    .locals 2

    iget-wide v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/mC;->Ubf:J

    return-wide v0
.end method

.method public ex(Lcom/bytedance/sdk/openadsdk/utils/lv;)V
    .locals 2

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/mC;->eV:Lcom/bytedance/sdk/openadsdk/utils/lv;

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/mC;->hjc:Lcom/bytedance/sdk/openadsdk/utils/lv;

    invoke-virtual {p1, v0}, Lcom/bytedance/sdk/openadsdk/utils/lv;->Fj(Lcom/bytedance/sdk/openadsdk/utils/lv;)J

    move-result-wide v0

    iput-wide v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/mC;->mSE:J

    return-void
.end method

.method public hjc()J
    .locals 2

    iget-wide v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/mC;->WR:J

    return-wide v0
.end method

.method public svN()J
    .locals 2

    iget-wide v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/mC;->Ko:J

    return-wide v0
.end method
