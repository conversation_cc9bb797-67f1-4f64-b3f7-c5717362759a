.class public interface abstract Landroidx/media3/common/h0;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/media3/common/h0$d;,
        Landroidx/media3/common/h0$b;,
        Landroidx/media3/common/h0$e;,
        Landroidx/media3/common/h0$c;
    }
.end annotation


# virtual methods
.method public abstract A()Z
.end method

.method public abstract B(ZI)V
.end method

.method public abstract C()V
.end method

.method public abstract D(IILjava/util/List;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(II",
            "Ljava/util/List<",
            "Landroidx/media3/common/b0;",
            ">;)V"
        }
    .end annotation
.end method

.method public abstract E(I)V
.end method

.method public abstract F()I
.end method

.method public abstract G(II)V
.end method

.method public abstract H(Landroidx/media3/common/b0;)V
.end method

.method public abstract I(Landroidx/media3/common/b0;)V
.end method

.method public abstract J(Landroidx/media3/common/h0$d;)V
.end method

.method public abstract K(Landroidx/media3/common/h0$d;)V
.end method

.method public abstract M(ILandroidx/media3/common/b0;)V
.end method

.method public abstract N(I)Landroidx/media3/common/b0;
.end method

.method public abstract P()J
.end method

.method public abstract R(ILjava/util/List;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Ljava/util/List<",
            "Landroidx/media3/common/b0;",
            ">;)V"
        }
    .end annotation
.end method

.method public abstract T(Landroidx/media3/common/p0;)V
.end method

.method public abstract U()Z
.end method

.method public abstract b(Landroidx/media3/common/g0;)V
.end method

.method public abstract c()Landroidx/media3/common/PlaybackException;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end method

.method public abstract clearVideoSurface()V
.end method

.method public abstract clearVideoSurfaceView(Landroid/view/SurfaceView;)V
    .param p1    # Landroid/view/SurfaceView;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
.end method

.method public abstract clearVideoTextureView(Landroid/view/TextureView;)V
    .param p1    # Landroid/view/TextureView;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
.end method

.method public abstract d()J
.end method

.method public abstract e(Ljava/util/List;Z)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Landroidx/media3/common/b0;",
            ">;Z)V"
        }
    .end annotation
.end method

.method public abstract f()V
.end method

.method public abstract g()Z
.end method

.method public abstract getContentPosition()J
.end method

.method public abstract getCurrentAdGroupIndex()I
.end method

.method public abstract getCurrentAdIndexInAdGroup()I
.end method

.method public abstract getCurrentPeriodIndex()I
.end method

.method public abstract getCurrentPosition()J
.end method

.method public abstract getCurrentTimeline()Landroidx/media3/common/m0;
.end method

.method public abstract getCurrentTracks()Landroidx/media3/common/q0;
.end method

.method public abstract getDuration()J
.end method

.method public abstract getPlayWhenReady()Z
.end method

.method public abstract getPlaybackParameters()Landroidx/media3/common/g0;
.end method

.method public abstract getPlaybackState()I
.end method

.method public abstract getRepeatMode()I
.end method

.method public abstract getShuffleModeEnabled()Z
.end method

.method public abstract getVolume()F
.end method

.method public abstract h()Ld2/b;
.end method

.method public abstract i(I)Z
.end method

.method public abstract isPlaying()Z
.end method

.method public abstract isPlayingAd()Z
.end method

.method public abstract j()Z
.end method

.method public abstract k()I
.end method

.method public abstract l()Landroid/os/Looper;
.end method

.method public abstract m()Landroidx/media3/common/p0;
.end method

.method public abstract n()V
.end method

.method public abstract o()Landroidx/media3/common/h0$b;
.end method

.method public abstract p()J
.end method

.method public abstract pause()V
.end method

.method public abstract play()V
.end method

.method public abstract prepare()V
.end method

.method public abstract q()Landroidx/media3/common/t0;
.end method

.method public abstract r()Z
.end method

.method public abstract release()V
.end method

.method public abstract s()J
.end method

.method public abstract seekTo(IJ)V
.end method

.method public abstract seekTo(J)V
.end method

.method public abstract seekToDefaultPosition()V
.end method

.method public abstract setPlayWhenReady(Z)V
.end method

.method public abstract setPlaybackSpeed(F)V
.end method

.method public abstract setRepeatMode(I)V
.end method

.method public abstract setShuffleModeEnabled(Z)V
.end method

.method public abstract setVideoSurfaceView(Landroid/view/SurfaceView;)V
    .param p1    # Landroid/view/SurfaceView;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
.end method

.method public abstract setVideoTextureView(Landroid/view/TextureView;)V
    .param p1    # Landroid/view/TextureView;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
.end method

.method public abstract setVolume(F)V
.end method

.method public abstract stop()V
.end method

.method public abstract t()Z
.end method

.method public abstract u()I
.end method

.method public abstract v()J
.end method

.method public abstract w()V
.end method

.method public abstract x()V
.end method

.method public abstract y()Landroidx/media3/common/d0;
.end method

.method public abstract z()J
.end method
