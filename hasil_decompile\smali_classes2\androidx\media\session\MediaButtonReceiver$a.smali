.class public final Landroidx/media/session/MediaButtonReceiver$a;
.super Ljava/lang/Object;


# annotations
.annotation build Landroidx/annotation/RequiresApi;
    value = 0x1f
.end annotation

.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/media/session/MediaButtonReceiver;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation


# direct methods
.method public static a(Ljava/lang/IllegalStateException;)Landroid/app/ForegroundServiceStartNotAllowedException;
    .locals 0

    invoke-static {p0}, Lb2/b;->a(Ljava/lang/Object;)Landroid/app/ForegroundServiceStartNotAllowedException;

    move-result-object p0

    return-object p0
.end method

.method public static b(Ljava/lang/IllegalStateException;)Z
    .locals 0

    invoke-static {p0}, Lb2/c;->a(<PERSON>ja<PERSON>/lang/Object;)Z

    move-result p0

    return p0
.end method
