.class Lcom/bytedance/sdk/openadsdk/component/reward/svN$2;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/reward/svN;->Fj(Lcom/bytedance/sdk/openadsdk/api/reward/PAGRewardedAd;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/api/reward/PAGRewardedAd;

.field final synthetic ex:Lcom/bytedance/sdk/openadsdk/component/reward/svN;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/reward/svN;Lcom/bytedance/sdk/openadsdk/api/reward/PAGRewardedAd;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/svN$2;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/svN;

    iput-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/svN$2;->Fj:Lcom/bytedance/sdk/openadsdk/api/reward/PAGRewardedAd;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/svN$2;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/svN;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/svN;->Fj:Lcom/bytedance/sdk/openadsdk/api/reward/PAGRewardedAdLoadListener;

    if-eqz v0, :cond_0

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/svN$2;->Fj:Lcom/bytedance/sdk/openadsdk/api/reward/PAGRewardedAd;

    invoke-interface {v0, v1}, Lcom/bytedance/sdk/openadsdk/api/PAGLoadListener;->onAdLoaded(Ljava/lang/Object;)V

    :cond_0
    return-void
.end method
