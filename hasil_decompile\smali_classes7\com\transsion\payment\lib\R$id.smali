.class public final Lcom/transsion/payment/lib/R$id;
.super Ljava/lang/Object;


# static fields
.field public static container:I = 0x7f0a0190

.field public static fl_web:I = 0x7f0a02ae

.field public static iv_back:I = 0x7f0a03dc

.field public static iv_close:I = 0x7f0a03ec

.field public static iv_right:I = 0x7f0a0467

.field public static progress:I = 0x7f0a0741

.field public static root:I = 0x7f0a07ad

.field public static tool_bar:I = 0x7f0a0916

.field public static tv_titleText:I = 0x7f0a0b1e

.field public static web_pay_include_loading:I = 0x7f0a0c29


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
