.class public abstract Landroidx/compose/runtime/m;
.super Ljava/lang/Object;


# annotations
.annotation runtime Lkotlin/Metadata;
.end annotation


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public abstract a(Landroidx/compose/runtime/y;Lkotlin/jvm/functions/Function2;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/compose/runtime/y;",
            "Lkotlin/jvm/functions/Function2<",
            "-",
            "Landroidx/compose/runtime/i;",
            "-",
            "Ljava/lang/Integer;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation
.end method

.method public abstract b(Landroidx/compose/runtime/y0;)V
.end method

.method public c()V
    .locals 0

    return-void
.end method

.method public abstract d()Z
.end method

.method public abstract e()Z
.end method

.method public abstract f()Z
.end method

.method public g()Landroidx/compose/runtime/n1;
    .locals 1

    invoke-static {}, Landroidx/compose/runtime/n;->a()Landroidx/compose/runtime/n1;

    move-result-object v0

    return-object v0
.end method

.method public abstract h()I
.end method

.method public abstract i()Lkotlin/coroutines/CoroutineContext;
.end method

.method public j()Landroidx/compose/runtime/u;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public abstract k(Landroidx/compose/runtime/y0;)V
.end method

.method public abstract l(Landroidx/compose/runtime/y;)V
.end method

.method public abstract m(Landroidx/compose/runtime/y0;Landroidx/compose/runtime/x0;)V
.end method

.method public n(Landroidx/compose/runtime/y0;)Landroidx/compose/runtime/x0;
    .locals 0

    const/4 p1, 0x0

    return-object p1
.end method

.method public o(Ljava/util/Set;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Set<",
            "Lb0/a;",
            ">;)V"
        }
    .end annotation

    return-void
.end method

.method public p(Landroidx/compose/runtime/i;)V
    .locals 0

    return-void
.end method

.method public abstract q(Landroidx/compose/runtime/y;)V
.end method

.method public r()V
    .locals 0

    return-void
.end method

.method public s(Landroidx/compose/runtime/i;)V
    .locals 0

    return-void
.end method

.method public abstract t(Landroidx/compose/runtime/y;)V
.end method
