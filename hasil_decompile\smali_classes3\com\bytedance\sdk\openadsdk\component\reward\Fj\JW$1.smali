.class Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW$1;
.super Lcom/bytedance/sdk/openadsdk/core/ex/svN;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW;->Fj()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW;Ljava/lang/String;Lcom/bytedance/sdk/openadsdk/core/svN/Fj;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW;

    invoke-direct {p0, p2, p3}, Lcom/bytedance/sdk/openadsdk/core/ex/svN;-><init>(Ljava/lang/String;Lcom/bytedance/sdk/openadsdk/core/svN/Fj;)V

    return-void
.end method
