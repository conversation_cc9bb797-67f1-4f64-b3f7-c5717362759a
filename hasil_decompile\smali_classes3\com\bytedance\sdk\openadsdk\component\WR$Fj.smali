.class Lcom/bytedance/sdk/openadsdk/component/WR$Fj;
.super Lcom/bytedance/sdk/component/svN/BcC;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/sdk/openadsdk/component/WR;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = "Fj"
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/component/WR;

.field private final ex:Lcom/bytedance/sdk/openadsdk/component/Ubf/Fj;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/WR;Lcom/bytedance/sdk/openadsdk/component/Ubf/Fj;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/WR$Fj;->Fj:Lcom/bytedance/sdk/openadsdk/component/WR;

    const-string p1, "App Open Ad Write Cache"

    invoke-direct {p0, p1}, Lcom/bytedance/sdk/component/svN/BcC;-><init>(Ljava/lang/String;)V

    iput-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/WR$Fj;->ex:Lcom/bytedance/sdk/openadsdk/component/Ubf/Fj;

    return-void
.end method


# virtual methods
.method public run()V
    .locals 4

    :try_start_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/WR$Fj;->ex:Lcom/bytedance/sdk/openadsdk/component/Ubf/Fj;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/Ubf/Fj;->ex()Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->QV()Lorg/json/JSONObject;

    move-result-object v0

    invoke-static {v0}, Lcom/bytedance/sdk/component/utils/Fj;->Fj(Lorg/json/JSONObject;)Lorg/json/JSONObject;

    move-result-object v0

    invoke-virtual {v0}, Lorg/json/JSONObject;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v1, "tt_openad_materialMeta"

    new-instance v2, Ljava/lang/StringBuilder;

    const-string v3, "material"

    invoke-direct {v2, v3}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    iget-object v3, p0, Lcom/bytedance/sdk/openadsdk/component/WR$Fj;->ex:Lcom/bytedance/sdk/openadsdk/component/Ubf/Fj;

    invoke-virtual {v3}, Lcom/bytedance/sdk/openadsdk/component/Ubf/Fj;->Fj()I

    move-result v3

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-static {v1, v2, v0}, Lcom/bytedance/sdk/openadsdk/multipro/eV/eV;->Fj(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :catchall_0
    return-void
.end method
