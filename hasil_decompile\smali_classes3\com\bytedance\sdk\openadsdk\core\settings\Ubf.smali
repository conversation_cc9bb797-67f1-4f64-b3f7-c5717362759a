.class public interface abstract Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;
.super Ljava/lang/Object;


# virtual methods
.method public abstract Af(Ljava/lang/String;)I
.end method

.method public abstract Af()Ljava/lang/String;
.end method

.method public abstract At()I
.end method

.method public abstract BcC()Z
.end method

.method public abstract BcC(Ljava/lang/String;)Z
.end method

.method public abstract Bzy()Z
.end method

.method public abstract Eev()I
.end method

.method public abstract Fj(I)I
.end method

.method public abstract Fj(Ljava/lang/String;)I
.end method

.method public abstract Fj(Ljava/lang/String;Z)I
.end method

.method public abstract Fj()V
.end method

.method public abstract Fj(IZ)V
.end method

.method public abstract Fj(J)V
.end method

.method public abstract Fj(Landroid/content/Context;)V
.end method

.method public abstract Fj(Lorg/json/JSONObject;Lcom/bytedance/sdk/openadsdk/core/settings/eV$Fj;)V
.end method

.method public abstract Gv()Z
.end method

.method public abstract HQ()Z
.end method

.method public abstract HY()Z
.end method

.method public abstract JU()I
.end method

.method public abstract JU(Ljava/lang/String;)Z
.end method

.method public abstract JW()I
.end method

.method public abstract JW(Ljava/lang/String;)Z
.end method

.method public abstract JZ()Z
.end method

.method public abstract Jq()I
.end method

.method public abstract KZ()I
.end method

.method public abstract Kf()Lorg/json/JSONObject;
.end method

.method public abstract Kk()Z
.end method

.method public abstract Ko(Ljava/lang/String;)I
.end method

.method public abstract Ko()Ljava/lang/String;
.end method

.method public abstract Moo(Ljava/lang/String;)Lcom/bytedance/sdk/openadsdk/core/settings/Fj;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end method

.method public abstract Moo()Lcom/bytedance/sdk/openadsdk/core/settings/WR;
.end method

.method public abstract Nyg()I
.end method

.method public abstract OK()Z
.end method

.method public abstract OXv()Lcom/bytedance/sdk/openadsdk/ex/Fj/Tc;
.end method

.method public abstract Obv()Z
.end method

.method public abstract PpV()Z
.end method

.method public abstract QR()I
.end method

.method public abstract QV()Z
.end method

.method public abstract Ql(Ljava/lang/String;)I
.end method

.method public abstract Ql()Z
.end method

.method public abstract Tc(Ljava/lang/String;)I
.end method

.method public abstract Tc()Ljava/util/Set;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Set<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end method

.method public abstract UF()I
.end method

.method public abstract UYd()I
.end method

.method public abstract UYd(Ljava/lang/String;)I
.end method

.method public abstract Ubf()Lorg/json/JSONObject;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end method

.method public abstract Ubf(Ljava/lang/String;)Z
.end method

.method public abstract Vq()I
.end method

.method public abstract Vq(Ljava/lang/String;)Z
.end method

.method public abstract WR()J
.end method

.method public abstract WR(Ljava/lang/String;)Z
.end method

.method public abstract YH()I
.end method

.method public abstract cB()Z
.end method

.method public abstract cB(Ljava/lang/String;)Z
.end method

.method public abstract cs()Z
.end method

.method public abstract dG()Z
.end method

.method public abstract dG(Ljava/lang/String;)Z
.end method

.method public abstract eV()I
.end method

.method public abstract eV(I)V
.end method

.method public abstract eV(Ljava/lang/String;)Z
.end method

.method public abstract efV()Ljava/lang/String;
.end method

.method public abstract eh()Z
.end method

.method public abstract ei()Lorg/json/JSONObject;
.end method

.method public abstract ex(I)I
.end method

.method public abstract ex(Ljava/lang/String;)I
.end method

.method public abstract ex()V
.end method

.method public abstract ex(Lorg/json/JSONObject;Lcom/bytedance/sdk/openadsdk/core/settings/eV$Fj;)V
.end method

.method public abstract fj()I
.end method

.method public abstract flF()I
.end method

.method public abstract gXF()Ljava/lang/String;
.end method

.method public abstract gci()Z
.end method

.method public abstract haP()[Ljava/lang/String;
.end method

.method public abstract hjc(I)I
.end method

.method public abstract hjc()Ljava/lang/String;
.end method

.method public abstract hjc(Ljava/lang/String;)Z
.end method

.method public abstract iT()I
.end method

.method public abstract jsD()Ljava/lang/String;
.end method

.method public abstract kF()Ljava/lang/String;
.end method

.method public abstract ks()V
.end method

.method public abstract lv()I
.end method

.method public abstract mC()Lcom/bytedance/sdk/openadsdk/core/settings/BcC;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end method

.method public abstract mC(Ljava/lang/String;)Z
.end method

.method public abstract mE()I
.end method

.method public abstract mE(Ljava/lang/String;)I
.end method

.method public abstract mSE(Ljava/lang/String;)I
.end method

.method public abstract mSE()Z
.end method

.method public abstract mj()I
.end method

.method public abstract mt()Z
.end method

.method public abstract nsB(Ljava/lang/String;)I
.end method

.method public abstract nsB()J
.end method

.method public abstract oX()Ljava/lang/String;
.end method

.method public abstract qPr()I
.end method

.method public abstract qg()I
.end method

.method public abstract rAx()I
.end method

.method public abstract rAx(Ljava/lang/String;)Z
.end method

.method public abstract rS()Z
.end method

.method public abstract rS(Ljava/lang/String;)Z
.end method

.method public abstract rXP()V
.end method

.method public abstract rf()Ljava/lang/String;
.end method

.method public abstract rf(Ljava/lang/String;)Z
    .param p1    # Ljava/lang/String;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
.end method

.method public abstract spi()Ljava/lang/String;
.end method

.method public abstract ss()J
.end method

.method public abstract svN(Ljava/lang/String;)I
.end method

.method public abstract svN()V
.end method

.method public abstract tyC()Ljava/lang/String;
.end method

.method public abstract uM()Ljava/lang/String;
.end method

.method public abstract uy()I
.end method

.method public abstract uy(Ljava/lang/String;)I
.end method

.method public abstract vYf(Ljava/lang/String;)I
.end method

.method public abstract vYf()Z
.end method

.method public abstract xEF()Z
.end method

.method public abstract yR()Z
.end method

.method public abstract yo()J
.end method

.method public abstract zf()I
.end method
