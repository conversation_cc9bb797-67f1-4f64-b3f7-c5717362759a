.class public Lcom/bytedance/sdk/openadsdk/component/hjc$Fj;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/openadsdk/component/WR$eV;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/sdk/openadsdk/component/hjc;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = "Fj"
.end annotation


# instance fields
.field Fj:Ljava/lang/ref/WeakReference;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/lang/ref/WeakReference<",
            "Landroid/app/Activity;",
            ">;"
        }
    .end annotation
.end field

.field final synthetic ex:Lcom/bytedance/sdk/openadsdk/component/hjc;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/hjc;Landroid/app/Activity;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/hjc$Fj;->ex:Lcom/bytedance/sdk/openadsdk/component/hjc;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance p1, Ljava/lang/ref/WeakReference;

    invoke-direct {p1, p2}, Ljava/lang/ref/WeakReference;-><init>(Ljava/lang/Object;)V

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/hjc$Fj;->Fj:Ljava/lang/ref/WeakReference;

    return-void
.end method


# virtual methods
.method public Fj(Landroid/graphics/Bitmap;)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/hjc$Fj;->Fj:Ljava/lang/ref/WeakReference;

    invoke-virtual {v0}, Ljava/lang/ref/Reference;->get()Ljava/lang/Object;

    move-result-object v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/hjc$Fj;->Fj:Ljava/lang/ref/WeakReference;

    invoke-virtual {v0}, Ljava/lang/ref/Reference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/app/Activity;

    invoke-virtual {v0}, Landroid/app/Activity;->isFinishing()Z

    move-result v0

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/hjc$Fj;->ex:Lcom/bytedance/sdk/openadsdk/component/hjc;

    invoke-static {v0, p1}, Lcom/bytedance/sdk/openadsdk/component/hjc;->Fj(Lcom/bytedance/sdk/openadsdk/component/hjc;Landroid/graphics/Bitmap;)V

    :cond_0
    return-void
.end method
