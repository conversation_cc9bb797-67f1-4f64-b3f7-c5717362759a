.class public Lcom/bytedance/sdk/openadsdk/component/WR/ex;
.super Ljava/lang/Object;

# interfaces
.implements Landroid/os/Handler$Callback;


# instance fields
.field private Fj:Landroid/os/Handler;

.field private Ubf:I

.field private WR:I

.field private eV:I

.field private final ex:Lcom/bytedance/sdk/openadsdk/component/BcC/Fj;

.field private hjc:Lcom/bytedance/sdk/openadsdk/component/WR/Fj;

.field private svN:Z


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/BcC/Fj;)V
    .locals 2

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Landroid/os/Handler;

    invoke-static {}, Landroid/os/Looper;->myLooper()Landroid/os/Looper;

    move-result-object v1

    invoke-direct {v0, v1, p0}, Landroid/os/Handler;-><init>(Landroid/os/Looper;Landroid/os/Handler$Callback;)V

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/WR/ex;->Fj:Landroid/os/Handler;

    const/4 v0, 0x0

    iput v0, p0, Lcom/bytedance/sdk/openadsdk/component/WR/ex;->eV:I

    const/4 v1, 0x5

    iput v1, p0, Lcom/bytedance/sdk/openadsdk/component/WR/ex;->Ubf:I

    iput v0, p0, Lcom/bytedance/sdk/openadsdk/component/WR/ex;->WR:I

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/WR/ex;->ex:Lcom/bytedance/sdk/openadsdk/component/BcC/Fj;

    return-void
.end method


# virtual methods
.method public Fj()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/component/WR/ex;->WR:I

    return v0
.end method

.method public Fj(F)V
    .locals 0

    float-to-int p1, p1

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/component/WR/ex;->Ubf:I

    if-gtz p1, :cond_0

    const/4 p1, 0x5

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/component/WR/ex;->Ubf:I

    :cond_0
    return-void
.end method

.method public Fj(I)V
    .locals 4

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/component/WR/ex;->eV:I

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/component/WR/ex;->Ubf:I

    sub-int/2addr v0, p1

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/WR/ex;->ex:Lcom/bytedance/sdk/openadsdk/component/BcC/Fj;

    int-to-long v2, v0

    invoke-virtual {v1, v2, v3}, Lcom/bytedance/sdk/openadsdk/component/BcC/Fj;->Fj(J)V

    const/4 v1, 0x1

    const/4 v2, 0x0

    if-gtz p1, :cond_1

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/WR/ex;->hjc:Lcom/bytedance/sdk/openadsdk/component/WR/Fj;

    if-eqz p1, :cond_0

    iget-boolean v3, p0, Lcom/bytedance/sdk/openadsdk/component/WR/ex;->svN:Z

    if-nez v3, :cond_0

    invoke-interface {p1}, Lcom/bytedance/sdk/openadsdk/component/WR/Fj;->ex()V

    iput-boolean v1, p0, Lcom/bytedance/sdk/openadsdk/component/WR/ex;->svN:Z

    :cond_0
    const/4 p1, 0x0

    :cond_1
    iget v3, p0, Lcom/bytedance/sdk/openadsdk/component/WR/ex;->WR:I

    if-lt v0, v3, :cond_2

    goto :goto_0

    :cond_2
    const/4 v1, 0x0

    :goto_0
    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/component/WR/ex;->hjc:Lcom/bytedance/sdk/openadsdk/component/WR/Fj;

    if-eqz v2, :cond_3

    invoke-interface {v2, p1, v0, v1}, Lcom/bytedance/sdk/openadsdk/component/WR/Fj;->Fj(IIZ)V

    :cond_3
    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/openadsdk/component/WR/Fj;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/WR/ex;->hjc:Lcom/bytedance/sdk/openadsdk/component/WR/Fj;

    return-void
.end method

.method public Ubf()V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/WR/ex;->Fj:Landroid/os/Handler;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Landroid/os/Handler;->removeCallbacksAndMessages(Ljava/lang/Object;)V

    iput-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/WR/ex;->Fj:Landroid/os/Handler;

    return-void
.end method

.method public eV()V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/WR/ex;->Fj:Landroid/os/Handler;

    if-eqz v0, :cond_0

    const/16 v1, 0x64

    invoke-virtual {v0, v1}, Landroid/os/Handler;->removeMessages(I)V

    :cond_0
    return-void
.end method

.method public ex()V
    .locals 4

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/WR/ex;->Fj:Landroid/os/Handler;

    if-eqz v0, :cond_0

    iget v1, p0, Lcom/bytedance/sdk/openadsdk/component/WR/ex;->Ubf:I

    const/4 v2, 0x0

    const/16 v3, 0x64

    invoke-virtual {v0, v3, v1, v2}, Landroid/os/Handler;->obtainMessage(III)Landroid/os/Message;

    move-result-object v1

    invoke-virtual {v0, v1}, Landroid/os/Handler;->sendMessage(Landroid/os/Message;)Z

    :cond_0
    return-void
.end method

.method public ex(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/component/WR/ex;->WR:I

    return-void
.end method

.method public handleMessage(Landroid/os/Message;)Z
    .locals 4
    .param p1    # Landroid/os/Message;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    iget v0, p1, Landroid/os/Message;->what:I

    const/4 v1, 0x1

    const/16 v2, 0x64

    if-ne v0, v2, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/WR/ex;->Fj:Landroid/os/Handler;

    if-eqz v0, :cond_0

    iget p1, p1, Landroid/os/Message;->arg1:I

    invoke-virtual {p0, p1}, Lcom/bytedance/sdk/openadsdk/component/WR/ex;->Fj(I)V

    if-lez p1, :cond_0

    invoke-static {}, Landroid/os/Message;->obtain()Landroid/os/Message;

    move-result-object v0

    iput v2, v0, Landroid/os/Message;->what:I

    sub-int/2addr p1, v1

    iput p1, v0, Landroid/os/Message;->arg1:I

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/WR/ex;->Fj:Landroid/os/Handler;

    const-wide/16 v2, 0x3e8

    invoke-virtual {p1, v0, v2, v3}, Landroid/os/Handler;->sendMessageDelayed(Landroid/os/Message;J)Z

    :cond_0
    return v1
.end method

.method public hjc()V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/WR/ex;->Fj:Landroid/os/Handler;

    if-eqz v0, :cond_0

    invoke-static {}, Landroid/os/Message;->obtain()Landroid/os/Message;

    move-result-object v0

    const/16 v1, 0x64

    iput v1, v0, Landroid/os/Message;->what:I

    iget v1, p0, Lcom/bytedance/sdk/openadsdk/component/WR/ex;->eV:I

    iput v1, v0, Landroid/os/Message;->arg1:I

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/WR/ex;->Fj:Landroid/os/Handler;

    invoke-virtual {v1, v0}, Landroid/os/Handler;->sendMessage(Landroid/os/Message;)Z

    :cond_0
    return-void
.end method
