.class Lcom/bytedance/sdk/openadsdk/component/reward/eV$4;
.super Lcom/bytedance/sdk/component/WR/Fj/Fj;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/reward/eV;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/component/reward/eV$Fj;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/component/reward/eV$Fj;

.field final synthetic ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

.field final synthetic hjc:Lcom/bytedance/sdk/openadsdk/component/reward/eV;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/reward/eV;Lcom/bytedance/sdk/openadsdk/component/reward/eV$Fj;Lcom/bytedance/sdk/openadsdk/core/model/Ql;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/eV$4;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/eV;

    iput-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/eV$4;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/eV$Fj;

    iput-object p3, p0, Lcom/bytedance/sdk/openadsdk/component/reward/eV$4;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-direct {p0}, Lcom/bytedance/sdk/component/WR/Fj/Fj;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj(Lcom/bytedance/sdk/component/WR/ex/hjc;Lcom/bytedance/sdk/component/WR/ex;)V
    .locals 8

    invoke-virtual {p2}, Lcom/bytedance/sdk/component/WR/ex;->WR()Z

    move-result p1

    const/4 v0, 0x0

    if-eqz p1, :cond_1

    invoke-virtual {p2}, Lcom/bytedance/sdk/component/WR/ex;->Ubf()Ljava/io/File;

    move-result-object p1

    if-eqz p1, :cond_1

    invoke-virtual {p2}, Lcom/bytedance/sdk/component/WR/ex;->Ubf()Ljava/io/File;

    move-result-object p1

    invoke-virtual {p1}, Ljava/io/File;->exists()Z

    move-result p1

    if-eqz p1, :cond_1

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/eV$4;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/eV$Fj;

    if-eqz p1, :cond_0

    const/4 v1, 0x1

    invoke-interface {p1, v1, v0}, Lcom/bytedance/sdk/openadsdk/component/reward/eV$Fj;->Fj(ZLjava/lang/Object;)V

    :cond_0
    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/eV$4;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/eV;

    const/4 v3, 0x1

    iget-object v4, p0, Lcom/bytedance/sdk/openadsdk/component/reward/eV$4;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {p2}, Lcom/bytedance/sdk/component/WR/ex;->Fj()I

    move-result p1

    int-to-long v5, p1

    invoke-virtual {p2}, Lcom/bytedance/sdk/component/WR/ex;->ex()Ljava/lang/String;

    move-result-object v7

    invoke-static/range {v2 .. v7}, Lcom/bytedance/sdk/openadsdk/component/reward/eV;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/eV;ZLcom/bytedance/sdk/openadsdk/core/model/Ql;JLjava/lang/String;)V

    return-void

    :cond_1
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/eV$4;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/eV$Fj;

    if-eqz p1, :cond_2

    const/4 v1, 0x0

    invoke-interface {p1, v1, v0}, Lcom/bytedance/sdk/openadsdk/component/reward/eV$Fj;->Fj(ZLjava/lang/Object;)V

    :cond_2
    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/eV$4;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/eV;

    const/4 v3, 0x0

    iget-object v4, p0, Lcom/bytedance/sdk/openadsdk/component/reward/eV$4;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {p2}, Lcom/bytedance/sdk/component/WR/ex;->Fj()I

    move-result p1

    int-to-long v5, p1

    invoke-virtual {p2}, Lcom/bytedance/sdk/component/WR/ex;->ex()Ljava/lang/String;

    move-result-object v7

    invoke-static/range {v2 .. v7}, Lcom/bytedance/sdk/openadsdk/component/reward/eV;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/eV;ZLcom/bytedance/sdk/openadsdk/core/model/Ql;JLjava/lang/String;)V

    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/component/WR/ex/hjc;Ljava/io/IOException;)V
    .locals 8

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/eV$4;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/eV$Fj;

    if-eqz p1, :cond_0

    const/4 v0, 0x0

    const/4 v1, 0x0

    invoke-interface {p1, v0, v1}, Lcom/bytedance/sdk/openadsdk/component/reward/eV$Fj;->Fj(ZLjava/lang/Object;)V

    :cond_0
    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/eV$4;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/eV;

    const/4 v3, 0x0

    iget-object v4, p0, Lcom/bytedance/sdk/openadsdk/component/reward/eV$4;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    const-wide/16 v5, -0x2

    invoke-virtual {p2}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    move-result-object v7

    invoke-static/range {v2 .. v7}, Lcom/bytedance/sdk/openadsdk/component/reward/eV;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/eV;ZLcom/bytedance/sdk/openadsdk/core/model/Ql;JLjava/lang/String;)V

    return-void
.end method
