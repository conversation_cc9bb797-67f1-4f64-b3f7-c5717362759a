.class Lcom/bytedance/sdk/openadsdk/component/reward/Fj/mSE$3$1;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/reward/Fj/mSE$3;->onSystemUiVisibilityChange(I)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/mSE$3;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/mSE$3;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/mSE$3$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/mSE$3;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/mSE$3$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/mSE$3;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/mSE$3;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/mSE;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/mSE;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/mSE;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object v0

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Kk:Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/utils/eh;->Fj(Landroid/app/Activity;)V

    return-void
.end method
