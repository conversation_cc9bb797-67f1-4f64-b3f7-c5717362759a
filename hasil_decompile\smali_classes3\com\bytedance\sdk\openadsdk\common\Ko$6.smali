.class Lcom/bytedance/sdk/openadsdk/common/Ko$6;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/openadsdk/common/TTAdDislikeDialog$Fj;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/common/Ko;->WR()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/common/Ko;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/common/Ko;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/common/Ko$6;->Fj:Lcom/bytedance/sdk/openadsdk/common/Ko;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj(ILcom/bytedance/sdk/openadsdk/FilterWord;)V
    .locals 0

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/common/Ko$6;->Fj:Lcom/bytedance/sdk/openadsdk/common/Ko;

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/common/Ko;->eV:Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-virtual {p1}, Ljava/util/concurrent/atomic/AtomicBoolean;->get()Z

    move-result p1

    if-nez p1, :cond_0

    if-eqz p2, :cond_0

    invoke-virtual {p2}, Lcom/bytedance/sdk/openadsdk/FilterWord;->hasSecondOptions()Z

    move-result p1

    if-nez p1, :cond_0

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/common/Ko$6;->Fj:Lcom/bytedance/sdk/openadsdk/common/Ko;

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/common/Ko;->eV:Ljava/util/concurrent/atomic/AtomicBoolean;

    const/4 p2, 0x1

    invoke-virtual {p1, p2}, Ljava/util/concurrent/atomic/AtomicBoolean;->set(Z)V

    :cond_0
    return-void
.end method

.method public Fj(Landroid/view/View;)V
    .locals 1

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/common/Ko$6;->Fj:Lcom/bytedance/sdk/openadsdk/common/Ko;

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/common/Ko;->hjc:Ljava/util/concurrent/atomic/AtomicBoolean;

    const/4 v0, 0x1

    invoke-virtual {p1, v0}, Ljava/util/concurrent/atomic/AtomicBoolean;->set(Z)V

    return-void
.end method

.method public ex(Landroid/view/View;)V
    .locals 1

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/common/Ko$6;->Fj:Lcom/bytedance/sdk/openadsdk/common/Ko;

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/common/Ko;->hjc:Ljava/util/concurrent/atomic/AtomicBoolean;

    const/4 v0, 0x0

    invoke-virtual {p1, v0}, Ljava/util/concurrent/atomic/AtomicBoolean;->set(Z)V

    return-void
.end method
