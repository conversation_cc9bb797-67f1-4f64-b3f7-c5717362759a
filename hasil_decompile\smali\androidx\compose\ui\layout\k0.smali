.class public abstract Landroidx/compose/ui/layout/k0;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/compose/ui/layout/a0;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/compose/ui/layout/k0$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# instance fields
.field public a:I

.field public b:I

.field public c:J

.field public d:J

.field public e:J


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>()V
    .locals 2

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    invoke-static {v0, v0}, Lv0/u;->a(II)J

    move-result-wide v0

    iput-wide v0, p0, Landroidx/compose/ui/layout/k0;->c:J

    invoke-static {}, Landroidx/compose/ui/layout/PlaceableKt;->c()J

    move-result-wide v0

    iput-wide v0, p0, Landroidx/compose/ui/layout/k0;->d:J

    sget-object v0, Lv0/p;->b:Lv0/p$a;

    invoke-virtual {v0}, Lv0/p$a;->a()J

    move-result-wide v0

    iput-wide v0, p0, Landroidx/compose/ui/layout/k0;->e:J

    return-void
.end method

.method public static final synthetic b0(Landroidx/compose/ui/layout/k0;)J
    .locals 2

    iget-wide v0, p0, Landroidx/compose/ui/layout/k0;->e:J

    return-wide v0
.end method

.method public static final synthetic d0(Landroidx/compose/ui/layout/k0;JFLandroidx/compose/ui/graphics/layer/GraphicsLayer;)V
    .locals 0

    invoke-virtual {p0, p1, p2, p3, p4}, Landroidx/compose/ui/layout/k0;->x0(JFLandroidx/compose/ui/graphics/layer/GraphicsLayer;)V

    return-void
.end method

.method public static final synthetic e0(Landroidx/compose/ui/layout/k0;JFLkotlin/jvm/functions/Function1;)V
    .locals 0

    invoke-virtual {p0, p1, p2, p3, p4}, Landroidx/compose/ui/layout/k0;->A0(JFLkotlin/jvm/functions/Function1;)V

    return-void
.end method


# virtual methods
.method public abstract A0(JFLkotlin/jvm/functions/Function1;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(JF",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Landroidx/compose/ui/graphics/e4;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation
.end method

.method public final C0(J)V
    .locals 2

    iget-wide v0, p0, Landroidx/compose/ui/layout/k0;->c:J

    invoke-static {v0, v1, p1, p2}, Lv0/t;->e(JJ)Z

    move-result v0

    if-nez v0, :cond_0

    iput-wide p1, p0, Landroidx/compose/ui/layout/k0;->c:J

    invoke-virtual {p0}, Landroidx/compose/ui/layout/k0;->u0()V

    :cond_0
    return-void
.end method

.method public final D0(J)V
    .locals 2

    iget-wide v0, p0, Landroidx/compose/ui/layout/k0;->d:J

    invoke-static {v0, v1, p1, p2}, Lv0/b;->f(JJ)Z

    move-result v0

    if-nez v0, :cond_0

    iput-wide p1, p0, Landroidx/compose/ui/layout/k0;->d:J

    invoke-virtual {p0}, Landroidx/compose/ui/layout/k0;->u0()V

    :cond_0
    return-void
.end method

.method public synthetic G()Ljava/lang/Object;
    .locals 1

    invoke-static {p0}, Landroidx/compose/ui/layout/z;->a(Landroidx/compose/ui/layout/a0;)Ljava/lang/Object;

    move-result-object v0

    return-object v0
.end method

.method public final g0()J
    .locals 2

    iget-wide v0, p0, Landroidx/compose/ui/layout/k0;->e:J

    return-wide v0
.end method

.method public final h0()I
    .locals 1

    iget v0, p0, Landroidx/compose/ui/layout/k0;->b:I

    return v0
.end method

.method public o0()I
    .locals 2

    iget-wide v0, p0, Landroidx/compose/ui/layout/k0;->c:J

    invoke-static {v0, v1}, Lv0/t;->f(J)I

    move-result v0

    return v0
.end method

.method public final q0()J
    .locals 2

    iget-wide v0, p0, Landroidx/compose/ui/layout/k0;->c:J

    return-wide v0
.end method

.method public r0()I
    .locals 2

    iget-wide v0, p0, Landroidx/compose/ui/layout/k0;->c:J

    invoke-static {v0, v1}, Lv0/t;->g(J)I

    move-result v0

    return v0
.end method

.method public final s0()J
    .locals 2

    iget-wide v0, p0, Landroidx/compose/ui/layout/k0;->d:J

    return-wide v0
.end method

.method public final t0()I
    .locals 1

    iget v0, p0, Landroidx/compose/ui/layout/k0;->a:I

    return v0
.end method

.method public final u0()V
    .locals 4

    iget-wide v0, p0, Landroidx/compose/ui/layout/k0;->c:J

    invoke-static {v0, v1}, Lv0/t;->g(J)I

    move-result v0

    iget-wide v1, p0, Landroidx/compose/ui/layout/k0;->d:J

    invoke-static {v1, v2}, Lv0/b;->n(J)I

    move-result v1

    iget-wide v2, p0, Landroidx/compose/ui/layout/k0;->d:J

    invoke-static {v2, v3}, Lv0/b;->l(J)I

    move-result v2

    invoke-static {v0, v1, v2}, Lkotlin/ranges/RangesKt;->k(III)I

    move-result v0

    iput v0, p0, Landroidx/compose/ui/layout/k0;->a:I

    iget-wide v0, p0, Landroidx/compose/ui/layout/k0;->c:J

    invoke-static {v0, v1}, Lv0/t;->f(J)I

    move-result v0

    iget-wide v1, p0, Landroidx/compose/ui/layout/k0;->d:J

    invoke-static {v1, v2}, Lv0/b;->m(J)I

    move-result v1

    iget-wide v2, p0, Landroidx/compose/ui/layout/k0;->d:J

    invoke-static {v2, v3}, Lv0/b;->k(J)I

    move-result v2

    invoke-static {v0, v1, v2}, Lkotlin/ranges/RangesKt;->k(III)I

    move-result v0

    iput v0, p0, Landroidx/compose/ui/layout/k0;->b:I

    iget v0, p0, Landroidx/compose/ui/layout/k0;->a:I

    iget-wide v1, p0, Landroidx/compose/ui/layout/k0;->c:J

    invoke-static {v1, v2}, Lv0/t;->g(J)I

    move-result v1

    sub-int/2addr v0, v1

    div-int/lit8 v0, v0, 0x2

    iget v1, p0, Landroidx/compose/ui/layout/k0;->b:I

    iget-wide v2, p0, Landroidx/compose/ui/layout/k0;->c:J

    invoke-static {v2, v3}, Lv0/t;->f(J)I

    move-result v2

    sub-int/2addr v1, v2

    div-int/lit8 v1, v1, 0x2

    invoke-static {v0, v1}, Lv0/q;->a(II)J

    move-result-wide v0

    iput-wide v0, p0, Landroidx/compose/ui/layout/k0;->e:J

    return-void
.end method

.method public x0(JFLandroidx/compose/ui/graphics/layer/GraphicsLayer;)V
    .locals 0

    const/4 p4, 0x0

    invoke-virtual {p0, p1, p2, p3, p4}, Landroidx/compose/ui/layout/k0;->A0(JFLkotlin/jvm/functions/Function1;)V

    return-void
.end method
