.class public Lcom/bytedance/sdk/openadsdk/component/svN;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/component/utils/Vq$Fj;


# instance fields
.field private BcC:I

.field private final Fj:Landroid/content/Context;

.field private final Ko:Lcom/bytedance/sdk/openadsdk/core/model/mC;

.field private Ubf:I

.field private WR:Lcom/bytedance/sdk/openadsdk/AdSlot;

.field private final eV:Ljava/util/concurrent/atomic/AtomicBoolean;

.field private final ex:Lcom/bytedance/sdk/openadsdk/core/Ql;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bytedance/sdk/openadsdk/core/Ql<",
            "Lcom/bytedance/sdk/openadsdk/ex/Fj;",
            ">;"
        }
    .end annotation
.end field

.field private final hjc:Lcom/bytedance/sdk/openadsdk/component/WR;

.field private volatile mSE:I

.field private rAx:Z

.field private svN:Lcom/bytedance/sdk/openadsdk/api/open/PAGAppOpenAdLoadListener;


# direct methods
.method public constructor <init>(Landroid/content/Context;)V
    .locals 2

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Ljava/util/concurrent/atomic/AtomicBoolean;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Ljava/util/concurrent/atomic/AtomicBoolean;-><init>(Z)V

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/svN;->eV:Ljava/util/concurrent/atomic/AtomicBoolean;

    iput v1, p0, Lcom/bytedance/sdk/openadsdk/component/svN;->Ubf:I

    iput v1, p0, Lcom/bytedance/sdk/openadsdk/component/svN;->mSE:I

    new-instance v0, Lcom/bytedance/sdk/openadsdk/core/model/mC;

    invoke-direct {v0}, Lcom/bytedance/sdk/openadsdk/core/model/mC;-><init>()V

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/svN;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/mC;

    if-eqz p1, :cond_0

    invoke-virtual {p1}, Landroid/content/Context;->getApplicationContext()Landroid/content/Context;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/svN;->Fj:Landroid/content/Context;

    goto :goto_0

    :cond_0
    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/JU;->Fj()Landroid/content/Context;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/svN;->Fj:Landroid/content/Context;

    :goto_0
    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/JU;->hjc()Lcom/bytedance/sdk/openadsdk/core/Ql;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/svN;->ex:Lcom/bytedance/sdk/openadsdk/core/Ql;

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/svN;->Fj:Landroid/content/Context;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/WR;->Fj(Landroid/content/Context;)Lcom/bytedance/sdk/openadsdk/component/WR;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/svN;->hjc:Lcom/bytedance/sdk/openadsdk/component/WR;

    return-void
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/openadsdk/component/svN;I)I
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/component/svN;->mSE:I

    return p1
.end method

.method public static Fj(Landroid/content/Context;)Lcom/bytedance/sdk/openadsdk/component/svN;
    .locals 1

    new-instance v0, Lcom/bytedance/sdk/openadsdk/component/svN;

    invoke-direct {v0, p0}, Lcom/bytedance/sdk/openadsdk/component/svN;-><init>(Landroid/content/Context;)V

    return-object v0
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/openadsdk/component/svN;)Lcom/bytedance/sdk/openadsdk/core/model/mC;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/openadsdk/component/svN;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/mC;

    return-object p0
.end method

.method private Fj()V
    .locals 2

    new-instance v0, Lcom/bytedance/sdk/openadsdk/component/svN$2;

    const-string v1, "tryGetAppOpenAdFromCache"

    invoke-direct {v0, p0, v1}, Lcom/bytedance/sdk/openadsdk/component/svN$2;-><init>(Lcom/bytedance/sdk/openadsdk/component/svN;Ljava/lang/String;)V

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/utils/uy;->ex(Lcom/bytedance/sdk/component/svN/BcC;)V

    return-void
.end method

.method private Fj(Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;)V
    .locals 10

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;->ex()I

    move-result v0

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;->hjc()I

    move-result v1

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/component/svN;->eV:Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-virtual {v2}, Ljava/util/concurrent/atomic/AtomicBoolean;->get()Z

    move-result v2

    const/16 v3, 0x64

    const/4 v4, 0x1

    if-eqz v2, :cond_1

    if-ne v0, v4, :cond_0

    if-ne v1, v3, :cond_0

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;->Fj()Z

    move-result v0

    if-eqz v0, :cond_0

    new-instance v0, Lcom/bytedance/sdk/openadsdk/component/Ubf/Fj;

    iget v1, p0, Lcom/bytedance/sdk/openadsdk/component/svN;->Ubf:I

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;->eV()Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    move-result-object v2

    invoke-direct {v0, v1, v2}, Lcom/bytedance/sdk/openadsdk/component/Ubf/Fj;-><init>(ILcom/bytedance/sdk/openadsdk/core/model/Ql;)V

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/JU;->Fj()Landroid/content/Context;

    move-result-object v1

    invoke-static {v1}, Lcom/bytedance/sdk/openadsdk/component/WR;->Fj(Landroid/content/Context;)Lcom/bytedance/sdk/openadsdk/component/WR;

    move-result-object v1

    invoke-virtual {v1, v0}, Lcom/bytedance/sdk/openadsdk/component/WR;->Fj(Lcom/bytedance/sdk/openadsdk/component/Ubf/Fj;)V

    iget-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/component/svN;->rAx:Z

    if-nez v0, :cond_0

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;->eV()Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    move-result-object p1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/svN;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/mC;

    invoke-static {p1, v4, v0}, Lcom/bytedance/sdk/openadsdk/component/eV/Fj;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;ILcom/bytedance/sdk/openadsdk/core/model/mC;)V

    :cond_0
    return-void

    :cond_1
    if-ne v0, v4, :cond_6

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/svN;->svN:Lcom/bytedance/sdk/openadsdk/api/open/PAGAppOpenAdLoadListener;

    const/4 v2, 0x0

    const/16 v5, 0x65

    if-eqz v0, :cond_3

    new-instance v0, Lcom/bytedance/sdk/openadsdk/component/eV;

    iget-object v6, p0, Lcom/bytedance/sdk/openadsdk/component/svN;->Fj:Landroid/content/Context;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;->eV()Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    move-result-object v7

    if-ne v1, v5, :cond_2

    const/4 v8, 0x1

    goto :goto_0

    :cond_2
    const/4 v8, 0x0

    :goto_0
    iget-object v9, p0, Lcom/bytedance/sdk/openadsdk/component/svN;->WR:Lcom/bytedance/sdk/openadsdk/AdSlot;

    invoke-direct {v0, v6, v7, v8, v9}, Lcom/bytedance/sdk/openadsdk/component/eV;-><init>(Landroid/content/Context;Lcom/bytedance/sdk/openadsdk/core/model/Ql;ZLcom/bytedance/sdk/openadsdk/AdSlot;)V

    iget-object v6, p0, Lcom/bytedance/sdk/openadsdk/component/svN;->svN:Lcom/bytedance/sdk/openadsdk/api/open/PAGAppOpenAdLoadListener;

    invoke-interface {v6, v0}, Lcom/bytedance/sdk/openadsdk/api/PAGLoadListener;->onAdLoaded(Ljava/lang/Object;)V

    :cond_3
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/svN;->eV:Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-virtual {v0, v4}, Ljava/util/concurrent/atomic/AtomicBoolean;->set(Z)V

    if-ne v1, v5, :cond_4

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;->eV()Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    move-result-object p1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/svN;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/mC;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/mC;->Fj()Lcom/bytedance/sdk/openadsdk/utils/lv;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/utils/lv;->hjc()J

    move-result-wide v0

    invoke-static {p1, v0, v1}, Lcom/bytedance/sdk/openadsdk/component/eV/Fj;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;J)V

    return-void

    :cond_4
    if-ne v1, v3, :cond_9

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;->eV()Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/svN;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/mC;

    invoke-static {v0, v2, v1}, Lcom/bytedance/sdk/openadsdk/component/eV/Fj;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;ILcom/bytedance/sdk/openadsdk/core/model/mC;)V

    iput-boolean v4, p0, Lcom/bytedance/sdk/openadsdk/component/svN;->rAx:Z

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/svN;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/mC;

    iget-boolean v0, v0, Lcom/bytedance/sdk/openadsdk/core/model/mC;->Fj:Z

    if-nez v0, :cond_9

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/settings/JW;->Ud()Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/svN;->WR:Lcom/bytedance/sdk/openadsdk/AdSlot;

    invoke-virtual {v1}, Lcom/bytedance/sdk/openadsdk/AdSlot;->getCodeId()Ljava/lang/String;

    move-result-object v1

    invoke-interface {v0, v1}, Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;->nsB(Ljava/lang/String;)I

    move-result v0

    if-nez v0, :cond_5

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/svN;->hjc:Lcom/bytedance/sdk/openadsdk/component/WR;

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/svN;->WR:Lcom/bytedance/sdk/openadsdk/AdSlot;

    invoke-virtual {p1, v0}, Lcom/bytedance/sdk/openadsdk/component/WR;->Fj(Lcom/bytedance/sdk/openadsdk/AdSlot;)V

    return-void

    :cond_5
    new-instance v0, Lcom/bytedance/sdk/openadsdk/component/Ubf/Fj;

    iget v1, p0, Lcom/bytedance/sdk/openadsdk/component/svN;->Ubf:I

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;->eV()Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    move-result-object p1

    invoke-direct {v0, v1, p1}, Lcom/bytedance/sdk/openadsdk/component/Ubf/Fj;-><init>(ILcom/bytedance/sdk/openadsdk/core/model/Ql;)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/svN;->hjc:Lcom/bytedance/sdk/openadsdk/component/WR;

    invoke-virtual {p1, v0}, Lcom/bytedance/sdk/openadsdk/component/WR;->Fj(Lcom/bytedance/sdk/openadsdk/component/Ubf/Fj;)V

    return-void

    :cond_6
    const/4 v1, 0x2

    const/4 v2, 0x3

    if-eq v0, v1, :cond_7

    if-ne v0, v2, :cond_9

    :cond_7
    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/svN;->svN:Lcom/bytedance/sdk/openadsdk/api/open/PAGAppOpenAdLoadListener;

    if-eqz v1, :cond_8

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;->Ubf()I

    move-result v3

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;->WR()Ljava/lang/String;

    move-result-object p1

    invoke-interface {v1, v3, p1}, Lcom/bytedance/sdk/openadsdk/api/PAGLoadListener;->onError(ILjava/lang/String;)V

    :cond_8
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/svN;->eV:Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-virtual {p1, v4}, Ljava/util/concurrent/atomic/AtomicBoolean;->set(Z)V

    if-ne v0, v2, :cond_9

    iget p1, p0, Lcom/bytedance/sdk/openadsdk/component/svN;->mSE:I

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/component/svN;->BcC:I

    invoke-static {p1, v0}, Lcom/bytedance/sdk/openadsdk/component/eV/Fj;->Fj(II)V

    :cond_9
    return-void
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/openadsdk/component/svN;Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;)V
    .locals 0

    invoke-direct {p0, p1}, Lcom/bytedance/sdk/openadsdk/component/svN;->Fj(Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;)V

    return-void
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/openadsdk/component/svN;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/AdSlot;Z)V
    .locals 0

    invoke-direct {p0, p1, p2, p3}, Lcom/bytedance/sdk/openadsdk/component/svN;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/AdSlot;Z)V

    return-void
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/openadsdk/component/svN;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Z)V
    .locals 0

    invoke-direct {p0, p1, p2}, Lcom/bytedance/sdk/openadsdk/component/svN;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Z)V

    return-void
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/openadsdk/component/svN;Z)V
    .locals 0

    invoke-direct {p0, p1}, Lcom/bytedance/sdk/openadsdk/component/svN;->Fj(Z)V

    return-void
.end method

.method private Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/AdSlot;Z)V
    .locals 3
    .param p1    # Lcom/bytedance/sdk/openadsdk/core/model/Ql;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/svN;->hjc:Lcom/bytedance/sdk/openadsdk/component/WR;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/svN;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/mC;

    new-instance v2, Lcom/bytedance/sdk/openadsdk/component/svN$3;

    invoke-direct {v2, p0, p3, p1}, Lcom/bytedance/sdk/openadsdk/component/svN$3;-><init>(Lcom/bytedance/sdk/openadsdk/component/svN;ZLcom/bytedance/sdk/openadsdk/core/model/Ql;)V

    invoke-virtual {v0, p1, p2, v1, v2}, Lcom/bytedance/sdk/openadsdk/component/WR;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/AdSlot;Lcom/bytedance/sdk/openadsdk/core/model/mC;Lcom/bytedance/sdk/openadsdk/component/WR$hjc;)V

    return-void
.end method

.method private Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Z)V
    .locals 3
    .param p1    # Lcom/bytedance/sdk/openadsdk/core/model/Ql;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/svN;->hjc:Lcom/bytedance/sdk/openadsdk/component/WR;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/svN;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/mC;

    new-instance v2, Lcom/bytedance/sdk/openadsdk/component/svN$4;

    invoke-direct {v2, p0, p2, p1}, Lcom/bytedance/sdk/openadsdk/component/svN$4;-><init>(Lcom/bytedance/sdk/openadsdk/component/svN;ZLcom/bytedance/sdk/openadsdk/core/model/Ql;)V

    invoke-virtual {v0, p1, v1, v2}, Lcom/bytedance/sdk/openadsdk/component/WR;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/core/model/mC;Lcom/bytedance/sdk/openadsdk/component/WR$ex;)V

    return-void
.end method

.method private Fj(Z)V
    .locals 1

    if-eqz p1, :cond_0

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/svN;->hjc:Lcom/bytedance/sdk/openadsdk/component/WR;

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/component/svN;->Ubf:I

    invoke-virtual {p1, v0}, Lcom/bytedance/sdk/openadsdk/component/WR;->svN(I)V

    :cond_0
    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/settings/JW;->Ud()Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;

    move-result-object p1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/svN;->WR:Lcom/bytedance/sdk/openadsdk/AdSlot;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/AdSlot;->getCodeId()Ljava/lang/String;

    move-result-object v0

    invoke-interface {p1, v0}, Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;->nsB(Ljava/lang/String;)I

    move-result p1

    const/4 v0, 0x1

    if-ne p1, v0, :cond_1

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/svN;->WR:Lcom/bytedance/sdk/openadsdk/AdSlot;

    invoke-direct {p0, p1}, Lcom/bytedance/sdk/openadsdk/component/svN;->ex(Lcom/bytedance/sdk/openadsdk/AdSlot;)V

    :cond_1
    return-void
.end method

.method public static synthetic eV(Lcom/bytedance/sdk/openadsdk/component/svN;)Lcom/bytedance/sdk/openadsdk/AdSlot;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/openadsdk/component/svN;->WR:Lcom/bytedance/sdk/openadsdk/AdSlot;

    return-object p0
.end method

.method public static synthetic ex(Lcom/bytedance/sdk/openadsdk/component/svN;)I
    .locals 0

    iget p0, p0, Lcom/bytedance/sdk/openadsdk/component/svN;->Ubf:I

    return p0
.end method

.method private ex(Lcom/bytedance/sdk/openadsdk/AdSlot;)V
    .locals 4
    .param p1    # Lcom/bytedance/sdk/openadsdk/AdSlot;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/utils/lv;->Fj()Lcom/bytedance/sdk/openadsdk/utils/lv;

    move-result-object v0

    const/4 v1, 0x1

    iput v1, p0, Lcom/bytedance/sdk/openadsdk/component/svN;->mSE:I

    new-instance v2, Lcom/bytedance/sdk/openadsdk/core/model/vYf;

    invoke-direct {v2}, Lcom/bytedance/sdk/openadsdk/core/model/vYf;-><init>()V

    iget-object v3, p0, Lcom/bytedance/sdk/openadsdk/component/svN;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/mC;

    iput-object v3, v2, Lcom/bytedance/sdk/openadsdk/core/model/vYf;->BcC:Lcom/bytedance/sdk/openadsdk/core/model/mC;

    iput v1, v2, Lcom/bytedance/sdk/openadsdk/core/model/vYf;->eV:I

    const/4 v1, 0x2

    iput v1, v2, Lcom/bytedance/sdk/openadsdk/core/model/vYf;->WR:I

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/svN;->ex:Lcom/bytedance/sdk/openadsdk/core/Ql;

    new-instance v3, Lcom/bytedance/sdk/openadsdk/component/svN$1;

    invoke-direct {v3, p0, p1, v0}, Lcom/bytedance/sdk/openadsdk/component/svN$1;-><init>(Lcom/bytedance/sdk/openadsdk/component/svN;Lcom/bytedance/sdk/openadsdk/AdSlot;Lcom/bytedance/sdk/openadsdk/utils/lv;)V

    const/4 v0, 0x3

    invoke-interface {v1, p1, v2, v0, v3}, Lcom/bytedance/sdk/openadsdk/core/Ql;->Fj(Lcom/bytedance/sdk/openadsdk/AdSlot;Lcom/bytedance/sdk/openadsdk/core/model/vYf;ILcom/bytedance/sdk/openadsdk/core/Ql$Fj;)V

    return-void
.end method

.method public static synthetic hjc(Lcom/bytedance/sdk/openadsdk/component/svN;)Lcom/bytedance/sdk/openadsdk/component/WR;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/openadsdk/component/svN;->hjc:Lcom/bytedance/sdk/openadsdk/component/WR;

    return-object p0
.end method


# virtual methods
.method public Fj(Lcom/bytedance/sdk/openadsdk/AdSlot;)I
    .locals 0
    .param p1    # Lcom/bytedance/sdk/openadsdk/AdSlot;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    :try_start_0
    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/AdSlot;->getCodeId()Ljava/lang/String;

    move-result-object p1

    invoke-static {p1}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result p1
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_0

    :catchall_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public Fj(Landroid/os/Message;)V
    .locals 4

    iget p1, p1, Landroid/os/Message;->what:I

    const/4 v0, 0x1

    if-ne p1, v0, :cond_1

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/svN;->eV:Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-virtual {p1}, Ljava/util/concurrent/atomic/AtomicBoolean;->get()Z

    move-result p1

    if-eqz p1, :cond_0

    return-void

    :cond_0
    new-instance p1, Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;

    const/16 v0, 0x66

    const/16 v1, 0x2712

    invoke-static {v1}, Lcom/bytedance/sdk/openadsdk/core/svN;->Fj(I)Ljava/lang/String;

    move-result-object v2

    const/4 v3, 0x3

    invoke-direct {p1, v3, v0, v1, v2}, Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;-><init>(IIILjava/lang/String;)V

    invoke-direct {p0, p1}, Lcom/bytedance/sdk/openadsdk/component/svN;->Fj(Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;)V

    :cond_1
    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/openadsdk/AdSlot;Lcom/bytedance/sdk/openadsdk/common/hjc;I)V
    .locals 2
    .param p1    # Lcom/bytedance/sdk/openadsdk/AdSlot;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    if-nez p2, :cond_0

    return-void

    :cond_0
    if-gtz p3, :cond_1

    const/16 p3, 0xdac

    :cond_1
    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/svN;->WR:Lcom/bytedance/sdk/openadsdk/AdSlot;

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/svN;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/mC;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/AdSlot;->getBidAdm()Ljava/lang/String;

    move-result-object p1

    invoke-static {p1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result p1

    const/4 v1, 0x1

    xor-int/2addr p1, v1

    iput-boolean p1, v0, Lcom/bytedance/sdk/openadsdk/core/model/mC;->Fj:Z

    instance-of p1, p2, Lcom/bytedance/sdk/openadsdk/api/open/PAGAppOpenAdLoadListener;

    if-eqz p1, :cond_2

    check-cast p2, Lcom/bytedance/sdk/openadsdk/api/open/PAGAppOpenAdLoadListener;

    iput-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/svN;->svN:Lcom/bytedance/sdk/openadsdk/api/open/PAGAppOpenAdLoadListener;

    :cond_2
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/svN;->WR:Lcom/bytedance/sdk/openadsdk/AdSlot;

    invoke-virtual {p0, p1}, Lcom/bytedance/sdk/openadsdk/component/svN;->Fj(Lcom/bytedance/sdk/openadsdk/AdSlot;)I

    move-result p1

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/component/svN;->Ubf:I

    iput p3, p0, Lcom/bytedance/sdk/openadsdk/component/svN;->BcC:I

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/svN;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/mC;

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/utils/lv;->Fj()Lcom/bytedance/sdk/openadsdk/utils/lv;

    move-result-object p2

    invoke-virtual {p1, p2}, Lcom/bytedance/sdk/openadsdk/core/model/mC;->Fj(Lcom/bytedance/sdk/openadsdk/utils/lv;)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/svN;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/mC;

    iget-boolean p1, p1, Lcom/bytedance/sdk/openadsdk/core/model/mC;->Fj:Z

    if-nez p1, :cond_3

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/settings/JW;->Ud()Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;

    move-result-object p1

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/svN;->WR:Lcom/bytedance/sdk/openadsdk/AdSlot;

    invoke-virtual {p2}, Lcom/bytedance/sdk/openadsdk/AdSlot;->getCodeId()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;->nsB(Ljava/lang/String;)I

    move-result p1

    if-nez p1, :cond_4

    :cond_3
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/svN;->WR:Lcom/bytedance/sdk/openadsdk/AdSlot;

    invoke-direct {p0, p1}, Lcom/bytedance/sdk/openadsdk/component/svN;->ex(Lcom/bytedance/sdk/openadsdk/AdSlot;)V

    :cond_4
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/svN;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/mC;

    iget-boolean p1, p1, Lcom/bytedance/sdk/openadsdk/core/model/mC;->Fj:Z

    if-nez p1, :cond_5

    new-instance p1, Lcom/bytedance/sdk/component/utils/Vq;

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/dG;->ex()Landroid/os/Handler;

    move-result-object p2

    invoke-virtual {p2}, Landroid/os/Handler;->getLooper()Landroid/os/Looper;

    move-result-object p2

    invoke-direct {p1, p2, p0}, Lcom/bytedance/sdk/component/utils/Vq;-><init>(Landroid/os/Looper;Lcom/bytedance/sdk/component/utils/Vq$Fj;)V

    int-to-long p2, p3

    invoke-virtual {p1, v1, p2, p3}, Landroid/os/Handler;->sendEmptyMessageDelayed(IJ)Z

    invoke-direct {p0}, Lcom/bytedance/sdk/openadsdk/component/svN;->Fj()V

    :cond_5
    return-void
.end method
