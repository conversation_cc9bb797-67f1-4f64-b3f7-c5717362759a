.class Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$1;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/openadsdk/core/nativeexpress/UYd;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;->Fj([FLcom/bytedance/sdk/openadsdk/core/video/hjc/ex;Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/core/video/hjc/ex;

.field final synthetic ex:Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;

.field final synthetic hjc:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;Lcom/bytedance/sdk/openadsdk/core/video/hjc/ex;Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;

    iput-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$1;->Fj:Lcom/bytedance/sdk/openadsdk/core/video/hjc/ex;

    iput-object p3, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$1;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj()V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object v0

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->gci:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Ko;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Ko;->eV()V

    return-void
.end method

.method public Fj(I)V
    .locals 3

    const/4 v0, 0x1

    if-eq p1, v0, :cond_3

    const/4 v0, 0x2

    if-eq p1, v0, :cond_2

    const/4 v0, 0x3

    if-eq p1, v0, :cond_1

    const/4 v0, 0x4

    if-eq p1, v0, :cond_0

    const/4 v0, 0x5

    if-eq p1, v0, :cond_3

    goto :goto_0

    :cond_0
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object p1

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->kF:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->UYd()V

    :goto_0
    return-void

    :cond_1
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object p1

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->kF:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$1;->Fj:Lcom/bytedance/sdk/openadsdk/core/video/hjc/ex;

    invoke-virtual {p1, v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Fj(Lcom/bytedance/sdk/openadsdk/core/video/hjc/ex;)V

    return-void

    :cond_2
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object p1

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->kF:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Vq()V

    return-void

    :cond_3
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object p1

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->kF:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->ex()Z

    move-result p1

    if-nez p1, :cond_5

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object p1

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->kF:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->eV()Z

    move-result p1

    if-eqz p1, :cond_4

    goto :goto_1

    :cond_4
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$1;->Fj:Lcom/bytedance/sdk/openadsdk/core/video/hjc/ex;

    const-wide/16 v0, 0x0

    const/4 v2, 0x0

    invoke-interface {p1, v0, v1, v2}, Lcom/bytedance/sdk/openadsdk/core/video/hjc/ex;->Fj(JZ)Z

    :cond_5
    :goto_1
    return-void
.end method

.method public Fj(ILjava/lang/String;)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object v0

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->kF:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;

    invoke-virtual {v0, p1, p2}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Fj(ILjava/lang/String;)V

    return-void
.end method

.method public Fj(Z)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object v0

    iget-boolean v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->eV:Z

    if-eq v0, p1, :cond_0

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object p1

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->gci:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Ko;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Ko;->Ubf()V

    :cond_0
    return-void
.end method

.method public Ubf()V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object v0

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->KZ:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$1;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;)V

    return-void
.end method

.method public eV()I
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object v0

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->spi:Lcom/bytedance/sdk/openadsdk/component/reward/view/hjc;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/view/hjc;->hjc()Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 v0, 0x4

    return v0

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object v0

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->spi:Lcom/bytedance/sdk/openadsdk/component/reward/view/hjc;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/view/hjc;->eV()Z

    move-result v0

    if-eqz v0, :cond_1

    const/4 v0, 0x5

    return v0

    :cond_1
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object v0

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->kF:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Ubf()Z

    move-result v0

    if-eqz v0, :cond_2

    const/4 v0, 0x1

    return v0

    :cond_2
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object v0

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->kF:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->ex()Z

    move-result v0

    if-eqz v0, :cond_3

    const/4 v0, 0x2

    return v0

    :cond_3
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object v0

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->kF:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->eV()Z

    const/4 v0, 0x3

    return v0
.end method

.method public ex()V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object v0

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Obv:Lcom/bytedance/sdk/openadsdk/component/reward/view/Ubf;

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object v0

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Obv:Lcom/bytedance/sdk/openadsdk/component/reward/view/Ubf;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/view/Ubf;->Ko()Landroid/view/View;

    move-result-object v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object v0

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Obv:Lcom/bytedance/sdk/openadsdk/component/reward/view/Ubf;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/view/Ubf;->Ko()Landroid/view/View;

    move-result-object v0

    invoke-virtual {v0}, Landroid/view/View;->performClick()Z

    :cond_0
    return-void
.end method

.method public ex(I)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object v0

    iput p1, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->BcC:I

    return-void
.end method

.method public hjc()J
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object v0

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->kF:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->BcC()J

    move-result-wide v0

    return-wide v0
.end method
