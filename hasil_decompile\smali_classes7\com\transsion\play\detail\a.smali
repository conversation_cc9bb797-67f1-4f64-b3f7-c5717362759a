.class public final synthetic Lcom/transsion/play/detail/a;
.super Ljava/lang/Object;

# interfaces
.implements Landroid/view/View$OnClickListener;


# instance fields
.field public final synthetic a:I

.field public final synthetic b:Lcom/transsion/play/detail/PlayDetailBottomRecHelper;

.field public final synthetic c:Lcom/transsion/play/detail/widget/PlayDetailResTabTitleView;

.field public final synthetic d:Lcom/transsion/play/detail/PlayDetailBottomRecHelper$b;


# direct methods
.method public synthetic constructor <init>(ILcom/transsion/play/detail/PlayDetailBottomRecHelper;Lcom/transsion/play/detail/widget/PlayDetailResTabTitleView;Lcom/transsion/play/detail/PlayDetailBottomRecHelper$b;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput p1, p0, Lcom/transsion/play/detail/a;->a:I

    iput-object p2, p0, Lcom/transsion/play/detail/a;->b:Lcom/transsion/play/detail/PlayDetailBottomRecHelper;

    iput-object p3, p0, Lcom/transsion/play/detail/a;->c:Lcom/transsion/play/detail/widget/PlayDetailResTabTitleView;

    iput-object p4, p0, Lcom/transsion/play/detail/a;->d:Lcom/transsion/play/detail/PlayDetailBottomRecHelper$b;

    return-void
.end method


# virtual methods
.method public final onClick(Landroid/view/View;)V
    .locals 4

    iget v0, p0, Lcom/transsion/play/detail/a;->a:I

    iget-object v1, p0, Lcom/transsion/play/detail/a;->b:Lcom/transsion/play/detail/PlayDetailBottomRecHelper;

    iget-object v2, p0, Lcom/transsion/play/detail/a;->c:Lcom/transsion/play/detail/widget/PlayDetailResTabTitleView;

    iget-object v3, p0, Lcom/transsion/play/detail/a;->d:Lcom/transsion/play/detail/PlayDetailBottomRecHelper$b;

    invoke-static {v0, v1, v2, v3, p1}, Lcom/transsion/play/detail/PlayDetailBottomRecHelper$b;->i(ILcom/transsion/play/detail/PlayDetailBottomRecHelper;Lcom/transsion/play/detail/widget/PlayDetailResTabTitleView;Lcom/transsion/play/detail/PlayDetailBottomRecHelper$b;Landroid/view/View;)V

    return-void
.end method
