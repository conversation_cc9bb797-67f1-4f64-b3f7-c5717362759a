.class Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql$1;
.super Lcom/bytedance/sdk/component/svN/BcC;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql$1;->Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;

    invoke-direct {p0, p2}, Lcom/bytedance/sdk/component/svN/BcC;-><init>(Ljava/lang/String;)V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql$1;->Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->Fj(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;)Ljava/util/concurrent/atomic/AtomicBoolean;

    move-result-object v0

    invoke-virtual {v0}, Ljava/util/concurrent/atomic/AtomicBoolean;->get()Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql$1;->Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->ex(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;)Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    move-result-object v0

    instance-of v0, v0, Lcom/bytedance/sdk/openadsdk/core/model/rS;

    if-eqz v0, :cond_1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql$1;->Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->ex(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;)Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    move-result-object v0

    check-cast v0, Lcom/bytedance/sdk/openadsdk/core/model/rS;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/rS;->Wr()Z

    move-result v0

    if-eqz v0, :cond_1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql$1;->Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;

    const/4 v1, 0x1

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->ex(Z)V

    :cond_1
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql$1;->Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->rAx()Lcom/bytedance/sdk/component/adexpress/ex/dG;

    move-result-object v1

    invoke-virtual {v1}, Lcom/bytedance/sdk/component/adexpress/ex/dG;->hjc()Lorg/json/JSONObject;

    move-result-object v1

    invoke-static {v0, v1}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->Fj(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;Lorg/json/JSONObject;)Lorg/json/JSONObject;

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql$1;->Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->hjc(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;)Lorg/json/JSONObject;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->Fj(Lorg/json/JSONObject;)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql$1;->Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->ex(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;)Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    move-result-object v0

    if-eqz v0, :cond_2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql$1;->Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->ex(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;)Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->uM()Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;

    move-result-object v0

    if-eqz v0, :cond_2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql$1;->Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->ex(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;)Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    move-result-object v1

    invoke-virtual {v1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->uM()Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;

    move-result-object v1

    invoke-virtual {v1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;->Ubf()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Lcom/bytedance/sdk/component/adexpress/Fj/ex/ex;->hjc(Ljava/lang/String;)Lcom/bytedance/sdk/component/adexpress/Fj/hjc/ex;

    move-result-object v1

    invoke-static {v0, v1}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->Fj(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;Lcom/bytedance/sdk/component/adexpress/Fj/hjc/ex;)Lcom/bytedance/sdk/component/adexpress/Fj/hjc/ex;

    :cond_2
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql$1;->Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->eV(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;)I

    move-result v0

    if-nez v0, :cond_3

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql$1;->Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->Ubf(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;)V

    :cond_3
    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/dG;->hjc()Landroid/os/Handler;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql$1;->Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;

    invoke-static {v1}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->WR(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;)Ljava/lang/Runnable;

    move-result-object v1

    invoke-virtual {v0, v1}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    return-void
.end method
