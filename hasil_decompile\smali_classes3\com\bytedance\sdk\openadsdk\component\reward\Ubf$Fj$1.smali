.class Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$Fj$1;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/openadsdk/Ko/ex;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$Fj;->onError(ILjava/lang/String;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$Fj;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$Fj;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$Fj$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$Fj;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public getLogStats()Lcom/bytedance/sdk/openadsdk/Ko/Fj/ex;
    .locals 3
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Exception;
        }
    .end annotation

    new-instance v0, Lorg/json/JSONObject;

    invoke-direct {v0}, Lorg/json/JSONObject;-><init>()V

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$Fj$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$Fj;

    invoke-static {v1}, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$Fj;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$Fj;)Lcom/bytedance/sdk/openadsdk/core/model/Fj;

    move-result-object v1

    invoke-virtual {v1}, Lcom/bytedance/sdk/openadsdk/core/model/Fj;->Fj()Ljava/lang/String;

    move-result-object v1

    const-string v2, "req_id"

    invoke-virtual {v0, v2, v1}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/Ko/Fj/hjc;->ex()Lcom/bytedance/sdk/openadsdk/Ko/Fj/hjc;

    move-result-object v1

    const-string v2, "choose_ad_load_error"

    invoke-virtual {v1, v2}, Lcom/bytedance/sdk/openadsdk/Ko/Fj/hjc;->Fj(Ljava/lang/String;)Lcom/bytedance/sdk/openadsdk/Ko/Fj/hjc;

    move-result-object v1

    invoke-virtual {v0}, Lorg/json/JSONObject;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v1, v0}, Lcom/bytedance/sdk/openadsdk/Ko/Fj/hjc;->ex(Ljava/lang/String;)Lcom/bytedance/sdk/openadsdk/Ko/Fj/hjc;

    move-result-object v0

    return-object v0
.end method
