.class Lcom/bytedance/sdk/openadsdk/core/model/JW$11;
.super Ljava/lang/Object;

# interfaces
.implements Landroid/view/View$OnTouchListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/core/model/JW;->BcC()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/core/model/JW;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/core/model/JW;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/JW$11;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/JW;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public onTouch(Landroid/view/View;Landroid/view/MotionEvent;)Z
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/JW$11;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/JW;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/model/JW;->ex(Lcom/bytedance/sdk/openadsdk/core/model/JW;)Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/JW$11;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/JW;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/core/model/JW;->svN:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/model/JW;->WR(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v0

    if-eqz v0, :cond_1

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/JW$11;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/JW;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/core/model/JW;->Tc:Lcom/bytedance/sdk/openadsdk/core/ex/ex;

    invoke-virtual {v0, p1, p2}, Lcom/bytedance/sdk/openadsdk/core/ex/hjc;->onTouch(Landroid/view/View;Landroid/view/MotionEvent;)Z

    :cond_1
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/JW$11;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/JW;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/core/model/JW;->ex(Lcom/bytedance/sdk/openadsdk/core/model/JW;)Z

    move-result p1

    if-eqz p1, :cond_2

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/JW$11;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/JW;

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/core/model/JW;->svN:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/core/model/JW;->WR(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result p1

    if-eqz p1, :cond_3

    :cond_2
    invoke-virtual {p2}, Landroid/view/MotionEvent;->getAction()I

    move-result p1

    const/4 v0, 0x1

    if-ne p1, v0, :cond_3

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/JW$11;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/JW;

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/core/model/JW;->Tc:Lcom/bytedance/sdk/openadsdk/core/ex/ex;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/ex/hjc;->svN()Z

    move-result p1

    if-eqz p1, :cond_3

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/JW$11;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/JW;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/core/model/JW;->mE(Lcom/bytedance/sdk/openadsdk/core/model/JW;)Lcom/bytedance/sdk/component/widget/SSWebView;

    move-result-object p1

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/widget/SSWebView;->getWebView()Landroid/webkit/WebView;

    move-result-object p1

    invoke-virtual {p1}, Landroid/view/View;->performClick()Z

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/JW$11;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/JW;

    invoke-static {p1, v0}, Lcom/bytedance/sdk/openadsdk/core/model/JW;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/JW;Z)Z

    :cond_3
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/JW$11;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/JW;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/core/model/JW;->Af(Lcom/bytedance/sdk/openadsdk/core/model/JW;)Lcom/bytedance/sdk/openadsdk/ex/Ko;

    move-result-object p1

    if-eqz p1, :cond_4

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/JW$11;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/JW;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/core/model/JW;->Af(Lcom/bytedance/sdk/openadsdk/core/model/JW;)Lcom/bytedance/sdk/openadsdk/ex/Ko;

    move-result-object p1

    invoke-virtual {p2}, Landroid/view/MotionEvent;->getActionMasked()I

    move-result p2

    invoke-virtual {p1, p2}, Lcom/bytedance/sdk/openadsdk/ex/Ko;->ex(I)V

    :cond_4
    const/4 p1, 0x0

    return p1
.end method
