.class Lcom/bytedance/sdk/openadsdk/core/Fj/Fj$2;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/core/Fj/Fj;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Fj;Lcom/bytedance/sdk/openadsdk/core/model/ex;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/core/model/Fj;

.field final synthetic ex:Lcom/bytedance/sdk/openadsdk/core/model/ex;

.field final synthetic hjc:Lcom/bytedance/sdk/openadsdk/core/Fj/Fj;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/core/Fj/Fj;Lcom/bytedance/sdk/openadsdk/core/model/Fj;Lcom/bytedance/sdk/openadsdk/core/model/ex;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/Fj/Fj$2;->hjc:Lcom/bytedance/sdk/openadsdk/core/Fj/Fj;

    iput-object p2, p0, Lcom/bytedance/sdk/openadsdk/core/Fj/Fj$2;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Fj;

    iput-object p3, p0, Lcom/bytedance/sdk/openadsdk/core/Fj/Fj$2;->ex:Lcom/bytedance/sdk/openadsdk/core/model/ex;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 3

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/Fj/Fj$2;->hjc:Lcom/bytedance/sdk/openadsdk/core/Fj/Fj;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/Fj/Fj;->Fj(Lcom/bytedance/sdk/openadsdk/core/Fj/Fj;)Lcom/bytedance/sdk/openadsdk/core/Ql$Fj;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/core/Fj/Fj$2;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Fj;

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/core/Fj/Fj$2;->ex:Lcom/bytedance/sdk/openadsdk/core/model/ex;

    invoke-interface {v0, v1, v2}, Lcom/bytedance/sdk/openadsdk/core/Ql$Fj;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Fj;Lcom/bytedance/sdk/openadsdk/core/model/ex;)V

    return-void
.end method
