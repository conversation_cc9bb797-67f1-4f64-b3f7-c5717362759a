.class Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$3;
.super Lcom/bytedance/sdk/openadsdk/core/widget/Fj/eV;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->WR()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;Lcom/bytedance/sdk/openadsdk/core/Vq;Lcom/bytedance/sdk/openadsdk/ex/Ko;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$3;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;

    invoke-direct {p0, p2, p3}, Lcom/bytedance/sdk/openadsdk/core/widget/Fj/eV;-><init>(Lcom/bytedance/sdk/openadsdk/core/Vq;Lcom/bytedance/sdk/openadsdk/ex/Ko;)V

    return-void
.end method


# virtual methods
.method public onProgressChanged(Landroid/webkit/WebView;I)V
    .locals 1

    invoke-super {p0, p1, p2}, Lcom/bytedance/sdk/openadsdk/core/widget/Fj/eV;->onProgressChanged(Landroid/webkit/WebView;I)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$3;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->ex(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object v0

    iget-boolean v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->vYf:Z

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$3;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->ex(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object v0

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->PpV:Lcom/bytedance/sdk/openadsdk/common/svN;

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$3;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->ex(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object v0

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->PpV:Lcom/bytedance/sdk/openadsdk/common/svN;

    invoke-virtual {v0, p1, p2}, Lcom/bytedance/sdk/openadsdk/common/svN;->Fj(Landroid/webkit/WebView;I)V

    :cond_0
    return-void
.end method
