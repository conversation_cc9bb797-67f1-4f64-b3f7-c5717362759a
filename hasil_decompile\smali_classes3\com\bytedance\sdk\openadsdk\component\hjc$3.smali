.class Lcom/bytedance/sdk/openadsdk/component/hjc$3;
.super Ljava/lang/Object;

# interfaces
.implements Landroid/view/View$OnClickListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/hjc;->Fj()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/component/hjc;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/hjc;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/hjc$3;->Fj:Lcom/bytedance/sdk/openadsdk/component/hjc;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public onClick(Landroid/view/View;)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/hjc$3;->Fj:Lcom/bytedance/sdk/openadsdk/component/hjc;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/hjc;->Ubf:Lcom/bytedance/sdk/openadsdk/component/Fj;

    if-eqz v0, :cond_0

    invoke-virtual {v0, p1}, Lcom/bytedance/sdk/openadsdk/component/Fj;->ex(Landroid/view/View;)V

    :cond_0
    return-void
.end method
