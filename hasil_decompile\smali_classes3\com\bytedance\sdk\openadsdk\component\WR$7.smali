.class Lcom/bytedance/sdk/openadsdk/component/WR$7;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/openadsdk/utils/rAx$Fj;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/WR;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/core/model/mC;Lcom/bytedance/sdk/openadsdk/component/WR$ex;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:I

.field final synthetic Ubf:Lcom/bytedance/sdk/openadsdk/component/WR$ex;

.field final synthetic WR:Lcom/bytedance/sdk/openadsdk/component/WR;

.field final synthetic eV:Lcom/bytedance/sdk/openadsdk/core/model/mC;

.field final synthetic ex:Lcom/bytedance/sdk/openadsdk/utils/lv;

.field final synthetic hjc:Lcom/bytedance/sdk/openadsdk/core/model/Ql;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/WR;ILcom/bytedance/sdk/openadsdk/utils/lv;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/core/model/mC;Lcom/bytedance/sdk/openadsdk/component/WR$ex;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/WR$7;->WR:Lcom/bytedance/sdk/openadsdk/component/WR;

    iput p2, p0, Lcom/bytedance/sdk/openadsdk/component/WR$7;->Fj:I

    iput-object p3, p0, Lcom/bytedance/sdk/openadsdk/component/WR$7;->ex:Lcom/bytedance/sdk/openadsdk/utils/lv;

    iput-object p4, p0, Lcom/bytedance/sdk/openadsdk/component/WR$7;->hjc:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    iput-object p5, p0, Lcom/bytedance/sdk/openadsdk/component/WR$7;->eV:Lcom/bytedance/sdk/openadsdk/core/model/mC;

    iput-object p6, p0, Lcom/bytedance/sdk/openadsdk/component/WR$7;->Ubf:Lcom/bytedance/sdk/openadsdk/component/WR$ex;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj()V
    .locals 4

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/WR$7;->hjc:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/WR$7;->ex:Lcom/bytedance/sdk/openadsdk/utils/lv;

    invoke-virtual {v1}, Lcom/bytedance/sdk/openadsdk/utils/lv;->hjc()J

    move-result-wide v1

    const/4 v3, 0x0

    invoke-static {v0, v1, v2, v3}, Lcom/bytedance/sdk/openadsdk/component/eV/Fj;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;JZ)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/WR$7;->Ubf:Lcom/bytedance/sdk/openadsdk/component/WR$ex;

    invoke-interface {v0}, Lcom/bytedance/sdk/openadsdk/component/WR$ex;->Fj()V

    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/openadsdk/rAx/Fj/ex;)V
    .locals 4
    .param p1    # Lcom/bytedance/sdk/openadsdk/rAx/Fj/ex;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/rAx/Fj/ex;->eV()Z

    move-result v0

    if-eqz v0, :cond_1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/WR$7;->WR:Lcom/bytedance/sdk/openadsdk/component/WR;

    iget v1, p0, Lcom/bytedance/sdk/openadsdk/component/WR$7;->Fj:I

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/component/WR;->hjc(I)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/WR$7;->ex:Lcom/bytedance/sdk/openadsdk/utils/lv;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/utils/lv;->hjc()J

    move-result-wide v0

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/component/WR$7;->hjc:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    const/4 v3, 0x1

    invoke-static {v2, v0, v1, v3}, Lcom/bytedance/sdk/openadsdk/component/eV/Fj;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;JZ)V

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/component/WR$7;->eV:Lcom/bytedance/sdk/openadsdk/core/model/mC;

    if-eqz v2, :cond_0

    invoke-virtual {v2, v0, v1}, Lcom/bytedance/sdk/openadsdk/core/model/mC;->Fj(J)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/WR$7;->eV:Lcom/bytedance/sdk/openadsdk/core/model/mC;

    const/4 v1, 0x2

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/core/model/mC;->Fj(I)V

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/WR$7;->Ubf:Lcom/bytedance/sdk/openadsdk/component/WR$ex;

    invoke-interface {v0, p1}, Lcom/bytedance/sdk/openadsdk/component/WR$ex;->Fj(Lcom/bytedance/sdk/openadsdk/rAx/Fj/ex;)V

    return-void

    :cond_1
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/WR$7;->hjc:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/WR$7;->ex:Lcom/bytedance/sdk/openadsdk/utils/lv;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/utils/lv;->hjc()J

    move-result-wide v0

    const/4 v2, 0x0

    invoke-static {p1, v0, v1, v2}, Lcom/bytedance/sdk/openadsdk/component/eV/Fj;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;JZ)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/WR$7;->Ubf:Lcom/bytedance/sdk/openadsdk/component/WR$ex;

    invoke-interface {p1}, Lcom/bytedance/sdk/openadsdk/component/WR$ex;->Fj()V

    return-void
.end method
