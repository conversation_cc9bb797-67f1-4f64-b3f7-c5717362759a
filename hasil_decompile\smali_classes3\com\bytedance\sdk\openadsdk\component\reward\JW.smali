.class Lcom/bytedance/sdk/openadsdk/component/reward/JW;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/openadsdk/TTClientBidding;


# instance fields
.field private final Fj:Lcom/bytedance/sdk/openadsdk/component/reward/BcC;


# direct methods
.method public constructor <init>(Landroid/content/Context;Lcom/bytedance/sdk/openadsdk/core/model/Fj;Lcom/bytedance/sdk/openadsdk/AdSlot;)V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Lcom/bytedance/sdk/openadsdk/component/reward/BcC;

    invoke-direct {v0, p1, p2, p3}, Lcom/bytedance/sdk/openadsdk/component/reward/BcC;-><init>(Landroid/content/Context;Lcom/bytedance/sdk/openadsdk/core/model/Fj;Lcom/bytedance/sdk/openadsdk/AdSlot;)V

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/JW;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/BcC;

    return-void
.end method


# virtual methods
.method public Fj()Lcom/bytedance/sdk/openadsdk/component/reward/BcC;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/JW;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/BcC;

    return-object v0
.end method

.method public ex()V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/JW;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/BcC;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/BcC;->Fj()V

    return-void
.end method

.method public loss(Ljava/lang/Double;Ljava/lang/String;Ljava/lang/String;)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/JW;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/BcC;

    invoke-virtual {v0, p1, p2, p3}, Lcom/bytedance/sdk/openadsdk/component/reward/BcC;->loss(Ljava/lang/Double;Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public win(Ljava/lang/Double;)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/JW;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/BcC;

    invoke-virtual {v0, p1}, Lcom/bytedance/sdk/openadsdk/component/reward/BcC;->win(Ljava/lang/Double;)V

    return-void
.end method
