.class public Lcom/bytedance/sdk/openadsdk/component/ex/Fj;
.super Ljava/lang/Object;


# static fields
.field private static volatile Fj:Lcom/bytedance/sdk/openadsdk/component/ex/Fj;


# instance fields
.field private final ex:Lcom/bytedance/sdk/openadsdk/core/Ql;


# direct methods
.method private constructor <init>()V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/JU;->hjc()Lcom/bytedance/sdk/openadsdk/core/Ql;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/ex/Fj;->ex:Lcom/bytedance/sdk/openadsdk/core/Ql;

    return-void
.end method

.method public static Fj()Lcom/bytedance/sdk/openadsdk/component/ex/Fj;
    .locals 2

    sget-object v0, Lcom/bytedance/sdk/openadsdk/component/ex/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/component/ex/Fj;

    if-nez v0, :cond_1

    const-class v0, Lcom/bytedance/sdk/openadsdk/component/ex/Fj;

    monitor-enter v0

    :try_start_0
    sget-object v1, Lcom/bytedance/sdk/openadsdk/component/ex/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/component/ex/Fj;

    if-nez v1, :cond_0

    new-instance v1, Lcom/bytedance/sdk/openadsdk/component/ex/Fj;

    invoke-direct {v1}, Lcom/bytedance/sdk/openadsdk/component/ex/Fj;-><init>()V

    sput-object v1, Lcom/bytedance/sdk/openadsdk/component/ex/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/component/ex/Fj;

    goto :goto_0

    :catchall_0
    move-exception v1

    goto :goto_1

    :cond_0
    :goto_0
    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_2

    :goto_1
    monitor-exit v0

    throw v1

    :cond_1
    :goto_2
    sget-object v0, Lcom/bytedance/sdk/openadsdk/component/ex/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/component/ex/Fj;

    return-object v0
.end method


# virtual methods
.method public Fj(Landroid/content/Context;Lcom/bytedance/sdk/openadsdk/AdSlot;Lcom/bytedance/sdk/openadsdk/common/hjc;)V
    .locals 9

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/utils/lv;->Fj()Lcom/bytedance/sdk/openadsdk/utils/lv;

    move-result-object v5

    iget-object v6, p0, Lcom/bytedance/sdk/openadsdk/component/ex/Fj;->ex:Lcom/bytedance/sdk/openadsdk/core/Ql;

    new-instance v7, Lcom/bytedance/sdk/openadsdk/core/model/vYf;

    invoke-direct {v7}, Lcom/bytedance/sdk/openadsdk/core/model/vYf;-><init>()V

    new-instance v8, Lcom/bytedance/sdk/openadsdk/component/ex/Fj$1;

    move-object v0, v8

    move-object v1, p0

    move-object v2, p3

    move-object v3, p1

    move-object v4, p2

    invoke-direct/range {v0 .. v5}, Lcom/bytedance/sdk/openadsdk/component/ex/Fj$1;-><init>(Lcom/bytedance/sdk/openadsdk/component/ex/Fj;Lcom/bytedance/sdk/openadsdk/common/hjc;Landroid/content/Context;Lcom/bytedance/sdk/openadsdk/AdSlot;Lcom/bytedance/sdk/openadsdk/utils/lv;)V

    const/4 p1, 0x5

    invoke-interface {v6, p2, v7, p1, v8}, Lcom/bytedance/sdk/openadsdk/core/Ql;->Fj(Lcom/bytedance/sdk/openadsdk/AdSlot;Lcom/bytedance/sdk/openadsdk/core/model/vYf;ILcom/bytedance/sdk/openadsdk/core/Ql$Fj;)V

    return-void
.end method
