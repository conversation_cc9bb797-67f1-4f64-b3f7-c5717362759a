.class Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj$1;
.super Landroid/os/CountDownTimer;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;->mSE()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:J

.field final synthetic ex:J

.field final synthetic hjc:Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;JJJJ)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;

    iput-wide p6, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj$1;->Fj:J

    iput-wide p8, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj$1;->ex:J

    invoke-direct {p0, p2, p3, p4, p5}, Landroid/os/CountDownTimer;-><init>(JJ)V

    return-void
.end method


# virtual methods
.method public onFinish()V
    .locals 4

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;

    const/4 v1, 0x4

    invoke-static {v0, v1}, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;I)I

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;->eV(Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;)Lcom/bykv/vk/openvk/component/video/api/eV/hjc$Fj;

    move-result-object v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;->eV(Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;)Lcom/bykv/vk/openvk/component/video/api/eV/hjc$Fj;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;

    invoke-virtual {v1}, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;->JW()J

    move-result-wide v1

    const/16 v3, 0x64

    invoke-interface {v0, v1, v2, v3}, Lcom/bykv/vk/openvk/component/video/api/eV/hjc$Fj;->Fj(JI)V

    :cond_0
    new-instance v0, Lcom/bytedance/sdk/openadsdk/ex/eV/ex/JW$Fj;

    invoke-direct {v0}, Lcom/bytedance/sdk/openadsdk/ex/eV/ex/JW$Fj;-><init>()V

    iget-wide v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj$1;->ex:J

    invoke-virtual {v0, v1, v2}, Lcom/bytedance/sdk/openadsdk/ex/eV/ex/JW$Fj;->Fj(J)V

    iget-wide v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj$1;->ex:J

    invoke-virtual {v0, v1, v2}, Lcom/bytedance/sdk/openadsdk/ex/eV/ex/JW$Fj;->hjc(J)V

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;

    invoke-virtual {v1}, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;->UYd()J

    move-result-wide v1

    invoke-virtual {v0, v1, v2}, Lcom/bytedance/sdk/openadsdk/ex/eV/ex/JW$Fj;->ex(J)V

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;

    invoke-virtual {v1}, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;->dG()I

    move-result v1

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/ex/eV/ex/JW$Fj;->eV(I)V

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;

    invoke-static {v1}, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;->Ubf(Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;)Lcom/bykv/vk/openvk/component/video/api/ex/Fj;

    move-result-object v1

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;

    invoke-static {v2}, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;->WR(Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;)Lcom/bytedance/sdk/openadsdk/ex/svN;

    move-result-object v2

    invoke-static {v1, v0, v2}, Lcom/bytedance/sdk/openadsdk/ex/eV/Fj/Fj;->ex(Lcom/bykv/vk/openvk/component/video/api/ex/Fj;Lcom/bytedance/sdk/openadsdk/ex/eV/ex/JW$Fj;Lcom/bytedance/sdk/openadsdk/ex/svN;)V

    return-void
.end method

.method public onTick(J)V
    .locals 4

    iget-wide v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj$1;->Fj:J

    sub-long/2addr v0, p1

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;->hjc(Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;)J

    move-result-wide p1

    add-long/2addr v0, p1

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;

    invoke-static {p1, v0, v1}, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;J)J

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;->eV(Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;)Lcom/bykv/vk/openvk/component/video/api/eV/hjc$Fj;

    move-result-object p1

    if-eqz p1, :cond_0

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;->eV(Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;)Lcom/bykv/vk/openvk/component/video/api/eV/hjc$Fj;

    move-result-object p1

    iget-wide v2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj$1;->ex:J

    invoke-interface {p1, v0, v1, v2, v3}, Lcom/bykv/vk/openvk/component/video/api/eV/hjc$Fj;->Fj(JJ)V

    :cond_0
    return-void
.end method
