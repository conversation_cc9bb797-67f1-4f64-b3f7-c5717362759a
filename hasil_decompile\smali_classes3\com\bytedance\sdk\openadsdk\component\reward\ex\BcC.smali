.class public Lcom/bytedance/sdk/openadsdk/component/reward/ex/BcC;
.super Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;


# instance fields
.field private JW:Lcom/bytedance/sdk/openadsdk/component/reward/view/FullInteractionStyleView;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;)V
    .locals 0

    invoke-direct {p0, p1}, Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;)V

    return-void
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/openadsdk/component/reward/ex/BcC;)Lcom/bytedance/sdk/openadsdk/component/reward/view/FullInteractionStyleView;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/BcC;->JW:Lcom/bytedance/sdk/openadsdk/component/reward/view/FullInteractionStyleView;

    return-object p0
.end method

.method public static Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z
    .locals 2

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->zX()Z

    move-result v0

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    return v1

    :cond_0
    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->HQ()F

    move-result p0

    const/high16 v0, 0x42c80000    # 100.0f

    cmpl-float p0, p0, v0

    if-eqz p0, :cond_1

    const/4 p0, 0x1

    return p0

    :cond_1
    return v1
.end method

.method private fj()Z
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Fj;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->Ubf(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v0

    return v0
.end method


# virtual methods
.method public Fj(Landroid/widget/FrameLayout;)V
    .locals 8

    new-instance v0, Lcom/bytedance/sdk/openadsdk/component/reward/view/FullInteractionStyleView;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v1, v1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Kk:Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;->rAx:Ljava/lang/String;

    invoke-direct {v0, v1, v2}, Lcom/bytedance/sdk/openadsdk/component/reward/view/FullInteractionStyleView;-><init>(Landroid/content/Context;Ljava/lang/String;)V

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/BcC;->JW:Lcom/bytedance/sdk/openadsdk/component/reward/view/FullInteractionStyleView;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;->UYd:Lcom/bytedance/sdk/openadsdk/core/ex/Ubf;

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/component/reward/view/FullInteractionStyleView;->setDownloadListener(Lcom/bytedance/sdk/openadsdk/core/ex/Ubf;)V

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/BcC;->JW:Lcom/bytedance/sdk/openadsdk/component/reward/view/FullInteractionStyleView;

    iget-object v3, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Fj;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget v4, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->rAx:F

    iget v5, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Ko:I

    iget v6, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Fj;->hjc:I

    iget v7, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Fj;->eV:I

    invoke-virtual/range {v2 .. v7}, Lcom/bytedance/sdk/openadsdk/component/reward/view/FullInteractionStyleView;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;FIII)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/BcC;->JW:Lcom/bytedance/sdk/openadsdk/component/reward/view/FullInteractionStyleView;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/view/FullInteractionStyleView;->getInteractionStyleRootView()Landroid/view/View;

    move-result-object v0

    invoke-virtual {p1, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    return-void
.end method

.method public Ubf()Z
    .locals 1

    invoke-direct {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/ex/BcC;->fj()Z

    move-result v0

    return v0
.end method

.method public WR()Z
    .locals 1

    invoke-direct {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/ex/BcC;->fj()Z

    move-result v0

    return v0
.end method

.method public eV()Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex$Fj;
    .locals 1

    new-instance v0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/BcC$1;

    invoke-direct {v0, p0}, Lcom/bytedance/sdk/openadsdk/component/reward/ex/BcC$1;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/ex/BcC;)V

    return-object v0
.end method

.method public rXP()Landroid/widget/FrameLayout;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/BcC;->JW:Lcom/bytedance/sdk/openadsdk/component/reward/view/FullInteractionStyleView;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/view/FullInteractionStyleView;->getVideoContainer()Landroid/widget/FrameLayout;

    move-result-object v0

    return-object v0

    :cond_0
    const/4 v0, 0x0

    return-object v0
.end method

.method public svN()V
    .locals 4

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Fj;->WR:Lcom/bytedance/sdk/openadsdk/component/reward/view/Ubf;

    const/16 v1, 0x8

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/component/reward/view/Ubf;->eV(I)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Fj;->WR:Lcom/bytedance/sdk/openadsdk/component/reward/view/Ubf;

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/component/reward/view/Ubf;->hjc(I)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Fj;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->mE()I

    move-result v0

    const/4 v2, 0x2

    const/4 v3, 0x0

    if-ne v0, v2, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Fj;->BcC:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Ko;

    invoke-virtual {v0, v3}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Ko;->Fj(Z)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Fj;->BcC:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Ko;

    invoke-virtual {v0, v3}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Ko;->hjc(Z)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Fj;->BcC:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Ko;

    invoke-virtual {v0, v3}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Ko;->eV(Z)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Fj;->WR:Lcom/bytedance/sdk/openadsdk/component/reward/view/Ubf;

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/component/reward/view/Ubf;->WR(I)V

    return-void

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Fj;->BcC:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Ko;

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Fj;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v2}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->yR()Z

    move-result v2

    invoke-virtual {v0, v2}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Ko;->Fj(Z)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Fj;->BcC:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Ko;

    invoke-direct {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/ex/BcC;->fj()Z

    move-result v2

    invoke-virtual {v0, v2}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Ko;->hjc(Z)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Fj;->BcC:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Ko;

    invoke-direct {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/ex/BcC;->fj()Z

    move-result v2

    invoke-virtual {v0, v2}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Ko;->eV(Z)V

    invoke-direct {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/ex/BcC;->fj()Z

    move-result v0

    if-eqz v0, :cond_1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Fj;->WR:Lcom/bytedance/sdk/openadsdk/component/reward/view/Ubf;

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/component/reward/view/Ubf;->WR(I)V

    return-void

    :cond_1
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Fj;->BcC:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Ko;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Ko;->WR()V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Fj;->WR:Lcom/bytedance/sdk/openadsdk/component/reward/view/Ubf;

    invoke-virtual {v0, v3}, Lcom/bytedance/sdk/openadsdk/component/reward/view/Ubf;->WR(I)V

    return-void
.end method
