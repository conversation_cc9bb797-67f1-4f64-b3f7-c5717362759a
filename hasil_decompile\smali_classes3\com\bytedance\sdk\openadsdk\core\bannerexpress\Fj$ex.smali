.class Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$ex;
.super Lcom/bytedance/sdk/component/svN/BcC;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "ex"
.end annotation


# instance fields
.field Fj:Z

.field ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

.field hjc:Ljava/lang/ref/WeakReference;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/lang/ref/WeakReference<",
            "Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(ZLcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;)V
    .locals 1

    const-string v0, "ReportWindowFocusChangedAdShow"

    invoke-direct {p0, v0}, Lcom/bytedance/sdk/component/svN/BcC;-><init>(Ljava/lang/String;)V

    iput-boolean p1, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$ex;->Fj:Z

    iput-object p2, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$ex;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    new-instance p1, Ljava/lang/ref/WeakReference;

    invoke-direct {p1, p3}, Ljava/lang/ref/WeakReference;-><init>(Ljava/lang/Object;)V

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$ex;->hjc:Ljava/lang/ref/WeakReference;

    return-void
.end method


# virtual methods
.method public run()V
    .locals 3

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$ex;->hjc:Ljava/lang/ref/WeakReference;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Ljava/lang/ref/Reference;->get()Ljava/lang/Object;

    move-result-object v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$ex;->hjc:Ljava/lang/ref/WeakReference;

    invoke-virtual {v0}, Ljava/lang/ref/Reference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;

    iget-boolean v1, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$ex;->Fj:Z

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$ex;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {v0, v1, v2}, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;->ex(Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;ZLcom/bytedance/sdk/openadsdk/core/model/Ql;)V

    :cond_0
    return-void
.end method
