.class public interface abstract Landroidx/compose/ui/graphics/o4;
.super Ljava/lang/Object;


# annotations
.annotation runtime L<PERSON>lin/Metadata;
.end annotation


# virtual methods
.method public abstract A()Landroid/graphics/Shader;
.end method

.method public abstract B(Landroidx/compose/ui/graphics/x1;)V
.end method

.method public abstract C(F)V
.end method

.method public abstract D()I
.end method

.method public abstract E(I)V
.end method

.method public abstract F(F)V
.end method

.method public abstract G()F
.end method

.method public abstract a()F
.end method

.method public abstract b(F)V
.end method

.method public abstract c()J
.end method

.method public abstract l()Landroidx/compose/ui/graphics/x1;
.end method

.method public abstract n()I
.end method

.method public abstract o(I)V
.end method

.method public abstract p(I)V
.end method

.method public abstract q(I)V
.end method

.method public abstract r()I
.end method

.method public abstract s(Landroidx/compose/ui/graphics/r4;)V
.end method

.method public abstract t(I)V
.end method

.method public abstract u(J)V
.end method

.method public abstract v()Landroidx/compose/ui/graphics/r4;
.end method

.method public abstract w()I
.end method

.method public abstract x()F
.end method

.method public abstract y()Landroid/graphics/Paint;
.end method

.method public abstract z(Landroid/graphics/Shader;)V
.end method
