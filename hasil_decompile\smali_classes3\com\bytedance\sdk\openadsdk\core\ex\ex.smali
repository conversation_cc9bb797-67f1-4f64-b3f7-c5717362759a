.class public Lcom/bytedance/sdk/openadsdk/core/ex/ex;
.super Lcom/bytedance/sdk/openadsdk/core/ex/hjc;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bytedance/sdk/openadsdk/core/ex/ex$Fj;
    }
.end annotation


# instance fields
.field protected BcC:Ljava/lang/ref/WeakReference;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/lang/ref/WeakReference<",
            "Landroid/view/View;",
            ">;"
        }
    .end annotation
.end field

.field private Fj:Ljava/lang/String;

.field protected JU:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field

.field protected JW:Lcom/com/bytedance/overseas/sdk/Fj/hjc;

.field protected Ko:Lcom/bytedance/sdk/openadsdk/core/model/mSE;

.field protected Ql:Lcom/bytedance/sdk/openadsdk/api/PangleAd;

.field protected Tc:Z

.field protected UYd:Lcom/bytedance/sdk/openadsdk/api/nativeAd/PAGNativeAd;

.field protected final Ubf:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

.field protected final WR:Ljava/lang/String;

.field protected dG:Lcom/bykv/vk/openvk/component/video/api/eV/hjc;

.field public eV:Lcom/bytedance/sdk/openadsdk/core/model/dG;

.field private ex:Ljava/lang/ref/WeakReference;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/lang/ref/WeakReference<",
            "Landroid/app/Activity;",
            ">;"
        }
    .end annotation
.end field

.field protected hjc:Landroid/content/Context;

.field private kF:Z

.field protected mSE:Ljava/lang/ref/WeakReference;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/lang/ref/WeakReference<",
            "Landroid/view/View;",
            ">;"
        }
    .end annotation
.end field

.field protected rAx:Lcom/bytedance/sdk/openadsdk/core/ex/ex$Fj;

.field protected rS:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Fj;

.field protected final svN:I

.field protected vYf:I


# direct methods
.method public constructor <init>(Landroid/content/Context;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Ljava/lang/String;I)V
    .locals 1
    .param p1    # Landroid/content/Context;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p2    # Lcom/bytedance/sdk/openadsdk/core/model/Ql;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p3    # Ljava/lang/String;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    invoke-direct {p0}, Lcom/bytedance/sdk/openadsdk/core/ex/hjc;-><init>()V

    const/4 v0, 0x0

    iput-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->Tc:Z

    iput v0, p0, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->vYf:I

    iput-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->kF:Z

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->hjc:Landroid/content/Context;

    iput-object p2, p0, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->Ubf:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    iput-object p3, p0, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->WR:Ljava/lang/String;

    iput p4, p0, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->svN:I

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Ljava/lang/String;IZ)V
    .locals 0
    .param p1    # Landroid/content/Context;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p2    # Lcom/bytedance/sdk/openadsdk/core/model/Ql;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p3    # Ljava/lang/String;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    invoke-direct {p0, p1, p2, p3, p4}, Lcom/bytedance/sdk/openadsdk/core/ex/ex;-><init>(Landroid/content/Context;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Ljava/lang/String;I)V

    iput-boolean p5, p0, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->kF:Z

    return-void
.end method

.method public static Fj(Landroid/view/View;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Z)Z
    .locals 3

    const/4 v0, 0x1

    if-eqz p0, :cond_7

    if-nez p1, :cond_0

    goto :goto_1

    :cond_0
    :try_start_0
    sget v1, Lcom/bytedance/sdk/component/adexpress/dynamic/Fj;->mE:I

    invoke-virtual {p0, v1}, Landroid/view/View;->getTag(I)Ljava/lang/Object;

    move-result-object v2

    invoke-static {v2}, Ljava/lang/String;->valueOf(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {p0, v1}, Landroid/view/View;->getTag(I)Ljava/lang/Object;

    move-result-object v1

    if-eqz v1, :cond_2

    invoke-static {v2}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v1

    if-nez v1, :cond_2

    const-string v1, "click"

    invoke-virtual {v1, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p0
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    if-eqz p0, :cond_1

    return p2

    :cond_1
    return v0

    :catch_0
    nop

    :cond_2
    invoke-static {p0}, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->hjc(Landroid/view/View;)Z

    move-result p0

    const/4 v1, 0x0

    if-eqz p0, :cond_5

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->Ql()I

    move-result p0

    if-ne p0, v0, :cond_4

    if-eqz p2, :cond_3

    goto :goto_0

    :cond_3
    return v1

    :cond_4
    :goto_0
    return v0

    :cond_5
    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->JU()I

    move-result p0

    if-ne p0, v0, :cond_7

    if-eqz p2, :cond_6

    goto :goto_1

    :cond_6
    return v1

    :cond_7
    :goto_1
    return v0
.end method

.method public static hjc(Landroid/view/View;)Z
    .locals 2

    invoke-virtual {p0}, Landroid/view/View;->getId()I

    move-result v0

    const v1, 0x1f000009

    if-eq v1, v0, :cond_1

    const v0, 0x1f00000b

    invoke-virtual {p0}, Landroid/view/View;->getId()I

    move-result v1

    if-eq v0, v1, :cond_1

    const v0, 0x1f000007

    invoke-virtual {p0}, Landroid/view/View;->getId()I

    move-result v1

    if-eq v0, v1, :cond_1

    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    const-string v1, "btn_native_creative"

    invoke-static {v0, v1}, Lcom/bytedance/sdk/component/utils/mE;->Ubf(Landroid/content/Context;Ljava/lang/String;)I

    move-result v0

    invoke-virtual {p0}, Landroid/view/View;->getId()I

    move-result v1

    if-eq v0, v1, :cond_1

    sget v0, Lcom/bytedance/sdk/openadsdk/utils/Ko;->OXv:I

    invoke-virtual {p0}, Landroid/view/View;->getId()I

    move-result v1

    if-eq v0, v1, :cond_1

    sget v0, Lcom/bytedance/sdk/openadsdk/utils/Ko;->We:I

    invoke-virtual {p0}, Landroid/view/View;->getId()I

    move-result p0

    if-ne v0, p0, :cond_0

    goto :goto_0

    :cond_0
    const/4 p0, 0x0

    return p0

    :cond_1
    :goto_0
    const/4 p0, 0x1

    return p0
.end method


# virtual methods
.method public Fj(FFFFLandroid/util/SparseArray;JJLandroid/view/View;Landroid/view/View;Ljava/lang/String;FIFILorg/json/JSONObject;)Lcom/bytedance/sdk/openadsdk/core/model/mSE;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(FFFF",
            "Landroid/util/SparseArray<",
            "Lcom/bytedance/sdk/openadsdk/core/ex/hjc$Fj;",
            ">;JJ",
            "Landroid/view/View;",
            "Landroid/view/View;",
            "Ljava/lang/String;",
            "FIFI",
            "Lorg/json/JSONObject;",
            ")",
            "Lcom/bytedance/sdk/openadsdk/core/model/mSE;"
        }
    .end annotation

    move-object v0, p0

    new-instance v1, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    invoke-direct {v1}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;-><init>()V

    move v2, p1

    invoke-virtual {v1, p1}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->WR(F)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    move-result-object v1

    move v2, p2

    invoke-virtual {v1, p2}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->Ubf(F)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    move-result-object v1

    move v2, p3

    invoke-virtual {v1, p3}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->eV(F)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    move-result-object v1

    move v2, p4

    invoke-virtual {v1, p4}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->hjc(F)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    move-result-object v1

    move-wide v2, p6

    invoke-virtual {v1, p6, p7}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->ex(J)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    move-result-object v1

    move-wide v2, p8

    invoke-virtual {v1, p8, p9}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->Fj(J)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    move-result-object v1

    invoke-static {p10}, Lcom/bytedance/sdk/openadsdk/utils/eh;->Fj(Landroid/view/View;)[I

    move-result-object v2

    invoke-virtual {v1, v2}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->ex([I)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    move-result-object v1

    invoke-static {p11}, Lcom/bytedance/sdk/openadsdk/utils/eh;->Fj(Landroid/view/View;)[I

    move-result-object v2

    invoke-virtual {v1, v2}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->Fj([I)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    move-result-object v1

    invoke-static {p10}, Lcom/bytedance/sdk/openadsdk/utils/eh;->hjc(Landroid/view/View;)[I

    move-result-object v2

    invoke-virtual {v1, v2}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->hjc([I)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    move-result-object v1

    invoke-static {p11}, Lcom/bytedance/sdk/openadsdk/utils/eh;->hjc(Landroid/view/View;)[I

    move-result-object v2

    invoke-virtual {v1, v2}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->eV([I)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    move-result-object v1

    iget v2, v0, Lcom/bytedance/sdk/openadsdk/core/ex/hjc;->Moo:I

    invoke-virtual {v1, v2}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->eV(I)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    move-result-object v1

    iget v2, v0, Lcom/bytedance/sdk/openadsdk/core/ex/hjc;->rf:I

    invoke-virtual {v1, v2}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->Ubf(I)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    move-result-object v1

    iget v2, v0, Lcom/bytedance/sdk/openadsdk/core/ex/hjc;->uy:I

    invoke-virtual {v1, v2}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->WR(I)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    move-result-object v1

    move-object v2, p5

    invoke-virtual {v1, p5}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->Fj(Landroid/util/SparseArray;)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    move-result-object v1

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/BcC;->ex()Lcom/bytedance/sdk/openadsdk/core/BcC;

    move-result-object v2

    invoke-virtual {v2}, Lcom/bytedance/sdk/openadsdk/core/BcC;->Fj()Z

    move-result v2

    if-eqz v2, :cond_0

    const/4 v2, 0x1

    goto :goto_0

    :cond_0
    const/4 v2, 0x2

    :goto_0
    invoke-virtual {v1, v2}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->ex(I)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    move-result-object v1

    move-object/from16 v2, p12

    invoke-virtual {v1, v2}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->Fj(Ljava/lang/String;)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    move-result-object v1

    move/from16 v2, p13

    invoke-virtual {v1, v2}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->Fj(F)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    move-result-object v1

    move/from16 v2, p14

    invoke-virtual {v1, v2}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->hjc(I)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    move-result-object v1

    move/from16 v2, p15

    invoke-virtual {v1, v2}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->ex(F)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    move-result-object v1

    move/from16 v2, p16

    invoke-virtual {v1, v2}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->Fj(I)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    move-result-object v1

    move-object/from16 v2, p17

    invoke-virtual {v1, v2}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->Fj(Lorg/json/JSONObject;)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    move-result-object v1

    invoke-virtual {v1}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->Fj()Lcom/bytedance/sdk/openadsdk/core/model/mSE;

    move-result-object v1

    return-object v1
.end method

.method public Fj(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/ex/hjc;->uy:I

    return-void
.end method

.method public Fj(Landroid/app/Activity;)V
    .locals 1

    if-nez p1, :cond_0

    return-void

    :cond_0
    new-instance v0, Ljava/lang/ref/WeakReference;

    invoke-direct {v0, p1}, Ljava/lang/ref/WeakReference;-><init>(Ljava/lang/Object;)V

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->ex:Ljava/lang/ref/WeakReference;

    return-void
.end method

.method public Fj(Landroid/view/View;)V
    .locals 1

    if-nez p1, :cond_0

    return-void

    :cond_0
    new-instance v0, Ljava/lang/ref/WeakReference;

    invoke-direct {v0, p1}, Ljava/lang/ref/WeakReference;-><init>(Ljava/lang/Object;)V

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->BcC:Ljava/lang/ref/WeakReference;

    return-void
.end method

.method public Fj(Landroid/view/View;FFFFLandroid/util/SparseArray;Z)V
    .locals 28
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/view/View;",
            "FFFF",
            "Landroid/util/SparseArray<",
            "Lcom/bytedance/sdk/openadsdk/core/ex/hjc$Fj;",
            ">;Z)V"
        }
    .end annotation

    move-object/from16 v11, p0

    move-object/from16 v10, p1

    move/from16 v9, p7

    iget-object v0, v11, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->hjc:Landroid/content/Context;

    if-nez v0, :cond_0

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/JU;->Fj()Landroid/content/Context;

    move-result-object v0

    iput-object v0, v11, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->hjc:Landroid/content/Context;

    :cond_0
    iget-boolean v0, v11, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->kF:Z

    if-nez v0, :cond_1

    const/4 v2, 0x1

    move-object/from16 v0, p0

    move-object/from16 v1, p1

    move/from16 v3, p2

    move/from16 v4, p3

    move/from16 v5, p4

    move/from16 v6, p5

    move-object/from16 v7, p6

    move/from16 v8, p7

    invoke-virtual/range {v0 .. v8}, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->Fj(Landroid/view/View;IFFFFLandroid/util/SparseArray;Z)Z

    move-result v0

    if-eqz v0, :cond_1

    return-void

    :cond_1
    iget-object v0, v11, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->hjc:Landroid/content/Context;

    if-nez v0, :cond_2

    return-void

    :cond_2
    iget-object v0, v11, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->eV:Lcom/bytedance/sdk/openadsdk/core/model/dG;

    const/4 v8, -0x1

    const/16 v18, 0x0

    if-eqz v0, :cond_3

    iget v1, v0, Lcom/bytedance/sdk/openadsdk/core/model/dG;->UYd:I

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/core/model/dG;->dG:Lorg/json/JSONObject;

    move-object/from16 v17, v0

    move/from16 v16, v1

    goto :goto_0

    :cond_3
    move-object/from16 v17, v18

    const/16 v16, -0x1

    :goto_0
    iget-wide v6, v11, Lcom/bytedance/sdk/openadsdk/core/ex/hjc;->nsB:J

    iget-wide v4, v11, Lcom/bytedance/sdk/openadsdk/core/ex/hjc;->Vq:J

    iget-object v0, v11, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->BcC:Ljava/lang/ref/WeakReference;

    if-nez v0, :cond_4

    move-object/from16 v19, v18

    goto :goto_1

    :cond_4
    invoke-virtual {v0}, Ljava/lang/ref/Reference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/view/View;

    move-object/from16 v19, v0

    :goto_1
    iget-object v0, v11, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->mSE:Ljava/lang/ref/WeakReference;

    if-nez v0, :cond_5

    move-object/from16 v20, v18

    goto :goto_2

    :cond_5
    invoke-virtual {v0}, Ljava/lang/ref/Reference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/view/View;

    move-object/from16 v20, v0

    :goto_2
    invoke-virtual/range {p0 .. p0}, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->WR()Ljava/lang/String;

    move-result-object v12

    iget-object v0, v11, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->hjc:Landroid/content/Context;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/utils/eh;->Ubf(Landroid/content/Context;)F

    move-result v13

    iget-object v0, v11, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->hjc:Landroid/content/Context;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/utils/eh;->svN(Landroid/content/Context;)I

    move-result v14

    iget-object v0, v11, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->hjc:Landroid/content/Context;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/utils/eh;->WR(Landroid/content/Context;)F

    move-result v15

    move-object/from16 v0, p0

    move/from16 v1, p2

    move/from16 v2, p3

    move/from16 v3, p4

    move-wide/from16 v21, v4

    move/from16 v4, p5

    move-object/from16 v5, p6

    move-wide/from16 v8, v21

    move-object/from16 v10, v19

    move-object/from16 v11, v20

    invoke-virtual/range {v0 .. v17}, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->Fj(FFFFLandroid/util/SparseArray;JJLandroid/view/View;Landroid/view/View;Ljava/lang/String;FIFILorg/json/JSONObject;)Lcom/bytedance/sdk/openadsdk/core/model/mSE;

    move-result-object v0

    move-object/from16 v1, p0

    iput-object v0, v1, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/mSE;

    iget-object v2, v1, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->JU:Ljava/util/Map;

    invoke-virtual {v1, v0, v2}, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/mSE;Ljava/util/Map;)Z

    move-result v0

    if-eqz v0, :cond_6

    return-void

    :cond_6
    iget-object v0, v1, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->dG:Lcom/bykv/vk/openvk/component/video/api/eV/hjc;

    if-eqz v0, :cond_8

    iget-object v0, v1, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->JU:Ljava/util/Map;

    if-nez v0, :cond_7

    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    iput-object v0, v1, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->JU:Ljava/util/Map;

    :cond_7
    iget-object v0, v1, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->JU:Ljava/util/Map;

    iget-object v2, v1, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->dG:Lcom/bykv/vk/openvk/component/video/api/eV/hjc;

    invoke-interface {v2}, Lcom/bykv/vk/openvk/component/video/api/eV/hjc;->Ubf()J

    move-result-wide v2

    invoke-static {v2, v3}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v2

    const-string v3, "duration"

    invoke-interface {v0, v3, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_8
    iget-boolean v0, v1, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->kF:Z

    const/4 v2, 0x2

    const/4 v3, 0x1

    if-eqz v0, :cond_a

    const-string v0, "click"

    iget-object v4, v1, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->Ubf:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    iget-object v5, v1, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/mSE;

    iget-object v6, v1, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->WR:Ljava/lang/String;

    const/4 v7, 0x1

    iget-object v8, v1, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->JU:Ljava/util/Map;

    move/from16 v9, p7

    if-eqz v9, :cond_9

    const/4 v2, 0x1

    :cond_9
    move-object/from16 p1, v0

    move-object/from16 p2, v4

    move-object/from16 p3, v5

    move-object/from16 p4, v6

    move/from16 p5, v7

    move-object/from16 p6, v8

    move/from16 p7, v2

    invoke-static/range {p1 .. p7}, Lcom/bytedance/sdk/openadsdk/ex/hjc;->Fj(Ljava/lang/String;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/core/model/mSE;Ljava/lang/String;ZLjava/util/Map;I)V

    return-void

    :cond_a
    move/from16 v9, p7

    iget-object v0, v1, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->rAx:Lcom/bytedance/sdk/openadsdk/core/ex/ex$Fj;

    move-object/from16 v4, p1

    if-eqz v0, :cond_b

    const/4 v5, -0x1

    invoke-interface {v0, v4, v5}, Lcom/bytedance/sdk/openadsdk/core/ex/ex$Fj;->Fj(Landroid/view/View;I)V

    :cond_b
    invoke-virtual {v1, v4, v9}, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->Fj(Landroid/view/View;Z)Z

    move-result v0

    if-nez v0, :cond_c

    return-void

    :cond_c
    iget-object v0, v1, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->Ubf:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/model/mE;->ex(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v26

    if-eqz v26, :cond_d

    iget-object v0, v1, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->WR:Ljava/lang/String;

    :goto_3
    move-object/from16 v24, v0

    goto :goto_4

    :cond_d
    iget v0, v1, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->svN:I

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/utils/rXP;->Fj(I)Ljava/lang/String;

    move-result-object v0

    goto :goto_3

    :goto_4
    if-eqz v4, :cond_e

    const v0, 0x1f000042

    :try_start_0
    invoke-virtual {v4, v0}, Landroid/view/View;->getTag(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/Boolean;

    invoke-virtual {v0}, Ljava/lang/Boolean;->booleanValue()Z

    move-result v0

    if-eqz v0, :cond_e

    invoke-static {v3}, Lcom/bytedance/sdk/openadsdk/core/rf;->Fj(Z)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_5

    :catch_0
    nop

    :cond_e
    :goto_5
    if-eqz v4, :cond_f

    invoke-static/range {p1 .. p1}, Lcom/bytedance/sdk/component/utils/ex;->Fj(Landroid/view/View;)Landroid/app/Activity;

    move-result-object v18

    :cond_f
    if-nez v18, :cond_10

    iget-object v0, v1, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->hjc:Landroid/content/Context;

    move-object/from16 v19, v0

    goto :goto_6

    :cond_10
    move-object/from16 v19, v18

    :goto_6
    iget-object v0, v1, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->Ubf:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    iget v4, v1, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->svN:I

    iget-object v5, v1, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->UYd:Lcom/bytedance/sdk/openadsdk/api/nativeAd/PAGNativeAd;

    iget-object v6, v1, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->Ql:Lcom/bytedance/sdk/openadsdk/api/PangleAd;

    iget-object v7, v1, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->JW:Lcom/com/bytedance/overseas/sdk/Fj/hjc;

    const/16 v27, 0x0

    move-object/from16 v20, v0

    move/from16 v21, v4

    move-object/from16 v22, v5

    move-object/from16 v23, v6

    move-object/from16 v25, v7

    invoke-static/range {v19 .. v27}, Lcom/bytedance/sdk/openadsdk/core/rf;->Fj(Landroid/content/Context;Lcom/bytedance/sdk/openadsdk/core/model/Ql;ILcom/bytedance/sdk/openadsdk/api/nativeAd/PAGNativeAd;Lcom/bytedance/sdk/openadsdk/api/PangleAd;Ljava/lang/String;Lcom/com/bytedance/overseas/sdk/Fj/hjc;ZI)Z

    move-result v0

    const/4 v4, 0x0

    invoke-static {v4}, Lcom/bytedance/sdk/openadsdk/core/rf;->Fj(Z)V

    if-nez v0, :cond_11

    iget-object v4, v1, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->Ubf:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    if-eqz v4, :cond_11

    invoke-virtual {v4}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->qg()Lcom/bytedance/sdk/openadsdk/core/model/Ko;

    move-result-object v4

    if-eqz v4, :cond_11

    iget-object v4, v1, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->Ubf:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v4}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->qg()Lcom/bytedance/sdk/openadsdk/core/model/Ko;

    move-result-object v4

    invoke-virtual {v4}, Lcom/bytedance/sdk/openadsdk/core/model/Ko;->hjc()I

    move-result v4

    if-ne v4, v2, :cond_11

    return-void

    :cond_11
    iget-object v4, v1, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->Ubf:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    if-eqz v4, :cond_12

    if-nez v0, :cond_12

    invoke-virtual {v4}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->mj()Ljava/lang/String;

    move-result-object v4

    invoke-static {v4}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v4

    if-eqz v4, :cond_12

    iget-object v4, v1, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->WR:Ljava/lang/String;

    invoke-static {v4}, Lcom/bytedance/sdk/openadsdk/ex/ex;->Fj(Ljava/lang/String;)Z

    move-result v4

    if-eqz v4, :cond_12

    iget-object v4, v1, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->hjc:Landroid/content/Context;

    iget-object v5, v1, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->Ubf:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    iget-object v6, v1, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->WR:Ljava/lang/String;

    invoke-static {v4, v5, v6}, Lcom/com/bytedance/overseas/sdk/Fj/eV;->Fj(Landroid/content/Context;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Ljava/lang/String;)Lcom/com/bytedance/overseas/sdk/Fj/hjc;

    move-result-object v4

    invoke-interface {v4}, Lcom/com/bytedance/overseas/sdk/Fj/hjc;->eV()V

    :cond_12
    const-string v4, "click"

    iget-object v5, v1, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->Ubf:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    iget-object v6, v1, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/mSE;

    iget-object v7, v1, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->WR:Ljava/lang/String;

    iget-object v8, v1, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->JU:Ljava/util/Map;

    if-eqz v9, :cond_13

    const/4 v2, 0x1

    :cond_13
    move-object/from16 p1, v4

    move-object/from16 p2, v5

    move-object/from16 p3, v6

    move-object/from16 p4, v7

    move/from16 p5, v0

    move-object/from16 p6, v8

    move/from16 p7, v2

    invoke-static/range {p1 .. p7}, Lcom/bytedance/sdk/openadsdk/ex/hjc;->Fj(Ljava/lang/String;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/core/model/mSE;Ljava/lang/String;ZLjava/util/Map;I)V

    return-void
.end method

.method public Fj(Lcom/bykv/vk/openvk/component/video/api/eV/hjc;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->dG:Lcom/bykv/vk/openvk/component/video/api/eV/hjc;

    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/openadsdk/api/PangleAd;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->Ql:Lcom/bytedance/sdk/openadsdk/api/PangleAd;

    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/openadsdk/api/nativeAd/PAGNativeAd;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->UYd:Lcom/bytedance/sdk/openadsdk/api/nativeAd/PAGNativeAd;

    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/openadsdk/core/ex/ex$Fj;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->rAx:Lcom/bytedance/sdk/openadsdk/core/ex/ex$Fj;

    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Fj;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->rS:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Fj;

    return-void
.end method

.method public Fj(Lcom/com/bytedance/overseas/sdk/Fj/hjc;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->JW:Lcom/com/bytedance/overseas/sdk/Fj/hjc;

    return-void
.end method

.method public Fj(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->Fj:Ljava/lang/String;

    return-void
.end method

.method public Fj(Ljava/util/Map;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/Object;",
            ">;)V"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->JU:Ljava/util/Map;

    if-eqz v0, :cond_0

    invoke-interface {v0, p1}, Ljava/util/Map;->putAll(Ljava/util/Map;)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->JU:Ljava/util/Map;

    invoke-interface {p1, v0}, Ljava/util/Map;->putAll(Ljava/util/Map;)V

    :cond_0
    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->JU:Ljava/util/Map;

    return-void
.end method

.method public Fj(Landroid/view/View;IFFFFLandroid/util/SparseArray;Z)Z
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/view/View;",
            "IFFFF",
            "Landroid/util/SparseArray<",
            "Lcom/bytedance/sdk/openadsdk/core/ex/hjc$Fj;",
            ">;Z)Z"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->rS:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Fj;

    const/4 v1, 0x0

    if-eqz v0, :cond_1

    const/4 v0, 0x2

    new-array v2, v0, [I

    new-array v0, v0, [I

    iget-object v3, p0, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->mSE:Ljava/lang/ref/WeakReference;

    if-eqz v3, :cond_0

    invoke-virtual {v3}, Ljava/lang/ref/Reference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/view/View;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/utils/eh;->Fj(Landroid/view/View;)[I

    move-result-object v2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->mSE:Ljava/lang/ref/WeakReference;

    invoke-virtual {v0}, Ljava/lang/ref/Reference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/view/View;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/utils/eh;->hjc(Landroid/view/View;)[I

    move-result-object v0

    :cond_0
    new-instance v3, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;

    invoke-direct {v3}, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;-><init>()V

    invoke-virtual {v3, p3}, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->eV(F)Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;

    move-result-object p3

    invoke-virtual {p3, p4}, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->hjc(F)Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;

    move-result-object p3

    invoke-virtual {p3, p5}, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->ex(F)Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;

    move-result-object p3

    invoke-virtual {p3, p6}, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->Fj(F)Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;

    move-result-object p3

    iget-wide p4, p0, Lcom/bytedance/sdk/openadsdk/core/ex/hjc;->nsB:J

    invoke-virtual {p3, p4, p5}, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->ex(J)Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;

    move-result-object p3

    iget-wide p4, p0, Lcom/bytedance/sdk/openadsdk/core/ex/hjc;->Vq:J

    invoke-virtual {p3, p4, p5}, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->Fj(J)Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;

    move-result-object p3

    aget p4, v2, v1

    invoke-virtual {p3, p4}, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->hjc(I)Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;

    move-result-object p3

    const/4 p4, 0x1

    aget p5, v2, p4

    invoke-virtual {p3, p5}, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->eV(I)Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;

    move-result-object p3

    aget p5, v0, v1

    invoke-virtual {p3, p5}, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->Ubf(I)Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;

    move-result-object p3

    aget p5, v0, p4

    invoke-virtual {p3, p5}, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->WR(I)Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;

    move-result-object p3

    invoke-virtual {p3, p7}, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->Fj(Landroid/util/SparseArray;)Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;

    move-result-object p3

    invoke-virtual {p3, p8}, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->Fj(Z)Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;

    move-result-object p3

    invoke-virtual {p3}, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->Fj()Lcom/bytedance/sdk/openadsdk/core/model/dG;

    move-result-object p3

    iget-object p5, p0, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->rS:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Fj;

    invoke-interface {p5, p1, p2, p3}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Fj;->Fj(Landroid/view/View;ILcom/bytedance/sdk/openadsdk/core/model/dG;)V

    return p4

    :cond_1
    return v1
.end method

.method public Fj(Landroid/view/View;Z)Z
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->Ubf:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {p1, v0, p2}, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->Fj(Landroid/view/View;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Z)Z

    move-result p1

    return p1
.end method

.method public Fj(Lcom/bytedance/sdk/openadsdk/core/model/mSE;Ljava/util/Map;)Z
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/bytedance/sdk/openadsdk/core/model/mSE;",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/Object;",
            ">;)Z"
        }
    .end annotation

    const/4 p1, 0x0

    return p1
.end method

.method public Ubf()Landroid/view/View;
    .locals 3

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->ex:Ljava/lang/ref/WeakReference;

    if-eqz v0, :cond_2

    invoke-virtual {v0}, Ljava/lang/ref/Reference;->get()Ljava/lang/Object;

    move-result-object v0

    if-nez v0, :cond_0

    goto :goto_0

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->ex:Ljava/lang/ref/WeakReference;

    invoke-virtual {v0}, Ljava/lang/ref/Reference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/app/Activity;

    const v1, 0x1f000011

    invoke-virtual {v0, v1}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->ex:Ljava/lang/ref/WeakReference;

    invoke-virtual {v2}, Ljava/lang/ref/Reference;->get()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Landroid/app/Activity;

    invoke-virtual {v2, v1}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v1

    if-nez v0, :cond_1

    return-object v1

    :cond_1
    return-object v0

    :cond_2
    :goto_0
    const/4 v0, 0x0

    return-object v0
.end method

.method public WR()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->Fj:Ljava/lang/String;

    return-object v0
.end method

.method public eV()Landroid/view/View;
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->ex:Ljava/lang/ref/WeakReference;

    if-eqz v0, :cond_1

    invoke-virtual {v0}, Ljava/lang/ref/Reference;->get()Ljava/lang/Object;

    move-result-object v0

    if-nez v0, :cond_0

    goto :goto_0

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->ex:Ljava/lang/ref/WeakReference;

    invoke-virtual {v0}, Ljava/lang/ref/Reference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/app/Activity;

    const v1, 0x1020002

    invoke-virtual {v0, v1}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    return-object v0

    :cond_1
    :goto_0
    const/4 v0, 0x0

    return-object v0
.end method

.method public eV(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->vYf:I

    return-void
.end method

.method public eV(Z)V
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->Tc:Z

    return-void
.end method

.method public ex(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/ex/hjc;->rf:I

    return-void
.end method

.method public ex(Landroid/view/View;)V
    .locals 1

    if-nez p1, :cond_0

    return-void

    :cond_0
    new-instance v0, Ljava/lang/ref/WeakReference;

    invoke-direct {v0, p1}, Ljava/lang/ref/WeakReference;-><init>(Ljava/lang/Object;)V

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->mSE:Ljava/lang/ref/WeakReference;

    return-void
.end method

.method public hjc(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/ex/hjc;->Moo:I

    return-void
.end method
