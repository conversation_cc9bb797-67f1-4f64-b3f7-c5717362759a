.class Lcom/bytedance/sdk/openadsdk/core/nativeexpress/ex$1;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/component/adexpress/ex/svN;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/core/nativeexpress/ex;->Fj()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/ex;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/ex;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/ex$1;->Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/ex;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj(ILjava/lang/String;)V
    .locals 0

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/ex$1;->Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/ex;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/ex;->ex(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/ex;)Lcom/bytedance/sdk/component/adexpress/ex/JW;

    move-result-object p1

    if-eqz p1, :cond_0

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/ex$1;->Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/ex;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/ex;->ex(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/ex;)Lcom/bytedance/sdk/component/adexpress/ex/JW;

    move-result-object p1

    const/16 p2, 0x6a

    invoke-interface {p1, p2}, Lcom/bytedance/sdk/component/adexpress/ex/JW;->a_(I)V

    :cond_0
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/ex$1;->Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/ex;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/ex;->eV(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/ex;)V

    return-void
.end method

.method public Fj(Landroid/view/View;Lcom/bytedance/sdk/component/adexpress/ex/Tc;)V
    .locals 3

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/ex$1;->Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/ex;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/ex;->Fj(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/ex;)Lcom/bytedance/sdk/openadsdk/core/nativeexpress/NativeExpressView;

    move-result-object v0

    if-eqz v0, :cond_2

    if-nez p1, :cond_0

    goto :goto_0

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/ex$1;->Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/ex;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/ex;->Fj(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/ex;)Lcom/bytedance/sdk/openadsdk/core/nativeexpress/NativeExpressView;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/view/ViewGroup;->removeView(Landroid/view/View;)V

    invoke-virtual {p1}, Landroid/view/View;->getParent()Landroid/view/ViewParent;

    move-result-object v0

    if-eqz v0, :cond_1

    invoke-virtual {p1}, Landroid/view/View;->getParent()Landroid/view/ViewParent;

    move-result-object v0

    check-cast v0, Landroid/view/ViewGroup;

    invoke-virtual {v0, p1}, Landroid/view/ViewGroup;->removeView(Landroid/view/View;)V

    :cond_1
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/ex$1;->Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/ex;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/ex;->Fj(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/ex;)Lcom/bytedance/sdk/openadsdk/core/nativeexpress/NativeExpressView;

    move-result-object v0

    new-instance v1, Landroid/view/ViewGroup$LayoutParams;

    const/4 v2, -0x1

    invoke-direct {v1, v2, v2}, Landroid/view/ViewGroup$LayoutParams;-><init>(II)V

    invoke-virtual {v0, p1, v1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;Landroid/view/ViewGroup$LayoutParams;)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/ex$1;->Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/ex;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/ex;->ex(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/ex;)Lcom/bytedance/sdk/component/adexpress/ex/JW;

    move-result-object p1

    if-eqz p1, :cond_3

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/ex$1;->Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/ex;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/ex;->ex(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/ex;)Lcom/bytedance/sdk/component/adexpress/ex/JW;

    move-result-object p1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/ex$1;->Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/ex;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/ex;->hjc(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/ex;)Lcom/bytedance/sdk/openadsdk/core/nativeexpress/ex$Fj;

    move-result-object v0

    invoke-interface {p1, v0, p2}, Lcom/bytedance/sdk/component/adexpress/ex/JW;->Fj(Lcom/bytedance/sdk/component/adexpress/ex/eV;Lcom/bytedance/sdk/component/adexpress/ex/Tc;)V

    goto :goto_1

    :cond_2
    :goto_0
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/ex$1;->Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/ex;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/ex;->ex(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/ex;)Lcom/bytedance/sdk/component/adexpress/ex/JW;

    move-result-object p1

    if-eqz p1, :cond_3

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/ex$1;->Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/ex;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/ex;->ex(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/ex;)Lcom/bytedance/sdk/component/adexpress/ex/JW;

    move-result-object p1

    const/16 p2, 0x6a

    invoke-interface {p1, p2}, Lcom/bytedance/sdk/component/adexpress/ex/JW;->a_(I)V

    :cond_3
    :goto_1
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/ex$1;->Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/ex;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/ex;->eV(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/ex;)V

    return-void
.end method
