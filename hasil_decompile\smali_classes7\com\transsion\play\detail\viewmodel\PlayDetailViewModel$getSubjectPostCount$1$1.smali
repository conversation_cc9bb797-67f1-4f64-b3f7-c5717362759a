.class final Lcom/transsion/play/detail/viewmodel/PlayDetailViewModel$getSubjectPostCount$1$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/transsion/play/detail/viewmodel/PlayDetailViewModel$getSubjectPostCount$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/flow/b<",
        "-",
        "Lcom/transsion/moviedetailapi/bean/SubjectPostCount;",
        ">;",
        "Lkotlin/coroutines/Continuation<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation

.annotation runtime Lkotlin/coroutines/jvm/internal/DebugMetadata;
    c = "com.transsion.play.detail.viewmodel.PlayDetailViewModel$getSubjectPostCount$1$1"
    f = "PlayDetailViewModel.kt"
    l = {
        0x28,
        0x2a,
        0x2c
    }
    m = "invokeSuspend"
.end annotation


# instance fields
.field final synthetic $subjectId:Ljava/lang/String;

.field private synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lcom/transsion/play/detail/viewmodel/PlayDetailViewModel;


# direct methods
.method public constructor <init>(Lcom/transsion/play/detail/viewmodel/PlayDetailViewModel;Ljava/lang/String;Lkotlin/coroutines/Continuation;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/transsion/play/detail/viewmodel/PlayDetailViewModel;",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/Continuation<",
            "-",
            "Lcom/transsion/play/detail/viewmodel/PlayDetailViewModel$getSubjectPostCount$1$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lcom/transsion/play/detail/viewmodel/PlayDetailViewModel$getSubjectPostCount$1$1;->this$0:Lcom/transsion/play/detail/viewmodel/PlayDetailViewModel;

    iput-object p2, p0, Lcom/transsion/play/detail/viewmodel/PlayDetailViewModel$getSubjectPostCount$1$1;->$subjectId:Ljava/lang/String;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p3}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/Continuation;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/Continuation;)Lkotlin/coroutines/Continuation;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/Continuation<",
            "*>;)",
            "Lkotlin/coroutines/Continuation<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lcom/transsion/play/detail/viewmodel/PlayDetailViewModel$getSubjectPostCount$1$1;

    iget-object v1, p0, Lcom/transsion/play/detail/viewmodel/PlayDetailViewModel$getSubjectPostCount$1$1;->this$0:Lcom/transsion/play/detail/viewmodel/PlayDetailViewModel;

    iget-object v2, p0, Lcom/transsion/play/detail/viewmodel/PlayDetailViewModel$getSubjectPostCount$1$1;->$subjectId:Ljava/lang/String;

    invoke-direct {v0, v1, v2, p2}, Lcom/transsion/play/detail/viewmodel/PlayDetailViewModel$getSubjectPostCount$1$1;-><init>(Lcom/transsion/play/detail/viewmodel/PlayDetailViewModel;Ljava/lang/String;Lkotlin/coroutines/Continuation;)V

    iput-object p1, v0, Lcom/transsion/play/detail/viewmodel/PlayDetailViewModel$getSubjectPostCount$1$1;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    check-cast p1, Lkotlinx/coroutines/flow/b;

    check-cast p2, Lkotlin/coroutines/Continuation;

    invoke-virtual {p0, p1, p2}, Lcom/transsion/play/detail/viewmodel/PlayDetailViewModel$getSubjectPostCount$1$1;->invoke(Lkotlinx/coroutines/flow/b;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/flow/b;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/flow/b<",
            "-",
            "Lcom/transsion/moviedetailapi/bean/SubjectPostCount;",
            ">;",
            "Lkotlin/coroutines/Continuation<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    invoke-virtual {p0, p1, p2}, Lcom/transsion/play/detail/viewmodel/PlayDetailViewModel$getSubjectPostCount$1$1;->create(Ljava/lang/Object;Lkotlin/coroutines/Continuation;)Lkotlin/coroutines/Continuation;

    move-result-object p1

    check-cast p1, Lcom/transsion/play/detail/viewmodel/PlayDetailViewModel$getSubjectPostCount$1$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lcom/transsion/play/detail/viewmodel/PlayDetailViewModel$getSubjectPostCount$1$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 7

    invoke-static {}, Lkotlin/coroutines/intrinsics/IntrinsicsKt;->e()Ljava/lang/Object;

    move-result-object v0

    iget v1, p0, Lcom/transsion/play/detail/viewmodel/PlayDetailViewModel$getSubjectPostCount$1$1;->label:I

    const/4 v2, 0x3

    const/4 v3, 0x2

    const/4 v4, 0x1

    if-eqz v1, :cond_3

    if-eq v1, v4, :cond_2

    if-eq v1, v3, :cond_1

    if-ne v1, v2, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1

    :cond_1
    :goto_0
    invoke-static {p1}, Lkotlin/ResultKt;->b(Ljava/lang/Object;)V

    goto :goto_2

    :cond_2
    iget-object v1, p0, Lcom/transsion/play/detail/viewmodel/PlayDetailViewModel$getSubjectPostCount$1$1;->L$0:Ljava/lang/Object;

    check-cast v1, Lkotlinx/coroutines/flow/b;

    invoke-static {p1}, Lkotlin/ResultKt;->b(Ljava/lang/Object;)V

    goto :goto_1

    :cond_3
    invoke-static {p1}, Lkotlin/ResultKt;->b(Ljava/lang/Object;)V

    iget-object p1, p0, Lcom/transsion/play/detail/viewmodel/PlayDetailViewModel$getSubjectPostCount$1$1;->L$0:Ljava/lang/Object;

    move-object v1, p1

    check-cast v1, Lkotlinx/coroutines/flow/b;

    iget-object p1, p0, Lcom/transsion/play/detail/viewmodel/PlayDetailViewModel$getSubjectPostCount$1$1;->this$0:Lcom/transsion/play/detail/viewmodel/PlayDetailViewModel;

    invoke-static {p1}, Lcom/transsion/play/detail/viewmodel/PlayDetailViewModel;->b(Lcom/transsion/play/detail/viewmodel/PlayDetailViewModel;)Ljo/a;

    move-result-object p1

    sget-object v5, Lfj/a;->a:Lfj/a$a;

    invoke-virtual {v5}, Lfj/a$a;->a()Ljava/lang/String;

    move-result-object v5

    iget-object v6, p0, Lcom/transsion/play/detail/viewmodel/PlayDetailViewModel$getSubjectPostCount$1$1;->$subjectId:Ljava/lang/String;

    iput-object v1, p0, Lcom/transsion/play/detail/viewmodel/PlayDetailViewModel$getSubjectPostCount$1$1;->L$0:Ljava/lang/Object;

    iput v4, p0, Lcom/transsion/play/detail/viewmodel/PlayDetailViewModel$getSubjectPostCount$1$1;->label:I

    invoke-interface {p1, v5, v6, p0}, Ljo/a;->a(Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;

    move-result-object p1

    if-ne p1, v0, :cond_4

    return-object v0

    :cond_4
    :goto_1
    check-cast p1, Lcom/tn/lib/net/bean/BaseDto;

    invoke-virtual {p1}, Lcom/tn/lib/net/bean/BaseDto;->getCode()Ljava/lang/String;

    move-result-object v4

    const-string v5, "0"

    invoke-static {v4, v5}, Lkotlin/jvm/internal/Intrinsics;->b(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v4

    const/4 v5, 0x0

    if-nez v4, :cond_5

    iput-object v5, p0, Lcom/transsion/play/detail/viewmodel/PlayDetailViewModel$getSubjectPostCount$1$1;->L$0:Ljava/lang/Object;

    iput v3, p0, Lcom/transsion/play/detail/viewmodel/PlayDetailViewModel$getSubjectPostCount$1$1;->label:I

    invoke-interface {v1, v5, p0}, Lkotlinx/coroutines/flow/b;->emit(Ljava/lang/Object;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;

    move-result-object p1

    if-ne p1, v0, :cond_6

    return-object v0

    :cond_5
    invoke-virtual {p1}, Lcom/tn/lib/net/bean/BaseDto;->getData()Ljava/lang/Object;

    move-result-object p1

    iput-object v5, p0, Lcom/transsion/play/detail/viewmodel/PlayDetailViewModel$getSubjectPostCount$1$1;->L$0:Ljava/lang/Object;

    iput v2, p0, Lcom/transsion/play/detail/viewmodel/PlayDetailViewModel$getSubjectPostCount$1$1;->label:I

    invoke-interface {v1, p1, p0}, Lkotlinx/coroutines/flow/b;->emit(Ljava/lang/Object;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;

    move-result-object p1

    if-ne p1, v0, :cond_6

    return-object v0

    :cond_6
    :goto_2
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    return-object p1
.end method
