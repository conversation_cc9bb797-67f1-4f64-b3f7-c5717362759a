.class Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf$1;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/openadsdk/core/Ql$Fj;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->Fj(Lcom/bytedance/sdk/openadsdk/AdSlot;Lcom/bytedance/sdk/openadsdk/common/hjc;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/AdSlot;

.field final synthetic ex:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;Lcom/bytedance/sdk/openadsdk/AdSlot;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf$1;->ex:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;

    iput-object p2, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf$1;->Fj:Lcom/bytedance/sdk/openadsdk/AdSlot;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj(ILjava/lang/String;)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf$1;->ex:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;

    invoke-static {v0, p1, p2}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->Fj(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;ILjava/lang/String;)V

    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/openadsdk/core/model/Fj;Lcom/bytedance/sdk/openadsdk/core/model/ex;)V
    .locals 2

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/model/Fj;->hjc()Ljava/util/List;

    move-result-object v0

    if-eqz v0, :cond_0

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/model/Fj;->hjc()Ljava/util/List;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_0

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf$1;->ex:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/model/Fj;->hjc()Ljava/util/List;

    move-result-object v0

    invoke-static {p2, v0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->Fj(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;Ljava/util/List;)Ljava/util/List;

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf$1;->ex:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/model/Fj;->hjc()Ljava/util/List;

    move-result-object p1

    invoke-static {p2, p1}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->ex(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;Ljava/util/List;)Ljava/util/List;

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf$1;->ex:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf$1;->Fj:Lcom/bytedance/sdk/openadsdk/AdSlot;

    invoke-static {p1, p2}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->Fj(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;Lcom/bytedance/sdk/openadsdk/AdSlot;)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf$1;->ex:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->Fj(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;)Lcom/bytedance/sdk/openadsdk/utils/lv;

    move-result-object p2

    invoke-static {p1, p2}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->Fj(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;Lcom/bytedance/sdk/openadsdk/utils/lv;)V

    return-void

    :cond_0
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf$1;->ex:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;

    const/4 v0, -0x3

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/svN;->Fj(I)Ljava/lang/String;

    move-result-object v1

    invoke-static {p1, v0, v1}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->Fj(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;ILjava/lang/String;)V

    invoke-virtual {p2, v0}, Lcom/bytedance/sdk/openadsdk/core/model/ex;->Fj(I)V

    invoke-static {p2}, Lcom/bytedance/sdk/openadsdk/core/model/ex;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/ex;)V

    return-void
.end method
