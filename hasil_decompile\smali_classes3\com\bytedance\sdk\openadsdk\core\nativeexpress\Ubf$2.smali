.class Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf$2;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->Fj(Lcom/bytedance/sdk/openadsdk/utils/lv;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/utils/lv;

.field final synthetic ex:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;Lcom/bytedance/sdk/openadsdk/utils/lv;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf$2;->ex:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;

    iput-object p2, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf$2;->Fj:Lcom/bytedance/sdk/openadsdk/utils/lv;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf$2;->ex:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->ex(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;)Ljava/util/List;

    move-result-object v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf$2;->ex:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->ex(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;)Ljava/util/List;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    if-lez v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf$2;->ex:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf$2;->Fj:Lcom/bytedance/sdk/openadsdk/utils/lv;

    invoke-static {v0, v1}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->ex(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;Lcom/bytedance/sdk/openadsdk/utils/lv;)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf$2;->ex:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->hjc(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;)Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf$Fj;

    move-result-object v0

    if-eqz v0, :cond_1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf$2;->ex:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->hjc(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;)Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf$Fj;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf$2;->ex:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;

    invoke-static {v1}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->ex(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;)Ljava/util/List;

    move-result-object v1

    invoke-interface {v0, v1}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf$Fj;->Fj(Ljava/util/List;)V

    goto :goto_0

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf$2;->ex:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->hjc(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;)Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf$Fj;

    move-result-object v0

    if-eqz v0, :cond_1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf$2;->ex:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->hjc(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;)Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf$Fj;

    move-result-object v0

    invoke-interface {v0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf$Fj;->Fj()V

    :cond_1
    :goto_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf$2;->ex:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;->eV(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ubf;)V

    return-void
.end method
