.class Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex$2;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;->mC()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex$2;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex$2;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->mj:Lcom/bytedance/sdk/openadsdk/mSE/svN;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/mSE/svN;->Fj()I

    move-result v0

    if-lez v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex$2;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->mj:Lcom/bytedance/sdk/openadsdk/mSE/svN;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/mSE/svN;->Fj(Z)V

    :cond_0
    return-void
.end method
