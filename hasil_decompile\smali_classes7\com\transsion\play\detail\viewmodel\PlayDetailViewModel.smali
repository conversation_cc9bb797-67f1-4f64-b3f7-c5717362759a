.class public final Lcom/transsion/play/detail/viewmodel/PlayDetailViewModel;
.super Landroidx/lifecycle/u0;


# annotations
.annotation runtime Lkotlin/Metadata;
.end annotation


# instance fields
.field public final a:Lkotlin/Lazy;

.field public final b:Landroidx/lifecycle/c0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Landroidx/lifecycle/c0<",
            "Lcom/transsion/moviedetailapi/bean/SubjectPostCount;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>()V
    .locals 2

    invoke-direct {p0}, Landroidx/lifecycle/u0;-><init>()V

    sget-object v0, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    sget-object v1, Lcom/transsion/play/detail/viewmodel/PlayDetailViewModel$service$2;->INSTANCE:Lcom/transsion/play/detail/viewmodel/PlayDetailViewModel$service$2;

    invoke-static {v0, v1}, Lkotlin/LazyKt;->a(L<PERSON>lin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/Lazy;

    move-result-object v0

    iput-object v0, p0, Lcom/transsion/play/detail/viewmodel/PlayDetailViewModel;->a:Lkotlin/Lazy;

    new-instance v0, Landroidx/lifecycle/c0;

    invoke-direct {v0}, Landroidx/lifecycle/c0;-><init>()V

    iput-object v0, p0, Lcom/transsion/play/detail/viewmodel/PlayDetailViewModel;->b:Landroidx/lifecycle/c0;

    return-void
.end method

.method public static final synthetic b(Lcom/transsion/play/detail/viewmodel/PlayDetailViewModel;)Ljo/a;
    .locals 0

    invoke-virtual {p0}, Lcom/transsion/play/detail/viewmodel/PlayDetailViewModel;->d()Ljo/a;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic c(Lcom/transsion/play/detail/viewmodel/PlayDetailViewModel;)Landroidx/lifecycle/c0;
    .locals 0

    iget-object p0, p0, Lcom/transsion/play/detail/viewmodel/PlayDetailViewModel;->b:Landroidx/lifecycle/c0;

    return-object p0
.end method


# virtual methods
.method public final d()Ljo/a;
    .locals 1

    iget-object v0, p0, Lcom/transsion/play/detail/viewmodel/PlayDetailViewModel;->a:Lkotlin/Lazy;

    invoke-interface {v0}, Lkotlin/Lazy;->getValue()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljo/a;

    return-object v0
.end method

.method public final e(Ljava/lang/String;)V
    .locals 7

    const-string v0, "subjectId"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-static {p0}, Landroidx/lifecycle/v0;->a(Landroidx/lifecycle/u0;)Lkotlinx/coroutines/k0;

    move-result-object v1

    const/4 v2, 0x0

    const/4 v3, 0x0

    new-instance v4, Lcom/transsion/play/detail/viewmodel/PlayDetailViewModel$getSubjectPostCount$1;

    const/4 v0, 0x0

    invoke-direct {v4, p0, p1, v0}, Lcom/transsion/play/detail/viewmodel/PlayDetailViewModel$getSubjectPostCount$1;-><init>(Lcom/transsion/play/detail/viewmodel/PlayDetailViewModel;Ljava/lang/String;Lkotlin/coroutines/Continuation;)V

    const/4 v5, 0x3

    const/4 v6, 0x0

    invoke-static/range {v1 .. v6}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/k0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/q1;

    return-void
.end method

.method public final f()Landroidx/lifecycle/c0;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Landroidx/lifecycle/c0<",
            "Lcom/transsion/moviedetailapi/bean/SubjectPostCount;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Lcom/transsion/play/detail/viewmodel/PlayDetailViewModel;->b:Landroidx/lifecycle/c0;

    return-object v0
.end method
