.class public Lcom/bytedance/sdk/openadsdk/core/nativeexpress/eV;
.super Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj;


# instance fields
.field private final Fj:Lcom/bytedance/sdk/component/adexpress/ex/dG;

.field private final eV:Ljava/lang/Runnable;

.field private ex:Lcom/bytedance/sdk/component/adexpress/ex/svN;

.field private final hjc:Lcom/bytedance/sdk/component/svN/BcC;


# direct methods
.method public constructor <init>(Landroid/content/Context;Lcom/bytedance/sdk/component/adexpress/theme/ThemeStatusBroadcastReceiver;ZLcom/bytedance/sdk/component/adexpress/dynamic/eV/BcC;Lcom/bytedance/sdk/component/adexpress/ex/dG;Lcom/bytedance/sdk/component/adexpress/dynamic/Ubf/Fj;)V
    .locals 0

    invoke-direct/range {p0 .. p6}, Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj;-><init>(Landroid/content/Context;Lcom/bytedance/sdk/component/adexpress/theme/ThemeStatusBroadcastReceiver;ZLcom/bytedance/sdk/component/adexpress/dynamic/eV/BcC;Lcom/bytedance/sdk/component/adexpress/ex/dG;Lcom/bytedance/sdk/component/adexpress/dynamic/Ubf/Fj;)V

    new-instance p1, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/eV$1;

    const-string p2, "dynamic_render_template"

    invoke-direct {p1, p0, p2}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/eV$1;-><init>(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/eV;Ljava/lang/String;)V

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/eV;->hjc:Lcom/bytedance/sdk/component/svN/BcC;

    new-instance p1, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/eV$2;

    invoke-direct {p1, p0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/eV$2;-><init>(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/eV;)V

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/eV;->eV:Ljava/lang/Runnable;

    iput-object p5, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/eV;->Fj:Lcom/bytedance/sdk/component/adexpress/ex/dG;

    return-void
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/eV;)Lcom/bytedance/sdk/component/adexpress/ex/dG;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/eV;->Fj:Lcom/bytedance/sdk/component/adexpress/ex/dG;

    return-object p0
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/eV;Lcom/bytedance/sdk/component/adexpress/ex/svN;)V
    .locals 0

    invoke-super {p0, p1}, Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj;->Fj(Lcom/bytedance/sdk/component/adexpress/ex/svN;)V

    return-void
.end method

.method public static synthetic ex(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/eV;)Ljava/lang/Runnable;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/eV;->eV:Ljava/lang/Runnable;

    return-object p0
.end method

.method public static synthetic hjc(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/eV;)Lcom/bytedance/sdk/component/adexpress/ex/svN;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/eV;->ex:Lcom/bytedance/sdk/component/adexpress/ex/svN;

    return-object p0
.end method


# virtual methods
.method public Fj(Lcom/bytedance/sdk/component/adexpress/ex/svN;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/eV;->ex:Lcom/bytedance/sdk/component/adexpress/ex/svN;

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/eV;->hjc:Lcom/bytedance/sdk/component/svN/BcC;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/utils/uy;->ex(Lcom/bytedance/sdk/component/svN/BcC;)V

    return-void
.end method

.method public ex()V
    .locals 2

    invoke-super {p0}, Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj;->ex()V

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/dG;->hjc()Landroid/os/Handler;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/eV;->eV:Ljava/lang/Runnable;

    invoke-virtual {v0, v1}, Landroid/os/Handler;->removeCallbacks(Ljava/lang/Runnable;)V

    return-void
.end method
