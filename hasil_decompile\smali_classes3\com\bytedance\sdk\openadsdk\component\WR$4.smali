.class Lcom/bytedance/sdk/openadsdk/component/WR$4;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/openadsdk/component/WR$hjc;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/WR;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/AdSlot;Lcom/bytedance/sdk/openadsdk/core/model/mC;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:I

.field final synthetic Ubf:Lcom/bytedance/sdk/openadsdk/component/WR;

.field final synthetic eV:Lcom/bytedance/sdk/openadsdk/AdSlot;

.field final synthetic ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

.field final synthetic hjc:Lcom/bytedance/sdk/openadsdk/core/model/mC;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/WR;ILcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/core/model/mC;Lcom/bytedance/sdk/openadsdk/AdSlot;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/WR$4;->Ubf:Lcom/bytedance/sdk/openadsdk/component/WR;

    iput p2, p0, Lcom/bytedance/sdk/openadsdk/component/WR$4;->Fj:I

    iput-object p3, p0, Lcom/bytedance/sdk/openadsdk/component/WR$4;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    iput-object p4, p0, Lcom/bytedance/sdk/openadsdk/component/WR$4;->hjc:Lcom/bytedance/sdk/openadsdk/core/model/mC;

    iput-object p5, p0, Lcom/bytedance/sdk/openadsdk/component/WR$4;->eV:Lcom/bytedance/sdk/openadsdk/AdSlot;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj()V
    .locals 3

    new-instance v0, Lcom/bytedance/sdk/openadsdk/component/Ubf/Fj;

    iget v1, p0, Lcom/bytedance/sdk/openadsdk/component/WR$4;->Fj:I

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/component/WR$4;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-direct {v0, v1, v2}, Lcom/bytedance/sdk/openadsdk/component/Ubf/Fj;-><init>(ILcom/bytedance/sdk/openadsdk/core/model/Ql;)V

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/WR$4;->Ubf:Lcom/bytedance/sdk/openadsdk/component/WR;

    invoke-virtual {v1, v0}, Lcom/bytedance/sdk/openadsdk/component/WR;->Fj(Lcom/bytedance/sdk/openadsdk/component/Ubf/Fj;)V

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/Ubf/Fj;->ex()Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    move-result-object v0

    const/4 v1, 0x1

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/component/WR$4;->hjc:Lcom/bytedance/sdk/openadsdk/core/model/mC;

    invoke-static {v0, v1, v2}, Lcom/bytedance/sdk/openadsdk/component/eV/Fj;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;ILcom/bytedance/sdk/openadsdk/core/model/mC;)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/WR$4;->Ubf:Lcom/bytedance/sdk/openadsdk/component/WR;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/WR$4;->eV:Lcom/bytedance/sdk/openadsdk/AdSlot;

    invoke-static {v0, v1}, Lcom/bytedance/sdk/openadsdk/component/WR;->Fj(Lcom/bytedance/sdk/openadsdk/component/WR;Lcom/bytedance/sdk/openadsdk/AdSlot;)V

    return-void
.end method

.method public Fj(ILjava/lang/String;)V
    .locals 0

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/WR$4;->Ubf:Lcom/bytedance/sdk/openadsdk/component/WR;

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/WR$4;->eV:Lcom/bytedance/sdk/openadsdk/AdSlot;

    invoke-static {p1, p2}, Lcom/bytedance/sdk/openadsdk/component/WR;->Fj(Lcom/bytedance/sdk/openadsdk/component/WR;Lcom/bytedance/sdk/openadsdk/AdSlot;)V

    return-void
.end method
