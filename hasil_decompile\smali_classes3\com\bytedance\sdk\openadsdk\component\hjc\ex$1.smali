.class Lcom/bytedance/sdk/openadsdk/component/hjc/ex$1;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/hjc/ex;->onError(ILjava/lang/String;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:I

.field final synthetic ex:Ljava/lang/String;

.field final synthetic hjc:Lcom/bytedance/sdk/openadsdk/component/hjc/ex;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/hjc/ex;ILjava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/hjc/ex$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/hjc/ex;

    iput p2, p0, Lcom/bytedance/sdk/openadsdk/component/hjc/ex$1;->Fj:I

    iput-object p3, p0, Lcom/bytedance/sdk/openadsdk/component/hjc/ex$1;->ex:Ljava/lang/String;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 3

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/hjc/ex$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/hjc/ex;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/hjc/ex;->Fj:Lcom/bytedance/sdk/openadsdk/api/interstitial/PAGInterstitialAdLoadListener;

    if-eqz v0, :cond_0

    iget v1, p0, Lcom/bytedance/sdk/openadsdk/component/hjc/ex$1;->Fj:I

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/component/hjc/ex$1;->ex:Ljava/lang/String;

    invoke-interface {v0, v1, v2}, Lcom/bytedance/sdk/openadsdk/api/PAGLoadListener;->onError(ILjava/lang/String;)V

    :cond_0
    return-void
.end method
