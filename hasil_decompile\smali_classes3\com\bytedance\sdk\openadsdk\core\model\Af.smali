.class public Lcom/bytedance/sdk/openadsdk/core/model/Af;
.super Lcom/bytedance/sdk/openadsdk/core/model/Ql;


# instance fields
.field private AY:I

.field private Af:I

.field private At:I

.field private Bb:Lcom/bytedance/sdk/openadsdk/core/model/mE;

.field private BcC:I

.field private Bzy:Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;

.field private CML:I

.field private Eev:I

.field private Fi:Lorg/json/JSONObject;

.field public Fj:Z

.field private GZ:[Ljava/lang/String;

.field private Gv:Lorg/json/JSONObject;

.field private HQ:I

.field private HY:Lorg/json/JSONObject;

.field private Im:Ljava/lang/String;

.field private JU:Ljava/lang/String;

.field private JW:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field private JZ:I

.field private Jq:Lcom/bytedance/sdk/openadsdk/AdSlot;

.field private KV:Z

.field private KZ:Z

.field private Kf:I

.field private Kk:I

.field private Ko:Lcom/bytedance/sdk/openadsdk/core/model/Tc;

.field private Moo:Ljava/lang/String;

.field private NgJ:Ljava/lang/String;

.field private Nyg:I

.field private OK:I

.field private OQa:Lcom/bytedance/sdk/openadsdk/core/ugen/Ubf/Fj;

.field private OXv:I

.field private Obv:Ljava/lang/String;

.field private PpV:I

.field private QR:F

.field private QV:Lcom/bytedance/sdk/openadsdk/core/model/rAx;

.field private Ql:Ljava/lang/String;

.field private Tc:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field private UF:I

.field private UYd:Ljava/lang/String;

.field private final Ubf:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/bytedance/sdk/openadsdk/FilterWord;",
            ">;"
        }
    .end annotation
.end field

.field private Ud:Lcom/bytedance/sdk/openadsdk/core/model/JU;

.field private VXW:I

.field private Vq:I

.field private final WR:Lcom/bytedance/sdk/openadsdk/utils/lv;

.field private Wx:Lcom/bytedance/sdk/openadsdk/core/model/UYd;

.field private Xnz:I

.field private YH:Lcom/bytedance/sdk/openadsdk/core/model/BcC;

.field private YP:Ljava/lang/String;

.field private aGk:I

.field private aYy:I

.field private cB:Lcom/bytedance/sdk/openadsdk/core/model/hjc;

.field private cP:Z

.field private cs:I

.field private dG:Ljava/lang/String;

.field private final eV:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field private efV:Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

.field private eh:I

.field private ei:Ljava/lang/String;

.field public ex:J

.field private fHV:Lcom/bytedance/sdk/openadsdk/core/svN/Fj;

.field private fU:J

.field private fj:I

.field private fjZ:I

.field private flF:I

.field private gXF:I

.field private gci:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field

.field private haP:I

.field private final hjc:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/bytedance/sdk/openadsdk/core/model/Tc;",
            ">;"
        }
    .end annotation
.end field

.field private iT:Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

.field private jID:I

.field private jsD:I

.field private kF:J

.field private kl:Z

.field private ks:I

.field private lv:Ljava/lang/String;

.field private mC:Ljava/lang/String;

.field private mE:Ljava/lang/String;

.field private mSE:I

.field private mj:Z

.field private mt:I

.field private nsB:Lcom/bytedance/sdk/openadsdk/core/model/Ko;

.field private oX:Z

.field private qPr:Z

.field private qg:I

.field private qw:Z

.field private rAx:Lcom/bytedance/sdk/openadsdk/core/model/Tc;

.field private rS:Ljava/lang/String;

.field private rXP:I

.field private rf:Ljava/lang/String;

.field private rqT:Ljava/lang/String;

.field private spi:Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

.field private ss:I

.field private sse:Z

.field private svN:I

.field private tc:Z

.field private volatile ttV:Z

.field private tyC:Ljava/lang/String;

.field private uM:I

.field private uy:Ljava/lang/String;

.field private vYf:Ljava/lang/String;

.field private vg:Ljava/lang/String;

.field private xEF:I

.field private xx:I

.field private yR:Ljava/lang/String;

.field private yo:Ljava/lang/String;

.field private zDD:Ljava/lang/String;

.field private zX:Ljava/lang/String;

.field private zYh:Lcom/bytedance/sdk/openadsdk/core/model/eV;

.field private zf:I


# direct methods
.method public constructor <init>()V
    .locals 4

    invoke-direct {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;-><init>()V

    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->hjc:Ljava/util/List;

    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->eV:Ljava/util/List;

    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Ubf:Ljava/util/List;

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/utils/lv;->ex()Lcom/bytedance/sdk/openadsdk/utils/lv;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->WR:Lcom/bytedance/sdk/openadsdk/utils/lv;

    const/4 v0, 0x0

    iput-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Fj:Z

    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    iput-object v1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Tc:Ljava/util/List;

    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    iput-object v1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->JW:Ljava/util/List;

    const-string v1, "0"

    iput-object v1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->mC:Ljava/lang/String;

    const-string v1, ""

    iput-object v1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->lv:Ljava/lang/String;

    iput v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->rXP:I

    const/4 v1, 0x2

    iput v1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->fj:I

    iput v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->eh:I

    iput v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Kk:I

    const/4 v2, 0x1

    iput v2, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->cs:I

    new-instance v3, Lcom/bytedance/sdk/openadsdk/core/model/BcC;

    invoke-direct {v3}, Lcom/bytedance/sdk/openadsdk/core/model/BcC;-><init>()V

    iput-object v3, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->YH:Lcom/bytedance/sdk/openadsdk/core/model/BcC;

    const/16 v3, -0xc8

    iput v3, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->At:I

    iput v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->qg:I

    iput v2, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->flF:I

    iput v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->ks:I

    iput v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->JZ:I

    iput v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Eev:I

    iput v2, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Nyg:I

    iput v2, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->ss:I

    const/high16 v3, 0x42c80000    # 100.0f

    iput v3, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->QR:F

    iput v1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->UF:I

    iput v2, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Kf:I

    iput v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->mt:I

    const/4 v1, -0x1

    iput v1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->AY:I

    const-wide/16 v1, -0x1

    iput-wide v1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->fU:J

    iput v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->xx:I

    iput v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->VXW:I

    iput-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->kl:Z

    iput v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->aYy:I

    return-void
.end method

.method private BG()Lcom/bytedance/sdk/openadsdk/core/ugen/Ubf/Fj;
    .locals 2

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->uM()Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;

    move-result-object v0

    if-nez v0, :cond_0

    const/4 v0, 0x0

    return-object v0

    :cond_0
    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->aYy()I

    move-result v1

    invoke-static {v1}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;->ex(Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;Ljava/lang/String;)Lcom/bytedance/sdk/openadsdk/core/ugen/Ubf/Fj;

    move-result-object v0

    return-object v0
.end method

.method private static Ubf(Lorg/json/JSONObject;)D
    .locals 3

    const-wide/16 v0, 0x0

    if-eqz p0, :cond_0

    const-string v2, "pack_time"

    invoke-virtual {p0, v2, v0, v1}, Lorg/json/JSONObject;->optDouble(Ljava/lang/String;D)D

    move-result-wide v0

    :cond_0
    return-wide v0
.end method

.method private ex(Lcom/bytedance/sdk/openadsdk/FilterWord;)Lorg/json/JSONObject;
    .locals 4

    const/4 v0, 0x0

    if-nez p1, :cond_0

    return-object v0

    :cond_0
    :try_start_0
    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/FilterWord;->isValid()Z

    move-result v1

    if-eqz v1, :cond_3

    new-instance v1, Lorg/json/JSONObject;

    invoke-direct {v1}, Lorg/json/JSONObject;-><init>()V

    const-string v2, "id"

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/FilterWord;->getId()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v1, v2, v3}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    const-string v2, "name"

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/FilterWord;->getName()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v1, v2, v3}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    const-string v2, "is_selected"

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/FilterWord;->getIsSelected()Z

    move-result v3

    invoke-virtual {v1, v2, v3}, Lorg/json/JSONObject;->put(Ljava/lang/String;Z)Lorg/json/JSONObject;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/FilterWord;->hasSecondOptions()Z

    move-result v2

    if-eqz v2, :cond_2

    new-instance v2, Lorg/json/JSONArray;

    invoke-direct {v2}, Lorg/json/JSONArray;-><init>()V

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/FilterWord;->getOptions()Ljava/util/List;

    move-result-object p1

    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_1

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/bytedance/sdk/openadsdk/FilterWord;

    invoke-direct {p0, v3}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->ex(Lcom/bytedance/sdk/openadsdk/FilterWord;)Lorg/json/JSONObject;

    move-result-object v3

    invoke-virtual {v2, v3}, Lorg/json/JSONArray;->put(Ljava/lang/Object;)Lorg/json/JSONArray;

    goto :goto_0

    :cond_1
    invoke-virtual {v2}, Lorg/json/JSONArray;->length()I

    move-result p1

    if-lez p1, :cond_2

    const-string p1, "options"

    invoke-virtual {v1, p1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :cond_2
    return-object v1

    :catchall_0
    :cond_3
    return-object v0
.end method


# virtual methods
.method public AY()[Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->GZ:[Ljava/lang/String;

    if-eqz v0, :cond_0

    array-length v0, v0

    if-gtz v0, :cond_1

    :cond_0
    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Obv()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-nez v0, :cond_1

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Obv()Ljava/lang/String;

    move-result-object v0

    filled-new-array {v0}, [Ljava/lang/String;

    move-result-object v0

    return-object v0

    :cond_1
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->GZ:[Ljava/lang/String;

    return-object v0
.end method

.method public Af(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->ss:I

    return-void
.end method

.method public Af(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->yR:Ljava/lang/String;

    return-void
.end method

.method public Af()Z
    .locals 2

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->ks:I

    const/4 v1, 0x1

    if-ne v0, v1, :cond_0

    return v1

    :cond_0
    const/4 v0, 0x0

    return v0
.end method

.method public At()Lcom/bytedance/sdk/openadsdk/core/model/hjc;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->cB:Lcom/bytedance/sdk/openadsdk/core/model/hjc;

    return-object v0
.end method

.method public Bb()V
    .locals 1

    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->ttV:Z

    return-void
.end method

.method public BcC()Lcom/bytedance/sdk/openadsdk/core/model/JU;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Ud:Lcom/bytedance/sdk/openadsdk/core/model/JU;

    return-object v0
.end method

.method public BcC(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->cs:I

    return-void
.end method

.method public BcC(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->tyC:Ljava/lang/String;

    return-void
.end method

.method public Bzy()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->eV:Ljava/util/List;

    return-object v0
.end method

.method public CML()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->xx:I

    return v0
.end method

.method public ES()Z
    .locals 2

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->uM()Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;

    move-result-object v0

    if-eqz v0, :cond_0

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->uM()Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;->hjc()Ljava/util/List;

    move-result-object v0

    if-eqz v0, :cond_0

    const/16 v1, 0x39

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    invoke-interface {v0, v1}, Ljava/util/List;->contains(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    return v0

    :cond_0
    const/4 v0, 0x0

    return v0
.end method

.method public Eev()Ljava/util/Map;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->gci:Ljava/util/Map;

    return-object v0
.end method

.method public Fi()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->aGk:I

    return v0
.end method

.method public Fj(D)V
    .locals 3

    const-wide/high16 v0, 0x4000000000000000L    # 2.0

    cmpl-double v2, p1, v0

    if-eqz v2, :cond_0

    const-wide/high16 v0, 0x3ff0000000000000L    # 1.0

    cmpl-double v2, p1, v0

    if-eqz v2, :cond_0

    const/4 p1, 0x1

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Kf:I

    return-void

    :cond_0
    double-to-int p1, p1

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Kf:I

    return-void
.end method

.method public Fj(F)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->QR:F

    return-void
.end method

.method public Fj(I)V
    .locals 1

    const/4 v0, -0x1

    invoke-virtual {p0, p1, v0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Fj(II)V

    return-void
.end method

.method public Fj(II)V
    .locals 6

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->mSE:I

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->BcC:I

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Jq:Lcom/bytedance/sdk/openadsdk/AdSlot;

    const/4 v1, 0x0

    if-nez v0, :cond_0

    iput v1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->BcC:I

    return-void

    :cond_0
    if-gtz p2, :cond_1

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Im()I

    move-result p2

    :cond_1
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Jq:Lcom/bytedance/sdk/openadsdk/AdSlot;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/AdSlot;->getNativeAdType()I

    move-result v0

    const/4 v2, 0x3

    if-eq v0, v2, :cond_12

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Jq:Lcom/bytedance/sdk/openadsdk/AdSlot;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/AdSlot;->getDurationSlotType()I

    move-result v0

    if-eq v0, v2, :cond_12

    if-ne p2, v2, :cond_2

    goto/16 :goto_1

    :cond_2
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Jq:Lcom/bytedance/sdk/openadsdk/AdSlot;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/AdSlot;->getNativeAdType()I

    move-result v0

    const/4 v3, 0x5

    const/16 v4, 0x8

    const/4 v5, 0x7

    if-eq v0, v5, :cond_9

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Jq:Lcom/bytedance/sdk/openadsdk/AdSlot;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/AdSlot;->getNativeAdType()I

    move-result v0

    if-eq v0, v4, :cond_9

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Jq:Lcom/bytedance/sdk/openadsdk/AdSlot;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/AdSlot;->getDurationSlotType()I

    move-result v0

    if-eq v0, v5, :cond_9

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Jq:Lcom/bytedance/sdk/openadsdk/AdSlot;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/AdSlot;->getDurationSlotType()I

    move-result v0

    if-eq v0, v4, :cond_9

    if-eq p2, v5, :cond_9

    if-ne p2, v4, :cond_3

    goto :goto_0

    :cond_3
    invoke-static {p0}, Lcom/bytedance/sdk/openadsdk/core/model/mE;->ex(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result p2

    const/4 v0, 0x4

    if-eqz p2, :cond_4

    iput v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->BcC:I

    return-void

    :cond_4
    iget p2, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->mSE:I

    if-ne p2, v0, :cond_5

    invoke-static {p0}, Lcom/bytedance/sdk/openadsdk/core/model/mE;->ex(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result p2

    if-nez p2, :cond_5

    iput v1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->BcC:I

    :cond_5
    if-lt p1, v3, :cond_6

    iput v1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->BcC:I

    return-void

    :cond_6
    iget p2, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->mSE:I

    const/4 v0, 0x2

    if-ne p2, v0, :cond_7

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->efV()I

    move-result p2

    if-eq p2, v2, :cond_7

    iput v1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->BcC:I

    return-void

    :cond_7
    if-eqz p1, :cond_8

    const/4 p2, 0x1

    if-ne p1, p2, :cond_11

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->efV()I

    move-result p1

    if-eq p1, v2, :cond_11

    :cond_8
    iput v1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->BcC:I

    return-void

    :cond_9
    :goto_0
    invoke-static {p0}, Lcom/bytedance/sdk/openadsdk/core/model/mE;->rAx(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result p2

    if-eqz p2, :cond_a

    const/16 p1, 0xc

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->BcC:I

    return-void

    :cond_a
    invoke-static {p0}, Lcom/bytedance/sdk/openadsdk/core/model/mE;->ex(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result p2

    if-eqz p2, :cond_b

    const/16 p1, 0x9

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->BcC:I

    return-void

    :cond_b
    iget p2, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->mSE:I

    if-ne p2, v3, :cond_c

    invoke-static {p0}, Lcom/bytedance/sdk/openadsdk/core/model/JW;->hjc(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result p2

    if-nez p2, :cond_c

    iput v5, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->BcC:I

    :cond_c
    iget p2, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->mSE:I

    const/4 v0, 0x6

    if-ne p2, v0, :cond_d

    invoke-static {p0}, Lcom/bytedance/sdk/openadsdk/core/model/JW;->ex(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result p2

    if-nez p2, :cond_d

    iput v5, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->BcC:I

    :cond_d
    iget p2, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->mSE:I

    if-ne p2, v4, :cond_e

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Lvu()I

    move-result p2

    const/16 v0, 0x64

    if-eq p2, v0, :cond_e

    iput v5, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->BcC:I

    :cond_e
    iget p2, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->mSE:I

    const/16 v0, 0x13

    if-ne p2, v0, :cond_f

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->UYd:Ljava/lang/String;

    invoke-static {p2}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result p2

    if-eqz p2, :cond_f

    iput v5, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->BcC:I

    :cond_f
    iget p2, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->mSE:I

    const/16 v0, 0x14

    if-ne p2, v0, :cond_10

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->UYd:Ljava/lang/String;

    invoke-static {p2}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result p2

    if-eqz p2, :cond_10

    iput v5, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->BcC:I

    :cond_10
    if-ge p1, v3, :cond_11

    iput v5, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->BcC:I

    :cond_11
    return-void

    :cond_12
    :goto_1
    const/16 p1, 0xe

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->BcC:I

    return-void
.end method

.method public Fj(Lcom/bykv/vk/openvk/component/video/api/hjc/ex;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->spi:Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/openadsdk/AdSlot;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Jq:Lcom/bytedance/sdk/openadsdk/AdSlot;

    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/openadsdk/FilterWord;)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Ubf:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/openadsdk/core/model/BcC;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->YH:Lcom/bytedance/sdk/openadsdk/core/model/BcC;

    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/openadsdk/core/model/JU;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Ud:Lcom/bytedance/sdk/openadsdk/core/model/JU;

    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ko;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->nsB:Lcom/bytedance/sdk/openadsdk/core/model/Ko;

    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;)V
    .locals 2

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Bzy:Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->qK()Lcom/bytedance/sdk/component/adexpress/Fj/hjc/eV;

    move-result-object v0

    if-eqz v0, :cond_0

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Bzy:Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;

    invoke-virtual {v1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;->dG()Z

    move-result v1

    if-nez v1, :cond_0

    invoke-static {v0}, Lcom/bytedance/sdk/component/adexpress/Fj/ex/ex;->Fj(Lcom/bytedance/sdk/component/adexpress/Fj/hjc/eV;)V

    :cond_0
    if-eqz p1, :cond_1

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;->Tc()Ljava/lang/String;

    move-result-object p1

    invoke-static {p1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result p1

    if-nez p1, :cond_1

    invoke-direct {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->BG()Lcom/bytedance/sdk/openadsdk/core/ugen/Ubf/Fj;

    move-result-object p1

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/ugen/Fj/ex;->Fj()Lcom/bytedance/sdk/openadsdk/core/ugen/Fj/ex;

    move-result-object v0

    const-string v1, "ad"

    invoke-virtual {v0, p1, v1}, Lcom/bytedance/sdk/openadsdk/core/ugen/Fj/ex;->Fj(Lcom/bytedance/sdk/openadsdk/core/ugen/Ubf/Fj;Ljava/lang/String;)V

    :cond_1
    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/openadsdk/core/model/Tc;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Tc;

    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/openadsdk/core/model/UYd;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Wx:Lcom/bytedance/sdk/openadsdk/core/model/UYd;

    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/openadsdk/core/model/eV;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->zYh:Lcom/bytedance/sdk/openadsdk/core/model/eV;

    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/openadsdk/core/model/hjc;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->cB:Lcom/bytedance/sdk/openadsdk/core/model/hjc;

    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/openadsdk/core/model/mE;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Bb:Lcom/bytedance/sdk/openadsdk/core/model/mE;

    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/openadsdk/core/model/rAx;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->QV:Lcom/bytedance/sdk/openadsdk/core/model/rAx;

    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/openadsdk/core/svN/Fj;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->fHV:Lcom/bytedance/sdk/openadsdk/core/svN/Fj;

    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/openadsdk/core/ugen/Ubf/Fj;)V
    .locals 2

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->OQa:Lcom/bytedance/sdk/openadsdk/core/ugen/Ubf/Fj;

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/ugen/Fj/ex;->Fj()Lcom/bytedance/sdk/openadsdk/core/ugen/Fj/ex;

    move-result-object v0

    const-string v1, "endcard"

    invoke-virtual {v0, p1, v1}, Lcom/bytedance/sdk/openadsdk/core/ugen/Fj/ex;->Fj(Lcom/bytedance/sdk/openadsdk/core/ugen/Ubf/Fj;Ljava/lang/String;)V

    return-void
.end method

.method public Fj(Ljava/util/Map;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/Object;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->gci:Ljava/util/Map;

    return-void
.end method

.method public Fj(Z)V
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->oX:Z

    return-void
.end method

.method public Fj([Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->GZ:[Ljava/lang/String;

    return-void
.end method

.method public GZ()Lcom/bykv/vk/openvk/component/video/api/hjc/ex;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->efV:Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

    return-object v0
.end method

.method public Gv()Lorg/json/JSONObject;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Gv:Lorg/json/JSONObject;

    return-object v0
.end method

.method public HQ()F
    .locals 2

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->QR:F

    const/4 v1, 0x0

    cmpg-float v0, v0, v1

    if-gtz v0, :cond_0

    const/high16 v0, 0x42c80000    # 100.0f

    iput v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->QR:F

    :cond_0
    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->QR:F

    const/high16 v1, 0x447a0000    # 1000.0f

    mul-float v0, v0, v1

    div-float/2addr v0, v1

    return v0
.end method

.method public HY()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Ql:Ljava/lang/String;

    return-object v0
.end method

.method public IX()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->flF:I

    return v0
.end method

.method public Ic()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->tc:Z

    return v0
.end method

.method public IgC()I
    .locals 3

    const/4 v0, 0x0

    :try_start_0
    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->sse()Lorg/json/JSONObject;

    move-result-object v1

    if-eqz v1, :cond_0

    const-string v2, "rec_clk"

    invoke-virtual {v1, v2, v0}, Lorg/json/JSONObject;->optInt(Ljava/lang/String;I)I

    move-result v0
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    :catch_0
    :cond_0
    return v0
.end method

.method public Im()I
    .locals 3

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->AY:I

    if-gez v0, :cond_1

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->sse()Lorg/json/JSONObject;

    move-result-object v0

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    const-string v2, "ad_slot_type"

    invoke-virtual {v0, v2, v1}, Lorg/json/JSONObject;->optInt(Ljava/lang/String;I)I

    move-result v0

    iput v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->AY:I

    goto :goto_0

    :cond_0
    iput v1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->AY:I

    :cond_1
    :goto_0
    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->AY:I

    return v0
.end method

.method public JU()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Kf:I

    return v0
.end method

.method public JU(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->gXF:I

    return-void
.end method

.method public JU(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Ql:Ljava/lang/String;

    return-void
.end method

.method public JW()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->mSE:I

    return v0
.end method

.method public JW(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->HQ:I

    return-void
.end method

.method public JW(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->JU:Ljava/lang/String;

    return-void
.end method

.method public JZ()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->qPr:Z

    return v0
.end method

.method public Jq()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->uy:Ljava/lang/String;

    return-object v0
.end method

.method public KV()D
    .locals 2

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->sse()Lorg/json/JSONObject;

    move-result-object v0

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Ubf(Lorg/json/JSONObject;)D

    move-result-wide v0

    return-wide v0
.end method

.method public KZ()Lcom/bytedance/sdk/openadsdk/core/model/Tc;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->rAx:Lcom/bytedance/sdk/openadsdk/core/model/Tc;

    return-object v0
.end method

.method public Kf()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->rqT:Ljava/lang/String;

    return-object v0
.end method

.method public Kk()Ljava/lang/String;
    .locals 3

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->rS:Ljava/lang/String;

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->rS:Ljava/lang/String;

    return-object v0

    :cond_0
    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/JU;->Fj()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->efV()I

    move-result v1

    const/4 v2, 0x2

    if-eq v1, v2, :cond_3

    const/4 v2, 0x3

    if-eq v1, v2, :cond_3

    const/4 v2, 0x4

    if-eq v1, v2, :cond_1

    const/16 v2, 0x8

    if-eq v1, v2, :cond_3

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->rS:Ljava/lang/String;

    return-object v0

    :cond_1
    if-eqz v0, :cond_2

    const-string v1, "tt_video_download_apk"

    invoke-static {v0, v1}, Lcom/bytedance/sdk/component/utils/mE;->Fj(Landroid/content/Context;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    return-object v0

    :cond_2
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->rS:Ljava/lang/String;

    return-object v0

    :cond_3
    if-eqz v0, :cond_4

    const-string v1, "tt_video_mobile_go_detail"

    invoke-static {v0, v1}, Lcom/bytedance/sdk/component/utils/mE;->Fj(Landroid/content/Context;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    return-object v0

    :cond_4
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->rS:Ljava/lang/String;

    return-object v0
.end method

.method public Kkq()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->NgJ:Ljava/lang/String;

    return-object v0
.end method

.method public Ko()I
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->zYh:Lcom/bytedance/sdk/openadsdk/core/model/eV;

    if-nez v0, :cond_0

    const/4 v0, 0x2

    return v0

    :cond_0
    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/eV;->Fj()I

    move-result v0

    return v0
.end method

.method public Ko(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->flF:I

    return-void
.end method

.method public Ko(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Obv:Ljava/lang/String;

    return-void
.end method

.method public LEp()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->KV:Z

    return v0
.end method

.method public LKu()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->eh:I

    return v0
.end method

.method public LL()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->VXW:I

    return v0
.end method

.method public LZ()Z
    .locals 5

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->WR:Lcom/bytedance/sdk/openadsdk/utils/lv;

    iget-wide v0, v0, Lcom/bytedance/sdk/openadsdk/utils/lv;->Fj:J

    const-wide/16 v2, 0x0

    cmp-long v4, v0, v2

    if-lez v4, :cond_0

    const/4 v0, 0x1

    return v0

    :cond_0
    const/4 v0, 0x0

    return v0
.end method

.method public Lvu()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->gXF:I

    return v0
.end method

.method public LwI()Lcom/bytedance/sdk/openadsdk/core/ugen/Ubf/Fj;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->OQa:Lcom/bytedance/sdk/openadsdk/core/ugen/Ubf/Fj;

    return-object v0
.end method

.method public Moo()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->JZ:I

    return v0
.end method

.method public Moo(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->OXv:I

    return-void
.end method

.method public Moo(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Im:Ljava/lang/String;

    return-void
.end method

.method public NgJ()Z
    .locals 2

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Im()I

    move-result v0

    const/4 v1, 0x3

    if-eq v0, v1, :cond_1

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Im()I

    move-result v0

    const/4 v1, 0x4

    if-ne v0, v1, :cond_0

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    return v0

    :cond_1
    :goto_0
    const/4 v0, 0x1

    return v0
.end method

.method public Nyg()I
    .locals 2

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->ss:I

    const/4 v1, 0x2

    if-eq v0, v1, :cond_0

    const/4 v0, 0x1

    iput v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->ss:I

    :cond_0
    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->ss:I

    return v0
.end method

.method public OK()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Tc:Ljava/util/List;

    return-object v0
.end method

.method public OQa()Z
    .locals 2

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Im()I

    move-result v0

    const/4 v1, 0x1

    if-ne v0, v1, :cond_0

    return v1

    :cond_0
    const/4 v0, 0x0

    return v0
.end method

.method public OXv()Lcom/bytedance/sdk/openadsdk/core/svN/Fj;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->fHV:Lcom/bytedance/sdk/openadsdk/core/svN/Fj;

    return-object v0
.end method

.method public Obv()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->JU:Ljava/lang/String;

    return-object v0
.end method

.method public PpV()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/bytedance/sdk/openadsdk/core/model/Tc;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->hjc:Ljava/util/List;

    return-object v0
.end method

.method public QR()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->kl:Z

    return v0
.end method

.method public QV()Lorg/json/JSONObject;
    .locals 11

    const-string v0, "app_name"

    new-instance v1, Lorg/json/JSONObject;

    invoke-direct {v1}, Lorg/json/JSONObject;-><init>()V

    :try_start_0
    const-string v2, "interaction_type"

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->efV()I

    move-result v3

    invoke-virtual {v1, v2, v3}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    const-string v2, "interaction_method"

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->JW()I

    move-result v3

    invoke-virtual {v1, v2, v3}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    const-string v2, "target_url"

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->mj()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v1, v2, v3}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    const-string v2, "app_log_url"

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->YH()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v1, v2, v3}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    const-string v2, "gecko_id"

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->ei()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v1, v2, v3}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    const-string v2, "extension"

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->fjZ()Lorg/json/JSONObject;

    move-result-object v3

    invoke-virtual {v1, v2, v3}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    const-string v2, "ad_id"

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->cs()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v1, v2, v3}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    const-string v2, "source"

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->iT()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v1, v2, v3}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->oL()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v0, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    const-string v2, "screenshot"

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->JZ()Z

    move-result v3

    invoke-virtual {v1, v2, v3}, Lorg/json/JSONObject;->put(Ljava/lang/String;Z)Lorg/json/JSONObject;

    const-string v2, "dislike_control"

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->LKu()I

    move-result v3

    invoke-virtual {v1, v2, v3}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    const-string v2, "play_bar_show_time"

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->rXP()I

    move-result v3

    invoke-virtual {v1, v2, v3}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    const-string v2, "play_bar_style"

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->rf()I

    move-result v3

    invoke-virtual {v1, v2, v3}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    const-string v2, "if_block_lp"

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->pJO()I

    move-result v3

    invoke-virtual {v1, v2, v3}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    const-string v2, "cache_sort"

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->gf()I

    move-result v3

    invoke-virtual {v1, v2, v3}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    const-string v2, "if_sp_cache"

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->hg()I

    move-result v3

    invoke-virtual {v1, v2, v3}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    const-string v2, "render_control"

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->mE()I

    move-result v3

    invoke-virtual {v1, v2, v3}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    const-string v2, "mrc_report"

    iget v3, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->CML:I

    invoke-virtual {v1, v2, v3}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    const-string v2, "isMrcReportFinish"

    iget-boolean v3, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->ttV:Z

    invoke-virtual {v1, v2, v3}, Lorg/json/JSONObject;->put(Ljava/lang/String;Z)Lorg/json/JSONObject;

    new-instance v2, Lorg/json/JSONObject;

    invoke-direct {v2}, Lorg/json/JSONObject;-><init>()V

    const-string v3, "cta"

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Ql()I

    move-result v4

    invoke-virtual {v2, v3, v4}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    const-string v3, "other"

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->JU()I

    move-result v4

    invoke-virtual {v2, v3, v4}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    const-string v3, "set_click_type"

    invoke-virtual {v1, v3, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    new-instance v2, Lorg/json/JSONObject;

    invoke-direct {v2}, Lorg/json/JSONObject;-><init>()V

    const-string v3, "reward_name"

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->rS()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v2, v3, v4}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    const-string v3, "reward_amount"

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->vYf()I

    move-result v4

    invoke-virtual {v2, v3, v4}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    const-string v3, "reward_data"

    invoke-virtual {v1, v3, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Ubf()Ljava/lang/String;

    move-result-object v2

    invoke-static {v2}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v2

    if-nez v2, :cond_0

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->WR()Ljava/lang/String;

    move-result-object v2

    invoke-static {v2}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v2

    if-nez v2, :cond_0

    new-instance v2, Lorg/json/JSONObject;

    invoke-direct {v2}, Lorg/json/JSONObject;-><init>()V

    const-string v3, "adchoices_icon"

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Ubf()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v2, v3, v4}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    const-string v3, "adchoices_url"

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->WR()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v2, v3, v4}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    const-string v3, "dsp_adchoices"

    invoke-virtual {v1, v3, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    :cond_0
    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->oX()Lcom/bytedance/sdk/openadsdk/core/model/Tc;

    move-result-object v2
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    const-string v3, "width"

    const-string v4, "height"

    const-string v5, "url"

    if-eqz v2, :cond_1

    :try_start_1
    invoke-virtual {v2}, Lcom/bytedance/sdk/openadsdk/core/model/Tc;->Fj()Ljava/lang/String;

    move-result-object v6

    invoke-static {v6}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v6

    if-nez v6, :cond_1

    new-instance v6, Lorg/json/JSONObject;

    invoke-direct {v6}, Lorg/json/JSONObject;-><init>()V

    invoke-virtual {v2}, Lcom/bytedance/sdk/openadsdk/core/model/Tc;->Fj()Ljava/lang/String;

    move-result-object v7

    invoke-virtual {v6, v5, v7}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    invoke-virtual {v2}, Lcom/bytedance/sdk/openadsdk/core/model/Tc;->hjc()I

    move-result v7

    invoke-virtual {v6, v4, v7}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    invoke-virtual {v2}, Lcom/bytedance/sdk/openadsdk/core/model/Tc;->ex()I

    move-result v2

    invoke-virtual {v6, v3, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    const-string v2, "icon"

    invoke-virtual {v1, v2, v6}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    :cond_1
    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->KZ()Lcom/bytedance/sdk/openadsdk/core/model/Tc;

    move-result-object v2

    if-eqz v2, :cond_2

    invoke-virtual {v2}, Lcom/bytedance/sdk/openadsdk/core/model/Tc;->Fj()Ljava/lang/String;

    move-result-object v6

    invoke-static {v6}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v6

    if-nez v6, :cond_2

    new-instance v6, Lorg/json/JSONObject;

    invoke-direct {v6}, Lorg/json/JSONObject;-><init>()V

    invoke-virtual {v2}, Lcom/bytedance/sdk/openadsdk/core/model/Tc;->Fj()Ljava/lang/String;

    move-result-object v7

    invoke-virtual {v6, v5, v7}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    invoke-virtual {v2}, Lcom/bytedance/sdk/openadsdk/core/model/Tc;->hjc()I

    move-result v7

    invoke-virtual {v6, v4, v7}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    invoke-virtual {v2}, Lcom/bytedance/sdk/openadsdk/core/model/Tc;->ex()I

    move-result v2

    invoke-virtual {v6, v3, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    const-string v2, "cover_image"

    invoke-virtual {v1, v2, v6}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    :cond_2
    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Gv()Lorg/json/JSONObject;

    move-result-object v2

    if-eqz v2, :cond_3

    const-string v6, "session_params"

    invoke-virtual {v1, v6, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    :cond_3
    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->uy()Lcom/bytedance/sdk/openadsdk/core/model/BcC;

    move-result-object v2

    if-eqz v2, :cond_4

    new-instance v6, Lorg/json/JSONObject;

    invoke-direct {v6}, Lorg/json/JSONObject;-><init>()V

    const-string v7, "click_upper_content_area"

    iget-boolean v8, v2, Lcom/bytedance/sdk/openadsdk/core/model/BcC;->Fj:Z

    invoke-virtual {v6, v7, v8}, Lorg/json/JSONObject;->put(Ljava/lang/String;Z)Lorg/json/JSONObject;

    const-string v7, "click_upper_non_content_area"

    iget-boolean v8, v2, Lcom/bytedance/sdk/openadsdk/core/model/BcC;->ex:Z

    invoke-virtual {v6, v7, v8}, Lorg/json/JSONObject;->put(Ljava/lang/String;Z)Lorg/json/JSONObject;

    const-string v7, "click_lower_content_area"

    iget-boolean v8, v2, Lcom/bytedance/sdk/openadsdk/core/model/BcC;->hjc:Z

    invoke-virtual {v6, v7, v8}, Lorg/json/JSONObject;->put(Ljava/lang/String;Z)Lorg/json/JSONObject;

    const-string v7, "click_lower_non_content_area"

    iget-boolean v8, v2, Lcom/bytedance/sdk/openadsdk/core/model/BcC;->eV:Z

    invoke-virtual {v6, v7, v8}, Lorg/json/JSONObject;->put(Ljava/lang/String;Z)Lorg/json/JSONObject;

    const-string v7, "click_button_area"

    iget-boolean v8, v2, Lcom/bytedance/sdk/openadsdk/core/model/BcC;->Ubf:Z

    invoke-virtual {v6, v7, v8}, Lorg/json/JSONObject;->put(Ljava/lang/String;Z)Lorg/json/JSONObject;

    const-string v7, "click_video_area"

    iget-boolean v2, v2, Lcom/bytedance/sdk/openadsdk/core/model/BcC;->WR:Z

    invoke-virtual {v6, v7, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;Z)Lorg/json/JSONObject;

    const-string v2, "click_area"

    invoke-virtual {v1, v2, v6}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    :cond_4
    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->lv()Lcom/bytedance/sdk/openadsdk/AdSlot;

    move-result-object v2

    if-eqz v2, :cond_5

    invoke-virtual {v2}, Lcom/bytedance/sdk/openadsdk/AdSlot;->toJsonObj()Lorg/json/JSONObject;

    move-result-object v2

    const-string v6, "adslot"

    invoke-virtual {v1, v6, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    :cond_5
    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->PpV()Ljava/util/List;

    move-result-object v2

    if-eqz v2, :cond_7

    new-instance v6, Lorg/json/JSONArray;

    invoke-direct {v6}, Lorg/json/JSONArray;-><init>()V

    invoke-interface {v2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :goto_0
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v7

    if-eqz v7, :cond_6

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v7

    check-cast v7, Lcom/bytedance/sdk/openadsdk/core/model/Tc;

    new-instance v8, Lorg/json/JSONObject;

    invoke-direct {v8}, Lorg/json/JSONObject;-><init>()V

    invoke-virtual {v7}, Lcom/bytedance/sdk/openadsdk/core/model/Tc;->Fj()Ljava/lang/String;

    move-result-object v9

    invoke-virtual {v8, v5, v9}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    invoke-virtual {v7}, Lcom/bytedance/sdk/openadsdk/core/model/Tc;->hjc()I

    move-result v9

    invoke-virtual {v8, v4, v9}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    invoke-virtual {v7}, Lcom/bytedance/sdk/openadsdk/core/model/Tc;->ex()I

    move-result v9

    invoke-virtual {v8, v3, v9}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    const-string v9, "image_preview"

    invoke-virtual {v7}, Lcom/bytedance/sdk/openadsdk/core/model/Tc;->WR()Z

    move-result v10

    invoke-virtual {v8, v9, v10}, Lorg/json/JSONObject;->put(Ljava/lang/String;Z)Lorg/json/JSONObject;

    const-string v9, "image_key"

    invoke-virtual {v7}, Lcom/bytedance/sdk/openadsdk/core/model/Tc;->svN()Ljava/lang/String;

    move-result-object v7

    invoke-virtual {v8, v9, v7}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    invoke-virtual {v6, v8}, Lorg/json/JSONArray;->put(Ljava/lang/Object;)Lorg/json/JSONArray;

    goto :goto_0

    :cond_6
    const-string v2, "image"

    invoke-virtual {v1, v2, v6}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    :cond_7
    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->OK()Ljava/util/List;

    move-result-object v2

    if-eqz v2, :cond_9

    new-instance v3, Lorg/json/JSONArray;

    invoke-direct {v3}, Lorg/json/JSONArray;-><init>()V

    invoke-interface {v2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :goto_1
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v4

    if-eqz v4, :cond_8

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Ljava/lang/String;

    invoke-virtual {v3, v4}, Lorg/json/JSONArray;->put(Ljava/lang/Object;)Lorg/json/JSONArray;

    goto :goto_1

    :cond_8
    const-string v2, "show_url"

    invoke-virtual {v1, v2, v3}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    :cond_9
    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->gci()Ljava/util/List;

    move-result-object v2

    if-eqz v2, :cond_b

    new-instance v3, Lorg/json/JSONArray;

    invoke-direct {v3}, Lorg/json/JSONArray;-><init>()V

    invoke-interface {v2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :goto_2
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v4

    if-eqz v4, :cond_a

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Ljava/lang/String;

    invoke-virtual {v3, v4}, Lorg/json/JSONArray;->put(Ljava/lang/Object;)Lorg/json/JSONArray;

    goto :goto_2

    :cond_a
    const-string v2, "click_url"

    invoke-virtual {v1, v2, v3}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    :cond_b
    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Bzy()Ljava/util/List;

    move-result-object v2

    if-eqz v2, :cond_d

    new-instance v3, Lorg/json/JSONArray;

    invoke-direct {v3}, Lorg/json/JSONArray;-><init>()V

    invoke-interface {v2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :goto_3
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v4

    if-eqz v4, :cond_c

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Ljava/lang/String;

    invoke-virtual {v3, v4}, Lorg/json/JSONArray;->put(Ljava/lang/Object;)Lorg/json/JSONArray;

    goto :goto_3

    :cond_c
    const-string v2, "play_start"

    invoke-virtual {v1, v2, v3}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    :cond_d
    const-string v2, "phone_num"

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->qPr()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v1, v2, v3}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    const-string v2, "title"

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Obv()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v1, v2, v3}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    const-string v2, "description"

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->HY()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v1, v2, v3}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    const-string v2, "ext"

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Jq()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v1, v2, v3}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    const-string v2, "image_mode"

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->haP()I

    move-result v3

    invoke-virtual {v1, v2, v3}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    const-string v2, "cover_click_area"

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Lvu()I

    move-result v3

    invoke-virtual {v1, v2, v3}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    const-string v2, "intercept_flag"

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->ZA()I

    move-result v3

    invoke-virtual {v1, v2, v3}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    const-string v2, "button_text"

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Kk()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v1, v2, v3}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    const-string v2, "ad_logo"

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->IX()I

    move-result v3

    invoke-virtual {v1, v2, v3}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    const-string v2, "video_adaptation"

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Moo()I

    move-result v3

    invoke-virtual {v1, v2, v3}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    const-string v2, "feed_video_opentype"

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->mC()I

    move-result v3

    invoke-virtual {v1, v2, v3}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    const-string v2, "orientation"

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Nyg()I

    move-result v3

    invoke-virtual {v1, v2, v3}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    const-string v2, "aspect_ratio"

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->HQ()F

    move-result v3

    float-to-double v3, v3

    invoke-virtual {v1, v2, v3, v4}, Lorg/json/JSONObject;->put(Ljava/lang/String;D)Lorg/json/JSONObject;

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->At()Lcom/bytedance/sdk/openadsdk/core/model/hjc;

    move-result-object v2

    if-eqz v2, :cond_e

    new-instance v3, Lorg/json/JSONObject;

    invoke-direct {v3}, Lorg/json/JSONObject;-><init>()V

    invoke-virtual {v2}, Lcom/bytedance/sdk/openadsdk/core/model/hjc;->ex()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3, v0, v4}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    const-string v0, "package_name"

    invoke-virtual {v2}, Lcom/bytedance/sdk/openadsdk/core/model/hjc;->hjc()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3, v0, v4}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    const-string v0, "download_url"

    invoke-virtual {v2}, Lcom/bytedance/sdk/openadsdk/core/model/hjc;->Fj()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3, v0, v4}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    const-string v0, "score"

    invoke-virtual {v2}, Lcom/bytedance/sdk/openadsdk/core/model/hjc;->eV()D

    move-result-wide v6

    invoke-virtual {v3, v0, v6, v7}, Lorg/json/JSONObject;->put(Ljava/lang/String;D)Lorg/json/JSONObject;

    const-string v0, "comment_num"

    invoke-virtual {v2}, Lcom/bytedance/sdk/openadsdk/core/model/hjc;->Ubf()I

    move-result v4

    invoke-virtual {v3, v0, v4}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    const-string v0, "app_size"

    invoke-virtual {v2}, Lcom/bytedance/sdk/openadsdk/core/model/hjc;->WR()I

    move-result v4

    invoke-virtual {v3, v0, v4}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    const-string v0, "app_category"

    invoke-virtual {v2}, Lcom/bytedance/sdk/openadsdk/core/model/hjc;->svN()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v3, v0, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    const-string v0, "app"

    invoke-virtual {v1, v0, v3}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    :cond_e
    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->qg()Lcom/bytedance/sdk/openadsdk/core/model/Ko;

    move-result-object v0

    if-eqz v0, :cond_f

    new-instance v2, Lorg/json/JSONObject;

    invoke-direct {v2}, Lorg/json/JSONObject;-><init>()V

    const-string v3, "deeplink_url"

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ko;->Fj()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v2, v3, v4}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    const-string v3, "fallback_url"

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ko;->ex()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v2, v3, v4}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    const-string v3, "fallback_type"

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ko;->hjc()I

    move-result v0

    invoke-virtual {v2, v3, v0}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    const-string v0, "deep_link"

    invoke-virtual {v1, v0, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    :cond_f
    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->flF()Ljava/util/List;

    move-result-object v0

    if-eqz v0, :cond_12

    new-instance v2, Lorg/json/JSONArray;

    invoke-direct {v2}, Lorg/json/JSONArray;-><init>()V

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_10
    :goto_4
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_11

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/bytedance/sdk/openadsdk/FilterWord;

    invoke-direct {p0, v3}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->ex(Lcom/bytedance/sdk/openadsdk/FilterWord;)Lorg/json/JSONObject;

    move-result-object v3

    if-eqz v3, :cond_10

    invoke-virtual {v2, v3}, Lorg/json/JSONArray;->put(Ljava/lang/Object;)Lorg/json/JSONArray;

    goto :goto_4

    :cond_11
    const-string v0, "filter_words"

    invoke-virtual {v1, v0, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    :cond_12
    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->dG()Lcom/bytedance/sdk/openadsdk/core/model/mE;

    move-result-object v0

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/core/model/mE;->Fj(Lorg/json/JSONObject;)V

    const-string v0, "count_down"

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Tib()I

    move-result v2

    invoke-virtual {v1, v0, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    const-string v0, "expiration_time"

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->ks()J

    move-result-wide v2

    invoke-virtual {v1, v0, v2, v3}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->GZ()Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

    move-result-object v0

    if-eqz v0, :cond_13

    const-string v2, "video"

    invoke-virtual {v0}, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->rS()Lorg/json/JSONObject;

    move-result-object v0

    invoke-virtual {v1, v2, v0}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    :cond_13
    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Wx()Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

    move-result-object v0

    if-eqz v0, :cond_14

    const-string v2, "h265_video"

    invoke-virtual {v0}, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->rS()Lorg/json/JSONObject;

    move-result-object v0

    invoke-virtual {v1, v2, v0}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    :cond_14
    const-string v0, "video_encode_type"

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->xx()I

    move-result v2

    invoke-virtual {v1, v0, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    new-instance v0, Lorg/json/JSONObject;

    invoke-direct {v0}, Lorg/json/JSONObject;-><init>()V

    const-string v2, "if_send_click"

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->gXF()I

    move-result v3

    invoke-virtual {v0, v2, v3}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    const-string v2, "download_conf"

    invoke-virtual {v1, v2, v0}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Eev()Ljava/util/Map;

    move-result-object v0

    if-eqz v0, :cond_16

    new-instance v0, Lorg/json/JSONObject;

    invoke-direct {v0}, Lorg/json/JSONObject;-><init>()V

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Eev()Ljava/util/Map;

    move-result-object v2

    invoke-interface {v2}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    move-result-object v2

    invoke-interface {v2}, Ljava/util/Set;->isEmpty()Z

    move-result v3

    if-nez v3, :cond_15

    invoke-interface {v2}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :goto_5
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_15

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/util/Map$Entry;

    invoke-interface {v3}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Ljava/lang/String;

    invoke-interface {v3}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v3

    invoke-virtual {v0, v4, v3}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    goto :goto_5

    :cond_15
    const-string v2, "media_ext"

    invoke-virtual {v1, v2, v0}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    :cond_16
    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->uM()Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;

    move-result-object v0

    if-eqz v0, :cond_17

    new-instance v2, Lorg/json/JSONObject;

    invoke-direct {v2}, Lorg/json/JSONObject;-><init>()V

    const-string v3, "id"

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;->Ubf()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v2, v3, v4}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    const-string v3, "md5"

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;->WR()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v2, v3, v4}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;->svN()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v5, v3}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    const-string v3, "data"

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;->BcC()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v2, v3, v4}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    const-string v3, "diff_data"

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;->mSE()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v2, v3, v4}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    const-string v3, "version"

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;->eV()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v2, v3, v4}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    const-string v3, "dynamic_creative"

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;->Ko()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v2, v3, v4}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    const-string v3, "engine_version"

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;->UYd()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v2, v3, v4}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    const-string v3, "ugen_data"

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;->JU()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v2, v3, v4}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    const-string v3, "ugen_md5"

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;->JW()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v2, v3, v4}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    const-string v3, "ugen_url"

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;->Tc()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v2, v3, v0}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    const-string v0, "tpl_info"

    invoke-virtual {v1, v0, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    :cond_17
    const-string v0, "creative_extra"

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->fj()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v0, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    const-string v0, "market_url"

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->SZl()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v0, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    const-string v0, "auction_price"

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Vq()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v0, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    const-string v0, "ad_info"

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Wr()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v0, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    const-string v0, "is_package_open"

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->ss()I

    move-result v2

    invoke-virtual {v1, v0, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    const-string v0, "playable_duration_time"

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->tyC()I

    move-result v2

    invoke-virtual {v1, v0, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    const-string v0, "playable_endcard_close_time"

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->xEF()I

    move-result v2

    invoke-virtual {v1, v0, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    const-string v0, "endcard_close_time"

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->jsD()I

    move-result v2

    invoke-virtual {v1, v0, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    new-instance v0, Lorg/json/JSONObject;

    invoke-direct {v0}, Lorg/json/JSONObject;-><init>()V

    const-string v2, "render_sequence"

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->cB()I

    move-result v3

    invoke-virtual {v0, v2, v3}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    const-string v2, "backup_render_control"

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->nsB()I

    move-result v3

    invoke-virtual {v0, v2, v3}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    const-string v2, "reserve_time"

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->rqT()I

    move-result v3

    invoke-virtual {v0, v2, v3}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    const-string v2, "render_thread"

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Fi()I

    move-result v3

    invoke-virtual {v0, v2, v3}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    const-string v2, "render"

    invoke-virtual {v1, v2, v0}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Ud:Lcom/bytedance/sdk/openadsdk/core/model/JU;

    if-nez v0, :cond_18

    new-instance v0, Lcom/bytedance/sdk/openadsdk/core/model/JU;

    invoke-direct {v0}, Lcom/bytedance/sdk/openadsdk/core/model/JU;-><init>()V

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Ud:Lcom/bytedance/sdk/openadsdk/core/model/JU;

    const-string v2, ""

    invoke-virtual {v0, v2}, Lcom/bytedance/sdk/openadsdk/core/model/JU;->Fj(Ljava/lang/String;)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Ud:Lcom/bytedance/sdk/openadsdk/core/model/JU;

    const-wide/16 v2, 0xa

    invoke-virtual {v0, v2, v3}, Lcom/bytedance/sdk/openadsdk/core/model/JU;->Fj(J)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Ud:Lcom/bytedance/sdk/openadsdk/core/model/JU;

    const-wide/16 v2, 0x14

    invoke-virtual {v0, v2, v3}, Lcom/bytedance/sdk/openadsdk/core/model/JU;->ex(J)V

    :cond_18
    new-instance v0, Lorg/json/JSONObject;

    invoke-direct {v0}, Lorg/json/JSONObject;-><init>()V

    const-string v2, "onlylp_loading_maxtime"

    iget-object v3, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Ud:Lcom/bytedance/sdk/openadsdk/core/model/JU;

    invoke-virtual {v3}, Lcom/bytedance/sdk/openadsdk/core/model/JU;->Fj()J

    move-result-wide v3

    invoke-virtual {v0, v2, v3, v4}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    const-string v2, "straight_lp_showtime"

    iget-object v3, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Ud:Lcom/bytedance/sdk/openadsdk/core/model/JU;

    invoke-virtual {v3}, Lcom/bytedance/sdk/openadsdk/core/model/JU;->ex()J

    move-result-wide v3

    invoke-virtual {v0, v2, v3, v4}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    const-string v2, "loading_text"

    iget-object v3, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Ud:Lcom/bytedance/sdk/openadsdk/core/model/JU;

    invoke-virtual {v3}, Lcom/bytedance/sdk/openadsdk/core/model/JU;->hjc()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v0, v2, v3}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    const-string v2, "interaction_method_params"

    invoke-virtual {v1, v2, v0}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    new-instance v0, Lorg/json/JSONObject;

    invoke-direct {v0}, Lorg/json/JSONObject;-><init>()V

    const-string v2, "splash_clickarea"

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Ko()I

    move-result v3

    invoke-virtual {v0, v2, v3}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    const-string v2, "splash_layout_id"

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->rAx()I

    move-result v3

    invoke-virtual {v0, v2, v3}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    const-string v2, "load_wait_time"

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->UYd()J

    move-result-wide v3

    invoke-virtual {v0, v2, v3, v4}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    const-string v2, "splash_control"

    invoke-virtual {v1, v2, v0}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->UF()Z

    move-result v0

    if-eqz v0, :cond_19

    const-string v0, "dsp_html"

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->rqT:Ljava/lang/String;

    invoke-virtual {v1, v0, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    :cond_19
    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->zf()Z

    move-result v0

    if-eqz v0, :cond_1a

    const-string v0, "vast_json"

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->fHV:Lcom/bytedance/sdk/openadsdk/core/svN/Fj;

    invoke-virtual {v2}, Lcom/bytedance/sdk/openadsdk/core/svN/Fj;->Ko()Lorg/json/JSONObject;

    move-result-object v2

    invoke-virtual {v1, v0, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    :cond_1a
    const-string v0, "dsp_material_type"

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->LL()I

    move-result v2

    invoke-virtual {v1, v0, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    new-instance v0, Lorg/json/JSONObject;

    invoke-direct {v0}, Lorg/json/JSONObject;-><init>()V

    const-string v2, "loading_landingpage_type"

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->CML()I

    move-result v3

    invoke-virtual {v0, v2, v3}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->GZ:[Ljava/lang/String;

    if-eqz v2, :cond_1c

    array-length v2, v2

    if-lez v2, :cond_1c

    new-instance v2, Lorg/json/JSONArray;

    invoke-direct {v2}, Lorg/json/JSONArray;-><init>()V

    iget-object v3, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->GZ:[Ljava/lang/String;

    array-length v4, v3

    const/4 v5, 0x0

    :goto_6
    if-ge v5, v4, :cond_1b

    aget-object v6, v3, v5

    invoke-virtual {v2, v6}, Lorg/json/JSONArray;->put(Ljava/lang/Object;)Lorg/json/JSONArray;

    add-int/lit8 v5, v5, 0x1

    goto :goto_6

    :cond_1b
    const-string v3, "landingpage_text"

    invoke-virtual {v0, v3, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    :cond_1c
    const-string v2, "loading_page"

    invoke-virtual {v1, v2, v0}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    const-string v0, "deep_link_appname"

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->vYf:Ljava/lang/String;

    invoke-virtual {v1, v0, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    const-string v0, "landing_page_download_clicktype"

    iget v2, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Af:I

    invoke-virtual {v1, v0, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Wx:Lcom/bytedance/sdk/openadsdk/core/model/UYd;

    if-eqz v0, :cond_1d

    const-string v2, "dsp_style"

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/UYd;->Ubf()Lorg/json/JSONObject;

    move-result-object v0

    invoke-virtual {v1, v2, v0}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    :cond_1d
    const-string v0, "jump_probability"

    iget v2, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Xnz:I

    invoke-virtual {v1, v0, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    const-string v0, "identificationOverlayContent"

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->NgJ:Ljava/lang/String;

    invoke-virtual {v1, v0, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->OQa:Lcom/bytedance/sdk/openadsdk/core/ugen/Ubf/Fj;

    if-eqz v0, :cond_1e

    new-instance v0, Lorg/json/JSONObject;

    invoke-direct {v0}, Lorg/json/JSONObject;-><init>()V

    const-string v2, "endcard"

    iget-object v3, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->OQa:Lcom/bytedance/sdk/openadsdk/core/ugen/Ubf/Fj;

    invoke-virtual {v3}, Lcom/bytedance/sdk/openadsdk/core/ugen/Ubf/Fj;->WR()Lorg/json/JSONObject;

    move-result-object v3

    invoke-virtual {v0, v2, v3}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    const-string v2, "ugen"

    invoke-virtual {v1, v2, v0}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    :cond_1e
    const-string v0, "preload_h5_type"

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->tr()I

    move-result v2

    invoke-virtual {v1, v0, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;
    :try_end_1
    .catch Ljava/lang/Exception; {:try_start_1 .. :try_end_1} :catch_0

    :catch_0
    return-object v1
.end method

.method public Ql()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->UF:I

    return v0
.end method

.method public Ql(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->OK:I

    return-void
.end method

.method public Ql(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->rS:Ljava/lang/String;

    return-void
.end method

.method public Rgn()V
    .locals 1

    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->KV:Z

    return-void
.end method

.method public Rwq()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Fj:Z

    return v0
.end method

.method public SZl()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->ei:Ljava/lang/String;

    return-object v0
.end method

.method public Tc()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->BcC:I

    return v0
.end method

.method public Tc(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Nyg:I

    return-void
.end method

.method public Tc(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->dG:Ljava/lang/String;

    return-void
.end method

.method public Tib()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->uM:I

    return v0
.end method

.method public Tyd()Lcom/bytedance/sdk/openadsdk/utils/lv;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->WR:Lcom/bytedance/sdk/openadsdk/utils/lv;

    return-object v0
.end method

.method public UF()Z
    .locals 2

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->VXW:I

    const/4 v1, 0x2

    if-ne v0, v1, :cond_0

    const/4 v0, 0x1

    return v0

    :cond_0
    const/4 v0, 0x0

    return v0
.end method

.method public UYd()J
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->zYh:Lcom/bytedance/sdk/openadsdk/core/model/eV;

    if-nez v0, :cond_0

    const-wide/16 v0, 0x0

    return-wide v0

    :cond_0
    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/eV;->hjc()J

    move-result-wide v0

    return-wide v0
.end method

.method public UYd(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->haP:I

    return-void
.end method

.method public UYd(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->mE:Ljava/lang/String;

    return-void
.end method

.method public Ubf()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->YP:Ljava/lang/String;

    return-object v0
.end method

.method public Ubf(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->ks:I

    return-void
.end method

.method public Ubf(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->YP:Ljava/lang/String;

    return-void
.end method

.method public Ubf(Z)V
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->kl:Z

    return-void
.end method

.method public Ud()Z
    .locals 2

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->CML:I

    const/4 v1, 0x1

    if-ne v0, v1, :cond_0

    return v1

    :cond_0
    const/4 v0, 0x0

    return v0
.end method

.method public VXW()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->AY:I

    return v0
.end method

.method public Vq()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->yo:Ljava/lang/String;

    return-object v0
.end method

.method public Vq(Ljava/lang/String;)Ljava/lang/String;
    .locals 2

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->sse()Lorg/json/JSONObject;

    move-result-object v0

    if-eqz v0, :cond_0

    const-string v1, "rit"

    invoke-virtual {v0, v1, p1}, Lorg/json/JSONObject;->optString(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    :cond_0
    return-object p1
.end method

.method public Vq(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->zf:I

    return-void
.end method

.method public WR()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->zDD:Ljava/lang/String;

    return-object v0
.end method

.method public WR(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Eev:I

    return-void
.end method

.method public WR(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->zDD:Ljava/lang/String;

    return-void
.end method

.method public WR(Z)V
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Fj:Z

    return-void
.end method

.method public We()Z
    .locals 3

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->BcC:I

    const/4 v1, 0x2

    const/4 v2, 0x1

    if-eq v0, v1, :cond_0

    if-ne v0, v2, :cond_1

    :cond_0
    const/4 v0, 0x3

    iget v1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->svN:I

    if-ne v0, v1, :cond_1

    return v2

    :cond_1
    const/4 v0, 0x0

    return v0
.end method

.method public Wr()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->tyC:Ljava/lang/String;

    return-object v0
.end method

.method public Wx()Lcom/bykv/vk/openvk/component/video/api/hjc/ex;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->iT:Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

    return-object v0
.end method

.method public Xl()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->cP:Z

    return v0
.end method

.method public Xnz()J
    .locals 5

    iget-wide v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->fU:J

    const-wide/16 v2, 0x0

    cmp-long v4, v0, v2

    if-gez v4, :cond_1

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->sse()Lorg/json/JSONObject;

    move-result-object v0

    if-eqz v0, :cond_0

    const-string v1, "ad_id"

    invoke-virtual {v0, v1, v2, v3}, Lorg/json/JSONObject;->optLong(Ljava/lang/String;J)J

    move-result-wide v0

    iput-wide v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->fU:J

    goto :goto_0

    :cond_0
    iput-wide v2, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->fU:J

    :cond_1
    :goto_0
    iget-wide v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->fU:J

    return-wide v0
.end method

.method public YH()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->rf:Ljava/lang/String;

    return-object v0
.end method

.method public YP()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->vYf:Ljava/lang/String;

    return-object v0
.end method

.method public Yh()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->sse:Z

    return v0
.end method

.method public ZA()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->haP:I

    return v0
.end method

.method public aGk()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->mt:I

    return v0
.end method

.method public aYy()I
    .locals 3

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->sse()Lorg/json/JSONObject;

    move-result-object v0

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    const-string v2, "rit"

    invoke-virtual {v0, v2, v1}, Lorg/json/JSONObject;->optInt(Ljava/lang/String;I)I

    move-result v0

    return v0

    :cond_0
    return v1
.end method

.method public cB()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Kk:I

    return v0
.end method

.method public cB(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->xEF:I

    return-void
.end method

.method public cB(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->rqT:Ljava/lang/String;

    return-void
.end method

.method public cHy()V
    .locals 1

    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->qw:Z

    return-void
.end method

.method public cP()Z
    .locals 2

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Im()I

    move-result v0

    const/16 v1, 0x8

    if-ne v0, v1, :cond_0

    const/4 v0, 0x1

    return v0

    :cond_0
    const/4 v0, 0x0

    return v0
.end method

.method public cs()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->mC:Ljava/lang/String;

    return-object v0
.end method

.method public dG()Lcom/bytedance/sdk/openadsdk/core/model/mE;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Bb:Lcom/bytedance/sdk/openadsdk/core/model/mE;

    return-object v0
.end method

.method public dG(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->At:I

    return-void
.end method

.method public dG(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->UYd:Ljava/lang/String;

    return-void
.end method

.method public eV(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->fj:I

    return-void
.end method

.method public eV(Lorg/json/JSONObject;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Fi:Lorg/json/JSONObject;

    return-void
.end method

.method public eV(Z)V
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->qPr:Z

    return-void
.end method

.method public eV()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->KZ:Z

    return v0
.end method

.method public efV()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->svN:I

    return v0
.end method

.method public eh(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->mt:I

    return-void
.end method

.method public eh()Z
    .locals 2

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Lvu()I

    move-result v0

    const/16 v1, 0x64

    if-eq v0, v1, :cond_1

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->BcC:I

    const/16 v1, 0x14

    if-ne v0, v1, :cond_0

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    return v0

    :cond_1
    :goto_0
    const/4 v0, 0x1

    return v0
.end method

.method public ei()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->yR:Ljava/lang/String;

    return-object v0
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 4

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    :cond_0
    const/4 v1, 0x0

    if-eqz p1, :cond_2

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v2

    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v3

    if-eq v2, v3, :cond_1

    goto :goto_0

    :cond_1
    check-cast p1, Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->mC:Ljava/lang/String;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->cs()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_2

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->uy:Ljava/lang/String;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->Jq()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v2, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_2

    return v0

    :cond_2
    :goto_0
    return v1
.end method

.method public ex(D)V
    .locals 3

    const-wide/high16 v0, 0x4000000000000000L    # 2.0

    cmpl-double v2, p1, v0

    if-eqz v2, :cond_0

    const-wide/high16 v0, 0x3ff0000000000000L    # 1.0

    cmpl-double v2, p1, v0

    if-eqz v2, :cond_0

    const/4 p1, 0x2

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->UF:I

    return-void

    :cond_0
    double-to-int p1, p1

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->UF:I

    return-void
.end method

.method public ex(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->BcC:I

    return-void
.end method

.method public ex(J)V
    .locals 0

    iput-wide p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->kF:J

    return-void
.end method

.method public ex(Lcom/bykv/vk/openvk/component/video/api/hjc/ex;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->efV:Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

    return-void
.end method

.method public ex(Lcom/bytedance/sdk/openadsdk/core/model/Tc;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->rAx:Lcom/bytedance/sdk/openadsdk/core/model/Tc;

    return-void
.end method

.method public ex(Lorg/json/JSONObject;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Gv:Lorg/json/JSONObject;

    return-void
.end method

.method public ex(Z)V
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->KZ:Z

    return-void
.end method

.method public fHV()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->ttV:Z

    return v0
.end method

.method public fU()Lcom/bytedance/sdk/openadsdk/core/model/UYd;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Wx:Lcom/bytedance/sdk/openadsdk/core/model/UYd;

    return-object v0
.end method

.method public fj()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Obv:Ljava/lang/String;

    return-object v0
.end method

.method public fj(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->PpV:I

    return-void
.end method

.method public fjZ()Lorg/json/JSONObject;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Fi:Lorg/json/JSONObject;

    return-object v0
.end method

.method public flF()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/bytedance/sdk/openadsdk/FilterWord;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Ubf:Ljava/util/List;

    return-object v0
.end method

.method public gXF()I
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->QV:Lcom/bytedance/sdk/openadsdk/core/model/rAx;

    if-nez v0, :cond_0

    const/4 v0, 0x0

    return v0

    :cond_0
    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/rAx;->Fj()I

    move-result v0

    return v0
.end method

.method public gXF(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->VXW:I

    return-void
.end method

.method public gci()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->JW:Ljava/util/List;

    return-object v0
.end method

.method public gf()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Nyg:I

    return v0
.end method

.method public hE()Z
    .locals 3

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->LZ()Z

    move-result v0

    const/4 v1, 0x0

    if-nez v0, :cond_2

    new-instance v0, Ljava/util/Random;

    invoke-direct {v0}, Ljava/util/Random;-><init>()V

    const/16 v2, 0x64

    invoke-virtual {v0, v2}, Ljava/util/Random;->nextInt(I)I

    move-result v0

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->zf()Z

    move-result v2

    if-eqz v2, :cond_0

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Wx:Lcom/bytedance/sdk/openadsdk/core/model/UYd;

    if-eqz v2, :cond_1

    invoke-virtual {v2}, Lcom/bytedance/sdk/openadsdk/core/model/UYd;->ex()I

    move-result v2

    goto :goto_0

    :cond_0
    invoke-static {p0}, Lcom/bytedance/sdk/openadsdk/core/model/mE;->ex(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v2

    if-nez v2, :cond_1

    iget v2, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Xnz:I

    goto :goto_0

    :cond_1
    const/4 v2, 0x0

    :goto_0
    if-ge v0, v2, :cond_2

    const/4 v0, 0x1

    return v0

    :cond_2
    return v1
.end method

.method public haP()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Vq:I

    return v0
.end method

.method public hg()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->HQ:I

    return v0
.end method

.method public hjc(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->rXP:I

    return-void
.end method

.method public hjc(J)V
    .locals 0

    iput-wide p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->ex:J

    return-void
.end method

.method public hjc(Lcom/bykv/vk/openvk/component/video/api/hjc/ex;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->iT:Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

    return-void
.end method

.method public hjc(Lcom/bytedance/sdk/openadsdk/core/model/Tc;)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->hjc:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public hjc(Lorg/json/JSONObject;)V
    .locals 2

    if-nez p1, :cond_0

    return-void

    :cond_0
    const/4 v0, 0x4

    iput v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->svN:I

    const-string v0, "id"

    invoke-virtual {p1, v0}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->mC:Ljava/lang/String;

    const-string v0, "source"

    invoke-virtual {p1, v0}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Moo:Ljava/lang/String;

    new-instance v0, Lcom/bytedance/sdk/openadsdk/core/model/hjc;

    invoke-direct {v0}, Lcom/bytedance/sdk/openadsdk/core/model/hjc;-><init>()V

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->cB:Lcom/bytedance/sdk/openadsdk/core/model/hjc;

    const-string v1, "pkg_name"

    invoke-virtual {p1, v1}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/core/model/hjc;->hjc(Ljava/lang/String;)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->cB:Lcom/bytedance/sdk/openadsdk/core/model/hjc;

    const-string v1, "name"

    invoke-virtual {p1, v1}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/core/model/hjc;->ex(Ljava/lang/String;)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->cB:Lcom/bytedance/sdk/openadsdk/core/model/hjc;

    const-string v1, "download_url"

    invoke-virtual {p1, v1}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Lcom/bytedance/sdk/openadsdk/core/model/hjc;->Fj(Ljava/lang/String;)V

    return-void
.end method

.method public hjc(Z)V
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->mj:Z

    return-void
.end method

.method public hjc()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->oX:Z

    return v0
.end method

.method public iT()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Moo:Ljava/lang/String;

    return-object v0
.end method

.method public iT(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->aYy:I

    return-void
.end method

.method public iq()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->zX:Ljava/lang/String;

    return-object v0
.end method

.method public jID()Lcom/bytedance/sdk/component/widget/ex/Fj;
    .locals 2

    new-instance v0, Lcom/bytedance/sdk/component/widget/ex/Fj;

    invoke-direct {v0}, Lcom/bytedance/sdk/component/widget/ex/Fj;-><init>()V

    iget v1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->OK:I

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/widget/ex/Fj;->Fj(I)V

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->mC:Ljava/lang/String;

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/widget/ex/Fj;->ex(Ljava/lang/String;)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Jq()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/widget/ex/Fj;->Fj(Ljava/lang/String;)V

    return-object v0
.end method

.method public jsD()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->OXv:I

    return v0
.end method

.method public kF()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->OK:I

    return v0
.end method

.method public kF(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->fjZ:I

    return-void
.end method

.method public kOD()V
    .locals 1

    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->tc:Z

    return-void
.end method

.method public kl()Z
    .locals 2

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Im()I

    move-result v0

    const/4 v1, 0x5

    if-ne v0, v1, :cond_0

    const/4 v0, 0x1

    return v0

    :cond_0
    const/4 v0, 0x0

    return v0
.end method

.method public ks()J
    .locals 2

    iget-wide v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->kF:J

    return-wide v0
.end method

.method public lc()Z
    .locals 3

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->zf()Z

    move-result v0

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->LZ()Z

    move-result v0

    if-nez v0, :cond_0

    new-instance v0, Ljava/util/Random;

    invoke-direct {v0}, Ljava/util/Random;-><init>()V

    const/16 v2, 0x64

    invoke-virtual {v0, v2}, Ljava/util/Random;->nextInt(I)I

    move-result v0

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Wx:Lcom/bytedance/sdk/openadsdk/core/model/UYd;

    if-eqz v2, :cond_0

    invoke-virtual {v2}, Lcom/bytedance/sdk/openadsdk/core/model/UYd;->hjc()I

    move-result v2

    if-ge v0, v2, :cond_0

    const/4 v0, 0x1

    return v0

    :cond_0
    return v1
.end method

.method public lv()Lcom/bytedance/sdk/openadsdk/AdSlot;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Jq:Lcom/bytedance/sdk/openadsdk/AdSlot;

    return-object v0
.end method

.method public lv(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->aGk:I

    return-void
.end method

.method public mC()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Eev:I

    return v0
.end method

.method public mC(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->eh:I

    return-void
.end method

.method public mC(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->ei:Ljava/lang/String;

    return-void
.end method

.method public mE()I
    .locals 2

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->BcC:I

    const/4 v1, 0x5

    if-eq v0, v1, :cond_2

    const/4 v1, 0x6

    if-ne v0, v1, :cond_0

    goto :goto_0

    :cond_0
    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->vLw()Z

    move-result v0

    if-eqz v0, :cond_1

    const/4 v0, 0x2

    return v0

    :cond_1
    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->fj:I

    return v0

    :cond_2
    :goto_0
    const/4 v0, 0x1

    return v0
.end method

.method public mE(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->uM:I

    return-void
.end method

.method public mE(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->uy:Ljava/lang/String;

    return-void
.end method

.method public mSE()Lcom/bytedance/sdk/openadsdk/core/model/eV;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->zYh:Lcom/bytedance/sdk/openadsdk/core/model/eV;

    return-object v0
.end method

.method public mSE(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->JZ:I

    return-void
.end method

.method public mSE(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->yo:Ljava/lang/String;

    return-void
.end method

.method public mj()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->UYd:Ljava/lang/String;

    return-object v0
.end method

.method public mt()I
    .locals 3

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Fi:Lorg/json/JSONObject;

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    const-string v2, "easy_playable_skip_duration"

    invoke-virtual {v0, v2, v1}, Lorg/json/JSONObject;->optInt(Ljava/lang/String;I)I

    move-result v0

    return v0

    :cond_0
    return v1
.end method

.method public nsB()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->cs:I

    return v0
.end method

.method public nsB(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->jsD:I

    return-void
.end method

.method public nsB(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->vYf:Ljava/lang/String;

    return-void
.end method

.method public oL()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->mE:Ljava/lang/String;

    return-object v0
.end method

.method public oX()Lcom/bytedance/sdk/openadsdk/core/model/Tc;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Tc;

    return-object v0
.end method

.method public pJO()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->ks:I

    return v0
.end method

.method public qK()Lcom/bytedance/sdk/component/adexpress/Fj/hjc/eV;
    .locals 2

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->uM()Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;

    move-result-object v0

    if-nez v0, :cond_0

    const/4 v0, 0x0

    return-object v0

    :cond_0
    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->aYy()I

    move-result v1

    invoke-static {v1}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;Ljava/lang/String;)Lcom/bytedance/sdk/component/adexpress/Fj/hjc/eV;

    move-result-object v0

    return-object v0
.end method

.method public qPr()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->dG:Ljava/lang/String;

    return-object v0
.end method

.method public qg()Lcom/bytedance/sdk/openadsdk/core/model/Ko;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->nsB:Lcom/bytedance/sdk/openadsdk/core/model/Ko;

    return-object v0
.end method

.method public qj()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Im:Ljava/lang/String;

    return-object v0
.end method

.method public qw()Ljava/lang/String;
    .locals 3

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->vg:Ljava/lang/String;

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v1

    const-string v2, ""

    if-eqz v1, :cond_0

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->sse()Lorg/json/JSONObject;

    move-result-object v1

    if-eqz v1, :cond_0

    const-string v0, "req_id"

    invoke-virtual {v1, v0, v2}, Lorg/json/JSONObject;->optString(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->uy(Ljava/lang/String;)V

    :cond_0
    if-nez v0, :cond_1

    return-object v2

    :cond_1
    return-object v0
.end method

.method public rAx()I
    .locals 1

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->ES()Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 v0, 0x5

    return v0

    :cond_0
    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->zf()Z

    move-result v0

    if-eqz v0, :cond_1

    const/4 v0, 0x2

    return v0

    :cond_1
    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->UF()Z

    move-result v0

    if-eqz v0, :cond_2

    const/4 v0, 0x4

    return v0

    :cond_2
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->zYh:Lcom/bytedance/sdk/openadsdk/core/model/eV;

    if-nez v0, :cond_3

    const/4 v0, 0x1

    return v0

    :cond_3
    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/eV;->ex()I

    move-result v0

    return v0
.end method

.method public rAx(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->qg:I

    return-void
.end method

.method public rAx(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Moo:Ljava/lang/String;

    return-void
.end method

.method public rS()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->lv:Ljava/lang/String;

    return-object v0
.end method

.method public rS(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->svN:I

    return-void
.end method

.method public rS(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->mC:Ljava/lang/String;

    return-void
.end method

.method public rXP()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->At:I

    return v0
.end method

.method public rXP(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->xx:I

    return-void
.end method

.method public rf()I
    .locals 2

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->zf()Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 v0, 0x5

    return v0

    :cond_0
    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->HQ()F

    move-result v0

    const/high16 v1, 0x42c80000    # 100.0f

    cmpl-float v0, v0, v1

    if-nez v0, :cond_2

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->mE()I

    move-result v0

    const/4 v1, 0x2

    if-ne v0, v1, :cond_1

    goto :goto_0

    :cond_1
    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->qg:I

    return v0

    :cond_2
    :goto_0
    const/4 v0, 0x0

    return v0
.end method

.method public rf(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->CML:I

    return-void
.end method

.method public rf(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->NgJ:Ljava/lang/String;

    return-void
.end method

.method public rqT()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->jID:I

    return v0
.end method

.method public spi()Lcom/bykv/vk/openvk/component/video/api/hjc/ex;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->spi:Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

    return-object v0
.end method

.method public spi(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Xnz:I

    return-void
.end method

.method public ss()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->xEF:I

    return v0
.end method

.method public sse()Lorg/json/JSONObject;
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->HY:Lorg/json/JSONObject;

    if-nez v0, :cond_0

    :try_start_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->uy:Ljava/lang/String;

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-nez v0, :cond_0

    new-instance v0, Lorg/json/JSONObject;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->uy:Ljava/lang/String;

    invoke-direct {v0, v1}, Lorg/json/JSONObject;-><init>(Ljava/lang/String;)V

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->HY:Lorg/json/JSONObject;
    :try_end_0
    .catch Lorg/json/JSONException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception v0

    const-string v1, "MaterialMeta"

    invoke-virtual {v0}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    move-result-object v0

    invoke-static {v1, v0}, Lcom/bytedance/sdk/component/utils/dG;->Fj(Ljava/lang/String;Ljava/lang/String;)V

    :cond_0
    :goto_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->HY:Lorg/json/JSONObject;

    return-object v0
.end method

.method public svN(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Kk:I

    return-void
.end method

.method public svN(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->lv:Ljava/lang/String;

    return-void
.end method

.method public svN(Z)V
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->sse:Z

    return-void
.end method

.method public svN()Z
    .locals 1

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Ubf()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-nez v0, :cond_0

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->WR()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-nez v0, :cond_0

    const/4 v0, 0x1

    return v0

    :cond_0
    const/4 v0, 0x0

    return v0
.end method

.method public tS()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->qw:Z

    return v0
.end method

.method public tc()Ljava/lang/String;
    .locals 2
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->At()Lcom/bytedance/sdk/openadsdk/core/model/hjc;

    move-result-object v0

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/hjc;->Fj()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v1

    if-nez v1, :cond_0

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/hjc;->Fj()Ljava/lang/String;

    move-result-object v0

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return-object v0
.end method

.method public tr()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->aYy:I

    return v0
.end method

.method public ttV()Ljava/lang/String;
    .locals 2

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Obv()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->At()Lcom/bytedance/sdk/openadsdk/core/model/hjc;

    move-result-object v1

    if-eqz v1, :cond_0

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->At()Lcom/bytedance/sdk/openadsdk/core/model/hjc;

    move-result-object v1

    invoke-virtual {v1}, Lcom/bytedance/sdk/openadsdk/core/model/hjc;->ex()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v1

    if-nez v1, :cond_0

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->At()Lcom/bytedance/sdk/openadsdk/core/model/hjc;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/hjc;->ex()Ljava/lang/String;

    move-result-object v0

    :cond_0
    return-object v0
.end method

.method public tyC()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->jsD:I

    if-lez v0, :cond_0

    return v0

    :cond_0
    const/16 v0, 0x14

    return v0
.end method

.method public uJB()Z
    .locals 3

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->zf()Z

    move-result v0

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->LZ()Z

    move-result v0

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Wx:Lcom/bytedance/sdk/openadsdk/core/model/UYd;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/UYd;->Fj()I

    move-result v0

    const/4 v2, 0x1

    if-ne v0, v2, :cond_0

    return v2

    :cond_0
    return v1
.end method

.method public uM()Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Bzy:Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;

    return-object v0
.end method

.method public uM(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Af:I

    return-void
.end method

.method public uv()V
    .locals 3

    const-string v0, "style_id"

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->spi:Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

    if-eqz v1, :cond_1

    invoke-virtual {v1}, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->UYd()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v1

    if-nez v1, :cond_1

    :try_start_0
    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->spi:Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

    invoke-virtual {v1}, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->UYd()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v1, v0}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result v1

    const/4 v2, 0x0

    if-eqz v1, :cond_0

    const/4 v1, 0x1

    iput-boolean v1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->cP:Z

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->spi:Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

    invoke-virtual {v1}, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->UYd()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Landroid/net/Uri;->parse(Ljava/lang/String;)Landroid/net/Uri;

    move-result-object v1

    invoke-virtual {v1, v0}, Landroid/net/Uri;->getQueryParameters(Ljava/lang/String;)Ljava/util/List;

    move-result-object v0

    invoke-interface {v0, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/String;

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->zX:Ljava/lang/String;

    return-void

    :catchall_0
    move-exception v0

    goto :goto_0

    :cond_0
    iput-boolean v2, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->cP:Z
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    return-void

    :goto_0
    invoke-virtual {v0}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/bytedance/sdk/component/utils/dG;->ex(Ljava/lang/String;)V

    :cond_1
    return-void
.end method

.method public uy()Lcom/bytedance/sdk/openadsdk/core/model/BcC;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->YH:Lcom/bytedance/sdk/openadsdk/core/model/BcC;

    return-object v0
.end method

.method public uy(I)V
    .locals 1

    const/4 v0, 0x0

    invoke-static {v0, p1}, Ljava/lang/Math;->max(II)I

    move-result p1

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->jID:I

    return-void
.end method

.method public uy(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->vg:Ljava/lang/String;

    return-void
.end method

.method public vLw()Z
    .locals 3

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->lv()Lcom/bytedance/sdk/openadsdk/AdSlot;

    move-result-object v0

    const/4 v1, 0x0

    if-nez v0, :cond_0

    return v1

    :cond_0
    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->lv()Lcom/bytedance/sdk/openadsdk/AdSlot;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/AdSlot;->getDurationSlotType()I

    move-result v0

    const/16 v2, 0x8

    if-ne v0, v2, :cond_1

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->UF()Z

    move-result v0

    if-eqz v0, :cond_1

    const/4 v0, 0x1

    return v0

    :cond_1
    return v1
.end method

.method public vYf()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->rXP:I

    return v0
.end method

.method public vYf(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Vq:I

    return-void
.end method

.method public vYf(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->rf:Ljava/lang/String;

    return-void
.end method

.method public vg()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->PpV:I

    return v0
.end method

.method public vv()J
    .locals 2

    iget-wide v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->ex:J

    return-wide v0
.end method

.method public xEF()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->zf:I

    return v0
.end method

.method public xx()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->fjZ:I

    return v0
.end method

.method public yR()Z
    .locals 3

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->zf()Z

    move-result v0

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    return v1

    :cond_0
    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->LKu()I

    move-result v0

    const/4 v2, 0x1

    if-ne v0, v2, :cond_1

    return v2

    :cond_1
    return v1
.end method

.method public yo()Z
    .locals 3

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->hjc:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    return v1

    :cond_0
    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Vq:I

    const/4 v2, 0x4

    if-ne v0, v2, :cond_1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->hjc:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    const/4 v2, 0x3

    if-ge v0, v2, :cond_1

    return v1

    :cond_1
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->hjc:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_2
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_3

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/bytedance/sdk/openadsdk/core/model/Tc;

    invoke-virtual {v2}, Lcom/bytedance/sdk/openadsdk/core/model/Tc;->Ubf()Z

    move-result v2

    if-nez v2, :cond_2

    return v1

    :cond_3
    const/4 v0, 0x1

    return v0
.end method

.method public zDD()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Af:I

    return v0
.end method

.method public zX()Z
    .locals 2

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->Im()I

    move-result v0

    const/4 v1, 0x7

    if-ne v0, v1, :cond_0

    const/4 v0, 0x1

    return v0

    :cond_0
    const/4 v0, 0x0

    return v0
.end method

.method public zYh()Z
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->spi:Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

    const/4 v1, 0x1

    if-eqz v0, :cond_1

    invoke-virtual {v0}, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->vYf()I

    move-result v0

    if-eq v0, v1, :cond_0

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    return v0

    :cond_1
    :goto_0
    return v1
.end method

.method public zf()Z
    .locals 3

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->VXW:I

    const/4 v1, 0x1

    if-eq v0, v1, :cond_0

    const/4 v2, 0x3

    if-ne v0, v2, :cond_1

    :cond_0
    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->OXv()Lcom/bytedance/sdk/openadsdk/core/svN/Fj;

    move-result-object v0

    if-eqz v0, :cond_1

    return v1

    :cond_1
    const/4 v0, 0x0

    return v0
.end method

.method public zj()V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/Af;->WR:Lcom/bytedance/sdk/openadsdk/utils/lv;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/utils/lv;->eV()V

    const/4 v0, 0x0

    invoke-virtual {p0, v0}, Lcom/bytedance/sdk/openadsdk/core/model/Af;->svN(Z)V

    return-void
.end method
