.class public final Lcom/transsion/play/detail/PlayDetailBottomRecHelper;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/transsion/play/detail/PlayDetailBottomRecHelper$a;,
        Lcom/transsion/play/detail/PlayDetailBottomRecHelper$b;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation

.annotation build Lkotlin/jvm/internal/SourceDebugExtension;
.end annotation


# static fields
.field public static final p:Lcom/transsion/play/detail/PlayDetailBottomRecHelper$a;


# instance fields
.field public final a:Landroidx/fragment/app/Fragment;

.field public final b:Lcom/transsion/moviedetailapi/bean/Subject;

.field public final c:Lnet/lucode/hackware/magicindicator/MagicIndicator;

.field public final d:Landroidx/viewpager2/widget/ViewPager2;

.field public final e:Ljava/lang/String;

.field public final f:Z

.field public final g:Z

.field public h:Lcom/transsion/baseui/widget/magicindicator/ORCommonNavigator;

.field public i:Lcom/transsion/play/detail/PlayDetailBottomRecHelper$b;

.field public j:Landroidx/viewpager2/adapter/FragmentStateAdapter;

.field public k:I

.field public l:J

.field public m:Z

.field public final n:Lkotlin/Lazy;

.field public final o:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lcom/transsion/play/detail/PlayDetailBottomRecHelper$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lcom/transsion/play/detail/PlayDetailBottomRecHelper$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->p:Lcom/transsion/play/detail/PlayDetailBottomRecHelper$a;

    return-void
.end method

.method public constructor <init>(Landroidx/fragment/app/Fragment;Lcom/transsion/moviedetailapi/bean/Subject;Lnet/lucode/hackware/magicindicator/MagicIndicator;Landroidx/viewpager2/widget/ViewPager2;Ljava/lang/String;ZZ)V
    .locals 1

    const-string v0, "fragment"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->a:Landroidx/fragment/app/Fragment;

    iput-object p2, p0, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->b:Lcom/transsion/moviedetailapi/bean/Subject;

    iput-object p3, p0, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->c:Lnet/lucode/hackware/magicindicator/MagicIndicator;

    iput-object p4, p0, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->d:Landroidx/viewpager2/widget/ViewPager2;

    iput-object p5, p0, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->e:Ljava/lang/String;

    iput-boolean p6, p0, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->f:Z

    iput-boolean p7, p0, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->g:Z

    new-instance p1, Lcom/transsion/play/detail/PlayDetailBottomRecHelper$playDetailViewModel$2;

    invoke-direct {p1, p0}, Lcom/transsion/play/detail/PlayDetailBottomRecHelper$playDetailViewModel$2;-><init>(Lcom/transsion/play/detail/PlayDetailBottomRecHelper;)V

    invoke-static {p1}, Lkotlin/LazyKt;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/Lazy;

    move-result-object p1

    iput-object p1, p0, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->n:Lkotlin/Lazy;

    invoke-static {}, Lcom/blankj/utilcode/util/Utils;->a()Landroid/app/Application;

    move-result-object p1

    sget p2, Lcom/transsion/baseui/R$string;->for_you:I

    invoke-virtual {p1, p2}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    move-result-object p1

    invoke-static {}, Lcom/blankj/utilcode/util/Utils;->a()Landroid/app/Application;

    move-result-object p2

    sget p3, Lcom/transsion/baseui/R$string;->name_comments:I

    invoke-virtual {p2, p3}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    move-result-object p2

    filled-new-array {p1, p2}, [Ljava/lang/String;

    move-result-object p1

    invoke-static {p1}, Lkotlin/collections/CollectionsKt;->o([Ljava/lang/Object;)Ljava/util/List;

    move-result-object p1

    iput-object p1, p0, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->o:Ljava/util/List;

    invoke-virtual {p0}, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->p()V

    invoke-virtual {p0}, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->s()V

    invoke-virtual {p0}, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->r()V

    invoke-virtual {p0}, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->q()V

    return-void
.end method

.method public synthetic constructor <init>(Landroidx/fragment/app/Fragment;Lcom/transsion/moviedetailapi/bean/Subject;Lnet/lucode/hackware/magicindicator/MagicIndicator;Landroidx/viewpager2/widget/ViewPager2;Ljava/lang/String;ZZILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 10

    and-int/lit8 v0, p8, 0x20

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    const/4 v8, 0x0

    goto :goto_0

    :cond_0
    move/from16 v8, p6

    :goto_0
    and-int/lit8 v0, p8, 0x40

    if-eqz v0, :cond_1

    const/4 v9, 0x0

    goto :goto_1

    :cond_1
    move/from16 v9, p7

    :goto_1
    move-object v2, p0

    move-object v3, p1

    move-object v4, p2

    move-object v5, p3

    move-object v6, p4

    move-object v7, p5

    invoke-direct/range {v2 .. v9}, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;-><init>(Landroidx/fragment/app/Fragment;Lcom/transsion/moviedetailapi/bean/Subject;Lnet/lucode/hackware/magicindicator/MagicIndicator;Landroidx/viewpager2/widget/ViewPager2;Ljava/lang/String;ZZ)V

    return-void
.end method

.method public static final synthetic a(Lcom/transsion/play/detail/PlayDetailBottomRecHelper;)Lcom/transsion/baseui/widget/magicindicator/ORCommonNavigator;
    .locals 0

    iget-object p0, p0, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->h:Lcom/transsion/baseui/widget/magicindicator/ORCommonNavigator;

    return-object p0
.end method

.method public static final synthetic b(Lcom/transsion/play/detail/PlayDetailBottomRecHelper;)J
    .locals 2

    iget-wide v0, p0, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->l:J

    return-wide v0
.end method

.method public static final synthetic c(Lcom/transsion/play/detail/PlayDetailBottomRecHelper;)Landroidx/fragment/app/Fragment;
    .locals 0

    iget-object p0, p0, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->a:Landroidx/fragment/app/Fragment;

    return-object p0
.end method

.method public static final synthetic d(Lcom/transsion/play/detail/PlayDetailBottomRecHelper;)Lnet/lucode/hackware/magicindicator/MagicIndicator;
    .locals 0

    iget-object p0, p0, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->c:Lnet/lucode/hackware/magicindicator/MagicIndicator;

    return-object p0
.end method

.method public static final synthetic e(Lcom/transsion/play/detail/PlayDetailBottomRecHelper;)Ljava/lang/String;
    .locals 0

    iget-object p0, p0, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->e:Ljava/lang/String;

    return-object p0
.end method

.method public static final synthetic f(Lcom/transsion/play/detail/PlayDetailBottomRecHelper;)Lcom/transsion/moviedetailapi/bean/Subject;
    .locals 0

    iget-object p0, p0, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->b:Lcom/transsion/moviedetailapi/bean/Subject;

    return-object p0
.end method

.method public static final synthetic g(Lcom/transsion/play/detail/PlayDetailBottomRecHelper;)Ljava/util/List;
    .locals 0

    iget-object p0, p0, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->o:Ljava/util/List;

    return-object p0
.end method

.method public static final synthetic h(Lcom/transsion/play/detail/PlayDetailBottomRecHelper;)Z
    .locals 0

    iget-boolean p0, p0, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->f:Z

    return p0
.end method

.method public static final synthetic i(Lcom/transsion/play/detail/PlayDetailBottomRecHelper;)Z
    .locals 0

    iget-boolean p0, p0, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->g:Z

    return p0
.end method

.method public static final synthetic j(Lcom/transsion/play/detail/PlayDetailBottomRecHelper;)Z
    .locals 0

    iget-boolean p0, p0, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->m:Z

    return p0
.end method

.method public static final synthetic k(Lcom/transsion/play/detail/PlayDetailBottomRecHelper;J)V
    .locals 0

    iput-wide p1, p0, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->l:J

    return-void
.end method

.method public static final synthetic l(Lcom/transsion/play/detail/PlayDetailBottomRecHelper;I)V
    .locals 0

    iput p1, p0, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->k:I

    return-void
.end method

.method public static final synthetic m(Lcom/transsion/play/detail/PlayDetailBottomRecHelper;Z)V
    .locals 0

    iput-boolean p1, p0, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->m:Z

    return-void
.end method


# virtual methods
.method public final n()V
    .locals 3

    iget-object v0, p0, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->d:Landroidx/viewpager2/widget/ViewPager2;

    if-eqz v0, :cond_0

    new-instance v1, Lcom/transsion/play/detail/PlayDetailBottomRecHelper$c;

    invoke-direct {v1, p0}, Lcom/transsion/play/detail/PlayDetailBottomRecHelper$c;-><init>(Lcom/transsion/play/detail/PlayDetailBottomRecHelper;)V

    invoke-virtual {v0, v1}, Landroidx/viewpager2/widget/ViewPager2;->registerOnPageChangeCallback(Landroidx/viewpager2/widget/ViewPager2$OnPageChangeCallback;)V

    :cond_0
    iget-object v0, p0, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->d:Landroidx/viewpager2/widget/ViewPager2;

    if-eqz v0, :cond_1

    iget v1, p0, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->k:I

    const/4 v2, 0x0

    invoke-virtual {v0, v1, v2}, Landroidx/viewpager2/widget/ViewPager2;->setCurrentItem(IZ)V

    :cond_1
    return-void
.end method

.method public final o()Lcom/transsion/play/detail/viewmodel/PlayDetailViewModel;
    .locals 1

    iget-object v0, p0, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->n:Lkotlin/Lazy;

    invoke-interface {v0}, Lkotlin/Lazy;->getValue()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/transsion/play/detail/viewmodel/PlayDetailViewModel;

    return-object v0
.end method

.method public final p()V
    .locals 3

    sget-object v0, Lcom/transsion/baselib/report/launch/RoomAppMMKV;->a:Lcom/transsion/baselib/report/launch/RoomAppMMKV;

    invoke-virtual {v0}, Lcom/transsion/baselib/report/launch/RoomAppMMKV;->a()Lcom/tencent/mmkv/MMKV;

    move-result-object v0

    const-string v1, "key_subj_comment_dot"

    const/4 v2, 0x1

    invoke-virtual {v0, v1, v2}, Lcom/tencent/mmkv/MMKV;->getBoolean(Ljava/lang/String;Z)Z

    move-result v0

    iput-boolean v0, p0, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->m:Z

    return-void
.end method

.method public final q()V
    .locals 4

    new-instance v0, Lcom/transsion/baseui/widget/magicindicator/ORCommonNavigator;

    iget-object v1, p0, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->a:Landroidx/fragment/app/Fragment;

    invoke-virtual {v1}, Landroidx/fragment/app/Fragment;->requireContext()Landroid/content/Context;

    move-result-object v1

    invoke-direct {v0, v1}, Lcom/transsion/baseui/widget/magicindicator/ORCommonNavigator;-><init>(Landroid/content/Context;)V

    const/4 v1, 0x1

    invoke-virtual {v0, v1}, Lcom/transsion/baseui/widget/magicindicator/ORCommonNavigator;->setFollowTouch(Z)V

    new-instance v1, Lcom/transsion/play/detail/PlayDetailBottomRecHelper$b;

    iget-object v2, p0, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->d:Landroidx/viewpager2/widget/ViewPager2;

    iget-object v3, p0, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->o:Ljava/util/List;

    invoke-direct {v1, p0, v2, v3}, Lcom/transsion/play/detail/PlayDetailBottomRecHelper$b;-><init>(Lcom/transsion/play/detail/PlayDetailBottomRecHelper;Landroidx/viewpager2/widget/ViewPager2;Ljava/util/List;)V

    iput-object v1, p0, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->i:Lcom/transsion/play/detail/PlayDetailBottomRecHelper$b;

    invoke-virtual {v0, v1}, Lcom/transsion/baseui/widget/magicindicator/ORCommonNavigator;->setAdapter(Lul/a;)V

    iput-object v0, p0, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->h:Lcom/transsion/baseui/widget/magicindicator/ORCommonNavigator;

    iget-object v1, p0, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->c:Lnet/lucode/hackware/magicindicator/MagicIndicator;

    if-nez v1, :cond_0

    goto :goto_0

    :cond_0
    invoke-virtual {v1, v0}, Lnet/lucode/hackware/magicindicator/MagicIndicator;->setNavigator(Lgv/a;)V

    :goto_0
    return-void
.end method

.method public final r()V
    .locals 2

    iget-object v0, p0, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->a:Landroidx/fragment/app/Fragment;

    new-instance v1, Lcom/transsion/play/detail/PlayDetailBottomRecHelper$d;

    invoke-direct {v1, p0, v0}, Lcom/transsion/play/detail/PlayDetailBottomRecHelper$d;-><init>(Lcom/transsion/play/detail/PlayDetailBottomRecHelper;Landroidx/fragment/app/Fragment;)V

    iput-object v1, p0, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->j:Landroidx/viewpager2/adapter/FragmentStateAdapter;

    iget-object v0, p0, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->d:Landroidx/viewpager2/widget/ViewPager2;

    if-nez v0, :cond_0

    goto :goto_0

    :cond_0
    invoke-virtual {v0, v1}, Landroidx/viewpager2/widget/ViewPager2;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    :goto_0
    iget-object v0, p0, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->d:Landroidx/viewpager2/widget/ViewPager2;

    if-nez v0, :cond_1

    goto :goto_1

    :cond_1
    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Landroidx/viewpager2/widget/ViewPager2;->setUserInputEnabled(Z)V

    :goto_1
    invoke-virtual {p0}, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->n()V

    return-void
.end method

.method public final s()V
    .locals 7

    iget-object v1, p0, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->a:Landroidx/fragment/app/Fragment;

    new-instance v6, Lcom/transsion/play/detail/PlayDetailBottomRecHelper$initViwModel$1;

    invoke-direct {v6, p0}, Lcom/transsion/play/detail/PlayDetailBottomRecHelper$initViwModel$1;-><init>(Lcom/transsion/play/detail/PlayDetailBottomRecHelper;)V

    sget-object v0, Lcom/transsnet/flow/event/AppScopeVMlProvider;->INSTANCE:Lcom/transsnet/flow/event/AppScopeVMlProvider;

    const-class v2, Lcom/transsnet/flow/event/FlowEventBus;

    invoke-virtual {v0, v2}, Lcom/transsnet/flow/event/AppScopeVMlProvider;->getApplicationScopeViewModel(Ljava/lang/Class;)Landroidx/lifecycle/u0;

    move-result-object v0

    check-cast v0, Lcom/transsnet/flow/event/FlowEventBus;

    const-class v2, Lcom/transsnet/flow/event/sync/event/PublishEvent;

    invoke-virtual {v2}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v2

    const-string v3, "T::class.java.name"

    invoke-static {v2, v3}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    sget-object v3, Landroidx/lifecycle/Lifecycle$State;->CREATED:Landroidx/lifecycle/Lifecycle$State;

    invoke-static {}, Lkotlinx/coroutines/w0;->c()Lkotlinx/coroutines/a2;

    move-result-object v4

    invoke-virtual {v4}, Lkotlinx/coroutines/a2;->t()Lkotlinx/coroutines/a2;

    move-result-object v4

    const/4 v5, 0x0

    invoke-virtual/range {v0 .. v6}, Lcom/transsnet/flow/event/FlowEventBus;->observeEvent(Landroidx/lifecycle/u;Ljava/lang/String;Landroidx/lifecycle/Lifecycle$State;Lkotlinx/coroutines/CoroutineDispatcher;ZLkotlin/jvm/functions/Function1;)Lkotlinx/coroutines/q1;

    invoke-virtual {p0}, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->o()Lcom/transsion/play/detail/viewmodel/PlayDetailViewModel;

    move-result-object v0

    invoke-virtual {v0}, Lcom/transsion/play/detail/viewmodel/PlayDetailViewModel;->f()Landroidx/lifecycle/c0;

    move-result-object v0

    iget-object v1, p0, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->a:Landroidx/fragment/app/Fragment;

    new-instance v2, Lcom/transsion/play/detail/PlayDetailBottomRecHelper$initViwModel$2;

    invoke-direct {v2, p0}, Lcom/transsion/play/detail/PlayDetailBottomRecHelper$initViwModel$2;-><init>(Lcom/transsion/play/detail/PlayDetailBottomRecHelper;)V

    new-instance v3, Lcom/transsion/play/detail/PlayDetailBottomRecHelper$e;

    invoke-direct {v3, v2}, Lcom/transsion/play/detail/PlayDetailBottomRecHelper$e;-><init>(Lkotlin/jvm/functions/Function1;)V

    invoke-virtual {v0, v1, v3}, Landroidx/lifecycle/LiveData;->j(Landroidx/lifecycle/u;Landroidx/lifecycle/d0;)V

    iget-object v0, p0, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->b:Lcom/transsion/moviedetailapi/bean/Subject;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/transsion/moviedetailapi/bean/Subject;->getSubjectId()Ljava/lang/String;

    move-result-object v0

    if-eqz v0, :cond_0

    invoke-virtual {p0}, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->o()Lcom/transsion/play/detail/viewmodel/PlayDetailViewModel;

    move-result-object v1

    invoke-virtual {v1, v0}, Lcom/transsion/play/detail/viewmodel/PlayDetailViewModel;->e(Ljava/lang/String;)V

    :cond_0
    return-void
.end method

.method public final t(Z)V
    .locals 4

    sget-object v0, Lcom/transsion/baseui/util/c;->a:Lcom/transsion/baseui/util/c;

    const v1, 0x1636f

    const-wide/16 v2, 0x1f4

    invoke-virtual {v0, v1, v2, v3}, Lcom/transsion/baseui/util/c;->a(IJ)Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    iget-object v0, p0, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->a:Landroidx/fragment/app/Fragment;

    invoke-virtual {v0}, Landroidx/fragment/app/Fragment;->getChildFragmentManager()Landroidx/fragment/app/FragmentManager;

    move-result-object v0

    invoke-virtual {v0}, Landroidx/fragment/app/FragmentManager;->getFragments()Ljava/util/List;

    move-result-object v0

    const-string v1, "fragment.childFragmentManager.fragments"

    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    check-cast v0, Ljava/lang/Iterable;

    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_1
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_2

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroidx/fragment/app/Fragment;

    instance-of v2, v1, Lus/a;

    if-eqz v2, :cond_1

    check-cast v1, Lus/a;

    invoke-interface {v1, p1}, Lus/a;->l(Z)V

    goto :goto_0

    :cond_2
    return-void
.end method
