.class public final Landroidx/compose/runtime/w2;
.super Ljava/lang/Object;


# annotations
.annotation runtime Lkotlin/Metadata;
.end annotation


# direct methods
.method public static final a()Landroidx/compose/runtime/collection/b;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Landroidx/compose/runtime/collection/b<",
            "Landroidx/compose/runtime/a0;",
            ">;"
        }
    .end annotation

    invoke-static {}, Landroidx/compose/runtime/x2;->b()Landroidx/compose/runtime/collection/b;

    move-result-object v0

    return-object v0
.end method

.method public static final b(Landroidx/compose/runtime/v2;Lkotlin/jvm/functions/Function0;)Landroidx/compose/runtime/f3;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Landroidx/compose/runtime/v2<",
            "TT;>;",
            "Lkotlin/jvm/functions/Function0<",
            "+TT;>;)",
            "Landroidx/compose/runtime/f3<",
            "TT;>;"
        }
    .end annotation

    invoke-static {p0, p1}, Landroidx/compose/runtime/x2;->c(Landroidx/compose/runtime/v2;Lkotlin/jvm/functions/Function0;)Landroidx/compose/runtime/f3;

    move-result-object p0

    return-object p0
.end method

.method public static final c(Lkotlin/jvm/functions/Function0;)Landroidx/compose/runtime/f3;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Lkotlin/jvm/functions/Function0<",
            "+TT;>;)",
            "Landroidx/compose/runtime/f3<",
            "TT;>;"
        }
    .end annotation

    invoke-static {p0}, Landroidx/compose/runtime/x2;->d(Lkotlin/jvm/functions/Function0;)Landroidx/compose/runtime/f3;

    move-result-object p0

    return-object p0
.end method

.method public static final d()Landroidx/compose/runtime/snapshots/SnapshotStateList;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">()",
            "Landroidx/compose/runtime/snapshots/SnapshotStateList<",
            "TT;>;"
        }
    .end annotation

    invoke-static {}, Landroidx/compose/runtime/z2;->a()Landroidx/compose/runtime/snapshots/SnapshotStateList;

    move-result-object v0

    return-object v0
.end method

.method public static final e(Ljava/lang/Object;Landroidx/compose/runtime/v2;)Landroidx/compose/runtime/i1;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(TT;",
            "Landroidx/compose/runtime/v2<",
            "TT;>;)",
            "Landroidx/compose/runtime/i1<",
            "TT;>;"
        }
    .end annotation

    invoke-static {p0, p1}, Landroidx/compose/runtime/z2;->b(Ljava/lang/Object;Landroidx/compose/runtime/v2;)Landroidx/compose/runtime/i1;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic f(Ljava/lang/Object;Landroidx/compose/runtime/v2;ILjava/lang/Object;)Landroidx/compose/runtime/i1;
    .locals 0

    invoke-static {p0, p1, p2, p3}, Landroidx/compose/runtime/z2;->c(Ljava/lang/Object;Landroidx/compose/runtime/v2;ILjava/lang/Object;)Landroidx/compose/runtime/i1;

    move-result-object p0

    return-object p0
.end method

.method public static final g()Landroidx/compose/runtime/v2;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">()",
            "Landroidx/compose/runtime/v2<",
            "TT;>;"
        }
    .end annotation

    invoke-static {}, Landroidx/compose/runtime/y2;->a()Landroidx/compose/runtime/v2;

    move-result-object v0

    return-object v0
.end method

.method public static final h()Landroidx/compose/runtime/v2;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">()",
            "Landroidx/compose/runtime/v2<",
            "TT;>;"
        }
    .end annotation

    invoke-static {}, Landroidx/compose/runtime/y2;->b()Landroidx/compose/runtime/v2;

    move-result-object v0

    return-object v0
.end method

.method public static final i(Ljava/lang/Object;Landroidx/compose/runtime/i;I)Landroidx/compose/runtime/f3;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(TT;",
            "Landroidx/compose/runtime/i;",
            "I)",
            "Landroidx/compose/runtime/f3<",
            "TT;>;"
        }
    .end annotation

    invoke-static {p0, p1, p2}, Landroidx/compose/runtime/z2;->d(Ljava/lang/Object;Landroidx/compose/runtime/i;I)Landroidx/compose/runtime/f3;

    move-result-object p0

    return-object p0
.end method

.method public static final j()Landroidx/compose/runtime/v2;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">()",
            "Landroidx/compose/runtime/v2<",
            "TT;>;"
        }
    .end annotation

    invoke-static {}, Landroidx/compose/runtime/y2;->c()Landroidx/compose/runtime/v2;

    move-result-object v0

    return-object v0
.end method
