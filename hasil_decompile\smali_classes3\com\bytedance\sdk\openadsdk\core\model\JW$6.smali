.class Lcom/bytedance/sdk/openadsdk/core/model/JW$6;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/core/model/JW;->Fj()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/core/model/JW;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/core/model/JW;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/JW$6;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/JW;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 6

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/JW$6;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/JW;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/model/JW;->hjc(Lcom/bytedance/sdk/openadsdk/core/model/JW;)Ljava/util/concurrent/atomic/AtomicBoolean;

    move-result-object v0

    invoke-virtual {v0}, Ljava/util/concurrent/atomic/AtomicBoolean;->get()Z

    move-result v0

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/JW$6;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/JW;

    iget-object v1, v0, Lcom/bytedance/sdk/openadsdk/core/model/JW;->svN:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/model/JW;->eV(Lcom/bytedance/sdk/openadsdk/core/model/JW;)Ljava/lang/String;

    move-result-object v0

    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v2

    iget-object v4, p0, Lcom/bytedance/sdk/openadsdk/core/model/JW$6;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/JW;

    invoke-static {v4}, Lcom/bytedance/sdk/openadsdk/core/model/JW;->Ubf(Lcom/bytedance/sdk/openadsdk/core/model/JW;)J

    move-result-wide v4

    sub-long/2addr v2, v4

    const/4 v4, 0x0

    invoke-static {v1, v0, v2, v3, v4}, Lcom/bytedance/sdk/openadsdk/ex/hjc;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Ljava/lang/String;JZ)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/JW$6;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/JW;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/model/JW;->WR(Lcom/bytedance/sdk/openadsdk/core/model/JW;)V

    :cond_0
    return-void
.end method
