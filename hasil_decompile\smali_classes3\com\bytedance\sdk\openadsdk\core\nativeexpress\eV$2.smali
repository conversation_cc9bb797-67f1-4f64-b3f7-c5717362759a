.class Lcom/bytedance/sdk/openadsdk/core/nativeexpress/eV$2;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/sdk/openadsdk/core/nativeexpress/eV;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/eV;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/eV;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/eV$2;->Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/eV;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/eV$2;->Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/eV;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/eV;->hjc(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/eV;)Lcom/bytedance/sdk/component/adexpress/ex/svN;

    move-result-object v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/eV$2;->Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/eV;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/eV;->hjc(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/eV;)Lcom/bytedance/sdk/component/adexpress/ex/svN;

    move-result-object v1

    invoke-static {v0, v1}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/eV;->Fj(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/eV;Lcom/bytedance/sdk/component/adexpress/ex/svN;)V

    :cond_0
    return-void
.end method
