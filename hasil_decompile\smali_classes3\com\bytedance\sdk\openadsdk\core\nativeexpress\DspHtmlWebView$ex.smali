.class public interface abstract Lcom/bytedance/sdk/openadsdk/core/nativeexpress/DspHtmlWebView$ex;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/sdk/openadsdk/core/nativeexpress/DspHtmlWebView;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "ex"
.end annotation


# virtual methods
.method public abstract Fj()Landroid/view/View;
.end method

.method public abstract Fj(II)V
.end method

.method public abstract Fj(Landroid/view/View;I)V
.end method

.method public abstract c_()V
.end method

.method public abstract ex()Landroid/view/View;
.end method
