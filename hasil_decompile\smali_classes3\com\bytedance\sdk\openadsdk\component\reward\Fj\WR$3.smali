.class Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$3;
.super Lcom/bytedance/sdk/openadsdk/core/nativeexpress/svN;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;->Fj([FLcom/bytedance/sdk/openadsdk/core/video/hjc/ex;Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/core/video/hjc/ex;

.field final synthetic ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;Landroid/content/Context;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Ljava/lang/String;ILcom/bytedance/sdk/openadsdk/core/video/hjc/ex;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$3;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;

    iput-object p6, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$3;->Fj:Lcom/bytedance/sdk/openadsdk/core/video/hjc/ex;

    invoke-direct {p0, p2, p3, p4, p5}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/svN;-><init>(Landroid/content/Context;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Ljava/lang/String;I)V

    return-void
.end method


# virtual methods
.method public Fj(Landroid/view/View;FFFFLandroid/util/SparseArray;Z)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/view/View;",
            "FFFF",
            "Landroid/util/SparseArray<",
            "Lcom/bytedance/sdk/openadsdk/core/ex/hjc$Fj;",
            ">;Z)V"
        }
    .end annotation

    invoke-super/range {p0 .. p7}, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->Fj(Landroid/view/View;FFFFLandroid/util/SparseArray;Z)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$3;->Fj:Lcom/bytedance/sdk/openadsdk/core/video/hjc/ex;

    invoke-interface {p1}, Lcom/bytedance/sdk/openadsdk/core/video/hjc/ex;->rS()V

    return-void
.end method
