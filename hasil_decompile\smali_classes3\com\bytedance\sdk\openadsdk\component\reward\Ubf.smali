.class public Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$Fj;,
        Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$ex;
    }
.end annotation


# static fields
.field private static volatile Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;
    .annotation build Landroid/annotation/SuppressLint;
        value = {
            "StaticFieldLeak"
        }
    .end annotation
.end field


# instance fields
.field private Ubf:Lcom/bytedance/sdk/component/svN/BcC;

.field private final WR:Lcom/bytedance/sdk/component/utils/cB$Fj;

.field private final eV:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$ex;",
            ">;"
        }
    .end annotation
.end field

.field private final ex:Landroid/content/Context;

.field private final hjc:Ljava/util/concurrent/atomic/AtomicBoolean;


# direct methods
.method private constructor <init>(Landroid/content/Context;)V
    .locals 2

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Ljava/util/concurrent/atomic/AtomicBoolean;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Ljava/util/concurrent/atomic/AtomicBoolean;-><init>(Z)V

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;->hjc:Ljava/util/concurrent/atomic/AtomicBoolean;

    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    invoke-static {v0}, Ljava/util/Collections;->synchronizedList(Ljava/util/List;)Ljava/util/List;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;->eV:Ljava/util/List;

    new-instance v0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$7;

    invoke-direct {v0, p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$7;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;)V

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;->WR:Lcom/bytedance/sdk/component/utils/cB$Fj;

    if-nez p1, :cond_0

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/JU;->Fj()Landroid/content/Context;

    move-result-object p1

    goto :goto_0

    :cond_0
    invoke-virtual {p1}, Landroid/content/Context;->getApplicationContext()Landroid/content/Context;

    move-result-object p1

    :goto_0
    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;->ex:Landroid/content/Context;

    invoke-direct {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;->ex()V

    return-void
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;)Landroid/content/Context;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;->ex:Landroid/content/Context;

    return-object p0
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;Lcom/bytedance/sdk/component/svN/BcC;)Lcom/bytedance/sdk/component/svN/BcC;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;->Ubf:Lcom/bytedance/sdk/component/svN/BcC;

    return-object p1
.end method

.method public static Fj(Landroid/content/Context;)Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;
    .locals 2

    sget-object v0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;

    if-nez v0, :cond_1

    const-class v0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;

    monitor-enter v0

    :try_start_0
    sget-object v1, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;

    if-nez v1, :cond_0

    new-instance v1, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;

    invoke-direct {v1, p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;-><init>(Landroid/content/Context;)V

    sput-object v1, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;

    goto :goto_0

    :catchall_0
    move-exception p0

    goto :goto_1

    :cond_0
    :goto_0
    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_2

    :goto_1
    monitor-exit v0

    throw p0

    :cond_1
    :goto_2
    sget-object p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;

    return-object p0
.end method

.method private Fj(Lcom/bytedance/sdk/openadsdk/AdSlot;ZLcom/bytedance/sdk/openadsdk/utils/lv;Lcom/bytedance/sdk/openadsdk/common/hjc;)V
    .locals 11

    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v5

    new-instance v8, Lcom/bytedance/sdk/openadsdk/core/model/vYf;

    invoke-direct {v8}, Lcom/bytedance/sdk/openadsdk/core/model/vYf;-><init>()V

    const/4 v0, 0x2

    if-eqz p2, :cond_0

    const/4 v1, 0x2

    goto :goto_0

    :cond_0
    const/4 v1, 0x1

    :goto_0
    iput v1, v8, Lcom/bytedance/sdk/openadsdk/core/model/vYf;->hjc:I

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/JU;->eV()Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;

    move-result-object v1

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/AdSlot;->getCodeId()Ljava/lang/String;

    move-result-object v2

    invoke-interface {v1, v2}, Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;->rAx(Ljava/lang/String;)Z

    move-result v1

    if-nez v1, :cond_1

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/AdSlot;->getExpressViewAcceptedWidth()F

    move-result v1

    const/4 v2, 0x0

    cmpl-float v1, v1, v2

    if-gtz v1, :cond_1

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/AdSlot;->isExpressAd()Z

    move-result v1

    if-eqz v1, :cond_2

    :cond_1
    iput v0, v8, Lcom/bytedance/sdk/openadsdk/core/model/vYf;->WR:I

    :cond_2
    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/JU;->hjc()Lcom/bytedance/sdk/openadsdk/core/Ql;

    move-result-object v9

    new-instance v10, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$3;

    move-object v0, v10

    move-object v1, p0

    move v2, p2

    move-object v3, p4

    move-object v4, p1

    move-object v7, p3

    invoke-direct/range {v0 .. v7}, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$3;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;ZLcom/bytedance/sdk/openadsdk/common/hjc;Lcom/bytedance/sdk/openadsdk/AdSlot;JLcom/bytedance/sdk/openadsdk/utils/lv;)V

    const/16 p2, 0x8

    invoke-interface {v9, p1, v8, p2, v10}, Lcom/bytedance/sdk/openadsdk/core/Ql;->Fj(Lcom/bytedance/sdk/openadsdk/AdSlot;Lcom/bytedance/sdk/openadsdk/core/model/vYf;ILcom/bytedance/sdk/openadsdk/core/Ql$Fj;)V

    return-void
.end method

.method private Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$ex;)V
    .locals 2

    if-nez p1, :cond_0

    return-void

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;->eV:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    if-lez v0, :cond_1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;->eV:Ljava/util/List;

    const/4 v1, 0x0

    invoke-interface {v0, v1}, Ljava/util/List;->remove(I)Ljava/lang/Object;

    :cond_1
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;->eV:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;Lcom/bytedance/sdk/openadsdk/core/model/Fj;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/component/reward/Tc;Lcom/bytedance/sdk/openadsdk/AdSlot;ZLcom/bytedance/sdk/openadsdk/utils/lv;Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$Fj;Z)V
    .locals 0

    invoke-direct/range {p0 .. p8}, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Fj;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/component/reward/Tc;Lcom/bytedance/sdk/openadsdk/AdSlot;ZLcom/bytedance/sdk/openadsdk/utils/lv;Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$Fj;Z)V

    return-void
.end method

.method private Fj(Lcom/bytedance/sdk/openadsdk/core/model/Fj;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/component/reward/Tc;Lcom/bytedance/sdk/openadsdk/AdSlot;ZLcom/bytedance/sdk/openadsdk/utils/lv;Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$Fj;Z)V
    .locals 12

    move-object v8, p0

    move-object v9, p2

    move-object/from16 v6, p4

    move-object/from16 v4, p7

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/video/ex/Fj;->Fj()Lcom/bytedance/sdk/openadsdk/core/video/ex/Fj;

    move-result-object v0

    new-instance v1, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$4;

    invoke-direct {v1, p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$4;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;)V

    invoke-virtual {v0, p2, v1}, Lcom/bytedance/sdk/openadsdk/core/video/ex/Fj;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/core/video/ex/Fj$Fj;)V

    const/4 v0, 0x1

    if-eqz p5, :cond_0

    invoke-static {p2}, Lcom/bytedance/sdk/openadsdk/core/model/mE;->rAx(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v1

    if-nez v1, :cond_0

    invoke-static {p2}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->Ubf(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/JU;->eV()Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;

    move-result-object v1

    invoke-virtual/range {p4 .. p4}, Lcom/bytedance/sdk/openadsdk/AdSlot;->getCodeId()Ljava/lang/String;

    move-result-object v2

    invoke-interface {v1, v2}, Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;->Moo(Ljava/lang/String;)Lcom/bytedance/sdk/openadsdk/core/settings/Fj;

    move-result-object v1

    iget v1, v1, Lcom/bytedance/sdk/openadsdk/core/settings/Fj;->eV:I

    if-ne v1, v0, :cond_0

    iget-object v1, v8, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;->ex:Landroid/content/Context;

    invoke-static {v1}, Lcom/bytedance/sdk/component/utils/JU;->eV(Landroid/content/Context;)Z

    move-result v1

    if-nez v1, :cond_0

    new-instance v0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$ex;

    invoke-direct {v0, p2, v6}, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$ex;-><init>(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/AdSlot;)V

    invoke-direct {p0, v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$ex;)V

    return-void

    :cond_0
    if-eqz v4, :cond_1

    if-nez p8, :cond_2

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/JU;->eV()Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;

    move-result-object v1

    invoke-interface {v1}, Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;->mE()I

    move-result v1

    if-ne v1, v0, :cond_1

    goto :goto_0

    :cond_1
    const/4 v0, 0x0

    :cond_2
    :goto_0
    invoke-static {p2}, Lcom/bytedance/sdk/openadsdk/core/model/mE;->rAx(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v1

    if-nez v1, :cond_5

    invoke-static {p2}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->Ubf(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v1

    if-eqz v1, :cond_4

    sget v1, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v2, 0x17

    if-lt v1, v2, :cond_3

    invoke-virtual {p2}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->spi()Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

    move-result-object v1

    if-eqz v1, :cond_6

    invoke-virtual {p2}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->aGk()I

    move-result v0

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/CacheDirFactory;->getICacheDir(I)Lcom/bykv/vk/openvk/component/video/api/Fj/ex;

    move-result-object v0

    invoke-interface {v0}, Lcom/bykv/vk/openvk/component/video/api/Fj/ex;->Fj()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0, p2}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->Fj(Ljava/lang/String;Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Lcom/bytedance/sdk/openadsdk/core/video/Fj/ex;

    move-result-object v10

    const-string v0, "material_meta"

    invoke-virtual {v10, v0, p2}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->Fj(Ljava/lang/String;Ljava/lang/Object;)V

    const-string v0, "ad_slot"

    invoke-virtual {v10, v0, v6}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->Fj(Ljava/lang/String;Ljava/lang/Object;)V

    new-instance v9, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$5;

    move-object v0, v9

    move-object v1, p0

    move/from16 v2, p5

    move-object/from16 v3, p7

    move/from16 v4, p8

    move-object v5, p3

    move-object/from16 v6, p4

    move-object v7, p1

    invoke-direct/range {v0 .. v7}, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$5;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;ZLcom/bytedance/sdk/openadsdk/component/reward/Ubf$Fj;ZLcom/bytedance/sdk/openadsdk/component/reward/Tc;Lcom/bytedance/sdk/openadsdk/AdSlot;Lcom/bytedance/sdk/openadsdk/core/model/Fj;)V

    invoke-static {v10, v9}, Lcom/bytedance/sdk/openadsdk/core/video/eV/Fj;->Fj(Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;Lcom/bykv/vk/openvk/component/video/api/Ubf/Fj$Fj;)V

    goto :goto_1

    :cond_3
    iget-object v0, v8, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;->ex:Landroid/content/Context;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/eV;->Fj(Landroid/content/Context;)Lcom/bytedance/sdk/openadsdk/component/reward/eV;

    move-result-object v10

    new-instance v11, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$6;

    move-object v0, v11

    move-object v1, p0

    move/from16 v2, p5

    move-object v3, p3

    move-object/from16 v4, p7

    move/from16 v5, p8

    move-object/from16 v6, p4

    move-object v7, p1

    invoke-direct/range {v0 .. v7}, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$6;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;ZLcom/bytedance/sdk/openadsdk/component/reward/Tc;Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$Fj;ZLcom/bytedance/sdk/openadsdk/AdSlot;Lcom/bytedance/sdk/openadsdk/core/model/Fj;)V

    invoke-virtual {v10, p2, v11}, Lcom/bytedance/sdk/openadsdk/component/reward/eV;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/component/reward/eV$Fj;)V

    goto :goto_1

    :cond_4
    if-eqz p5, :cond_6

    :cond_5
    iget-object v1, v8, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;->ex:Landroid/content/Context;

    invoke-static {v1}, Lcom/bytedance/sdk/openadsdk/component/reward/eV;->Fj(Landroid/content/Context;)Lcom/bytedance/sdk/openadsdk/component/reward/eV;

    move-result-object v1

    move-object v2, p1

    invoke-virtual {v1, v6, p1}, Lcom/bytedance/sdk/openadsdk/component/reward/eV;->Fj(Lcom/bytedance/sdk/openadsdk/AdSlot;Lcom/bytedance/sdk/openadsdk/core/model/Fj;)V

    :cond_6
    if-eqz v0, :cond_7

    invoke-virtual {p3}, Lcom/bytedance/sdk/openadsdk/component/reward/Tc;->Fj()Lcom/bytedance/sdk/openadsdk/component/reward/WR;

    move-result-object v0

    invoke-interface {v4, v0}, Lcom/bytedance/sdk/openadsdk/api/PAGLoadListener;->onAdLoaded(Ljava/lang/Object;)V

    :cond_7
    :goto_1
    return-void
.end method

.method private Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/AdSlot;Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$Fj;Lcom/bytedance/sdk/openadsdk/utils/lv;Lcom/bytedance/sdk/openadsdk/component/reward/Tc;Z)V
    .locals 2

    if-eqz p3, :cond_0

    const/4 p4, 0x1

    if-nez p6, :cond_1

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/JU;->eV()Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;

    move-result-object v0

    invoke-interface {v0}, Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;->mE()I

    move-result v0

    if-ne v0, p4, :cond_0

    goto :goto_0

    :cond_0
    const/4 p4, 0x0

    :cond_1
    :goto_0
    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/core/model/mE;->rAx(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v0

    if-nez v0, :cond_2

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->Ubf(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v0

    if-eqz v0, :cond_2

    sget v0, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v1, 0x17

    if-lt v0, v1, :cond_2

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->spi()Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->aGk()I

    move-result p4

    invoke-static {p4}, Lcom/bytedance/sdk/openadsdk/CacheDirFactory;->getICacheDir(I)Lcom/bykv/vk/openvk/component/video/api/Fj/ex;

    move-result-object p4

    invoke-interface {p4}, Lcom/bykv/vk/openvk/component/video/api/Fj/ex;->Fj()Ljava/lang/String;

    move-result-object p4

    invoke-static {p4, p1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->Fj(Ljava/lang/String;Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Lcom/bytedance/sdk/openadsdk/core/video/Fj/ex;

    move-result-object p4

    const-string v0, "material_meta"

    invoke-virtual {p4, v0, p1}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->Fj(Ljava/lang/String;Ljava/lang/Object;)V

    const-string p1, "ad_slot"

    invoke-virtual {p4, p1, p2}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->Fj(Ljava/lang/String;Ljava/lang/Object;)V

    new-instance p1, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$2;

    invoke-direct {p1, p0, p3, p6, p5}, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$2;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$Fj;ZLcom/bytedance/sdk/openadsdk/component/reward/Tc;)V

    invoke-static {p4, p1}, Lcom/bytedance/sdk/openadsdk/core/video/eV/Fj;->Fj(Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;Lcom/bykv/vk/openvk/component/video/api/Ubf/Fj$Fj;)V

    goto :goto_1

    :cond_2
    if-eqz p4, :cond_3

    invoke-virtual {p5}, Lcom/bytedance/sdk/openadsdk/component/reward/Tc;->Fj()Lcom/bytedance/sdk/openadsdk/component/reward/WR;

    move-result-object p1

    invoke-interface {p3, p1}, Lcom/bytedance/sdk/openadsdk/api/PAGLoadListener;->onAdLoaded(Ljava/lang/Object;)V

    :cond_3
    :goto_1
    return-void
.end method

.method public static synthetic ex(Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;)Lcom/bytedance/sdk/component/svN/BcC;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;->Ubf:Lcom/bytedance/sdk/component/svN/BcC;

    return-object p0
.end method

.method private ex()V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;->hjc:Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-virtual {v0}, Ljava/util/concurrent/atomic/AtomicBoolean;->get()Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;->hjc:Ljava/util/concurrent/atomic/AtomicBoolean;

    const/4 v1, 0x1

    invoke-virtual {v0, v1}, Ljava/util/concurrent/atomic/AtomicBoolean;->set(Z)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;->WR:Lcom/bytedance/sdk/component/utils/cB$Fj;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;->ex:Landroid/content/Context;

    invoke-static {v0, v1}, Lcom/bytedance/sdk/component/utils/cB;->Fj(Lcom/bytedance/sdk/component/utils/cB$Fj;Landroid/content/Context;)V

    return-void
.end method

.method private ex(Lcom/bytedance/sdk/openadsdk/AdSlot;Lcom/bytedance/sdk/openadsdk/common/hjc;)V
    .locals 12

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/utils/lv;->Fj()Lcom/bytedance/sdk/openadsdk/utils/lv;

    move-result-object v7

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/AdSlot;->getBidAdm()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    const/4 v8, 0x0

    if-eqz v0, :cond_4

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;->ex:Landroid/content/Context;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/eV;->Fj(Landroid/content/Context;)Lcom/bytedance/sdk/openadsdk/component/reward/eV;

    move-result-object v0

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/AdSlot;->getCodeId()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/component/reward/eV;->ex(Ljava/lang/String;)Lcom/bytedance/sdk/openadsdk/core/model/Fj;

    move-result-object v9

    if-eqz v9, :cond_4

    invoke-virtual {v9}, Lcom/bytedance/sdk/openadsdk/core/model/Fj;->eV()Z

    move-result v0

    if-eqz v0, :cond_4

    new-instance v10, Lcom/bytedance/sdk/openadsdk/component/reward/Tc;

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;->ex:Landroid/content/Context;

    invoke-direct {v10, v0, v9}, Lcom/bytedance/sdk/openadsdk/component/reward/Tc;-><init>(Landroid/content/Context;Lcom/bytedance/sdk/openadsdk/core/model/Fj;)V

    invoke-virtual {v9}, Lcom/bytedance/sdk/openadsdk/core/model/Fj;->WR()Z

    move-result v0

    if-nez v0, :cond_0

    invoke-virtual {v9}, Lcom/bytedance/sdk/openadsdk/core/model/Fj;->Ubf()Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    move-result-object v0

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/model/mE;->rAx(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v0

    if-nez v0, :cond_0

    invoke-virtual {v10}, Lcom/bytedance/sdk/openadsdk/component/reward/Tc;->ex()V

    :cond_0
    if-eqz p2, :cond_2

    invoke-virtual {v9}, Lcom/bytedance/sdk/openadsdk/core/model/Fj;->WR()Z

    move-result v0

    if-nez v0, :cond_1

    instance-of v0, p2, Lcom/bytedance/sdk/openadsdk/api/interstitial/PAGInterstitialAdLoadListener;

    if-eqz v0, :cond_1

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/JU;->eV()Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;

    move-result-object v0

    invoke-interface {v0}, Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;->mE()I

    move-result v0

    if-nez v0, :cond_1

    move-object v0, p2

    check-cast v0, Lcom/bytedance/sdk/openadsdk/api/interstitial/PAGInterstitialAdLoadListener;

    invoke-virtual {v10}, Lcom/bytedance/sdk/openadsdk/component/reward/Tc;->Fj()Lcom/bytedance/sdk/openadsdk/component/reward/WR;

    move-result-object v1

    invoke-interface {v0, v1}, Lcom/bytedance/sdk/openadsdk/api/PAGLoadListener;->onAdLoaded(Ljava/lang/Object;)V

    :cond_1
    new-instance v11, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$Fj;

    const/4 v0, 0x0

    invoke-direct {v11, p2, v9, v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$Fj;-><init>(Lcom/bytedance/sdk/openadsdk/common/hjc;Lcom/bytedance/sdk/openadsdk/core/model/Fj;Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$1;)V

    const/4 p2, 0x0

    :goto_0
    invoke-virtual {v9}, Lcom/bytedance/sdk/openadsdk/core/model/Fj;->hjc()Ljava/util/List;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    if-ge p2, v0, :cond_2

    invoke-virtual {v9}, Lcom/bytedance/sdk/openadsdk/core/model/Fj;->hjc()Ljava/util/List;

    move-result-object v0

    invoke-interface {v0, p2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    move-object v1, v0

    check-cast v1, Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v9}, Lcom/bytedance/sdk/openadsdk/core/model/Fj;->WR()Z

    move-result v6

    move-object v0, p0

    move-object v2, p1

    move-object v3, v11

    move-object v4, v7

    move-object v5, v10

    invoke-direct/range {v0 .. v6}, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/AdSlot;Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$Fj;Lcom/bytedance/sdk/openadsdk/utils/lv;Lcom/bytedance/sdk/openadsdk/component/reward/Tc;Z)V

    add-int/lit8 p2, p2, 0x1

    goto :goto_0

    :cond_2
    :goto_1
    invoke-virtual {v9}, Lcom/bytedance/sdk/openadsdk/core/model/Fj;->hjc()Ljava/util/List;

    move-result-object p1

    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result p1

    if-ge v8, p1, :cond_3

    invoke-virtual {v9}, Lcom/bytedance/sdk/openadsdk/core/model/Fj;->hjc()Ljava/util/List;

    move-result-object p1

    invoke-interface {p1, v8}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/video/ex/Fj;->Fj()Lcom/bytedance/sdk/openadsdk/core/video/ex/Fj;

    move-result-object p2

    new-instance v0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$1;

    invoke-direct {v0, p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$1;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;)V

    invoke-virtual {p2, p1, v0}, Lcom/bytedance/sdk/openadsdk/core/video/ex/Fj;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/core/video/ex/Fj$Fj;)V

    add-int/lit8 v8, v8, 0x1

    goto :goto_1

    :cond_3
    return-void

    :cond_4
    invoke-direct {p0, p1, v8, v7, p2}, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;->Fj(Lcom/bytedance/sdk/openadsdk/AdSlot;ZLcom/bytedance/sdk/openadsdk/utils/lv;Lcom/bytedance/sdk/openadsdk/common/hjc;)V

    return-void
.end method

.method public static synthetic hjc(Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;)Ljava/util/List;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;->eV:Ljava/util/List;

    return-object p0
.end method

.method private hjc()V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;->hjc:Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-virtual {v0}, Ljava/util/concurrent/atomic/AtomicBoolean;->get()Z

    move-result v0

    if-nez v0, :cond_0

    return-void

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;->hjc:Ljava/util/concurrent/atomic/AtomicBoolean;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/util/concurrent/atomic/AtomicBoolean;->set(Z)V

    :try_start_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;->WR:Lcom/bytedance/sdk/component/utils/cB$Fj;

    invoke-static {v0}, Lcom/bytedance/sdk/component/utils/cB;->Fj(Lcom/bytedance/sdk/component/utils/cB$Fj;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    :catch_0
    return-void
.end method


# virtual methods
.method public Fj()V
    .locals 1

    :try_start_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;->ex:Landroid/content/Context;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/eV;->Fj(Landroid/content/Context;)Lcom/bytedance/sdk/openadsdk/component/reward/eV;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/eV;->Fj()V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :catchall_0
    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/openadsdk/AdSlot;)V
    .locals 2

    if-eqz p1, :cond_2

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/AdSlot;->getCodeId()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;->ex:Landroid/content/Context;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/eV;->Fj(Landroid/content/Context;)Lcom/bytedance/sdk/openadsdk/component/reward/eV;

    move-result-object v0

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/AdSlot;->getCodeId()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/component/reward/eV;->ex(Ljava/lang/String;)Lcom/bytedance/sdk/openadsdk/core/model/Fj;

    move-result-object v0

    if-eqz v0, :cond_1

    return-void

    :cond_1
    invoke-virtual {p0, p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;->ex(Lcom/bytedance/sdk/openadsdk/AdSlot;)V

    :cond_2
    :goto_0
    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/openadsdk/AdSlot;Lcom/bytedance/sdk/openadsdk/common/hjc;)V
    .locals 0

    invoke-direct {p0, p1, p2}, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;->ex(Lcom/bytedance/sdk/openadsdk/AdSlot;Lcom/bytedance/sdk/openadsdk/common/hjc;)V

    return-void
.end method

.method public Fj(Ljava/lang/String;)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;->ex:Landroid/content/Context;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/eV;->Fj(Landroid/content/Context;)Lcom/bytedance/sdk/openadsdk/component/reward/eV;

    move-result-object v0

    invoke-virtual {v0, p1}, Lcom/bytedance/sdk/openadsdk/component/reward/eV;->Fj(Ljava/lang/String;)V

    return-void
.end method

.method public ex(Lcom/bytedance/sdk/openadsdk/AdSlot;)V
    .locals 3

    if-eqz p1, :cond_0

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/AdSlot;->getBidAdm()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-nez v0, :cond_0

    return-void

    :cond_0
    invoke-static {}, Lcom/bytedance/sdk/openadsdk/utils/lv;->Fj()Lcom/bytedance/sdk/openadsdk/utils/lv;

    move-result-object v0

    const/4 v1, 0x1

    const/4 v2, 0x0

    invoke-direct {p0, p1, v1, v0, v2}, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;->Fj(Lcom/bytedance/sdk/openadsdk/AdSlot;ZLcom/bytedance/sdk/openadsdk/utils/lv;Lcom/bytedance/sdk/openadsdk/common/hjc;)V

    return-void
.end method

.method public finalize()V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Throwable;
        }
    .end annotation

    invoke-super {p0}, Ljava/lang/Object;->finalize()V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;->Ubf:Lcom/bytedance/sdk/component/svN/BcC;

    if-eqz v0, :cond_0

    :try_start_0
    invoke-static {}, Lcom/bytedance/sdk/component/utils/BcC;->Fj()Landroid/os/Handler;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;->Ubf:Lcom/bytedance/sdk/component/svN/BcC;

    invoke-virtual {v0, v1}, Landroid/os/Handler;->removeCallbacks(Ljava/lang/Runnable;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    :catch_0
    const/4 v0, 0x0

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;->Ubf:Lcom/bytedance/sdk/component/svN/BcC;

    :cond_0
    invoke-direct {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;->hjc()V

    return-void
.end method
