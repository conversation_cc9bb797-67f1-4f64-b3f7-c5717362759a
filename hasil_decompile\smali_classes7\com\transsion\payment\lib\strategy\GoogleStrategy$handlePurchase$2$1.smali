.class final Lcom/transsion/payment/lib/strategy/GoogleStrategy$handlePurchase$2$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/transsion/payment/lib/strategy/GoogleStrategy;->z(Lcom/android/billingclient/api/Purchase;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function1<",
        "Lkotlin/coroutines/Continuation<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime L<PERSON>lin/Metadata;
.end annotation

.annotation runtime Lkotlin/coroutines/jvm/internal/DebugMetadata;
    c = "com.transsion.payment.lib.strategy.GoogleStrategy$handlePurchase$2$1"
    f = "GoogleStrategy.kt"
    l = {
        0x11d
    }
    m = "invokeSuspend"
.end annotation


# instance fields
.field final synthetic $gpOrderId:Ljava/lang/String;

.field final synthetic $orderId:Ljava/lang/String;

.field final synthetic $productId:Ljava/lang/String;

.field final synthetic $purchaseToken:Ljava/lang/String;

.field label:I

.field final synthetic this$0:Lcom/transsion/payment/lib/strategy/GoogleStrategy;


# direct methods
.method public constructor <init>(Lcom/transsion/payment/lib/strategy/GoogleStrategy;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/transsion/payment/lib/strategy/GoogleStrategy;",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/Continuation<",
            "-",
            "Lcom/transsion/payment/lib/strategy/GoogleStrategy$handlePurchase$2$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lcom/transsion/payment/lib/strategy/GoogleStrategy$handlePurchase$2$1;->this$0:Lcom/transsion/payment/lib/strategy/GoogleStrategy;

    iput-object p2, p0, Lcom/transsion/payment/lib/strategy/GoogleStrategy$handlePurchase$2$1;->$productId:Ljava/lang/String;

    iput-object p3, p0, Lcom/transsion/payment/lib/strategy/GoogleStrategy$handlePurchase$2$1;->$purchaseToken:Ljava/lang/String;

    iput-object p4, p0, Lcom/transsion/payment/lib/strategy/GoogleStrategy$handlePurchase$2$1;->$orderId:Ljava/lang/String;

    iput-object p5, p0, Lcom/transsion/payment/lib/strategy/GoogleStrategy$handlePurchase$2$1;->$gpOrderId:Ljava/lang/String;

    const/4 p1, 0x1

    invoke-direct {p0, p1, p6}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/Continuation;)V

    return-void
.end method


# virtual methods
.method public final create(Lkotlin/coroutines/Continuation;)Lkotlin/coroutines/Continuation;
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/coroutines/Continuation<",
            "*>;)",
            "Lkotlin/coroutines/Continuation<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v7, Lcom/transsion/payment/lib/strategy/GoogleStrategy$handlePurchase$2$1;

    iget-object v1, p0, Lcom/transsion/payment/lib/strategy/GoogleStrategy$handlePurchase$2$1;->this$0:Lcom/transsion/payment/lib/strategy/GoogleStrategy;

    iget-object v2, p0, Lcom/transsion/payment/lib/strategy/GoogleStrategy$handlePurchase$2$1;->$productId:Ljava/lang/String;

    iget-object v3, p0, Lcom/transsion/payment/lib/strategy/GoogleStrategy$handlePurchase$2$1;->$purchaseToken:Ljava/lang/String;

    iget-object v4, p0, Lcom/transsion/payment/lib/strategy/GoogleStrategy$handlePurchase$2$1;->$orderId:Ljava/lang/String;

    iget-object v5, p0, Lcom/transsion/payment/lib/strategy/GoogleStrategy$handlePurchase$2$1;->$gpOrderId:Ljava/lang/String;

    move-object v0, v7

    move-object v6, p1

    invoke-direct/range {v0 .. v6}, Lcom/transsion/payment/lib/strategy/GoogleStrategy$handlePurchase$2$1;-><init>(Lcom/transsion/payment/lib/strategy/GoogleStrategy;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)V

    return-object v7
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    check-cast p1, Lkotlin/coroutines/Continuation;

    invoke-virtual {p0, p1}, Lcom/transsion/payment/lib/strategy/GoogleStrategy$handlePurchase$2$1;->invoke(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/coroutines/Continuation<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    invoke-virtual {p0, p1}, Lcom/transsion/payment/lib/strategy/GoogleStrategy$handlePurchase$2$1;->create(Lkotlin/coroutines/Continuation;)Lkotlin/coroutines/Continuation;

    move-result-object p1

    check-cast p1, Lcom/transsion/payment/lib/strategy/GoogleStrategy$handlePurchase$2$1;

    sget-object v0, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, v0}, Lcom/transsion/payment/lib/strategy/GoogleStrategy$handlePurchase$2$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 9

    invoke-static {}, Lkotlin/coroutines/intrinsics/IntrinsicsKt;->e()Ljava/lang/Object;

    move-result-object v0

    iget v1, p0, Lcom/transsion/payment/lib/strategy/GoogleStrategy$handlePurchase$2$1;->label:I

    const/4 v2, 0x1

    if-eqz v1, :cond_1

    if-ne v1, v2, :cond_0

    invoke-static {p1}, Lkotlin/ResultKt;->b(Ljava/lang/Object;)V

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1

    :cond_1
    invoke-static {p1}, Lkotlin/ResultKt;->b(Ljava/lang/Object;)V

    iget-object p1, p0, Lcom/transsion/payment/lib/strategy/GoogleStrategy$handlePurchase$2$1;->this$0:Lcom/transsion/payment/lib/strategy/GoogleStrategy;

    invoke-static {p1}, Lcom/transsion/payment/lib/strategy/GoogleStrategy;->k(Lcom/transsion/payment/lib/strategy/GoogleStrategy;)Lcom/transsion/payment/lib/PaymentService;

    move-result-object v3

    iget-object v4, p0, Lcom/transsion/payment/lib/strategy/GoogleStrategy$handlePurchase$2$1;->$productId:Ljava/lang/String;

    iget-object v5, p0, Lcom/transsion/payment/lib/strategy/GoogleStrategy$handlePurchase$2$1;->$purchaseToken:Ljava/lang/String;

    iget-object v6, p0, Lcom/transsion/payment/lib/strategy/GoogleStrategy$handlePurchase$2$1;->$orderId:Ljava/lang/String;

    iget-object v7, p0, Lcom/transsion/payment/lib/strategy/GoogleStrategy$handlePurchase$2$1;->$gpOrderId:Ljava/lang/String;

    iput v2, p0, Lcom/transsion/payment/lib/strategy/GoogleStrategy$handlePurchase$2$1;->label:I

    move-object v8, p0

    invoke-virtual/range {v3 .. v8}, Lcom/transsion/payment/lib/PaymentService;->e(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;

    move-result-object p1

    if-ne p1, v0, :cond_2

    return-object v0

    :cond_2
    :goto_0
    check-cast p1, Lcom/tn/lib/net/bean/BaseDto;

    const/4 v0, 0x0

    if-eqz p1, :cond_3

    invoke-virtual {p1}, Lcom/tn/lib/net/bean/BaseDto;->getCode()Ljava/lang/String;

    move-result-object v1

    goto :goto_1

    :cond_3
    move-object v1, v0

    :goto_1
    const-string v2, "0"

    invoke-static {v1, v2}, Lkotlin/jvm/internal/Intrinsics;->b(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_9

    iget-object v1, p0, Lcom/transsion/payment/lib/strategy/GoogleStrategy$handlePurchase$2$1;->this$0:Lcom/transsion/payment/lib/strategy/GoogleStrategy;

    invoke-static {v1}, Lcom/transsion/payment/lib/strategy/GoogleStrategy;->j(Lcom/transsion/payment/lib/strategy/GoogleStrategy;)Lcom/transsion/payment/lib/b;

    move-result-object v1

    const/4 v2, 0x0

    if-eqz v1, :cond_4

    invoke-interface {v1, v2}, Lcom/transsion/payment/lib/b;->b(Z)V

    :cond_4
    sget-object v1, Lcom/transsion/payment/lib/d;->a:Lcom/transsion/payment/lib/d;

    const-string v3, "Will callback success"

    invoke-virtual {v1, v3}, Lcom/transsion/payment/lib/d;->a(Ljava/lang/String;)V

    iget-object v3, p0, Lcom/transsion/payment/lib/strategy/GoogleStrategy$handlePurchase$2$1;->this$0:Lcom/transsion/payment/lib/strategy/GoogleStrategy;

    invoke-static {v3}, Lcom/transsion/payment/lib/strategy/GoogleStrategy;->j(Lcom/transsion/payment/lib/strategy/GoogleStrategy;)Lcom/transsion/payment/lib/b;

    move-result-object v3

    if-eqz v3, :cond_8

    invoke-virtual {p1}, Lcom/tn/lib/net/bean/BaseDto;->getData()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lcom/transsion/payment/lib/bean/CheckPaymentBean;

    if-eqz v4, :cond_5

    invoke-virtual {v4}, Lcom/transsion/payment/lib/bean/CheckPaymentBean;->getAddCoin()Ljava/lang/String;

    move-result-object v4

    if-eqz v4, :cond_5

    invoke-static {v4}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result v2

    :cond_5
    invoke-virtual {p1}, Lcom/tn/lib/net/bean/BaseDto;->getData()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/transsion/payment/lib/bean/CheckPaymentBean;

    if-eqz p1, :cond_6

    invoke-virtual {p1}, Lcom/transsion/payment/lib/bean/CheckPaymentBean;->getBalanceCoin()Ljava/lang/String;

    move-result-object p1

    if-nez p1, :cond_7

    :cond_6
    const-string p1, ""

    :cond_7
    iget-object v4, p0, Lcom/transsion/payment/lib/strategy/GoogleStrategy$handlePurchase$2$1;->$orderId:Ljava/lang/String;

    invoke-interface {v3, v2, p1, v4}, Lcom/transsion/payment/lib/b;->c(ILjava/lang/String;Ljava/lang/String;)V

    :cond_8
    iget-object p1, p0, Lcom/transsion/payment/lib/strategy/GoogleStrategy$handlePurchase$2$1;->this$0:Lcom/transsion/payment/lib/strategy/GoogleStrategy;

    invoke-static {p1}, Lcom/transsion/payment/lib/strategy/GoogleStrategy;->j(Lcom/transsion/payment/lib/strategy/GoogleStrategy;)Lcom/transsion/payment/lib/b;

    move-result-object p1

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "Has callback "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v1, p1}, Lcom/transsion/payment/lib/d;->a(Ljava/lang/String;)V

    iget-object p1, p0, Lcom/transsion/payment/lib/strategy/GoogleStrategy$handlePurchase$2$1;->this$0:Lcom/transsion/payment/lib/strategy/GoogleStrategy;

    invoke-static {p1, v0}, Lcom/transsion/payment/lib/strategy/GoogleStrategy;->r(Lcom/transsion/payment/lib/strategy/GoogleStrategy;Lcom/transsion/payment/lib/b;)V

    const-string p1, "handlePurchase \u53d1\u8d27\u6210\u529f"

    invoke-virtual {v1, p1}, Lcom/transsion/payment/lib/d;->a(Ljava/lang/String;)V

    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    return-object p1

    :cond_9
    new-instance v1, Ljava/lang/Exception;

    if-eqz p1, :cond_a

    invoke-virtual {p1}, Lcom/tn/lib/net/bean/BaseDto;->getMsg()Ljava/lang/String;

    move-result-object v0

    :cond_a
    invoke-direct {v1, v0}, Ljava/lang/Exception;-><init>(Ljava/lang/String;)V

    throw v1
.end method
