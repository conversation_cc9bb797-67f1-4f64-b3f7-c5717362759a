.class Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG$2;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->eV(Z)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Z

.field final synthetic ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;Z)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG$2;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;

    iput-boolean p2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG$2;->Fj:Z

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG$2;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;

    iget-boolean v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->hjc:Z

    if-nez v0, :cond_1

    iget-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG$2;->Fj:Z

    if-eqz v0, :cond_0

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/JU;->Fj()Landroid/content/Context;

    move-result-object v0

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/UYd;->Fj(Landroid/content/Context;)Lcom/bytedance/sdk/openadsdk/component/reward/UYd;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG$2;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;

    invoke-static {v1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->eV(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;)Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    move-result-object v1

    invoke-virtual {v1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->lv()Lcom/bytedance/sdk/openadsdk/AdSlot;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/component/reward/UYd;->Fj(Lcom/bytedance/sdk/openadsdk/AdSlot;)V

    return-void

    :cond_0
    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/JU;->Fj()Landroid/content/Context;

    move-result-object v0

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;->Fj(Landroid/content/Context;)Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG$2;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;

    invoke-static {v1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->eV(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;)Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    move-result-object v1

    invoke-virtual {v1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->lv()Lcom/bytedance/sdk/openadsdk/AdSlot;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;->Fj(Lcom/bytedance/sdk/openadsdk/AdSlot;)V

    :cond_1
    return-void
.end method
