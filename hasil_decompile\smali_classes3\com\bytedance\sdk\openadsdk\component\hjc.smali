.class public Lcom/bytedance/sdk/openadsdk/component/hjc;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bytedance/sdk/openadsdk/component/hjc$Fj;
    }
.end annotation


# static fields
.field private static cB:Ljava/lang/String; = "Skip"


# instance fields
.field private Af:Landroid/widget/TextView;

.field protected BcC:Landroid/widget/FrameLayout;

.field protected final Fj:Landroid/app/Activity;

.field private JU:Lcom/bytedance/sdk/openadsdk/core/customview/PAGTextView;

.field private JW:Lcom/bytedance/sdk/openadsdk/core/widget/TTRoundRectImageView;

.field private Ko:Landroid/widget/RelativeLayout;

.field private Moo:Lcom/bytedance/sdk/openadsdk/component/BcC/hjc;

.field private Ql:Lcom/bytedance/sdk/openadsdk/core/customview/PAGTextView;

.field private Tc:Landroid/widget/ImageView;

.field private UYd:Lcom/bytedance/sdk/openadsdk/core/widget/PAGLogoView;

.field protected final Ubf:Lcom/bytedance/sdk/openadsdk/component/Fj;

.field private Vq:Z

.field protected final WR:I

.field private dG:Lcom/bytedance/sdk/openadsdk/component/view/ButtonFlash;

.field protected final eV:Landroid/widget/FrameLayout;

.field protected final ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

.field protected final hjc:Z

.field private mC:Landroid/widget/TextView;

.field private final mE:Lcom/bytedance/sdk/openadsdk/component/view/Fj;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field protected final mSE:Lcom/bytedance/sdk/openadsdk/component/BcC/Fj;

.field private nsB:Z

.field private rAx:Landroid/widget/ImageView;

.field private rS:F

.field protected svN:I

.field private vYf:F


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Landroid/app/Activity;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Landroid/widget/FrameLayout;Lcom/bytedance/sdk/openadsdk/component/Fj;IZLcom/bytedance/sdk/openadsdk/component/BcC/Fj;)V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Lcom/bytedance/sdk/openadsdk/component/view/Fj;

    invoke-direct {v0}, Lcom/bytedance/sdk/openadsdk/component/view/Fj;-><init>()V

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->mE:Lcom/bytedance/sdk/openadsdk/component/view/Fj;

    const/4 v0, 0x0

    iput-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->nsB:Z

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->Fj:Landroid/app/Activity;

    iput-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    iput-object p3, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->eV:Landroid/widget/FrameLayout;

    iput p5, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->svN:I

    iput-boolean p6, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->hjc:Z

    iput-object p4, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->Ubf:Lcom/bytedance/sdk/openadsdk/component/Fj;

    invoke-virtual {p2}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->aYy()I

    move-result p1

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->WR:I

    iput-object p7, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->mSE:Lcom/bytedance/sdk/openadsdk/component/BcC/Fj;

    return-void
.end method

.method private BcC()V
    .locals 8

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->PpV()Ljava/util/List;

    move-result-object v0

    const/4 v1, 0x0

    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/bytedance/sdk/openadsdk/core/model/Tc;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Tc;->svN()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Tc;->Fj()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Lcom/bytedance/sdk/component/utils/Ubf;->Fj(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    goto :goto_0

    :cond_0
    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Tc;->svN()Ljava/lang/String;

    move-result-object v1

    :goto_0
    invoke-static {v1}, Lcom/bytedance/sdk/openadsdk/component/svN/Fj;->ex(Ljava/lang/String;)Ljava/io/File;

    move-result-object v1

    new-instance v2, Lcom/bytedance/sdk/openadsdk/rAx/Fj;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Tc;->Fj()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Tc;->svN()Ljava/lang/String;

    move-result-object v4

    invoke-direct {v2, v3, v4}, Lcom/bytedance/sdk/openadsdk/rAx/Fj;-><init>(Ljava/lang/String;Ljava/lang/String;)V

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Tc;->ex()I

    move-result v3

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Tc;->hjc()I

    move-result v4

    new-instance v5, Lcom/bytedance/sdk/openadsdk/component/hjc$6;

    invoke-direct {v5, p0}, Lcom/bytedance/sdk/openadsdk/component/hjc$6;-><init>(Lcom/bytedance/sdk/openadsdk/component/hjc;)V

    invoke-virtual {v1}, Ljava/io/File;->getParent()Ljava/lang/String;

    move-result-object v6

    const/16 v7, 0x19

    invoke-static/range {v2 .. v7}, Lcom/bytedance/sdk/openadsdk/utils/rAx;->Fj(Lcom/bytedance/sdk/openadsdk/rAx/Fj;IILcom/bytedance/sdk/openadsdk/utils/rAx$Fj;Ljava/lang/String;I)V

    return-void
.end method

.method private Fj(Landroid/graphics/Bitmap;)V
    .locals 2

    if-eqz p1, :cond_0

    :try_start_0
    new-instance v0, Landroid/graphics/drawable/BitmapDrawable;

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/JU;->Fj()Landroid/content/Context;

    move-result-object v1

    invoke-virtual {v1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    move-result-object v1

    invoke-direct {v0, v1, p1}, Landroid/graphics/drawable/BitmapDrawable;-><init>(Landroid/content/res/Resources;Landroid/graphics/Bitmap;)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->Tc:Landroid/widget/ImageView;

    invoke-virtual {p1, v0}, Landroid/widget/ImageView;->setImageDrawable(Landroid/graphics/drawable/Drawable;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_0

    :catchall_0
    const/4 p1, 0x2

    new-array p1, p1, [Ljava/lang/Object;

    const/4 v0, 0x0

    const-string v1, "open_ad"

    aput-object v1, p1, v0

    const/4 v0, 0x1

    const-string v1, "bindBackGroundImage error"

    aput-object v1, p1, v0

    const-string v0, "AppOpenAdNativeManager"

    invoke-static {v0, p1}, Lcom/bytedance/sdk/component/utils/dG;->Fj(Ljava/lang/String;[Ljava/lang/Object;)V

    :cond_0
    :goto_0
    return-void
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/openadsdk/component/hjc;Landroid/graphics/Bitmap;)V
    .locals 0

    invoke-direct {p0, p1}, Lcom/bytedance/sdk/openadsdk/component/hjc;->Fj(Landroid/graphics/Bitmap;)V

    return-void
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/openadsdk/component/hjc;)Z
    .locals 0

    iget-boolean p0, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->nsB:Z

    return p0
.end method

.method private WR()V
    .locals 3

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->mE:Lcom/bytedance/sdk/openadsdk/component/view/Fj;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/view/Fj;->Fj()V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->dG:Lcom/bytedance/sdk/openadsdk/component/view/ButtonFlash;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->Kk()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->ES()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-direct {p0}, Lcom/bytedance/sdk/openadsdk/component/hjc;->svN()V

    return-void

    :cond_0
    iget-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->hjc:Z

    const/16 v1, 0x8

    const/4 v2, 0x0

    if-eqz v0, :cond_2

    invoke-direct {p0, v2}, Lcom/bytedance/sdk/openadsdk/component/hjc;->ex(I)V

    invoke-virtual {p0, v1}, Lcom/bytedance/sdk/openadsdk/component/hjc;->Fj(I)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->BcC:Landroid/widget/FrameLayout;

    invoke-virtual {p0, v0}, Lcom/bytedance/sdk/openadsdk/component/hjc;->Fj(Landroid/widget/FrameLayout;)Z

    move-result v0

    if-eqz v0, :cond_1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->Ubf:Lcom/bytedance/sdk/openadsdk/component/Fj;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/Fj;->hjc()V

    goto :goto_0

    :cond_1
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->Ubf:Lcom/bytedance/sdk/openadsdk/component/Fj;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/Fj;->eV()V

    :goto_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    new-instance v1, Lcom/bytedance/sdk/openadsdk/component/hjc$Fj;

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->Fj:Landroid/app/Activity;

    invoke-direct {v1, p0, v2}, Lcom/bytedance/sdk/openadsdk/component/hjc$Fj;-><init>(Lcom/bytedance/sdk/openadsdk/component/hjc;Landroid/app/Activity;)V

    const/16 v2, 0x19

    invoke-static {v0, v1, v2}, Lcom/bytedance/sdk/openadsdk/component/WR;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/component/WR$eV;I)V

    return-void

    :cond_2
    invoke-direct {p0, v1}, Lcom/bytedance/sdk/openadsdk/component/hjc;->ex(I)V

    invoke-virtual {p0, v2}, Lcom/bytedance/sdk/openadsdk/component/hjc;->Fj(I)V

    invoke-direct {p0}, Lcom/bytedance/sdk/openadsdk/component/hjc;->BcC()V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->Ubf:Lcom/bytedance/sdk/openadsdk/component/Fj;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/Fj;->hjc()V

    return-void
.end method

.method private ex(I)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->BcC:Landroid/widget/FrameLayout;

    invoke-static {v0, p1}, Lcom/bytedance/sdk/openadsdk/utils/eh;->Fj(Landroid/view/View;I)V

    return-void
.end method

.method private svN()V
    .locals 7

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->JU:Lcom/bytedance/sdk/openadsdk/core/customview/PAGTextView;

    if-eqz v0, :cond_0

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->Obv()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->Ql:Lcom/bytedance/sdk/openadsdk/core/customview/PAGTextView;

    if-eqz v0, :cond_1

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->HY()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    :cond_1
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->JW:Lcom/bytedance/sdk/openadsdk/core/widget/TTRoundRectImageView;

    if-eqz v0, :cond_2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->oX()Lcom/bytedance/sdk/openadsdk/core/model/Tc;

    move-result-object v0

    if-eqz v0, :cond_2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->oX()Lcom/bytedance/sdk/openadsdk/core/model/Tc;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Tc;->Fj()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-nez v0, :cond_2

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/rAx/hjc;->Fj()Lcom/bytedance/sdk/openadsdk/rAx/hjc;

    move-result-object v1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->oX()Lcom/bytedance/sdk/openadsdk/core/model/Tc;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Tc;->Fj()Ljava/lang/String;

    move-result-object v2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->oX()Lcom/bytedance/sdk/openadsdk/core/model/Tc;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Tc;->ex()I

    move-result v3

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->oX()Lcom/bytedance/sdk/openadsdk/core/model/Tc;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Tc;->hjc()I

    move-result v4

    iget-object v5, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->JW:Lcom/bytedance/sdk/openadsdk/core/widget/TTRoundRectImageView;

    iget-object v6, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual/range {v1 .. v6}, Lcom/bytedance/sdk/openadsdk/rAx/hjc;->Fj(Ljava/lang/String;IILandroid/widget/ImageView;Lcom/bytedance/sdk/openadsdk/core/model/Ql;)V

    :cond_2
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->Ubf:Lcom/bytedance/sdk/openadsdk/component/Fj;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/Fj;->hjc()V

    return-void
.end method


# virtual methods
.method public Fj(Lorg/json/JSONObject;)Lorg/json/JSONObject;
    .locals 0

    const/4 p1, 0x0

    return-object p1
.end method

.method public Fj()V
    .locals 3
    .annotation build Landroid/annotation/SuppressLint;
        value = {
            "ClickableViewAccessibility"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->UYd:Lcom/bytedance/sdk/openadsdk/core/widget/PAGLogoView;

    new-instance v1, Lcom/bytedance/sdk/openadsdk/component/hjc$2;

    invoke-direct {v1, p0}, Lcom/bytedance/sdk/openadsdk/component/hjc$2;-><init>(Lcom/bytedance/sdk/openadsdk/component/hjc;)V

    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->Af:Landroid/widget/TextView;

    new-instance v1, Lcom/bytedance/sdk/openadsdk/component/hjc$3;

    invoke-direct {v1, p0}, Lcom/bytedance/sdk/openadsdk/component/hjc$3;-><init>(Lcom/bytedance/sdk/openadsdk/component/hjc;)V

    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->mC:Landroid/widget/TextView;

    new-instance v1, Lcom/bytedance/sdk/openadsdk/component/hjc$4;

    invoke-direct {v1, p0}, Lcom/bytedance/sdk/openadsdk/component/hjc$4;-><init>(Lcom/bytedance/sdk/openadsdk/component/hjc;)V

    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->UF()Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->Fj:Landroid/app/Activity;

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->mSE:Lcom/bytedance/sdk/openadsdk/component/BcC/Fj;

    invoke-static {v0, v1, v2}, Lcom/bytedance/sdk/openadsdk/component/Fj/ex;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Landroid/app/Activity;Lcom/bytedance/sdk/openadsdk/component/BcC/Fj;)Lcom/bytedance/sdk/openadsdk/component/Fj/Fj;

    move-result-object v0

    new-instance v1, Lcom/bytedance/sdk/openadsdk/component/hjc$5;

    invoke-direct {v1, p0}, Lcom/bytedance/sdk/openadsdk/component/hjc$5;-><init>(Lcom/bytedance/sdk/openadsdk/component/hjc;)V

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->Fj(Lcom/bytedance/sdk/openadsdk/core/ex/ex$Fj;)V

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->Moo:Lcom/bytedance/sdk/openadsdk/component/BcC/hjc;

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/core/ex/Fj;->Fj(Lcom/bytedance/sdk/openadsdk/core/ex/Fj$Fj;)V

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->Ko()I

    move-result v1

    const/4 v2, 0x1

    if-ne v1, v2, :cond_1

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->Ko:Landroid/widget/RelativeLayout;

    invoke-virtual {v1, v0}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->Ko:Landroid/widget/RelativeLayout;

    invoke-virtual {v1, v0}, Landroid/view/View;->setOnTouchListener(Landroid/view/View$OnTouchListener;)V

    :cond_1
    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->dG:Lcom/bytedance/sdk/openadsdk/component/view/ButtonFlash;

    invoke-virtual {v1, v0}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->dG:Lcom/bytedance/sdk/openadsdk/component/view/ButtonFlash;

    invoke-virtual {v1, v0}, Landroid/view/View;->setOnTouchListener(Landroid/view/View$OnTouchListener;)V

    return-void
.end method

.method public Fj(FF)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->vYf:F

    iput p2, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->rS:F

    return-void
.end method

.method public Fj(I)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->rAx:Landroid/widget/ImageView;

    invoke-static {v0, p1}, Lcom/bytedance/sdk/openadsdk/utils/eh;->Fj(Landroid/view/View;I)V

    return-void
.end method

.method public Fj(IIZ)V
    .locals 0

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->mC:Landroid/widget/TextView;

    if-eqz p2, :cond_2

    invoke-static {p1}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    move-result-object p1

    if-eqz p3, :cond_1

    iput-boolean p3, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->nsB:Z

    iget-boolean p2, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->Vq:Z

    if-eqz p2, :cond_0

    sget-object p1, Lcom/bytedance/sdk/openadsdk/component/hjc;->cB:Ljava/lang/String;

    goto :goto_0

    :cond_0
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string p1, " | "

    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object p1, Lcom/bytedance/sdk/openadsdk/component/hjc;->cB:Ljava/lang/String;

    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    :cond_1
    :goto_0
    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->mC:Landroid/widget/TextView;

    invoke-virtual {p2, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    :cond_2
    return-void
.end method

.method public Fj(Landroid/view/ViewGroup;)V
    .locals 10

    :try_start_0
    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/JU;->Fj()Landroid/content/Context;

    move-result-object v0

    const-string v1, "tt_txt_skip"

    invoke-static {v0, v1}, Lcom/bytedance/sdk/component/utils/mE;->Fj(Landroid/content/Context;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Lcom/bytedance/sdk/openadsdk/component/hjc;->cB:Ljava/lang/String;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_0

    :catchall_0
    move-exception v0

    const-string v1, "AppOpenAdNativeManager"

    invoke-virtual {v0}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    move-result-object v0

    invoke-static {v1, v0}, Lcom/bytedance/sdk/component/utils/dG;->Fj(Ljava/lang/String;Ljava/lang/String;)V

    :goto_0
    new-instance v0, Lcom/bytedance/sdk/openadsdk/component/view/PAGAppOpenTwoLayout;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->Fj:Landroid/app/Activity;

    invoke-direct {v0, v1}, Lcom/bytedance/sdk/openadsdk/component/view/PAGAppOpenTwoLayout;-><init>(Landroid/content/Context;)V

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->rAx()I

    move-result v1

    const/4 v2, 0x5

    const/4 v3, 0x3

    if-ne v1, v2, :cond_0

    new-instance v0, Lcom/bytedance/sdk/openadsdk/component/view/PAGAppOpenIconOnlyLayout;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->Fj:Landroid/app/Activity;

    invoke-direct {v0, v1}, Lcom/bytedance/sdk/openadsdk/component/view/PAGAppOpenIconOnlyLayout;-><init>(Landroid/content/Context;)V

    goto :goto_1

    :cond_0
    const/4 v2, 0x1

    if-ne v1, v2, :cond_1

    new-instance v0, Lcom/bytedance/sdk/openadsdk/component/view/PAGAppOpenOneLayout;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->Fj:Landroid/app/Activity;

    invoke-direct {v0, v1}, Lcom/bytedance/sdk/openadsdk/component/view/PAGAppOpenOneLayout;-><init>(Landroid/content/Context;)V

    goto :goto_1

    :cond_1
    if-ne v1, v3, :cond_2

    new-instance v0, Lcom/bytedance/sdk/openadsdk/component/view/PAGAppOpenThreeLayout;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->Fj:Landroid/app/Activity;

    invoke-direct {v0, v1}, Lcom/bytedance/sdk/openadsdk/component/view/PAGAppOpenThreeLayout;-><init>(Landroid/content/Context;)V

    goto :goto_1

    :cond_2
    const/4 v2, 0x4

    if-ne v1, v2, :cond_3

    new-instance v0, Lcom/bytedance/sdk/openadsdk/component/view/PAGAppOpenHtmlLayout;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->Fj:Landroid/app/Activity;

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-direct {v0, v1, v2}, Lcom/bytedance/sdk/openadsdk/component/view/PAGAppOpenHtmlLayout;-><init>(Landroid/content/Context;Lcom/bytedance/sdk/openadsdk/core/model/Ql;)V

    :cond_3
    :goto_1
    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->rAx()I

    move-result v1

    if-ne v1, v3, :cond_4

    iget v1, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->svN:I

    const/4 v2, 0x2

    if-eq v1, v2, :cond_4

    iput v2, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->svN:I

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->Ubf:Lcom/bytedance/sdk/openadsdk/component/Fj;

    invoke-virtual {v1, v2}, Lcom/bytedance/sdk/openadsdk/component/Fj;->Fj(I)V

    :cond_4
    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->Ko:Landroid/widget/RelativeLayout;

    invoke-virtual {p1, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/view/PAGAppOpenBaseLayout;->getBackImage()Lcom/bytedance/sdk/openadsdk/core/customview/PAGImageView;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->Tc:Landroid/widget/ImageView;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/view/PAGAppOpenBaseLayout;->getVideoContainer()Lcom/bytedance/sdk/openadsdk/core/customview/PAGFrameLayout;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->BcC:Landroid/widget/FrameLayout;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/view/PAGAppOpenBaseLayout;->getImageView()Lcom/bytedance/sdk/openadsdk/core/customview/PAGImageView;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->rAx:Landroid/widget/ImageView;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/view/PAGAppOpenBaseLayout;->getClickButton()Lcom/bytedance/sdk/openadsdk/component/view/ButtonFlash;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->dG:Lcom/bytedance/sdk/openadsdk/component/view/ButtonFlash;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/view/PAGAppOpenBaseLayout;->getAdLogo()Lcom/bytedance/sdk/openadsdk/core/widget/PAGLogoView;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->UYd:Lcom/bytedance/sdk/openadsdk/core/widget/PAGLogoView;

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->ES()Z

    move-result p1

    if-eqz p1, :cond_5

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/view/PAGAppOpenBaseLayout;->getIconOnlyView()Lcom/bytedance/sdk/openadsdk/core/widget/TTRoundRectImageView;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->JW:Lcom/bytedance/sdk/openadsdk/core/widget/TTRoundRectImageView;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/view/PAGAppOpenBaseLayout;->getTitle()Lcom/bytedance/sdk/openadsdk/core/customview/PAGTextView;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->JU:Lcom/bytedance/sdk/openadsdk/core/customview/PAGTextView;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/view/PAGAppOpenBaseLayout;->getContent()Lcom/bytedance/sdk/openadsdk/core/customview/PAGTextView;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->Ql:Lcom/bytedance/sdk/openadsdk/core/customview/PAGTextView;

    :cond_5
    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/view/PAGAppOpenBaseLayout;->getDspAdChoice()Lcom/bytedance/sdk/openadsdk/core/widget/DSPAdChoice;

    move-result-object p1

    if-eqz p1, :cond_6

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/view/PAGAppOpenBaseLayout;->getDspAdChoice()Lcom/bytedance/sdk/openadsdk/core/widget/DSPAdChoice;

    move-result-object p1

    const/16 v1, 0xe

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {p1, v1, v2}, Lcom/bytedance/sdk/openadsdk/core/widget/DSPAdChoice;->Fj(ILcom/bytedance/sdk/openadsdk/core/model/Ql;)V

    :cond_6
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->UF()Z

    move-result p1

    if-nez p1, :cond_7

    iget-object v4, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->mE:Lcom/bytedance/sdk/openadsdk/component/view/Fj;

    iget-object v6, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    iget v7, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->vYf:F

    iget v8, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->rS:F

    iget-boolean v9, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->hjc:Z

    move-object v5, v0

    invoke-virtual/range {v4 .. v9}, Lcom/bytedance/sdk/openadsdk/component/view/Fj;->Fj(Lcom/bytedance/sdk/openadsdk/component/view/PAGAppOpenBaseLayout;Lcom/bytedance/sdk/openadsdk/core/model/Ql;FFZ)V

    :cond_7
    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/view/PAGAppOpenBaseLayout;->getTopDisLike()Landroid/widget/TextView;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->Af:Landroid/widget/TextView;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/view/PAGAppOpenBaseLayout;->getTopSkip()Landroid/widget/TextView;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->mC:Landroid/widget/TextView;

    instance-of p1, v0, Lcom/bytedance/sdk/openadsdk/component/view/PAGAppOpenHtmlLayout;

    if-eqz p1, :cond_8

    check-cast v0, Lcom/bytedance/sdk/openadsdk/component/view/PAGAppOpenHtmlLayout;

    new-instance p1, Lcom/bytedance/sdk/openadsdk/component/hjc$1;

    invoke-direct {p1, p0}, Lcom/bytedance/sdk/openadsdk/component/hjc$1;-><init>(Lcom/bytedance/sdk/openadsdk/component/hjc;)V

    invoke-virtual {v0, p1}, Lcom/bytedance/sdk/openadsdk/component/view/PAGAppOpenHtmlLayout;->setRenderListener(Lcom/bytedance/sdk/openadsdk/component/view/PAGAppOpenHtmlLayout$Fj;)V

    :cond_8
    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/openadsdk/rAx/Fj/ex;)V
    .locals 2

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/rAx/Fj/ex;->ex()Landroid/graphics/Bitmap;

    move-result-object v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->rAx:Landroid/widget/ImageView;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/rAx/Fj/ex;->ex()Landroid/graphics/Bitmap;

    move-result-object p1

    invoke-virtual {v0, p1}, Landroid/widget/ImageView;->setImageBitmap(Landroid/graphics/Bitmap;)V

    return-void

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->PpV()Ljava/util/List;

    move-result-object v0

    if-eqz v0, :cond_1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->PpV()Ljava/util/List;

    move-result-object v0

    const/4 v1, 0x0

    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    if-eqz v0, :cond_1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->PpV()Ljava/util/List;

    move-result-object v0

    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/bytedance/sdk/openadsdk/core/model/Tc;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Tc;->ex()I

    move-result v0

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/rAx/Fj/ex;->hjc()[B

    move-result-object p1

    invoke-static {p1, v0}, Lcom/bytedance/sdk/openadsdk/utils/rAx;->Fj([BI)Landroid/graphics/drawable/Drawable;

    move-result-object p1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->rAx:Landroid/widget/ImageView;

    sget-object v1, Landroid/widget/ImageView$ScaleType;->FIT_CENTER:Landroid/widget/ImageView$ScaleType;

    invoke-virtual {v0, v1}, Landroid/widget/ImageView;->setScaleType(Landroid/widget/ImageView$ScaleType;)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->rAx:Landroid/widget/ImageView;

    invoke-virtual {v0, p1}, Landroid/widget/ImageView;->setImageDrawable(Landroid/graphics/drawable/Drawable;)V

    :cond_1
    return-void
.end method

.method public Fj(Landroid/widget/FrameLayout;)Z
    .locals 4

    new-instance v0, Lcom/bytedance/sdk/openadsdk/component/BcC/hjc;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->Fj:Landroid/app/Activity;

    invoke-direct {v0, v1}, Lcom/bytedance/sdk/openadsdk/component/BcC/hjc;-><init>(Landroid/content/Context;)V

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->Moo:Lcom/bytedance/sdk/openadsdk/component/BcC/hjc;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v0, p1, v1}, Lcom/bytedance/sdk/openadsdk/component/BcC/hjc;->Fj(Landroid/widget/FrameLayout;Lcom/bytedance/sdk/openadsdk/core/model/Ql;)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->Moo:Lcom/bytedance/sdk/openadsdk/component/BcC/hjc;

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->Ubf:Lcom/bytedance/sdk/openadsdk/component/Fj;

    invoke-virtual {p1, v0}, Lcom/bytedance/sdk/openadsdk/component/BcC/hjc;->Fj(Lcom/bykv/vk/openvk/component/video/api/eV/hjc$Fj;)V

    :try_start_0
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->Moo:Lcom/bytedance/sdk/openadsdk/component/BcC/hjc;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/component/BcC/hjc;->Fj()Z

    move-result p1
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    return p1

    :catchall_0
    move-exception p1

    const/4 v0, 0x2

    new-array v0, v0, [Ljava/lang/Object;

    const-string v1, "open_ad"

    const/4 v2, 0x0

    aput-object v1, v0, v2

    new-instance v1, Ljava/lang/StringBuilder;

    const-string v3, "ttAppOpenAd playVideo error: "

    invoke-direct {v1, v3}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    invoke-virtual {p1}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    const/4 v1, 0x1

    aput-object p1, v0, v1

    const-string p1, "AppOpenAdNativeManager"

    invoke-static {p1, v0}, Lcom/bytedance/sdk/component/utils/dG;->Fj(Ljava/lang/String;[Ljava/lang/Object;)V

    return v2
.end method

.method public Ubf()Lcom/bytedance/sdk/openadsdk/component/BcC/hjc;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->Moo:Lcom/bytedance/sdk/openadsdk/component/BcC/hjc;

    return-object v0
.end method

.method public eV()I
    .locals 1

    const/4 v0, -0x1

    return v0
.end method

.method public ex()V
    .locals 2

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/settings/JW;->Ud()Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;

    move-result-object v0

    iget v1, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->WR:I

    invoke-static {v1}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    move-result-object v1

    invoke-interface {v0, v1}, Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;->mC(Ljava/lang/String;)Z

    move-result v0

    iput-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->Vq:Z

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->UF()Z

    move-result v0

    if-nez v0, :cond_0

    invoke-direct {p0}, Lcom/bytedance/sdk/openadsdk/component/hjc;->WR()V

    return-void

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->Ubf:Lcom/bytedance/sdk/openadsdk/component/Fj;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/Fj;->hjc()V

    return-void
.end method

.method public hjc()V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->dG:Lcom/bytedance/sdk/openadsdk/component/view/ButtonFlash;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/view/ButtonFlash;->Fj()V

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/hjc;->Moo:Lcom/bytedance/sdk/openadsdk/component/BcC/hjc;

    if-eqz v0, :cond_1

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/BcC/hjc;->WR()V

    :cond_1
    return-void
.end method
