.class public interface abstract Lcom/bytedance/sdk/openadsdk/core/nativeexpress/UYd;
.super Ljava/lang/Object;


# virtual methods
.method public abstract Fj()V
.end method

.method public abstract Fj(I)V
.end method

.method public abstract Fj(ILjava/lang/String;)V
.end method

.method public abstract Fj(Z)V
.end method

.method public abstract Ubf()V
.end method

.method public abstract eV()I
.end method

.method public abstract ex()V
.end method

.method public abstract ex(I)V
.end method

.method public abstract hjc()J
.end method
