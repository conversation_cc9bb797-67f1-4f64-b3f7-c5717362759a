.class public Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Tc;
.super Lcom/bytedance/sdk/component/adexpress/ex/Fj;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lcom/bytedance/sdk/component/adexpress/ex/Fj<",
        "Lcom/bytedance/sdk/openadsdk/core/nativeexpress/BackupView;",
        ">;"
    }
.end annotation


# instance fields
.field Fj:Ljava/util/concurrent/atomic/AtomicBoolean;

.field private Ubf:Lcom/bytedance/sdk/component/adexpress/ex/svN;

.field private final WR:Lcom/bytedance/sdk/component/adexpress/ex/dG;

.field private eV:Lcom/bytedance/sdk/component/adexpress/ex/hjc;

.field private ex:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/BackupView;

.field private final hjc:Landroid/view/View;


# direct methods
.method public constructor <init>(Landroid/view/View;Lcom/bytedance/sdk/component/adexpress/theme/ThemeStatusBroadcastReceiver;Lcom/bytedance/sdk/component/adexpress/ex/dG;)V
    .locals 1

    invoke-direct {p0}, Lcom/bytedance/sdk/component/adexpress/ex/Fj;-><init>()V

    new-instance p2, Ljava/util/concurrent/atomic/AtomicBoolean;

    const/4 v0, 0x0

    invoke-direct {p2, v0}, Ljava/util/concurrent/atomic/AtomicBoolean;-><init>(Z)V

    iput-object p2, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Tc;->Fj:Ljava/util/concurrent/atomic/AtomicBoolean;

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Tc;->hjc:Landroid/view/View;

    iput-object p3, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Tc;->WR:Lcom/bytedance/sdk/component/adexpress/ex/dG;

    return-void
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Tc;)V
    .locals 0

    invoke-direct {p0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Tc;->ex()V

    return-void
.end method

.method private ex()V
    .locals 5

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Tc;->Fj:Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-virtual {v0}, Ljava/util/concurrent/atomic/AtomicBoolean;->get()Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Tc;->eV:Lcom/bytedance/sdk/component/adexpress/ex/hjc;

    const/16 v1, 0x6b

    if-eqz v0, :cond_4

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Tc;->hjc:Landroid/view/View;

    check-cast v2, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/NativeExpressView;

    const/4 v3, 0x0

    invoke-interface {v0, v2, v3}, Lcom/bytedance/sdk/component/adexpress/ex/hjc;->Fj(Landroid/view/ViewGroup;I)Z

    move-result v0

    if-eqz v0, :cond_4

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Tc;->WR:Lcom/bytedance/sdk/component/adexpress/ex/dG;

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/adexpress/ex/dG;->Ubf()Lcom/bytedance/sdk/component/adexpress/ex/mSE;

    move-result-object v0

    invoke-interface {v0}, Lcom/bytedance/sdk/component/adexpress/ex/mSE;->svN()V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Tc;->hjc:Landroid/view/View;

    const-string v2, "tt_express_backup_fl_tag_26"

    invoke-virtual {v0, v2}, Landroid/view/View;->findViewWithTag(Ljava/lang/Object;)Landroid/view/View;

    move-result-object v0

    check-cast v0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/BackupView;

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Tc;->ex:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/BackupView;

    if-eqz v0, :cond_3

    new-instance v0, Lcom/bytedance/sdk/component/adexpress/ex/Tc;

    invoke-direct {v0}, Lcom/bytedance/sdk/component/adexpress/ex/Tc;-><init>()V

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Tc;->ex:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/BackupView;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    const/4 v1, 0x0

    goto :goto_0

    :cond_1
    invoke-virtual {v1}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/BackupView;->getRealWidth()F

    move-result v1

    :goto_0
    iget-object v3, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Tc;->ex:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/BackupView;

    if-nez v3, :cond_2

    goto :goto_1

    :cond_2
    invoke-virtual {v3}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/BackupView;->getRealHeight()F

    move-result v2

    :goto_1
    const/4 v3, 0x1

    invoke-virtual {v0, v3}, Lcom/bytedance/sdk/component/adexpress/ex/Tc;->Fj(Z)V

    float-to-double v3, v1

    invoke-virtual {v0, v3, v4}, Lcom/bytedance/sdk/component/adexpress/ex/Tc;->Fj(D)V

    float-to-double v1, v2

    invoke-virtual {v0, v1, v2}, Lcom/bytedance/sdk/component/adexpress/ex/Tc;->ex(D)V

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Tc;->Ubf:Lcom/bytedance/sdk/component/adexpress/ex/svN;

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Tc;->ex:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/BackupView;

    invoke-interface {v1, v2, v0}, Lcom/bytedance/sdk/component/adexpress/ex/svN;->Fj(Landroid/view/View;Lcom/bytedance/sdk/component/adexpress/ex/Tc;)V

    return-void

    :cond_3
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Tc;->Ubf:Lcom/bytedance/sdk/component/adexpress/ex/svN;

    const-string v2, "backupview is null"

    invoke-interface {v0, v1, v2}, Lcom/bytedance/sdk/component/adexpress/ex/svN;->Fj(ILjava/lang/String;)V

    return-void

    :cond_4
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Tc;->Ubf:Lcom/bytedance/sdk/component/adexpress/ex/svN;

    const-string v2, "backup false"

    invoke-interface {v0, v1, v2}, Lcom/bytedance/sdk/component/adexpress/ex/svN;->Fj(ILjava/lang/String;)V

    return-void
.end method


# virtual methods
.method public Fj()Lcom/bytedance/sdk/openadsdk/core/nativeexpress/BackupView;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Tc;->ex:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/BackupView;

    return-object v0
.end method

.method public Fj(Lcom/bytedance/sdk/component/adexpress/ex/hjc;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Tc;->eV:Lcom/bytedance/sdk/component/adexpress/ex/hjc;

    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/component/adexpress/ex/svN;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Tc;->Ubf:Lcom/bytedance/sdk/component/adexpress/ex/svN;

    new-instance p1, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Tc$1;

    invoke-direct {p1, p0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Tc$1;-><init>(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Tc;)V

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/utils/uy;->Fj(Ljava/lang/Runnable;)V

    return-void
.end method

.method public synthetic Ubf()Landroid/view/View;
    .locals 1

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Tc;->Fj()Lcom/bytedance/sdk/openadsdk/core/nativeexpress/BackupView;

    move-result-object v0

    return-object v0
.end method
