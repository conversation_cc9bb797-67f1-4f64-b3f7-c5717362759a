.class Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$11;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/openadsdk/core/widget/ex;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Fj(Lcom/bytedance/sdk/openadsdk/mSE/eV;Ljava/lang/String;Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;

.field final synthetic ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$11;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;

    iput-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$11;->Fj:Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj()V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$11;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ubf(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;)Lcom/bytedance/sdk/openadsdk/core/widget/Fj/Ubf;

    move-result-object v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$11;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ubf(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;)Lcom/bytedance/sdk/openadsdk/core/widget/Fj/Ubf;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/widget/Fj/Ubf;->ex()V

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$11;->Fj:Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;

    if-eqz v0, :cond_1

    invoke-interface {v0}, Lcom/bytedance/sdk/openadsdk/core/video/hjc/ex;->vYf()V

    :cond_1
    return-void
.end method
