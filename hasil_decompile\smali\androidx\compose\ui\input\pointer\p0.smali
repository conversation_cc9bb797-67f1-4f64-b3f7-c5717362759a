.class public final Landroidx/compose/ui/input/pointer/p0;
.super Ljava/lang/Object;


# annotations
.annotation runtime L<PERSON><PERSON>/Metadata;
.end annotation

.annotation build Lkotlin/jvm/internal/SourceDebugExtension;
.end annotation


# static fields
.field public static final a:[Ljava/lang/StackTraceElement;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    const/4 v0, 0x0

    new-array v0, v0, [Ljava/lang/StackTraceElement;

    sput-object v0, Landroidx/compose/ui/input/pointer/p0;->a:[Ljava/lang/StackTraceElement;

    return-void
.end method

.method public static final synthetic a()[Ljava/lang/StackTraceElement;
    .locals 1

    sget-object v0, Landroidx/compose/ui/input/pointer/p0;->a:[Ljava/lang/StackTraceElement;

    return-object v0
.end method
