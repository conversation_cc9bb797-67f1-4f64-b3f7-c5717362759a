.class Lcom/bytedance/sdk/openadsdk/common/BcC$5;
.super Ljava/lang/Object;

# interfaces
.implements Landroid/view/View$OnClickListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/common/BcC;->hjc()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/common/BcC;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/common/BcC;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/common/BcC$5;->Fj:Lcom/bytedance/sdk/openadsdk/common/BcC;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public onClick(Landroid/view/View;)V
    .locals 0

    return-void
.end method
