.class public Lcom/bytedance/sdk/openadsdk/component/Fj;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bykv/vk/openvk/component/video/api/eV/hjc$Fj;
.implements Lcom/bytedance/sdk/openadsdk/component/WR/Fj;


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj()V
    .locals 0

    return-void
.end method

.method public Fj(I)V
    .locals 0

    return-void
.end method

.method public Fj(IIZ)V
    .locals 0

    return-void
.end method

.method public Fj(JI)V
    .locals 0

    return-void
.end method

.method public Fj(JJ)V
    .locals 0

    return-void
.end method

.method public Fj(Landroid/view/View;)V
    .locals 0

    return-void
.end method

.method public Ubf()V
    .locals 0

    return-void
.end method

.method public eV()V
    .locals 0

    return-void
.end method

.method public ex()V
    .locals 0

    return-void
.end method

.method public ex(JI)V
    .locals 0

    return-void
.end method

.method public ex(Landroid/view/View;)V
    .locals 0

    return-void
.end method

.method public hjc()V
    .locals 0

    return-void
.end method
