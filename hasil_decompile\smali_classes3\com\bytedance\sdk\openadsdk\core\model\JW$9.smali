.class Lcom/bytedance/sdk/openadsdk/core/model/JW$9;
.super Lcom/bytedance/sdk/openadsdk/core/widget/Fj/eV;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/core/model/JW;->BcC()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/core/model/JW;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/core/model/JW;Lcom/bytedance/sdk/openadsdk/core/Vq;Lcom/bytedance/sdk/openadsdk/ex/Ko;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/JW$9;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/JW;

    invoke-direct {p0, p2, p3}, Lcom/bytedance/sdk/openadsdk/core/widget/Fj/eV;-><init>(Lcom/bytedance/sdk/openadsdk/core/Vq;Lcom/bytedance/sdk/openadsdk/ex/Ko;)V

    return-void
.end method


# virtual methods
.method public onProgressChanged(Landroid/webkit/WebView;I)V
    .locals 0

    invoke-super {p0, p1, p2}, Lcom/bytedance/sdk/openadsdk/core/widget/Fj/eV;->onProgressChanged(Landroid/webkit/WebView;I)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/JW$9;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/JW;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/core/model/JW;->UYd(Lcom/bytedance/sdk/openadsdk/core/model/JW;)Landroid/app/Activity;

    move-result-object p1

    if-eqz p1, :cond_0

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/JW$9;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/JW;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/core/model/JW;->UYd(Lcom/bytedance/sdk/openadsdk/core/model/JW;)Landroid/app/Activity;

    move-result-object p1

    invoke-virtual {p1}, Landroid/app/Activity;->isFinishing()Z

    move-result p1

    if-nez p1, :cond_0

    const/16 p1, 0x64

    if-ne p2, p1, :cond_0

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/JW$9;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/JW;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/core/model/JW;->rAx(Lcom/bytedance/sdk/openadsdk/core/model/JW;)V

    :cond_0
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/JW$9;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/JW;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/core/model/JW;->rS(Lcom/bytedance/sdk/openadsdk/core/model/JW;)Lcom/bytedance/sdk/openadsdk/common/LandingPageLoadingLayout;

    move-result-object p1

    if-eqz p1, :cond_1

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/JW$9;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/JW;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/core/model/JW;->rS(Lcom/bytedance/sdk/openadsdk/core/model/JW;)Lcom/bytedance/sdk/openadsdk/common/LandingPageLoadingLayout;

    move-result-object p1

    invoke-virtual {p1, p2}, Lcom/bytedance/sdk/openadsdk/common/LandingPageLoadingLayout;->Fj(I)V

    :cond_1
    return-void
.end method
