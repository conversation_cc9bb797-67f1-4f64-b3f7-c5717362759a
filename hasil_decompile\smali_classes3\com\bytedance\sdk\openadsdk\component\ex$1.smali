.class Lcom/bytedance/sdk/openadsdk/component/ex$1;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/openadsdk/api/PAGExpressAdWrapperListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/ex;->Fj(Landroid/view/ViewGroup;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/component/ex;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/ex;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/ex$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/ex;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public onAdClicked()V
    .locals 0

    return-void
.end method

.method public onAdDismissed()V
    .locals 0

    return-void
.end method

.method public onAdShow(Landroid/view/View;I)V
    .locals 0

    return-void
.end method

.method public onRenderFail(Landroid/view/View;Ljava/lang/String;I)V
    .locals 0

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/ex$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/ex;

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/hjc;->Ubf:Lcom/bytedance/sdk/openadsdk/component/Fj;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/component/Fj;->eV()V

    return-void
.end method

.method public onRenderSuccess(Landroid/view/View;FF)V
    .locals 0

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/ex$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/ex;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/ex;->Fj(Lcom/bytedance/sdk/openadsdk/component/ex;)Lcom/bytedance/sdk/openadsdk/component/view/PAGAppOpenAdExpressView;

    move-result-object p1

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/NativeExpressView;->dG()Z

    move-result p1

    if-nez p1, :cond_3

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/ex$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/ex;

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/hjc;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->ES()Z

    move-result p1

    if-eqz p1, :cond_0

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/ex$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/ex;

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/hjc;->Ubf:Lcom/bytedance/sdk/openadsdk/component/Fj;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/component/Fj;->hjc()V

    return-void

    :cond_0
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/ex$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/ex;

    iget-boolean p2, p1, Lcom/bytedance/sdk/openadsdk/component/hjc;->hjc:Z

    if-eqz p2, :cond_2

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/ex;->Fj(Lcom/bytedance/sdk/openadsdk/component/ex;)Lcom/bytedance/sdk/openadsdk/component/view/PAGAppOpenAdExpressView;

    move-result-object p2

    invoke-virtual {p2}, Lcom/bytedance/sdk/openadsdk/component/view/PAGAppOpenAdExpressView;->getVideoFrameLayout()Landroid/widget/FrameLayout;

    move-result-object p2

    invoke-virtual {p1, p2}, Lcom/bytedance/sdk/openadsdk/component/hjc;->Fj(Landroid/widget/FrameLayout;)Z

    move-result p1

    if-eqz p1, :cond_1

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/ex$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/ex;

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/hjc;->Ubf:Lcom/bytedance/sdk/openadsdk/component/Fj;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/component/Fj;->hjc()V

    return-void

    :cond_1
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/ex$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/ex;

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/hjc;->Ubf:Lcom/bytedance/sdk/openadsdk/component/Fj;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/component/Fj;->eV()V

    return-void

    :cond_2
    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/hjc;->Ubf:Lcom/bytedance/sdk/openadsdk/component/Fj;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/component/Fj;->hjc()V

    return-void

    :cond_3
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/ex$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/ex;

    const/4 p2, 0x1

    invoke-static {p1, p2}, Lcom/bytedance/sdk/openadsdk/component/ex;->Fj(Lcom/bytedance/sdk/openadsdk/component/ex;Z)Z

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/ex$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/ex;

    iget-object p2, p1, Lcom/bytedance/sdk/openadsdk/component/hjc;->eV:Landroid/widget/FrameLayout;

    invoke-static {p1, p2}, Lcom/bytedance/sdk/openadsdk/component/ex;->Fj(Lcom/bytedance/sdk/openadsdk/component/ex;Landroid/view/ViewGroup;)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/ex$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/ex;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/ex;->ex(Lcom/bytedance/sdk/openadsdk/component/ex;)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/ex$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/ex;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/ex;->hjc(Lcom/bytedance/sdk/openadsdk/component/ex;)V

    return-void
.end method
