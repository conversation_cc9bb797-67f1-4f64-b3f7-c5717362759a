{"v": "5.8.0", "fr": 24, "ip": 0, "op": 18, "w": 48, "h": 48, "nm": "播放中_Light", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "图层 4", "parent": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [15, 0, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [15, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": -9, "s": [{"i": [[0, -1.657], [0, 0], [1.657, 0], [0, 1.657], [0, 0], [-1.657, 0]], "o": [[0, 0], [0, 1.657], [-1.657, 0], [0, 0], [0, -1.657], [1.657, 0]], "v": [[18, -15], [18, 15], [15, 18], [12, 15], [12, -15], [15, -18]], "c": true}], "e": [{"i": [[0, -1.657], [0, 0], [1.657, 0], [0, 1.657], [0, 0], [-1.657, 0]], "o": [[0, 0], [0, 1.657], [-1.657, 0], [0, 0], [0, -1.657], [1.657, 0]], "v": [[18, 8.312], [18, 15], [15, 18], [12, 15], [12, 8.312], [15, 5.312]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, -1.657], [0, 0], [1.657, 0], [0, 1.657], [0, 0], [-1.657, 0]], "o": [[0, 0], [0, 1.657], [-1.657, 0], [0, 0], [0, -1.657], [1.657, 0]], "v": [[18, 8.312], [18, 15], [15, 18], [12, 15], [12, 8.312], [15, 5.312]], "c": true}], "e": [{"i": [[0, -1.657], [0, 0], [1.657, 0], [0, 1.657], [0, 0], [-1.657, 0]], "o": [[0, 0], [0, 1.657], [-1.657, 0], [0, 0], [0, -1.657], [1.657, 0]], "v": [[18, -15], [18, 15], [15, 18], [12, 15], [12, -15], [15, -18]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 9, "s": [{"i": [[0, -1.657], [0, 0], [1.657, 0], [0, 1.657], [0, 0], [-1.657, 0]], "o": [[0, 0], [0, 1.657], [-1.657, 0], [0, 0], [0, -1.657], [1.657, 0]], "v": [[18, -15], [18, 15], [15, 18], [12, 15], [12, -15], [15, -18]], "c": true}], "e": [{"i": [[0, -1.657], [0, 0], [1.657, 0], [0, 1.657], [0, 0], [-1.657, 0]], "o": [[0, 0], [0, 1.657], [-1.657, 0], [0, 0], [0, -1.657], [1.657, 0]], "v": [[18, 8.312], [18, 15], [15, 18], [12, 15], [12, 8.312], [15, 5.312]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 18, "s": [{"i": [[0, -1.657], [0, 0], [1.657, 0], [0, 1.657], [0, 0], [-1.657, 0]], "o": [[0, 0], [0, 1.657], [-1.657, 0], [0, 0], [0, -1.657], [1.657, 0]], "v": [[18, 8.312], [18, 15], [15, 18], [12, 15], [12, 8.312], [15, 5.312]], "c": true}], "e": [{"i": [[0, -1.657], [0, 0], [1.657, 0], [0, 1.657], [0, 0], [-1.657, 0]], "o": [[0, 0], [0, 1.657], [-1.657, 0], [0, 0], [0, -1.657], [1.657, 0]], "v": [[18, -15], [18, 15], [15, 18], [12, 15], [12, -15], [15, -18]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 27, "s": [{"i": [[0, -1.657], [0, 0], [1.657, 0], [0, 1.657], [0, 0], [-1.657, 0]], "o": [[0, 0], [0, 1.657], [-1.657, 0], [0, 0], [0, -1.657], [1.657, 0]], "v": [[18, -15], [18, 15], [15, 18], [12, 15], [12, -15], [15, -18]], "c": true}], "e": [{"i": [[0, -1.657], [0, 0], [1.657, 0], [0, 1.657], [0, 0], [-1.657, 0]], "o": [[0, 0], [0, 1.657], [-1.657, 0], [0, 0], [0, -1.657], [1.657, 0]], "v": [[18, 8.312], [18, 15], [15, 18], [12, 15], [12, 8.312], [15, 5.312]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 36, "s": [{"i": [[0, -1.657], [0, 0], [1.657, 0], [0, 1.657], [0, 0], [-1.657, 0]], "o": [[0, 0], [0, 1.657], [-1.657, 0], [0, 0], [0, -1.657], [1.657, 0]], "v": [[18, 8.312], [18, 15], [15, 18], [12, 15], [12, 8.312], [15, 5.312]], "c": true}], "e": [{"i": [[0, -1.657], [0, 0], [1.657, 0], [0, 1.657], [0, 0], [-1.657, 0]], "o": [[0, 0], [0, 1.657], [-1.657, 0], [0, 0], [0, -1.657], [1.657, 0]], "v": [[18, -15], [18, 15], [15, 18], [12, 15], [12, -15], [15, -18]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 45, "s": [{"i": [[0, -1.657], [0, 0], [1.657, 0], [0, 1.657], [0, 0], [-1.657, 0]], "o": [[0, 0], [0, 1.657], [-1.657, 0], [0, 0], [0, -1.657], [1.657, 0]], "v": [[18, -15], [18, 15], [15, 18], [12, 15], [12, -15], [15, -18]], "c": true}], "e": [{"i": [[0, -1.657], [0, 0], [1.657, 0], [0, 1.657], [0, 0], [-1.657, 0]], "o": [[0, 0], [0, 1.657], [-1.657, 0], [0, 0], [0, -1.657], [1.657, 0]], "v": [[18, 8.312], [18, 15], [15, 18], [12, 15], [12, 8.312], [15, 5.312]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 54, "s": [{"i": [[0, -1.657], [0, 0], [1.657, 0], [0, 1.657], [0, 0], [-1.657, 0]], "o": [[0, 0], [0, 1.657], [-1.657, 0], [0, 0], [0, -1.657], [1.657, 0]], "v": [[18, 8.312], [18, 15], [15, 18], [12, 15], [12, 8.312], [15, 5.312]], "c": true}], "e": [{"i": [[0, -1.657], [0, 0], [1.657, 0], [0, 1.657], [0, 0], [-1.657, 0]], "o": [[0, 0], [0, 1.657], [-1.657, 0], [0, 0], [0, -1.657], [1.657, 0]], "v": [[18, -15], [18, 15], [15, 18], [12, 15], [12, -15], [15, -18]], "c": true}]}, {"t": 63}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "合并路径 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.027450980392, 0.721568627451, 0.305882352941, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": -9, "op": 111, "st": -9, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "图层 2", "parent": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, -1.657], [0, 0], [1.657, 0], [0, 1.657], [0, 0], [-1.657, 0]], "o": [[0, 0], [0, 1.657], [-1.657, 0], [0, 0], [0, -1.657], [1.657, 0]], "v": [[3, -15], [3, 15], [0, 18], [-3, 15], [-3, -15], [0, -18]], "c": true}], "e": [{"i": [[0, -1.657], [0, 0], [1.657, 0], [0, 1.657], [0, 0], [-1.657, 0]], "o": [[0, 0], [0, 1.657], [-1.657, 0], [0, 0], [0, -1.657], [1.657, 0]], "v": [[3, 10.375], [3, 15], [0, 18], [-3, 15], [-3, 10.375], [0, 7.375]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 9, "s": [{"i": [[0, -1.657], [0, 0], [1.657, 0], [0, 1.657], [0, 0], [-1.657, 0]], "o": [[0, 0], [0, 1.657], [-1.657, 0], [0, 0], [0, -1.657], [1.657, 0]], "v": [[3, 10.375], [3, 15], [0, 18], [-3, 15], [-3, 10.375], [0, 7.375]], "c": true}], "e": [{"i": [[0, -1.657], [0, 0], [1.657, 0], [0, 1.657], [0, 0], [-1.657, 0]], "o": [[0, 0], [0, 1.657], [-1.657, 0], [0, 0], [0, -1.657], [1.657, 0]], "v": [[3, -15], [3, 15], [0, 18], [-3, 15], [-3, -15], [0, -18]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 18, "s": [{"i": [[0, -1.657], [0, 0], [1.657, 0], [0, 1.657], [0, 0], [-1.657, 0]], "o": [[0, 0], [0, 1.657], [-1.657, 0], [0, 0], [0, -1.657], [1.657, 0]], "v": [[3, -15], [3, 15], [0, 18], [-3, 15], [-3, -15], [0, -18]], "c": true}], "e": [{"i": [[0, -1.657], [0, 0], [1.657, 0], [0, 1.657], [0, 0], [-1.657, 0]], "o": [[0, 0], [0, 1.657], [-1.657, 0], [0, 0], [0, -1.657], [1.657, 0]], "v": [[3, 10.375], [3, 15], [0, 18], [-3, 15], [-3, 10.375], [0, 7.375]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 27, "s": [{"i": [[0, -1.657], [0, 0], [1.657, 0], [0, 1.657], [0, 0], [-1.657, 0]], "o": [[0, 0], [0, 1.657], [-1.657, 0], [0, 0], [0, -1.657], [1.657, 0]], "v": [[3, 10.375], [3, 15], [0, 18], [-3, 15], [-3, 10.375], [0, 7.375]], "c": true}], "e": [{"i": [[0, -1.657], [0, 0], [1.657, 0], [0, 1.657], [0, 0], [-1.657, 0]], "o": [[0, 0], [0, 1.657], [-1.657, 0], [0, 0], [0, -1.657], [1.657, 0]], "v": [[3, -15], [3, 15], [0, 18], [-3, 15], [-3, -15], [0, -18]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 36, "s": [{"i": [[0, -1.657], [0, 0], [1.657, 0], [0, 1.657], [0, 0], [-1.657, 0]], "o": [[0, 0], [0, 1.657], [-1.657, 0], [0, 0], [0, -1.657], [1.657, 0]], "v": [[3, -15], [3, 15], [0, 18], [-3, 15], [-3, -15], [0, -18]], "c": true}], "e": [{"i": [[0, -1.657], [0, 0], [1.657, 0], [0, 1.657], [0, 0], [-1.657, 0]], "o": [[0, 0], [0, 1.657], [-1.657, 0], [0, 0], [0, -1.657], [1.657, 0]], "v": [[3, 10.375], [3, 15], [0, 18], [-3, 15], [-3, 10.375], [0, 7.375]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 45, "s": [{"i": [[0, -1.657], [0, 0], [1.657, 0], [0, 1.657], [0, 0], [-1.657, 0]], "o": [[0, 0], [0, 1.657], [-1.657, 0], [0, 0], [0, -1.657], [1.657, 0]], "v": [[3, 10.375], [3, 15], [0, 18], [-3, 15], [-3, 10.375], [0, 7.375]], "c": true}], "e": [{"i": [[0, -1.657], [0, 0], [1.657, 0], [0, 1.657], [0, 0], [-1.657, 0]], "o": [[0, 0], [0, 1.657], [-1.657, 0], [0, 0], [0, -1.657], [1.657, 0]], "v": [[3, -15], [3, 15], [0, 18], [-3, 15], [-3, -15], [0, -18]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 54, "s": [{"i": [[0, -1.657], [0, 0], [1.657, 0], [0, 1.657], [0, 0], [-1.657, 0]], "o": [[0, 0], [0, 1.657], [-1.657, 0], [0, 0], [0, -1.657], [1.657, 0]], "v": [[3, -15], [3, 15], [0, 18], [-3, 15], [-3, -15], [0, -18]], "c": true}], "e": [{"i": [[0, -1.657], [0, 0], [1.657, 0], [0, 1.657], [0, 0], [-1.657, 0]], "o": [[0, 0], [0, 1.657], [-1.657, 0], [0, 0], [0, -1.657], [1.657, 0]], "v": [[3, 10.375], [3, 15], [0, 18], [-3, 15], [-3, 10.375], [0, 7.375]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 63, "s": [{"i": [[0, -1.657], [0, 0], [1.657, 0], [0, 1.657], [0, 0], [-1.657, 0]], "o": [[0, 0], [0, 1.657], [-1.657, 0], [0, 0], [0, -1.657], [1.657, 0]], "v": [[3, 10.375], [3, 15], [0, 18], [-3, 15], [-3, 10.375], [0, 7.375]], "c": true}], "e": [{"i": [[0, -1.657], [0, 0], [1.657, 0], [0, 1.657], [0, 0], [-1.657, 0]], "o": [[0, 0], [0, 1.657], [-1.657, 0], [0, 0], [0, -1.657], [1.657, 0]], "v": [[3, -15], [3, 15], [0, 18], [-3, 15], [-3, -15], [0, -18]], "c": true}]}, {"t": 72}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "合并路径 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.027450980392, 0.721568627451, 0.305882352941, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 120, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "图层 3", "parent": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-15, 0, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-15, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": -4, "s": [{"i": [[0, -1.657], [0, 0], [1.657, 0], [0, 1.657], [0, 0], [-1.657, 0]], "o": [[0, 0], [0, 1.657], [-1.657, 0], [0, 0], [0, -1.657], [1.657, 0]], "v": [[-12, -15], [-12, 15], [-15, 18], [-18, 15], [-18, -15], [-15, -18]], "c": true}], "e": [{"i": [[0, -1.657], [0, 0], [1.657, 0], [0, 1.657], [0, 0], [-1.657, 0]], "o": [[0, 0], [0, 1.657], [-1.657, 0], [0, 0], [0, -1.657], [1.657, 0]], "v": [[-12, 9], [-12, 15], [-15, 18], [-18, 15], [-18, 9], [-15, 6]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 5, "s": [{"i": [[0, -1.657], [0, 0], [1.657, 0], [0, 1.657], [0, 0], [-1.657, 0]], "o": [[0, 0], [0, 1.657], [-1.657, 0], [0, 0], [0, -1.657], [1.657, 0]], "v": [[-12, 9], [-12, 15], [-15, 18], [-18, 15], [-18, 9], [-15, 6]], "c": true}], "e": [{"i": [[0, -1.657], [0, 0], [1.657, 0], [0, 1.657], [0, 0], [-1.657, 0]], "o": [[0, 0], [0, 1.657], [-1.657, 0], [0, 0], [0, -1.657], [1.657, 0]], "v": [[-12, -15], [-12, 15], [-15, 18], [-18, 15], [-18, -15], [-15, -18]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 14, "s": [{"i": [[0, -1.657], [0, 0], [1.657, 0], [0, 1.657], [0, 0], [-1.657, 0]], "o": [[0, 0], [0, 1.657], [-1.657, 0], [0, 0], [0, -1.657], [1.657, 0]], "v": [[-12, -15], [-12, 15], [-15, 18], [-18, 15], [-18, -15], [-15, -18]], "c": true}], "e": [{"i": [[0, -1.657], [0, 0], [1.657, 0], [0, 1.657], [0, 0], [-1.657, 0]], "o": [[0, 0], [0, 1.657], [-1.657, 0], [0, 0], [0, -1.657], [1.657, 0]], "v": [[-12, 9], [-12, 15], [-15, 18], [-18, 15], [-18, 9], [-15, 6]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 23, "s": [{"i": [[0, -1.657], [0, 0], [1.657, 0], [0, 1.657], [0, 0], [-1.657, 0]], "o": [[0, 0], [0, 1.657], [-1.657, 0], [0, 0], [0, -1.657], [1.657, 0]], "v": [[-12, 9], [-12, 15], [-15, 18], [-18, 15], [-18, 9], [-15, 6]], "c": true}], "e": [{"i": [[0, -1.657], [0, 0], [1.657, 0], [0, 1.657], [0, 0], [-1.657, 0]], "o": [[0, 0], [0, 1.657], [-1.657, 0], [0, 0], [0, -1.657], [1.657, 0]], "v": [[-12, -15], [-12, 15], [-15, 18], [-18, 15], [-18, -15], [-15, -18]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 32, "s": [{"i": [[0, -1.657], [0, 0], [1.657, 0], [0, 1.657], [0, 0], [-1.657, 0]], "o": [[0, 0], [0, 1.657], [-1.657, 0], [0, 0], [0, -1.657], [1.657, 0]], "v": [[-12, -15], [-12, 15], [-15, 18], [-18, 15], [-18, -15], [-15, -18]], "c": true}], "e": [{"i": [[0, -1.657], [0, 0], [1.657, 0], [0, 1.657], [0, 0], [-1.657, 0]], "o": [[0, 0], [0, 1.657], [-1.657, 0], [0, 0], [0, -1.657], [1.657, 0]], "v": [[-12, 9], [-12, 15], [-15, 18], [-18, 15], [-18, 9], [-15, 6]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 41, "s": [{"i": [[0, -1.657], [0, 0], [1.657, 0], [0, 1.657], [0, 0], [-1.657, 0]], "o": [[0, 0], [0, 1.657], [-1.657, 0], [0, 0], [0, -1.657], [1.657, 0]], "v": [[-12, 9], [-12, 15], [-15, 18], [-18, 15], [-18, 9], [-15, 6]], "c": true}], "e": [{"i": [[0, -1.657], [0, 0], [1.657, 0], [0, 1.657], [0, 0], [-1.657, 0]], "o": [[0, 0], [0, 1.657], [-1.657, 0], [0, 0], [0, -1.657], [1.657, 0]], "v": [[-12, -15], [-12, 15], [-15, 18], [-18, 15], [-18, -15], [-15, -18]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 50, "s": [{"i": [[0, -1.657], [0, 0], [1.657, 0], [0, 1.657], [0, 0], [-1.657, 0]], "o": [[0, 0], [0, 1.657], [-1.657, 0], [0, 0], [0, -1.657], [1.657, 0]], "v": [[-12, -15], [-12, 15], [-15, 18], [-18, 15], [-18, -15], [-15, -18]], "c": true}], "e": [{"i": [[0, -1.657], [0, 0], [1.657, 0], [0, 1.657], [0, 0], [-1.657, 0]], "o": [[0, 0], [0, 1.657], [-1.657, 0], [0, 0], [0, -1.657], [1.657, 0]], "v": [[-12, 9], [-12, 15], [-15, 18], [-18, 15], [-18, 9], [-15, 6]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 59, "s": [{"i": [[0, -1.657], [0, 0], [1.657, 0], [0, 1.657], [0, 0], [-1.657, 0]], "o": [[0, 0], [0, 1.657], [-1.657, 0], [0, 0], [0, -1.657], [1.657, 0]], "v": [[-12, 9], [-12, 15], [-15, 18], [-18, 15], [-18, 9], [-15, 6]], "c": true}], "e": [{"i": [[0, -1.657], [0, 0], [1.657, 0], [0, 1.657], [0, 0], [-1.657, 0]], "o": [[0, 0], [0, 1.657], [-1.657, 0], [0, 0], [0, -1.657], [1.657, 0]], "v": [[-12, -15], [-12, 15], [-15, 18], [-18, 15], [-18, -15], [-15, -18]], "c": true}]}, {"t": 68}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "合并路径 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.027450980392, 0.721568627451, 0.305882352941, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": -4, "op": 116, "st": -4, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 3, "nm": "图层 1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [24, 24, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 120, "st": 0, "bm": 0}], "markers": []}