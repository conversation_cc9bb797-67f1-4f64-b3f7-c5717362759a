.class Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$5$1;
.super Ljava/lang/Object;

# interfaces
.implements Landroid/view/View$OnClickListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$5;->Fj(Z)Landroid/view/View;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$5;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$5;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$5$1;->Fj:Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$5;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public onClick(Landroid/view/View;)V
    .locals 2

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$5$1;->Fj:Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$5;

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$5;->Fj:Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;

    iget-object v0, p1, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;->ex:Landroid/content/Context;

    iget-object v1, p1, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;->hjc:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;->rAx(Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;)Ljava/lang/String;

    move-result-object p1

    invoke-static {v0, v1, p1}, Lcom/bytedance/sdk/openadsdk/activity/TTWebsiteActivity;->Fj(Landroid/content/Context;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Ljava/lang/String;)V

    return-void
.end method
