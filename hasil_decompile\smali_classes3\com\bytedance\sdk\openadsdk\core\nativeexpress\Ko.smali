.class public Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ko;
.super Ljava/lang/Object;


# direct methods
.method public static Fj()V
    .locals 2

    invoke-static {}, Lcom/bytedance/sdk/component/adexpress/Fj/Fj/Fj;->Fj()Lcom/bytedance/sdk/component/adexpress/Fj/Fj/Fj;

    move-result-object v0

    new-instance v1, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ko$1;

    invoke-direct {v1}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ko$1;-><init>()V

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/adexpress/Fj/Fj/Fj;->Fj(Lcom/bytedance/sdk/component/adexpress/Fj/Fj/ex;)V

    invoke-static {}, Lcom/bytedance/sdk/component/adexpress/Fj/Fj/Fj;->Fj()Lcom/bytedance/sdk/component/adexpress/Fj/Fj/Fj;

    move-result-object v0

    new-instance v1, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ko$2;

    invoke-direct {v1}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ko$2;-><init>()V

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/adexpress/Fj/Fj/Fj;->Fj(Lcom/bytedance/sdk/component/adexpress/Fj/Fj/hjc;)V

    invoke-static {}, Lcom/bytedance/sdk/component/adexpress/Fj/Fj/Fj;->Fj()Lcom/bytedance/sdk/component/adexpress/Fj/Fj/Fj;

    move-result-object v0

    new-instance v1, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ko$3;

    invoke-direct {v1}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ko$3;-><init>()V

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/adexpress/Fj/Fj/Fj;->Fj(Lcom/bytedance/sdk/component/adexpress/Fj/Fj/eV;)V

    invoke-static {}, Lcom/bytedance/sdk/component/widget/Fj/Fj;->Fj()Lcom/bytedance/sdk/component/widget/Fj/Fj;

    move-result-object v0

    new-instance v1, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ko$4;

    invoke-direct {v1}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ko$4;-><init>()V

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/widget/Fj/Fj;->Fj(Lcom/bytedance/sdk/component/widget/Fj/ex;)V

    return-void
.end method
