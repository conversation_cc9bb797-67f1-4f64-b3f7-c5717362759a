.class Lcom/bytedance/sdk/openadsdk/component/reward/WR$2;
.super Lcom/bytedance/sdk/component/svN/BcC;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/reward/WR;->Fj(I)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:I

.field final synthetic ex:Lcom/bytedance/sdk/openadsdk/component/reward/WR;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/reward/WR;Ljava/lang/String;I)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/WR$2;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/WR;

    iput p3, p0, Lcom/bytedance/sdk/openadsdk/component/reward/WR$2;->Fj:I

    invoke-direct {p0, p2}, Lcom/bytedance/sdk/component/svN/BcC;-><init>(Ljava/lang/String;)V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 4

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/multipro/aidl/Fj;->Fj()Lcom/bytedance/sdk/openadsdk/multipro/aidl/Fj;

    move-result-object v0

    iget v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/WR$2;->Fj:I

    const/4 v2, 0x1

    if-ne v1, v2, :cond_0

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/WR$2;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/WR;

    invoke-static {v1}, Lcom/bytedance/sdk/openadsdk/component/reward/WR;->ex(Lcom/bytedance/sdk/openadsdk/component/reward/WR;)Lcom/bytedance/sdk/openadsdk/apiImpl/ex/ex;

    move-result-object v1

    if-eqz v1, :cond_0

    new-instance v1, Lcom/bytedance/sdk/openadsdk/multipro/aidl/ex/hjc;

    iget-object v3, p0, Lcom/bytedance/sdk/openadsdk/component/reward/WR$2;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/WR;

    invoke-static {v3}, Lcom/bytedance/sdk/openadsdk/component/reward/WR;->ex(Lcom/bytedance/sdk/openadsdk/component/reward/WR;)Lcom/bytedance/sdk/openadsdk/apiImpl/ex/ex;

    move-result-object v3

    invoke-direct {v1, v3}, Lcom/bytedance/sdk/openadsdk/multipro/aidl/ex/hjc;-><init>(Lcom/bytedance/sdk/openadsdk/apiImpl/ex/ex;)V

    invoke-virtual {v0, v2}, Lcom/bytedance/sdk/openadsdk/multipro/aidl/Fj;->Fj(I)Landroid/os/IBinder;

    move-result-object v0

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/IListenerManager$Stub;->asInterface(Landroid/os/IBinder;)Lcom/bytedance/sdk/openadsdk/IListenerManager;

    move-result-object v0

    if-eqz v0, :cond_0

    :try_start_0
    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/WR$2;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/WR;

    invoke-static {v2}, Lcom/bytedance/sdk/openadsdk/component/reward/WR;->hjc(Lcom/bytedance/sdk/openadsdk/component/reward/WR;)Ljava/lang/String;

    move-result-object v2

    invoke-interface {v0, v2, v1}, Lcom/bytedance/sdk/openadsdk/IListenerManager;->registerFullVideoListener(Ljava/lang/String;Lcom/bytedance/sdk/openadsdk/IFullScreenVideoAdInteractionListener;)V
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception v0

    const-string v1, "TTFullScreenVideoAdImpl"

    invoke-virtual {v0}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    move-result-object v0

    invoke-static {v1, v0}, Lcom/bytedance/sdk/component/utils/dG;->Fj(Ljava/lang/String;Ljava/lang/String;)V

    nop

    :cond_0
    :goto_0
    return-void
.end method
