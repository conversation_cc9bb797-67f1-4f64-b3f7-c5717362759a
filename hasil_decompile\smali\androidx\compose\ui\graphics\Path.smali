.class public interface abstract Landroidx/compose/ui/graphics/Path;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/compose/ui/graphics/Path$a;,
        Landroidx/compose/ui/graphics/Path$Direction;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# static fields
.field public static final a:Landroidx/compose/ui/graphics/Path$a;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    sget-object v0, Landroidx/compose/ui/graphics/Path$a;->a:Landroidx/compose/ui/graphics/Path$a;

    sput-object v0, Landroidx/compose/ui/graphics/Path;->a:Landroidx/compose/ui/graphics/Path$a;

    return-void
.end method


# virtual methods
.method public abstract a()Z
.end method

.method public abstract b(Ld0/i;Landroidx/compose/ui/graphics/Path$Direction;)V
.end method

.method public abstract c(FF)V
.end method

.method public abstract close()V
.end method

.method public abstract d(FFFFFF)V
.end method

.method public abstract e(I)V
.end method

.method public abstract f(FFFF)V
.end method

.method public abstract g(FFFF)V
.end method

.method public abstract getBounds()Ld0/i;
.end method

.method public abstract h(Ld0/k;Landroidx/compose/ui/graphics/Path$Direction;)V
.end method

.method public abstract i()I
.end method

.method public abstract isEmpty()Z
.end method

.method public abstract j(FF)V
.end method

.method public abstract k(FFFFFF)V
.end method

.method public abstract l(Landroidx/compose/ui/graphics/Path;Landroidx/compose/ui/graphics/Path;I)Z
.end method

.method public abstract m(FF)V
.end method

.method public abstract n(FF)V
.end method

.method public abstract reset()V
.end method

.method public abstract rewind()V
.end method
