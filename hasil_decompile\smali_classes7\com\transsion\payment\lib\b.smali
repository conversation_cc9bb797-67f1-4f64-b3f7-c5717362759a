.class public interface abstract Lcom/transsion/payment/lib/b;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/transsion/payment/lib/b$a;
    }
.end annotation

.annotation runtime L<PERSON>lin/Metadata;
.end annotation


# virtual methods
.method public abstract a(Ljava/lang/Integer;Ljava/lang/String;ZLjava/lang/String;)V
.end method

.method public abstract b(Z)V
.end method

.method public abstract c(ILjava/lang/String;Ljava/lang/String;)V
.end method
