.class Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc$1;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/openadsdk/common/TTAdDislikeDialog$Fj;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc;->ex(Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;

.field final synthetic ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc;Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc$1;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc;

    iput-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj(ILcom/bytedance/sdk/openadsdk/FilterWord;)V
    .locals 0

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc$1;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object p1

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->cB:Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-virtual {p1}, Ljava/util/concurrent/atomic/AtomicBoolean;->get()Z

    move-result p1

    if-nez p1, :cond_0

    if-eqz p2, :cond_0

    invoke-virtual {p2}, Lcom/bytedance/sdk/openadsdk/FilterWord;->hasSecondOptions()Z

    move-result p1

    if-nez p1, :cond_0

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc$1;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object p1

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->cB:Ljava/util/concurrent/atomic/AtomicBoolean;

    const/4 p2, 0x1

    invoke-virtual {p1, p2}, Ljava/util/concurrent/atomic/AtomicBoolean;->set(Z)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc$1;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc;->ex(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc;)V

    :cond_0
    return-void
.end method

.method public Fj(Landroid/view/View;)V
    .locals 2

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc$1;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object p1

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->mC:Ljava/util/concurrent/atomic/AtomicBoolean;

    const/4 v0, 0x1

    invoke-virtual {p1, v0}, Ljava/util/concurrent/atomic/AtomicBoolean;->set(Z)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc$1;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object p1

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->kF:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;

    iget-boolean p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Ubf:Z

    if-eqz p1, :cond_1

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc$1;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object p1

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->spi:Lcom/bytedance/sdk/openadsdk/component/reward/view/hjc;

    if-eqz p1, :cond_0

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc$1;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object p1

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->spi:Lcom/bytedance/sdk/openadsdk/component/reward/view/hjc;

    const/16 v0, 0x8

    const/4 v1, 0x0

    invoke-virtual {p1, v0, v1}, Lcom/bytedance/sdk/openadsdk/component/reward/view/hjc;->Fj(IZ)V

    :cond_0
    return-void

    :cond_1
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;->Ql()V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc$1;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object p1

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->kF:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->ex()Z

    move-result p1

    if-eqz p1, :cond_2

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc$1;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object p1

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->kF:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->JW()V

    :cond_2
    return-void
.end method

.method public ex(Landroid/view/View;)V
    .locals 2

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc$1;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object p1

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->mC:Ljava/util/concurrent/atomic/AtomicBoolean;

    const/4 v0, 0x0

    invoke-virtual {p1, v0}, Ljava/util/concurrent/atomic/AtomicBoolean;->set(Z)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc$1;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object p1

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->kF:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;

    iget-boolean p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Ubf:Z

    if-eqz p1, :cond_1

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc$1;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object p1

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->spi:Lcom/bytedance/sdk/openadsdk/component/reward/view/hjc;

    if-eqz p1, :cond_0

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc$1;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object p1

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->spi:Lcom/bytedance/sdk/openadsdk/component/reward/view/hjc;

    const/4 v1, 0x1

    invoke-virtual {p1, v0, v1}, Lcom/bytedance/sdk/openadsdk/component/reward/view/hjc;->Fj(IZ)V

    :cond_0
    return-void

    :cond_1
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc$1;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object p1

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->kF:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;

    invoke-virtual {p1, v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc$1;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object p1

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->kF:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->eV()Z

    move-result p1

    if-eqz p1, :cond_2

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc$1;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/hjc;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object p1

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->kF:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Tc()V

    :cond_2
    return-void
.end method
