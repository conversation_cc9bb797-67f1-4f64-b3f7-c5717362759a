.class public Lcom/bytedance/sdk/openadsdk/core/settings/svN;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Comparable;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ljava/lang/Comparable<",
        "Lcom/bytedance/sdk/openadsdk/core/settings/svN;",
        ">;"
    }
.end annotation


# instance fields
.field private final BcC:Ljava/lang/String;

.field private final Fj:Ljava/lang/String;

.field private Ubf:Ljava/lang/String;

.field private final WR:I

.field private final eV:I

.field private final ex:Ljava/lang/String;

.field private final hjc:I

.field private final svN:I


# direct methods
.method public constructor <init>(Ljava/lang/String;Ljava/lang/String;IILjava/lang/String;IILjava/lang/String;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/settings/svN;->Fj:Ljava/lang/String;

    iput-object p2, p0, Lcom/bytedance/sdk/openadsdk/core/settings/svN;->ex:Ljava/lang/String;

    iput p3, p0, Lcom/bytedance/sdk/openadsdk/core/settings/svN;->hjc:I

    iput p4, p0, Lcom/bytedance/sdk/openadsdk/core/settings/svN;->eV:I

    iput-object p5, p0, Lcom/bytedance/sdk/openadsdk/core/settings/svN;->Ubf:Ljava/lang/String;

    invoke-static {p5}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result p1

    if-eqz p1, :cond_0

    const-string p1, "0"

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/settings/svN;->Ubf:Ljava/lang/String;

    :cond_0
    iput p6, p0, Lcom/bytedance/sdk/openadsdk/core/settings/svN;->WR:I

    iput p7, p0, Lcom/bytedance/sdk/openadsdk/core/settings/svN;->svN:I

    iput-object p8, p0, Lcom/bytedance/sdk/openadsdk/core/settings/svN;->BcC:Ljava/lang/String;

    return-void
.end method


# virtual methods
.method public Fj()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/settings/svN;->WR:I

    return v0
.end method

.method public Fj(Lcom/bytedance/sdk/openadsdk/core/settings/svN;)I
    .locals 2

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/settings/svN;->WR:I

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/settings/svN;->Fj()I

    move-result v1

    if-ge v0, v1, :cond_0

    const/4 p1, -0x1

    return p1

    :cond_0
    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/settings/svN;->WR:I

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/settings/svN;->Fj()I

    move-result p1

    if-ne v0, p1, :cond_1

    const/4 p1, 0x0

    return p1

    :cond_1
    const/4 p1, 0x1

    return p1
.end method

.method public synthetic compareTo(Ljava/lang/Object;)I
    .locals 0

    check-cast p1, Lcom/bytedance/sdk/openadsdk/core/settings/svN;

    invoke-virtual {p0, p1}, Lcom/bytedance/sdk/openadsdk/core/settings/svN;->Fj(Lcom/bytedance/sdk/openadsdk/core/settings/svN;)I

    move-result p1

    return p1
.end method
