.class public Lcom/bytedance/sdk/openadsdk/component/reward/Fj/eV;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bytedance/sdk/openadsdk/component/reward/Fj/eV$Fj;
    }
.end annotation


# instance fields
.field Fj:Lcom/com/bytedance/overseas/sdk/Fj/hjc;

.field private final Ubf:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

.field private eV:Z

.field private final ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

.field private final hjc:Ljava/lang/String;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;)V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/eV;->Ubf:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/eV;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->svN:Ljava/lang/String;

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/eV;->hjc:Ljava/lang/String;

    return-void
.end method

.method private eV()V
    .locals 3

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/multipro/ex;->hjc()Z

    move-result v0

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/eV;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->efV()I

    move-result v0

    const/4 v1, 0x4

    if-ne v0, v1, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/eV;->Ubf:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->cs:Landroid/content/Context;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/eV;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/eV;->hjc:Ljava/lang/String;

    invoke-static {v0, v1, v2}, Lcom/com/bytedance/overseas/sdk/Fj/eV;->Fj(Landroid/content/Context;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Ljava/lang/String;)Lcom/com/bytedance/overseas/sdk/Fj/hjc;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/eV;->Fj:Lcom/com/bytedance/overseas/sdk/Fj/hjc;

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/eV;->Fj:Lcom/com/bytedance/overseas/sdk/Fj/hjc;

    if-nez v0, :cond_1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/eV;->Ubf:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Kk:Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/eV;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/eV;->hjc:Ljava/lang/String;

    invoke-static {v0, v1, v2}, Lcom/com/bytedance/overseas/sdk/Fj/eV;->Fj(Landroid/content/Context;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Ljava/lang/String;)Lcom/com/bytedance/overseas/sdk/Fj/hjc;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/eV;->Fj:Lcom/com/bytedance/overseas/sdk/Fj/hjc;

    :cond_1
    return-void
.end method


# virtual methods
.method public Fj()V
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/eV;->eV:Z

    if-eqz v0, :cond_0

    return-void

    :cond_0
    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/eV;->eV:Z

    invoke-direct {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/eV;->eV()V

    return-void
.end method

.method public Fj(Landroid/view/View;FFFFLandroid/util/SparseArray;IIILcom/bytedance/sdk/openadsdk/component/reward/Fj/eV$Fj;)V
    .locals 11
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/view/View;",
            "FFFF",
            "Landroid/util/SparseArray<",
            "Lcom/bytedance/sdk/openadsdk/core/ex/hjc$Fj;",
            ">;III",
            "Lcom/bytedance/sdk/openadsdk/component/reward/Fj/eV$Fj;",
            ")V"
        }
    .end annotation

    move-object v0, p0

    move-object/from16 v1, p10

    iget-object v2, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/eV;->Fj:Lcom/com/bytedance/overseas/sdk/Fj/hjc;

    if-eqz v2, :cond_4

    invoke-virtual {p1}, Landroid/view/View;->getId()I

    move-result v2

    sget v3, Lcom/bytedance/sdk/openadsdk/utils/Ko;->Ubf:I

    const/4 v4, 0x0

    if-ne v2, v3, :cond_0

    const-string v2, "click_play_star_level"

    invoke-interface {v1, v2, v4}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/eV$Fj;->Fj(Ljava/lang/String;Lorg/json/JSONObject;)V

    return-void

    :cond_0
    sget v3, Lcom/bytedance/sdk/openadsdk/utils/Ko;->eV:I

    if-ne v2, v3, :cond_1

    const-string v2, "click_play_star_nums"

    invoke-interface {v1, v2, v4}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/eV$Fj;->Fj(Ljava/lang/String;Lorg/json/JSONObject;)V

    return-void

    :cond_1
    sget v3, Lcom/bytedance/sdk/openadsdk/utils/Ko;->hjc:I

    if-ne v2, v3, :cond_2

    const-string v2, "click_play_source"

    invoke-interface {v1, v2, v4}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/eV$Fj;->Fj(Ljava/lang/String;Lorg/json/JSONObject;)V

    return-void

    :cond_2
    sget v3, Lcom/bytedance/sdk/openadsdk/utils/Ko;->ex:I

    if-ne v2, v3, :cond_3

    const-string v2, "click_play_logo"

    invoke-interface {v1, v2, v4}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/eV$Fj;->Fj(Ljava/lang/String;Lorg/json/JSONObject;)V

    :cond_3
    return-void

    :cond_4
    move-object/from16 v1, p10

    move-object v2, p1

    move v3, p2

    move v4, p3

    move v5, p4

    move/from16 v6, p5

    move-object/from16 v7, p6

    move/from16 v8, p7

    move/from16 v9, p8

    move/from16 v10, p9

    invoke-interface/range {v1 .. v10}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/eV$Fj;->Fj(Landroid/view/View;FFFFLandroid/util/SparseArray;III)V

    return-void
.end method

.method public ex()V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/eV;->Fj:Lcom/com/bytedance/overseas/sdk/Fj/hjc;

    if-eqz v0, :cond_0

    invoke-interface {v0}, Lcom/com/bytedance/overseas/sdk/Fj/hjc;->eV()V

    :cond_0
    return-void
.end method

.method public hjc()Lcom/com/bytedance/overseas/sdk/Fj/hjc;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/eV;->Fj:Lcom/com/bytedance/overseas/sdk/Fj/hjc;

    return-object v0
.end method
