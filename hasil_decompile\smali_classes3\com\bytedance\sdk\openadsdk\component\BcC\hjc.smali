.class public Lcom/bytedance/sdk/openadsdk/component/BcC/hjc;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/openadsdk/core/ex/Fj$Fj;


# instance fields
.field private Fj:Landroid/content/Context;

.field private eV:Lcom/bytedance/sdk/openadsdk/component/BcC/ex;

.field private ex:Landroid/widget/FrameLayout;

.field private hjc:Lcom/bytedance/sdk/openadsdk/core/model/Ql;


# direct methods
.method public constructor <init>(Landroid/content/Context;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    invoke-virtual {p1}, Landroid/content/Context;->getApplicationContext()Landroid/content/Context;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/BcC/hjc;->Fj:Landroid/content/Context;

    return-void
.end method


# virtual methods
.method public BcC()J
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/BcC/hjc;->eV:Lcom/bytedance/sdk/openadsdk/component/BcC/ex;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/video/Fj/Fj;->WR()J

    move-result-wide v0

    return-wide v0

    :cond_0
    const-wide/16 v0, 0x0

    return-wide v0
.end method

.method public Fj(I)V
    .locals 3

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/BcC/hjc;->eV:Lcom/bytedance/sdk/openadsdk/component/BcC/ex;

    if-eqz v0, :cond_0

    new-instance v0, Lcom/bytedance/sdk/openadsdk/ex/eV/ex/JW$Fj;

    invoke-direct {v0}, Lcom/bytedance/sdk/openadsdk/ex/eV/ex/JW$Fj;-><init>()V

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/component/BcC/hjc;->svN()J

    move-result-wide v1

    invoke-virtual {v0, v1, v2}, Lcom/bytedance/sdk/openadsdk/ex/eV/ex/JW$Fj;->Fj(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/component/BcC/hjc;->mSE()J

    move-result-wide v1

    invoke-virtual {v0, v1, v2}, Lcom/bytedance/sdk/openadsdk/ex/eV/ex/JW$Fj;->hjc(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/component/BcC/hjc;->BcC()J

    move-result-wide v1

    invoke-virtual {v0, v1, v2}, Lcom/bytedance/sdk/openadsdk/ex/eV/ex/JW$Fj;->ex(J)V

    invoke-virtual {v0, p1}, Lcom/bytedance/sdk/openadsdk/ex/eV/ex/JW$Fj;->hjc(I)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/BcC/hjc;->eV:Lcom/bytedance/sdk/openadsdk/component/BcC/ex;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/video/Fj/Fj;->svN()I

    move-result p1

    invoke-virtual {v0, p1}, Lcom/bytedance/sdk/openadsdk/ex/eV/ex/JW$Fj;->eV(I)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/BcC/hjc;->eV:Lcom/bytedance/sdk/openadsdk/component/BcC/ex;

    invoke-virtual {p1, v0}, Lcom/bytedance/sdk/openadsdk/component/BcC/ex;->Fj(Lcom/bytedance/sdk/openadsdk/ex/eV/ex/JW$Fj;)V

    :cond_0
    return-void
.end method

.method public Fj(Landroid/widget/FrameLayout;Lcom/bytedance/sdk/openadsdk/core/model/Ql;)V
    .locals 2

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/BcC/hjc;->ex:Landroid/widget/FrameLayout;

    iput-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/BcC/hjc;->hjc:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    new-instance v0, Lcom/bytedance/sdk/openadsdk/component/BcC/ex;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/BcC/hjc;->Fj:Landroid/content/Context;

    invoke-direct {v0, v1, p1, p2}, Lcom/bytedance/sdk/openadsdk/component/BcC/ex;-><init>(Landroid/content/Context;Landroid/view/ViewGroup;Lcom/bytedance/sdk/openadsdk/core/model/Ql;)V

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/BcC/hjc;->eV:Lcom/bytedance/sdk/openadsdk/component/BcC/ex;

    return-void
.end method

.method public Fj(Lcom/bykv/vk/openvk/component/video/api/eV/hjc$Fj;)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/BcC/hjc;->eV:Lcom/bytedance/sdk/openadsdk/component/BcC/ex;

    if-eqz v0, :cond_0

    invoke-virtual {v0, p1}, Lcom/bytedance/sdk/openadsdk/core/video/Fj/Fj;->Fj(Lcom/bykv/vk/openvk/component/video/api/eV/hjc$Fj;)V

    :cond_0
    return-void
.end method

.method public Fj()Z
    .locals 3

    sget v0, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v1, 0x17

    if-lt v0, v1, :cond_0

    const/4 v0, 0x0

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/CacheDirFactory;->getICacheDir(I)Lcom/bykv/vk/openvk/component/video/api/Fj/ex;

    move-result-object v0

    invoke-interface {v0}, Lcom/bykv/vk/openvk/component/video/api/Fj/ex;->ex()Ljava/lang/String;

    move-result-object v0

    goto :goto_0

    :cond_0
    :try_start_0
    invoke-static {}, Lcom/bytedance/sdk/openadsdk/component/svN/Fj;->Fj()Ljava/lang/String;

    move-result-object v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_0

    :catchall_0
    const-string v0, ""

    :goto_0
    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/BcC/hjc;->hjc:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {v0, v1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->Fj(Ljava/lang/String;Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Lcom/bytedance/sdk/openadsdk/core/video/Fj/ex;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/BcC/hjc;->hjc:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->cs()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->ex(Ljava/lang/String;)V

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/BcC/hjc;->ex:Landroid/widget/FrameLayout;

    invoke-virtual {v1}, Landroid/view/View;->getWidth()I

    move-result v1

    invoke-virtual {v0, v1}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->Fj(I)V

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/BcC/hjc;->ex:Landroid/widget/FrameLayout;

    invoke-virtual {v1}, Landroid/view/View;->getHeight()I

    move-result v1

    invoke-virtual {v0, v1}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->ex(I)V

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/BcC/hjc;->hjc:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->Jq()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->hjc(Ljava/lang/String;)V

    const-wide/16 v1, 0x0

    invoke-virtual {v0, v1, v2}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->Fj(J)V

    const/4 v1, 0x1

    invoke-virtual {v0, v1}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->Fj(Z)V

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/BcC/hjc;->eV:Lcom/bytedance/sdk/openadsdk/component/BcC/ex;

    invoke-virtual {v1, v0}, Lcom/bytedance/sdk/openadsdk/core/video/hjc/Fj;->Fj(Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;)Z

    move-result v0

    return v0
.end method

.method public Ubf()V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/BcC/hjc;->eV:Lcom/bytedance/sdk/openadsdk/component/BcC/ex;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/video/hjc/Fj;->ex()V

    :cond_0
    return-void
.end method

.method public WR()V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/BcC/hjc;->eV:Lcom/bytedance/sdk/openadsdk/component/BcC/ex;

    if-nez v0, :cond_0

    return-void

    :cond_0
    const/4 v1, 0x0

    iput-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/BcC/hjc;->Fj:Landroid/content/Context;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/video/hjc/Fj;->hjc()V

    iput-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/BcC/hjc;->eV:Lcom/bytedance/sdk/openadsdk/component/BcC/ex;

    return-void
.end method

.method public eV()V
    .locals 4

    :try_start_0
    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/component/BcC/hjc;->ex()Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/BcC/hjc;->eV:Lcom/bytedance/sdk/openadsdk/component/BcC/ex;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/video/Fj/Fj;->Fj()V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_0

    :catchall_0
    move-exception v0

    goto :goto_1

    :cond_0
    :goto_0
    return-void

    :goto_1
    const/4 v1, 0x2

    new-array v1, v1, [Ljava/lang/Object;

    const/4 v2, 0x0

    const-string v3, "open_ad"

    aput-object v3, v1, v2

    new-instance v2, Ljava/lang/StringBuilder;

    const-string v3, "AppOpenVideoManager onPause throw Exception :"

    invoke-direct {v2, v3}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    invoke-virtual {v0}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const/4 v2, 0x1

    aput-object v0, v1, v2

    const-string v0, "TTAppOpenVideoManager"

    invoke-static {v0, v1}, Lcom/bytedance/sdk/component/utils/dG;->Fj(Ljava/lang/String;[Ljava/lang/Object;)V

    return-void
.end method

.method public ex()Z
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/BcC/hjc;->eV:Lcom/bytedance/sdk/openadsdk/component/BcC/ex;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/video/Fj/Fj;->UYd()Lcom/bykv/vk/openvk/component/video/api/Fj;

    move-result-object v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/BcC/hjc;->eV:Lcom/bytedance/sdk/openadsdk/component/BcC/ex;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/video/Fj/Fj;->UYd()Lcom/bykv/vk/openvk/component/video/api/Fj;

    move-result-object v0

    invoke-interface {v0}, Lcom/bykv/vk/openvk/component/video/api/Fj;->WR()Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    return v0

    :cond_0
    const/4 v0, 0x0

    return v0
.end method

.method public getVideoProgress()J
    .locals 2

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/component/BcC/hjc;->svN()J

    move-result-wide v0

    return-wide v0
.end method

.method public hjc()Z
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/BcC/hjc;->eV:Lcom/bytedance/sdk/openadsdk/component/BcC/ex;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/video/Fj/Fj;->UYd()Lcom/bykv/vk/openvk/component/video/api/Fj;

    move-result-object v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/BcC/hjc;->eV:Lcom/bytedance/sdk/openadsdk/component/BcC/ex;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/video/Fj/Fj;->UYd()Lcom/bykv/vk/openvk/component/video/api/Fj;

    move-result-object v0

    invoke-interface {v0}, Lcom/bykv/vk/openvk/component/video/api/Fj;->svN()Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    return v0

    :cond_0
    const/4 v0, 0x0

    return v0
.end method

.method public mSE()J
    .locals 4

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/BcC/hjc;->eV:Lcom/bytedance/sdk/openadsdk/component/BcC/ex;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/video/Fj/Fj;->BcC()J

    move-result-wide v0

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/component/BcC/hjc;->eV:Lcom/bytedance/sdk/openadsdk/component/BcC/ex;

    invoke-virtual {v2}, Lcom/bytedance/sdk/openadsdk/core/video/Fj/Fj;->WR()J

    move-result-wide v2

    add-long/2addr v0, v2

    return-wide v0

    :cond_0
    const-wide/16 v0, 0x0

    return-wide v0
.end method

.method public svN()J
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/BcC/hjc;->eV:Lcom/bytedance/sdk/openadsdk/component/BcC/ex;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/video/Fj/Fj;->Ubf()J

    move-result-wide v0

    return-wide v0

    :cond_0
    const-wide/16 v0, 0x0

    return-wide v0
.end method
