.class public Lcom/bytedance/sdk/openadsdk/component/reward/hjc;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bykv/vk/openvk/component/video/api/eV/hjc;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;
    }
.end annotation


# instance fields
.field private final Fj:Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;

.field private Ubf:Z

.field private final WR:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

.field private eV:Z

.field private ex:Z

.field private hjc:J

.field private final svN:Lcom/bykv/vk/openvk/component/video/api/ex/Fj;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/ex/svN;)V
    .locals 8

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc;->ex:Z

    const-wide/16 v0, 0x0

    iput-wide v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc;->hjc:J

    const/4 v2, 0x0

    iput-boolean v2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc;->eV:Z

    new-instance v2, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$1;

    invoke-direct {v2, p0}, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$1;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/hjc;)V

    iput-object v2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc;->svN:Lcom/bykv/vk/openvk/component/video/api/ex/Fj;

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc;->WR:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->spi()Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

    move-result-object p1

    const-wide/16 v3, 0xa

    if-eqz p1, :cond_0

    invoke-virtual {p1}, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->WR()D

    move-result-wide v5

    double-to-long v5, v5

    goto :goto_0

    :cond_0
    move-wide v5, v3

    :goto_0
    cmp-long v7, v5, v0

    if-gtz v7, :cond_1

    const-wide/high16 v0, 0x4024000000000000L    # 10.0

    invoke-virtual {p1, v0, v1}, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->Fj(D)V

    goto :goto_1

    :cond_1
    move-wide v3, v5

    :goto_1
    new-instance p1, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;

    const-wide/16 v0, 0x3e8

    mul-long v3, v3, v0

    invoke-direct {p1, v3, v4, v2, p2}, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;-><init>(JLcom/bykv/vk/openvk/component/video/api/ex/Fj;Lcom/bytedance/sdk/openadsdk/ex/svN;)V

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;

    return-void
.end method


# virtual methods
.method public BcC()J
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;->Tc()J

    move-result-wide v0

    return-wide v0
.end method

.method public Fj()V
    .locals 3

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;->Ko()V

    new-instance v0, Lcom/bytedance/sdk/openadsdk/ex/eV/ex/JW$Fj;

    invoke-direct {v0}, Lcom/bytedance/sdk/openadsdk/ex/eV/ex/JW$Fj;-><init>()V

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/hjc;->Ubf()J

    move-result-wide v1

    invoke-virtual {v0, v1, v2}, Lcom/bytedance/sdk/openadsdk/ex/eV/ex/JW$Fj;->Fj(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/hjc;->BcC()J

    move-result-wide v1

    invoke-virtual {v0, v1, v2}, Lcom/bytedance/sdk/openadsdk/ex/eV/ex/JW$Fj;->hjc(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/hjc;->WR()J

    move-result-wide v1

    invoke-virtual {v0, v1, v2}, Lcom/bytedance/sdk/openadsdk/ex/eV/ex/JW$Fj;->ex(J)V

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc;->svN:Lcom/bykv/vk/openvk/component/video/api/ex/Fj;

    invoke-static {v1, v0}, Lcom/bytedance/sdk/openadsdk/ex/eV/Fj/Fj;->Fj(Lcom/bykv/vk/openvk/component/video/api/ex/Fj;Lcom/bytedance/sdk/openadsdk/ex/eV/ex/JW$Fj;)V

    return-void
.end method

.method public Fj(J)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;

    invoke-virtual {v0, p1, p2}, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;->Fj(J)V

    return-void
.end method

.method public Fj(Lcom/bykv/vk/openvk/component/video/api/eV/hjc$Fj;)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;

    invoke-virtual {v0, p1}, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;->Fj(Lcom/bykv/vk/openvk/component/video/api/eV/hjc$Fj;)V

    return-void
.end method

.method public Fj(Lcom/bykv/vk/openvk/component/video/api/eV/hjc$eV;)V
    .locals 0

    return-void
.end method

.method public Fj(Lcom/bykv/vk/openvk/component/video/api/eV/hjc$ex;)V
    .locals 0

    return-void
.end method

.method public Fj(Z)V
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc;->ex:Z

    return-void
.end method

.method public Fj(ZI)V
    .locals 0

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/hjc;->hjc()V

    return-void
.end method

.method public Fj(Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;)Z
    .locals 5

    invoke-virtual {p1}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->BcC()Z

    move-result v0

    iput-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc;->eV:Z

    invoke-virtual {p1}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->svN()J

    move-result-wide v0

    const-wide/16 v2, 0x0

    cmp-long v4, v0, v2

    if-lez v4, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;

    invoke-virtual {p1}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->svN()J

    move-result-wide v1

    invoke-virtual {v0, v1, v2}, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;->Fj(J)V

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc;->WR:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc;->svN:Lcom/bykv/vk/openvk/component/video/api/ex/Fj;

    invoke-static {v0, v1, p1}, Lcom/bytedance/sdk/openadsdk/ex/eV/Fj/Fj;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bykv/vk/openvk/component/video/api/ex/Fj;Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;->mSE()V

    const/4 p1, 0x1

    return p1
.end method

.method public JU()Z
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public JW()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc;->Ubf:Z

    return v0
.end method

.method public Ko()I
    .locals 4

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;)J

    move-result-wide v0

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;

    invoke-static {v2}, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;->ex(Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;)J

    move-result-wide v2

    invoke-static {v0, v1, v2, v3}, Lcom/bykv/vk/openvk/component/video/Fj/Ubf/Fj;->Fj(JJ)I

    move-result v0

    return v0
.end method

.method public Ql()Z
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public Tc()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc;->eV:Z

    return v0
.end method

.method public UYd()Lcom/bykv/vk/openvk/component/video/api/Fj;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;

    return-object v0
.end method

.method public Ubf()J
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;->JW()J

    move-result-wide v0

    return-wide v0
.end method

.method public Ubf(Z)V
    .locals 0

    return-void
.end method

.method public WR()J
    .locals 2

    const-wide/16 v0, 0x0

    return-wide v0
.end method

.method public dG()Lcom/bykv/vk/openvk/component/video/api/eV/ex;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public eV()V
    .locals 0

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/hjc;->hjc()V

    return-void
.end method

.method public eV(Z)V
    .locals 0

    return-void
.end method

.method public ex()V
    .locals 3

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;->mSE()V

    new-instance v0, Lcom/bytedance/sdk/openadsdk/ex/eV/ex/JW$Fj;

    invoke-direct {v0}, Lcom/bytedance/sdk/openadsdk/ex/eV/ex/JW$Fj;-><init>()V

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/hjc;->Ubf()J

    move-result-wide v1

    invoke-virtual {v0, v1, v2}, Lcom/bytedance/sdk/openadsdk/ex/eV/ex/JW$Fj;->Fj(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/hjc;->BcC()J

    move-result-wide v1

    invoke-virtual {v0, v1, v2}, Lcom/bytedance/sdk/openadsdk/ex/eV/ex/JW$Fj;->hjc(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/hjc;->WR()J

    move-result-wide v1

    invoke-virtual {v0, v1, v2}, Lcom/bytedance/sdk/openadsdk/ex/eV/ex/JW$Fj;->ex(J)V

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc;->svN:Lcom/bykv/vk/openvk/component/video/api/ex/Fj;

    invoke-static {v1, v0}, Lcom/bytedance/sdk/openadsdk/ex/eV/Fj/Fj;->ex(Lcom/bykv/vk/openvk/component/video/api/ex/Fj;Lcom/bytedance/sdk/openadsdk/ex/eV/ex/JW$Fj;)V

    return-void
.end method

.method public ex(J)V
    .locals 0

    iput-wide p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc;->hjc:J

    return-void
.end method

.method public ex(Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;)V
    .locals 0

    return-void
.end method

.method public ex(Z)V
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc;->eV:Z

    return-void
.end method

.method public hjc()V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;->rAx()V

    return-void
.end method

.method public hjc(J)V
    .locals 0

    return-void
.end method

.method public hjc(Z)V
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc;->Ubf:Z

    return-void
.end method

.method public mSE()J
    .locals 2

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/hjc;->Ubf()J

    move-result-wide v0

    return-wide v0
.end method

.method public rAx()Z
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public rS()Lcom/bykv/vk/openvk/component/video/api/ex/Fj;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc;->svN:Lcom/bykv/vk/openvk/component/video/api/ex/Fj;

    return-object v0
.end method

.method public svN()I
    .locals 1

    const/4 v0, 0x0

    return v0
.end method
