.class Lcom/bytedance/sdk/openadsdk/component/hjc$6;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/openadsdk/utils/rAx$Fj;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/hjc;->BcC()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/component/hjc;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/hjc;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/hjc$6;->Fj:Lcom/bytedance/sdk/openadsdk/component/hjc;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj()V
    .locals 0

    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/openadsdk/rAx/Fj/ex;)V
    .locals 1

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/rAx/Fj/ex;->eV()Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/hjc$6;->Fj:Lcom/bytedance/sdk/openadsdk/component/hjc;

    invoke-virtual {v0, p1}, Lcom/bytedance/sdk/openadsdk/component/hjc;->Fj(Lcom/bytedance/sdk/openadsdk/rAx/Fj/ex;)V

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/rAx/Fj/ex;->ex()Landroid/graphics/Bitmap;

    move-result-object v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/hjc$6;->Fj:Lcom/bytedance/sdk/openadsdk/component/hjc;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/rAx/Fj/ex;->Fj()Landroid/graphics/Bitmap;

    move-result-object p1

    invoke-static {v0, p1}, Lcom/bytedance/sdk/openadsdk/component/hjc;->Fj(Lcom/bytedance/sdk/openadsdk/component/hjc;Landroid/graphics/Bitmap;)V

    :cond_0
    return-void
.end method
