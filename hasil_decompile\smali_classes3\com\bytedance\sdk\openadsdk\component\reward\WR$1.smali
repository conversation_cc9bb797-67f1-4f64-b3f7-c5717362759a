.class Lcom/bytedance/sdk/openadsdk/component/reward/WR$1;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/component/utils/ex$Fj;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/reward/WR;->show(Landroid/app/Activity;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

.field final synthetic ex:Lcom/bytedance/sdk/openadsdk/component/reward/WR;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/reward/WR;Lcom/bytedance/sdk/openadsdk/core/model/Ql;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/WR$1;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/WR;

    iput-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/WR$1;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj()V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/WR$1;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/WR;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/WR;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/WR;)Z

    move-result v0

    if-eqz v0, :cond_0

    :try_start_0
    invoke-static {}, Lcom/bytedance/sdk/openadsdk/Ko/hjc;->Fj()Lcom/bytedance/sdk/openadsdk/Ko/hjc;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/WR$1;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->spi()Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

    move-result-object v1

    invoke-virtual {v1}, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->UYd()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/Ko/hjc;->Fj(Ljava/lang/String;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :catchall_0
    :cond_0
    return-void
.end method

.method public Fj(Ljava/lang/Throwable;)V
    .locals 3

    const-string v0, "TTFullScreenVideoAdImpl"

    const-string v1, "show full screen video error: "

    invoke-static {v0, v1, p1}, Lcom/bytedance/sdk/component/utils/dG;->Fj(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/WR$1;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/WR;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/WR;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/WR;)Z

    move-result v0

    if-eqz v0, :cond_1

    :try_start_0
    invoke-static {}, Lcom/bytedance/sdk/openadsdk/Ko/hjc;->Fj()Lcom/bytedance/sdk/openadsdk/Ko/hjc;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/WR$1;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->spi()Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

    move-result-object v1

    invoke-virtual {v1}, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->UYd()Ljava/lang/String;

    move-result-object v1

    if-eqz p1, :cond_0

    invoke-virtual {p1}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    move-result-object p1

    goto :goto_0

    :cond_0
    const-string p1, "playable tool error open"

    :goto_0
    const/4 v2, -0x1

    invoke-virtual {v0, v1, v2, p1}, Lcom/bytedance/sdk/openadsdk/Ko/hjc;->Fj(Ljava/lang/String;ILjava/lang/String;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :catchall_0
    :cond_1
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/WR$1;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    const-string v0, "fullscreen_interstitial_ad"

    const-string v1, "activity start  fail "

    invoke-static {p1, v0, v1}, Lcom/bytedance/sdk/openadsdk/ex/hjc;->ex(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method
