.class public Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/adsdk/ugeno/core/JU;
.implements Lcom/bytedance/sdk/component/adexpress/ex/mSE;


# instance fields
.field private final Fj:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

.field private Ubf:J

.field private final eV:Ljava/lang/String;

.field private final ex:Ljava/lang/String;

.field private final hjc:Lcom/bytedance/sdk/openadsdk/core/model/Ql;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;Ljava/lang/String;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Ljava/lang/String;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;->Fj:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    iput-object p2, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;->ex:Ljava/lang/String;

    iput-object p4, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;->eV:Ljava/lang/String;

    iput-object p3, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;->hjc:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    return-void
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;)Lcom/bytedance/sdk/openadsdk/core/model/Ql;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;->hjc:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    return-object p0
.end method

.method public static synthetic ex(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;)Ljava/lang/String;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;->ex:Ljava/lang/String;

    return-object p0
.end method


# virtual methods
.method public BcC()V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;->Fj:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    invoke-interface {v0}, Lcom/bytedance/sdk/openadsdk/ex/hjc/ex;->Tc()V

    return-void
.end method

.method public Fj()V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;->Fj:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    const-string v1, "ugen_render_start"

    invoke-interface {v0, v1}, Lcom/bytedance/sdk/openadsdk/ex/hjc/Fj;->Ubf(Ljava/lang/String;)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;->Fj:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    const-string v1, "ugen_sub_analysis_start"

    invoke-interface {v0, v1}, Lcom/bytedance/sdk/openadsdk/ex/hjc/Fj;->Ubf(Ljava/lang/String;)V

    return-void
.end method

.method public Fj(I)V
    .locals 2

    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v0

    iput-wide v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;->Ubf:J

    const/4 v0, 0x3

    if-ne p1, v0, :cond_0

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;->Fj:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    const-string v0, "dynamic_render2_start"

    invoke-interface {p1, v0}, Lcom/bytedance/sdk/openadsdk/ex/hjc/Fj;->hjc(Ljava/lang/String;)V

    return-void

    :cond_0
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;->Fj:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    const-string v0, "dynamic_render_start"

    invoke-interface {p1, v0}, Lcom/bytedance/sdk/openadsdk/ex/hjc/Fj;->hjc(Ljava/lang/String;)V

    return-void
.end method

.method public Fj(IILjava/lang/String;Z)V
    .locals 6

    if-nez p4, :cond_0

    iget-object p4, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;->Fj:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    const/4 v0, 0x1

    invoke-interface {p4, v0}, Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;->Fj(Z)V

    :cond_0
    const/4 p4, 0x3

    if-ne p1, p4, :cond_1

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;->Fj:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    const-string p4, "dynamic_render2_error"

    invoke-interface {p1, p2, p4}, Lcom/bytedance/sdk/openadsdk/ex/hjc/Fj;->ex(ILjava/lang/String;)V

    goto :goto_0

    :cond_1
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;->Fj:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    const-string p4, "dynamic_render_error"

    invoke-interface {p1, p2, p4}, Lcom/bytedance/sdk/openadsdk/ex/hjc/Fj;->ex(ILjava/lang/String;)V

    :goto_0
    const-string v0, "NDR"

    iget-object v3, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;->ex:Ljava/lang/String;

    iget-object v4, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;->eV:Ljava/lang/String;

    iget-object v5, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;->hjc:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    move v1, p2

    move-object v2, p3

    invoke-static/range {v0 .. v5}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/mSE;->Fj(Ljava/lang/String;ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Lcom/bytedance/sdk/openadsdk/core/model/Ql;)V

    return-void
.end method

.method public Fj(ILjava/lang/String;)V
    .locals 8

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;->Fj:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    const/4 v1, 0x0

    invoke-interface {v0, p1, v1}, Lcom/bytedance/sdk/openadsdk/ex/hjc/eV;->Fj(ILjava/lang/String;)V

    const-string v2, "Web"

    iget-object v5, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;->ex:Ljava/lang/String;

    iget-object v6, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;->eV:Ljava/lang/String;

    iget-object v7, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;->hjc:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    move v3, p1

    move-object v4, p2

    invoke-static/range {v2 .. v7}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/mSE;->Fj(Ljava/lang/String;ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Lcom/bytedance/sdk/openadsdk/core/model/Ql;)V

    return-void
.end method

.method public Fj(Lcom/bytedance/adsdk/ugeno/core/JW;)V
    .locals 9

    invoke-virtual {p1}, Lcom/bytedance/adsdk/ugeno/core/JW;->Fj()I

    move-result v0

    if-nez v0, :cond_0

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;->Fj:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    const-string v0, "ugen_sub_render_end"

    invoke-interface {p1, v0}, Lcom/bytedance/sdk/openadsdk/ex/hjc/Fj;->Ubf(Ljava/lang/String;)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;->Fj:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    const-string v0, "ugen_render_success"

    invoke-interface {p1, v0}, Lcom/bytedance/sdk/openadsdk/ex/hjc/Fj;->WR(Ljava/lang/String;)V

    goto :goto_0

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;->Fj:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    invoke-virtual {p1}, Lcom/bytedance/adsdk/ugeno/core/JW;->Fj()I

    move-result v1

    const-string v2, "ugen_render_error"

    invoke-interface {v0, v1, v2}, Lcom/bytedance/sdk/openadsdk/ex/hjc/Fj;->hjc(ILjava/lang/String;)V

    const-string v3, "UGen"

    invoke-virtual {p1}, Lcom/bytedance/adsdk/ugeno/core/JW;->Fj()I

    move-result v4

    invoke-virtual {p1}, Lcom/bytedance/adsdk/ugeno/core/JW;->ex()Ljava/lang/String;

    move-result-object v5

    iget-object v6, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;->ex:Ljava/lang/String;

    iget-object v7, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;->eV:Ljava/lang/String;

    iget-object v8, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;->hjc:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static/range {v3 .. v8}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/mSE;->Fj(Ljava/lang/String;ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Lcom/bytedance/sdk/openadsdk/core/model/Ql;)V

    :goto_0
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;->Fj:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    const/4 v0, 0x1

    invoke-interface {p1, v0}, Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;->Fj(Z)V

    return-void
.end method

.method public Fj(Z)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;->Fj:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    invoke-interface {v0, p1}, Lcom/bytedance/sdk/openadsdk/ex/hjc/WR;->Fj(I)V

    return-void
.end method

.method public Ko()V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;->Fj:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    invoke-interface {v0}, Lcom/bytedance/sdk/openadsdk/ex/hjc/eV;->ex()V

    return-void
.end method

.method public Ubf()V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;->Fj:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    invoke-interface {v0}, Lcom/bytedance/sdk/openadsdk/ex/hjc/eV;->ex()V

    return-void
.end method

.method public Ubf(I)V
    .locals 1

    const/4 v0, 0x3

    if-ne p1, v0, :cond_0

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;->Fj:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    const-string v0, "dynamic_sub_render2_end"

    invoke-interface {p1, v0}, Lcom/bytedance/sdk/openadsdk/ex/hjc/Fj;->eV(Ljava/lang/String;)V

    return-void

    :cond_0
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;->Fj:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    const-string v0, "dynamic_sub_render_end"

    invoke-interface {p1, v0}, Lcom/bytedance/sdk/openadsdk/ex/hjc/Fj;->eV(Ljava/lang/String;)V

    return-void
.end method

.method public WR()V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;->Fj:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    invoke-interface {v0}, Lcom/bytedance/sdk/openadsdk/ex/hjc/ex;->hjc()V

    return-void
.end method

.method public WR(I)V
    .locals 2

    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    const/4 v0, 0x3

    if-ne p1, v0, :cond_0

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;->Fj:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    const-string v0, "dynamic_render2_success"

    invoke-interface {p1, v0}, Lcom/bytedance/sdk/openadsdk/ex/hjc/Fj;->svN(Ljava/lang/String;)V

    const-string p1, "dynamic2_render"

    goto :goto_0

    :cond_0
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;->Fj:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    const-string v0, "dynamic_render_success"

    invoke-interface {p1, v0}, Lcom/bytedance/sdk/openadsdk/ex/hjc/Fj;->svN(Ljava/lang/String;)V

    const-string p1, "dynamic_backup_native_render"

    :goto_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;->Fj:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    const/4 v1, 0x1

    invoke-interface {v0, v1}, Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;->Fj(Z)V

    new-instance v0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx$1;

    const-string v1, "dynamic_success"

    invoke-direct {v0, p0, v1, p1}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx$1;-><init>(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;Ljava/lang/String;Ljava/lang/String;)V

    const/16 p1, 0xa

    invoke-static {v0, p1}, Lcom/bytedance/sdk/openadsdk/utils/uy;->ex(Lcom/bytedance/sdk/component/svN/BcC;I)V

    return-void
.end method

.method public eV()V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;->Fj:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    invoke-interface {v0}, Lcom/bytedance/sdk/openadsdk/ex/hjc/eV;->Fj()V

    return-void
.end method

.method public eV(I)V
    .locals 1

    const/4 v0, 0x3

    if-ne p1, v0, :cond_0

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;->Fj:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    const-string v0, "dynamic_sub_render2_start"

    invoke-interface {p1, v0}, Lcom/bytedance/sdk/openadsdk/ex/hjc/Fj;->eV(Ljava/lang/String;)V

    return-void

    :cond_0
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;->Fj:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    const-string v0, "dynamic_sub_render_start"

    invoke-interface {p1, v0}, Lcom/bytedance/sdk/openadsdk/ex/hjc/Fj;->eV(Ljava/lang/String;)V

    return-void
.end method

.method public ex()V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;->Fj:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    const-string v1, "ugen_sub_analysis_end"

    invoke-interface {v0, v1}, Lcom/bytedance/sdk/openadsdk/ex/hjc/Fj;->Ubf(Ljava/lang/String;)V

    return-void
.end method

.method public ex(I)V
    .locals 1

    const/4 v0, 0x3

    if-ne p1, v0, :cond_0

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;->Fj:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    const-string v0, "dynamic_sub_analysis2_start"

    invoke-interface {p1, v0}, Lcom/bytedance/sdk/openadsdk/ex/hjc/Fj;->eV(Ljava/lang/String;)V

    return-void

    :cond_0
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;->Fj:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    const-string v0, "dynamic_sub_analysis_start"

    invoke-interface {p1, v0}, Lcom/bytedance/sdk/openadsdk/ex/hjc/Fj;->eV(Ljava/lang/String;)V

    return-void
.end method

.method public hjc()V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;->Fj:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    const-string v1, "ugen_sub_render_start"

    invoke-interface {v0, v1}, Lcom/bytedance/sdk/openadsdk/ex/hjc/Fj;->Ubf(Ljava/lang/String;)V

    return-void
.end method

.method public hjc(I)V
    .locals 1

    const/4 v0, 0x3

    if-ne p1, v0, :cond_0

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;->Fj:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    const-string v0, "dynamic_sub_analysis2_end"

    invoke-interface {p1, v0}, Lcom/bytedance/sdk/openadsdk/ex/hjc/Fj;->eV(Ljava/lang/String;)V

    return-void

    :cond_0
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;->Fj:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    const-string v0, "dynamic_sub_analysis_end"

    invoke-interface {p1, v0}, Lcom/bytedance/sdk/openadsdk/ex/hjc/Fj;->eV(Ljava/lang/String;)V

    return-void
.end method

.method public mSE()V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;->Fj:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    invoke-interface {v0}, Lcom/bytedance/sdk/openadsdk/ex/hjc/eV;->JW()V

    return-void
.end method

.method public rAx()V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;->Fj:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    invoke-interface {v0}, Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;->rAx()V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;->Fj:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    invoke-interface {v0}, Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;->UYd()V

    return-void
.end method

.method public svN()V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;->Fj:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    const/4 v1, 0x1

    invoke-interface {v0, v1}, Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;->Fj(Z)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;->Fj:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    invoke-interface {v0}, Lcom/bytedance/sdk/openadsdk/ex/hjc/ex;->dG()V

    new-instance v0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx$2;

    const-string v1, "native_success"

    invoke-direct {v0, p0, v1}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx$2;-><init>(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;Ljava/lang/String;)V

    const/16 v1, 0xa

    invoke-static {v0, v1}, Lcom/bytedance/sdk/openadsdk/utils/uy;->ex(Lcom/bytedance/sdk/component/svN/BcC;I)V

    return-void
.end method
