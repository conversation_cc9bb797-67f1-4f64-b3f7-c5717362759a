.class Lcom/bytedance/sdk/openadsdk/component/svN$1$1;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/svN$1;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Fj;Lcom/bytedance/sdk/openadsdk/core/model/ex;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

.field final synthetic ex:Lcom/bytedance/sdk/openadsdk/component/svN$1;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/svN$1;Lcom/bytedance/sdk/openadsdk/core/model/Ql;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/svN$1$1;->ex:Lcom/bytedance/sdk/openadsdk/component/svN$1;

    iput-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/svN$1$1;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 5

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/svN$1$1;->ex:Lcom/bytedance/sdk/openadsdk/component/svN$1;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/svN$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/svN;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/svN;->Fj(Lcom/bytedance/sdk/openadsdk/component/svN;)Lcom/bytedance/sdk/openadsdk/core/model/mC;

    move-result-object v0

    const/4 v1, 0x2

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/core/model/mC;->Fj(I)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/svN$1$1;->ex:Lcom/bytedance/sdk/openadsdk/component/svN$1;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/svN$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/svN;

    new-instance v1, Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;

    const/16 v2, 0x64

    iget-object v3, p0, Lcom/bytedance/sdk/openadsdk/component/svN$1$1;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    const/4 v4, 0x1

    invoke-direct {v1, v4, v2, v3}, Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;-><init>(IILcom/bytedance/sdk/openadsdk/core/model/Ql;)V

    invoke-static {v0, v1}, Lcom/bytedance/sdk/openadsdk/component/svN;->Fj(Lcom/bytedance/sdk/openadsdk/component/svN;Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;)V

    return-void
.end method
