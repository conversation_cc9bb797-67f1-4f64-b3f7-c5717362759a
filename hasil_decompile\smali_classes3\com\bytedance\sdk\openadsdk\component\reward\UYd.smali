.class public Lcom/bytedance/sdk/openadsdk/component/reward/UYd;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bytedance/sdk/openadsdk/component/reward/UYd$Fj;,
        Lcom/bytedance/sdk/openadsdk/component/reward/UYd$ex;
    }
.end annotation


# static fields
.field private static volatile Fj:Lcom/bytedance/sdk/openadsdk/component/reward/UYd;
    .annotation build Landroid/annotation/SuppressLint;
        value = {
            "StaticFieldLeak"
        }
    .end annotation
.end field


# instance fields
.field private Ubf:Lcom/bytedance/sdk/component/svN/BcC;

.field private final WR:Lcom/bytedance/sdk/component/utils/cB$Fj;

.field private final eV:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/bytedance/sdk/openadsdk/component/reward/UYd$ex;",
            ">;"
        }
    .end annotation
.end field

.field private final ex:Landroid/content/Context;

.field private final hjc:Ljava/util/concurrent/atomic/AtomicBoolean;


# direct methods
.method private constructor <init>(Landroid/content/Context;)V
    .locals 2

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Ljava/util/concurrent/atomic/AtomicBoolean;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Ljava/util/concurrent/atomic/AtomicBoolean;-><init>(Z)V

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/UYd;->hjc:Ljava/util/concurrent/atomic/AtomicBoolean;

    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    invoke-static {v0}, Ljava/util/Collections;->synchronizedList(Ljava/util/List;)Ljava/util/List;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/UYd;->eV:Ljava/util/List;

    new-instance v0, Lcom/bytedance/sdk/openadsdk/component/reward/UYd$7;

    invoke-direct {v0, p0}, Lcom/bytedance/sdk/openadsdk/component/reward/UYd$7;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/UYd;)V

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/UYd;->WR:Lcom/bytedance/sdk/component/utils/cB$Fj;

    if-nez p1, :cond_0

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/JU;->Fj()Landroid/content/Context;

    move-result-object p1

    goto :goto_0

    :cond_0
    invoke-virtual {p1}, Landroid/content/Context;->getApplicationContext()Landroid/content/Context;

    move-result-object p1

    :goto_0
    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/UYd;->ex:Landroid/content/Context;

    invoke-direct {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/UYd;->ex()V

    return-void
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/openadsdk/component/reward/UYd;)Landroid/content/Context;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/UYd;->ex:Landroid/content/Context;

    return-object p0
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/openadsdk/component/reward/UYd;Lcom/bytedance/sdk/component/svN/BcC;)Lcom/bytedance/sdk/component/svN/BcC;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/UYd;->Ubf:Lcom/bytedance/sdk/component/svN/BcC;

    return-object p1
.end method

.method public static Fj(Landroid/content/Context;)Lcom/bytedance/sdk/openadsdk/component/reward/UYd;
    .locals 2

    sget-object v0, Lcom/bytedance/sdk/openadsdk/component/reward/UYd;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/UYd;

    if-nez v0, :cond_1

    const-class v0, Lcom/bytedance/sdk/openadsdk/component/reward/UYd;

    monitor-enter v0

    :try_start_0
    sget-object v1, Lcom/bytedance/sdk/openadsdk/component/reward/UYd;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/UYd;

    if-nez v1, :cond_0

    new-instance v1, Lcom/bytedance/sdk/openadsdk/component/reward/UYd;

    invoke-direct {v1, p0}, Lcom/bytedance/sdk/openadsdk/component/reward/UYd;-><init>(Landroid/content/Context;)V

    sput-object v1, Lcom/bytedance/sdk/openadsdk/component/reward/UYd;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/UYd;

    goto :goto_0

    :catchall_0
    move-exception p0

    goto :goto_1

    :cond_0
    :goto_0
    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_2

    :goto_1
    monitor-exit v0

    throw p0

    :cond_1
    :goto_2
    sget-object p0, Lcom/bytedance/sdk/openadsdk/component/reward/UYd;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/UYd;

    return-object p0
.end method

.method private Fj(Lcom/bytedance/sdk/openadsdk/AdSlot;ZLcom/bytedance/sdk/openadsdk/common/hjc;)V
    .locals 10

    invoke-static {}, Lcom/bytedance/sdk/component/utils/dG;->eV()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/AdSlot;->getBidAdm()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/bykv/vk/openvk/component/video/api/WR/ex;->Fj(Ljava/lang/String;)Ljava/lang/String;

    :cond_0
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v6

    new-instance v0, Lcom/bytedance/sdk/openadsdk/core/model/vYf;

    invoke-direct {v0}, Lcom/bytedance/sdk/openadsdk/core/model/vYf;-><init>()V

    const/4 v1, 0x2

    if-eqz p2, :cond_1

    const/4 v2, 0x2

    goto :goto_0

    :cond_1
    const/4 v2, 0x1

    :goto_0
    iput v2, v0, Lcom/bytedance/sdk/openadsdk/core/model/vYf;->ex:I

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/JU;->eV()Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;

    move-result-object v2

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/AdSlot;->getCodeId()Ljava/lang/String;

    move-result-object v3

    invoke-interface {v2, v3}, Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;->rAx(Ljava/lang/String;)Z

    move-result v2

    if-nez v2, :cond_2

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/AdSlot;->getExpressViewAcceptedWidth()F

    move-result v2

    const/4 v3, 0x0

    cmpl-float v2, v2, v3

    if-gtz v2, :cond_2

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/AdSlot;->isExpressAd()Z

    move-result v2

    if-eqz v2, :cond_3

    :cond_2
    iput v1, v0, Lcom/bytedance/sdk/openadsdk/core/model/vYf;->WR:I

    :cond_3
    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/JU;->hjc()Lcom/bytedance/sdk/openadsdk/core/Ql;

    move-result-object v8

    new-instance v9, Lcom/bytedance/sdk/openadsdk/component/reward/UYd$3;

    move-object v1, v9

    move-object v2, p0

    move v3, p2

    move-object v4, p3

    move-object v5, p1

    invoke-direct/range {v1 .. v7}, Lcom/bytedance/sdk/openadsdk/component/reward/UYd$3;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/UYd;ZLcom/bytedance/sdk/openadsdk/common/hjc;Lcom/bytedance/sdk/openadsdk/AdSlot;J)V

    const/4 p2, 0x7

    invoke-interface {v8, p1, v0, p2, v9}, Lcom/bytedance/sdk/openadsdk/core/Ql;->Fj(Lcom/bytedance/sdk/openadsdk/AdSlot;Lcom/bytedance/sdk/openadsdk/core/model/vYf;ILcom/bytedance/sdk/openadsdk/core/Ql$Fj;)V

    return-void
.end method

.method private Fj(Lcom/bytedance/sdk/openadsdk/component/reward/UYd$ex;)V
    .locals 2

    if-nez p1, :cond_0

    return-void

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/UYd;->eV:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    if-lez v0, :cond_1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/UYd;->eV:Ljava/util/List;

    const/4 v1, 0x0

    invoke-interface {v0, v1}, Ljava/util/List;->remove(I)Ljava/lang/Object;

    :cond_1
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/UYd;->eV:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/openadsdk/component/reward/UYd;Lcom/bytedance/sdk/openadsdk/core/model/Fj;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/component/reward/JW;Lcom/bytedance/sdk/openadsdk/AdSlot;ZLcom/bytedance/sdk/openadsdk/component/reward/UYd$Fj;Z)V
    .locals 0

    invoke-direct/range {p0 .. p7}, Lcom/bytedance/sdk/openadsdk/component/reward/UYd;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Fj;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/component/reward/JW;Lcom/bytedance/sdk/openadsdk/AdSlot;ZLcom/bytedance/sdk/openadsdk/component/reward/UYd$Fj;Z)V

    return-void
.end method

.method private Fj(Lcom/bytedance/sdk/openadsdk/core/model/Fj;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/component/reward/JW;Lcom/bytedance/sdk/openadsdk/AdSlot;ZLcom/bytedance/sdk/openadsdk/component/reward/UYd$Fj;Z)V
    .locals 12

    move-object v8, p0

    move-object v7, p1

    move-object v9, p2

    move-object/from16 v6, p4

    move-object/from16 v4, p6

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/video/ex/Fj;->Fj()Lcom/bytedance/sdk/openadsdk/core/video/ex/Fj;

    move-result-object v0

    new-instance v1, Lcom/bytedance/sdk/openadsdk/component/reward/UYd$4;

    invoke-direct {v1, p0}, Lcom/bytedance/sdk/openadsdk/component/reward/UYd$4;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/UYd;)V

    invoke-virtual {v0, p2, v1}, Lcom/bytedance/sdk/openadsdk/core/video/ex/Fj;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/core/video/ex/Fj$Fj;)V

    const/4 v0, 0x1

    if-eqz p5, :cond_0

    invoke-static {p2}, Lcom/bytedance/sdk/openadsdk/core/model/mE;->rAx(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v1

    if-nez v1, :cond_0

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/JU;->eV()Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;

    move-result-object v1

    invoke-virtual/range {p4 .. p4}, Lcom/bytedance/sdk/openadsdk/AdSlot;->getCodeId()Ljava/lang/String;

    move-result-object v2

    invoke-interface {v1, v2}, Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;->Moo(Ljava/lang/String;)Lcom/bytedance/sdk/openadsdk/core/settings/Fj;

    move-result-object v1

    iget v1, v1, Lcom/bytedance/sdk/openadsdk/core/settings/Fj;->eV:I

    if-ne v1, v0, :cond_0

    iget-object v1, v8, Lcom/bytedance/sdk/openadsdk/component/reward/UYd;->ex:Landroid/content/Context;

    invoke-static {v1}, Lcom/bytedance/sdk/component/utils/JU;->eV(Landroid/content/Context;)Z

    move-result v1

    if-nez v1, :cond_0

    new-instance v0, Lcom/bytedance/sdk/openadsdk/component/reward/UYd$ex;

    invoke-direct {v0, p2, v6, p1}, Lcom/bytedance/sdk/openadsdk/component/reward/UYd$ex;-><init>(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/AdSlot;Lcom/bytedance/sdk/openadsdk/core/model/Fj;)V

    invoke-direct {p0, v0}, Lcom/bytedance/sdk/openadsdk/component/reward/UYd;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/UYd$ex;)V

    return-void

    :cond_0
    if-eqz v4, :cond_1

    if-nez p7, :cond_2

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/JU;->eV()Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;

    move-result-object v1

    invoke-interface {v1}, Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;->mE()I

    move-result v1

    if-ne v1, v0, :cond_1

    goto :goto_0

    :cond_1
    const/4 v0, 0x0

    :cond_2
    :goto_0
    invoke-static {p2}, Lcom/bytedance/sdk/openadsdk/core/model/mE;->rAx(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v1

    if-nez v1, :cond_4

    sget v1, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v2, 0x17

    if-lt v1, v2, :cond_3

    invoke-virtual {p2}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->spi()Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

    move-result-object v1

    if-eqz v1, :cond_5

    invoke-virtual {p2}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->aGk()I

    move-result v0

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/CacheDirFactory;->getICacheDir(I)Lcom/bykv/vk/openvk/component/video/api/Fj/ex;

    move-result-object v0

    invoke-interface {v0}, Lcom/bykv/vk/openvk/component/video/api/Fj/ex;->Fj()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0, p2}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->Fj(Ljava/lang/String;Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Lcom/bytedance/sdk/openadsdk/core/video/Fj/ex;

    move-result-object v10

    const-string v0, "material_meta"

    invoke-virtual {v10, v0, p2}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->Fj(Ljava/lang/String;Ljava/lang/Object;)V

    const-string v0, "ad_slot"

    invoke-virtual {v10, v0, v6}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->Fj(Ljava/lang/String;Ljava/lang/Object;)V

    new-instance v9, Lcom/bytedance/sdk/openadsdk/component/reward/UYd$5;

    move-object v0, v9

    move-object v1, p0

    move-object v2, p3

    move/from16 v3, p5

    move-object/from16 v4, p6

    move/from16 v5, p7

    move-object/from16 v6, p4

    move-object v7, p1

    invoke-direct/range {v0 .. v7}, Lcom/bytedance/sdk/openadsdk/component/reward/UYd$5;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/UYd;Lcom/bytedance/sdk/openadsdk/component/reward/JW;ZLcom/bytedance/sdk/openadsdk/component/reward/UYd$Fj;ZLcom/bytedance/sdk/openadsdk/AdSlot;Lcom/bytedance/sdk/openadsdk/core/model/Fj;)V

    invoke-static {v10, v9}, Lcom/bytedance/sdk/openadsdk/core/video/eV/Fj;->Fj(Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;Lcom/bykv/vk/openvk/component/video/api/Ubf/Fj$Fj;)V

    goto :goto_1

    :cond_3
    iget-object v0, v8, Lcom/bytedance/sdk/openadsdk/component/reward/UYd;->ex:Landroid/content/Context;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/rAx;->Fj(Landroid/content/Context;)Lcom/bytedance/sdk/openadsdk/component/reward/rAx;

    move-result-object v10

    new-instance v11, Lcom/bytedance/sdk/openadsdk/component/reward/UYd$6;

    move-object v0, v11

    move-object v1, p0

    move/from16 v2, p5

    move-object v3, p3

    move-object/from16 v4, p6

    move/from16 v5, p7

    move-object/from16 v6, p4

    move-object v7, p1

    invoke-direct/range {v0 .. v7}, Lcom/bytedance/sdk/openadsdk/component/reward/UYd$6;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/UYd;ZLcom/bytedance/sdk/openadsdk/component/reward/JW;Lcom/bytedance/sdk/openadsdk/component/reward/UYd$Fj;ZLcom/bytedance/sdk/openadsdk/AdSlot;Lcom/bytedance/sdk/openadsdk/core/model/Fj;)V

    invoke-virtual {v10, p2, v11}, Lcom/bytedance/sdk/openadsdk/component/reward/rAx;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/component/reward/rAx$Fj;)V

    goto :goto_1

    :cond_4
    iget-object v1, v8, Lcom/bytedance/sdk/openadsdk/component/reward/UYd;->ex:Landroid/content/Context;

    invoke-static {v1}, Lcom/bytedance/sdk/openadsdk/component/reward/rAx;->Fj(Landroid/content/Context;)Lcom/bytedance/sdk/openadsdk/component/reward/rAx;

    move-result-object v1

    invoke-virtual {v1, v6, p1}, Lcom/bytedance/sdk/openadsdk/component/reward/rAx;->Fj(Lcom/bytedance/sdk/openadsdk/AdSlot;Lcom/bytedance/sdk/openadsdk/core/model/Fj;)V

    :cond_5
    if-eqz v0, :cond_6

    invoke-virtual {p3}, Lcom/bytedance/sdk/openadsdk/component/reward/JW;->Fj()Lcom/bytedance/sdk/openadsdk/component/reward/BcC;

    move-result-object v0

    invoke-interface {v4, v0}, Lcom/bytedance/sdk/openadsdk/api/PAGLoadListener;->onAdLoaded(Ljava/lang/Object;)V

    :cond_6
    :goto_1
    return-void
.end method

.method private Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/AdSlot;Lcom/bytedance/sdk/openadsdk/component/reward/UYd$Fj;Lcom/bytedance/sdk/openadsdk/component/reward/JW;Z)V
    .locals 3

    if-eqz p3, :cond_0

    const/4 v0, 0x1

    if-nez p5, :cond_1

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/JU;->eV()Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;

    move-result-object v1

    invoke-interface {v1}, Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;->mE()I

    move-result v1

    if-ne v1, v0, :cond_0

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :cond_1
    :goto_0
    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/core/model/mE;->rAx(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v1

    if-nez v1, :cond_2

    sget v1, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v2, 0x17

    if-lt v1, v2, :cond_2

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->aGk()I

    move-result v0

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/CacheDirFactory;->getICacheDir(I)Lcom/bykv/vk/openvk/component/video/api/Fj/ex;

    move-result-object v0

    invoke-interface {v0}, Lcom/bykv/vk/openvk/component/video/api/Fj/ex;->Fj()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0, p1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->Fj(Ljava/lang/String;Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Lcom/bytedance/sdk/openadsdk/core/video/Fj/ex;

    move-result-object v0

    const-string v1, "material_meta"

    invoke-virtual {v0, v1, p1}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->Fj(Ljava/lang/String;Ljava/lang/Object;)V

    const-string p1, "ad_slot"

    invoke-virtual {v0, p1, p2}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->Fj(Ljava/lang/String;Ljava/lang/Object;)V

    new-instance p1, Lcom/bytedance/sdk/openadsdk/component/reward/UYd$2;

    invoke-direct {p1, p0, p3, p5, p4}, Lcom/bytedance/sdk/openadsdk/component/reward/UYd$2;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/UYd;Lcom/bytedance/sdk/openadsdk/component/reward/UYd$Fj;ZLcom/bytedance/sdk/openadsdk/component/reward/JW;)V

    invoke-static {v0, p1}, Lcom/bytedance/sdk/openadsdk/core/video/eV/Fj;->Fj(Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;Lcom/bykv/vk/openvk/component/video/api/Ubf/Fj$Fj;)V

    goto :goto_1

    :cond_2
    if-eqz v0, :cond_3

    invoke-virtual {p4}, Lcom/bytedance/sdk/openadsdk/component/reward/JW;->Fj()Lcom/bytedance/sdk/openadsdk/component/reward/BcC;

    move-result-object p1

    invoke-interface {p3, p1}, Lcom/bytedance/sdk/openadsdk/api/PAGLoadListener;->onAdLoaded(Ljava/lang/Object;)V

    :cond_3
    :goto_1
    return-void
.end method

.method public static synthetic ex(Lcom/bytedance/sdk/openadsdk/component/reward/UYd;)Lcom/bytedance/sdk/component/svN/BcC;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/UYd;->Ubf:Lcom/bytedance/sdk/component/svN/BcC;

    return-object p0
.end method

.method private ex()V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/UYd;->hjc:Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-virtual {v0}, Ljava/util/concurrent/atomic/AtomicBoolean;->get()Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/UYd;->hjc:Ljava/util/concurrent/atomic/AtomicBoolean;

    const/4 v1, 0x1

    invoke-virtual {v0, v1}, Ljava/util/concurrent/atomic/AtomicBoolean;->set(Z)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/UYd;->WR:Lcom/bytedance/sdk/component/utils/cB$Fj;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/UYd;->ex:Landroid/content/Context;

    invoke-static {v0, v1}, Lcom/bytedance/sdk/component/utils/cB;->Fj(Lcom/bytedance/sdk/component/utils/cB$Fj;Landroid/content/Context;)V

    return-void
.end method

.method private ex(Lcom/bytedance/sdk/openadsdk/AdSlot;Lcom/bytedance/sdk/openadsdk/common/hjc;)V
    .locals 10

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/AdSlot;->getBidAdm()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    const/4 v1, 0x0

    if-eqz v0, :cond_4

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/UYd;->ex:Landroid/content/Context;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/rAx;->Fj(Landroid/content/Context;)Lcom/bytedance/sdk/openadsdk/component/reward/rAx;

    move-result-object v0

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/AdSlot;->getCodeId()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Lcom/bytedance/sdk/openadsdk/component/reward/rAx;->ex(Ljava/lang/String;)Lcom/bytedance/sdk/openadsdk/core/model/Fj;

    move-result-object v0

    if-eqz v0, :cond_4

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Fj;->eV()Z

    move-result v2

    if-eqz v2, :cond_4

    new-instance v2, Lcom/bytedance/sdk/openadsdk/component/reward/JW;

    iget-object v3, p0, Lcom/bytedance/sdk/openadsdk/component/reward/UYd;->ex:Landroid/content/Context;

    invoke-direct {v2, v3, v0, p1}, Lcom/bytedance/sdk/openadsdk/component/reward/JW;-><init>(Landroid/content/Context;Lcom/bytedance/sdk/openadsdk/core/model/Fj;Lcom/bytedance/sdk/openadsdk/AdSlot;)V

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Fj;->WR()Z

    move-result v3

    if-nez v3, :cond_0

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Fj;->Ubf()Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    move-result-object v3

    invoke-static {v3}, Lcom/bytedance/sdk/openadsdk/core/model/mE;->rAx(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v3

    if-nez v3, :cond_0

    invoke-virtual {v2}, Lcom/bytedance/sdk/openadsdk/component/reward/JW;->ex()V

    :cond_0
    if-eqz p2, :cond_2

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Fj;->WR()Z

    move-result v3

    if-nez v3, :cond_1

    instance-of v3, p2, Lcom/bytedance/sdk/openadsdk/api/reward/PAGRewardedAdLoadListener;

    if-eqz v3, :cond_1

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/JU;->eV()Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;

    move-result-object v3

    invoke-interface {v3}, Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;->mE()I

    move-result v3

    if-nez v3, :cond_1

    move-object v3, p2

    check-cast v3, Lcom/bytedance/sdk/openadsdk/api/reward/PAGRewardedAdLoadListener;

    invoke-virtual {v2}, Lcom/bytedance/sdk/openadsdk/component/reward/JW;->Fj()Lcom/bytedance/sdk/openadsdk/component/reward/BcC;

    move-result-object v4

    invoke-interface {v3, v4}, Lcom/bytedance/sdk/openadsdk/api/PAGLoadListener;->onAdLoaded(Ljava/lang/Object;)V

    :cond_1
    new-instance v9, Lcom/bytedance/sdk/openadsdk/component/reward/UYd$Fj;

    const/4 v3, 0x0

    invoke-direct {v9, p2, v0, v3}, Lcom/bytedance/sdk/openadsdk/component/reward/UYd$Fj;-><init>(Lcom/bytedance/sdk/openadsdk/common/hjc;Lcom/bytedance/sdk/openadsdk/core/model/Fj;Lcom/bytedance/sdk/openadsdk/component/reward/UYd$1;)V

    const/4 p2, 0x0

    :goto_0
    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Fj;->hjc()Ljava/util/List;

    move-result-object v3

    invoke-interface {v3}, Ljava/util/List;->size()I

    move-result v3

    if-ge p2, v3, :cond_2

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Fj;->hjc()Ljava/util/List;

    move-result-object v3

    invoke-interface {v3, p2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    move-object v4, v3

    check-cast v4, Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Fj;->WR()Z

    move-result v8

    move-object v3, p0

    move-object v5, p1

    move-object v6, v9

    move-object v7, v2

    invoke-direct/range {v3 .. v8}, Lcom/bytedance/sdk/openadsdk/component/reward/UYd;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/AdSlot;Lcom/bytedance/sdk/openadsdk/component/reward/UYd$Fj;Lcom/bytedance/sdk/openadsdk/component/reward/JW;Z)V

    add-int/lit8 p2, p2, 0x1

    goto :goto_0

    :cond_2
    :goto_1
    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Fj;->hjc()Ljava/util/List;

    move-result-object p1

    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result p1

    if-ge v1, p1, :cond_3

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Fj;->hjc()Ljava/util/List;

    move-result-object p1

    invoke-interface {p1, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/video/ex/Fj;->Fj()Lcom/bytedance/sdk/openadsdk/core/video/ex/Fj;

    move-result-object p2

    new-instance v2, Lcom/bytedance/sdk/openadsdk/component/reward/UYd$1;

    invoke-direct {v2, p0}, Lcom/bytedance/sdk/openadsdk/component/reward/UYd$1;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/UYd;)V

    invoke-virtual {p2, p1, v2}, Lcom/bytedance/sdk/openadsdk/core/video/ex/Fj;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/core/video/ex/Fj$Fj;)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_1

    :cond_3
    return-void

    :cond_4
    invoke-direct {p0, p1, v1, p2}, Lcom/bytedance/sdk/openadsdk/component/reward/UYd;->Fj(Lcom/bytedance/sdk/openadsdk/AdSlot;ZLcom/bytedance/sdk/openadsdk/common/hjc;)V

    return-void
.end method

.method public static synthetic hjc(Lcom/bytedance/sdk/openadsdk/component/reward/UYd;)Ljava/util/List;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/UYd;->eV:Ljava/util/List;

    return-object p0
.end method

.method private hjc()V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/UYd;->hjc:Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-virtual {v0}, Ljava/util/concurrent/atomic/AtomicBoolean;->get()Z

    move-result v0

    if-nez v0, :cond_0

    return-void

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/UYd;->hjc:Ljava/util/concurrent/atomic/AtomicBoolean;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/util/concurrent/atomic/AtomicBoolean;->set(Z)V

    :try_start_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/UYd;->WR:Lcom/bytedance/sdk/component/utils/cB$Fj;

    invoke-static {v0}, Lcom/bytedance/sdk/component/utils/cB;->Fj(Lcom/bytedance/sdk/component/utils/cB$Fj;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    :catch_0
    return-void
.end method


# virtual methods
.method public Fj()V
    .locals 1

    :try_start_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/UYd;->ex:Landroid/content/Context;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/rAx;->Fj(Landroid/content/Context;)Lcom/bytedance/sdk/openadsdk/component/reward/rAx;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/rAx;->Fj()V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :catchall_0
    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/openadsdk/AdSlot;)V
    .locals 2

    if-eqz p1, :cond_2

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/AdSlot;->getCodeId()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/UYd;->ex:Landroid/content/Context;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/rAx;->Fj(Landroid/content/Context;)Lcom/bytedance/sdk/openadsdk/component/reward/rAx;

    move-result-object v0

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/AdSlot;->getCodeId()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/component/reward/rAx;->ex(Ljava/lang/String;)Lcom/bytedance/sdk/openadsdk/core/model/Fj;

    move-result-object v0

    if-eqz v0, :cond_1

    return-void

    :cond_1
    invoke-virtual {p0, p1}, Lcom/bytedance/sdk/openadsdk/component/reward/UYd;->ex(Lcom/bytedance/sdk/openadsdk/AdSlot;)V

    :cond_2
    :goto_0
    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/openadsdk/AdSlot;Lcom/bytedance/sdk/openadsdk/common/hjc;)V
    .locals 0

    invoke-direct {p0, p1, p2}, Lcom/bytedance/sdk/openadsdk/component/reward/UYd;->ex(Lcom/bytedance/sdk/openadsdk/AdSlot;Lcom/bytedance/sdk/openadsdk/common/hjc;)V

    return-void
.end method

.method public Fj(Ljava/lang/String;)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/UYd;->ex:Landroid/content/Context;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/rAx;->Fj(Landroid/content/Context;)Lcom/bytedance/sdk/openadsdk/component/reward/rAx;

    move-result-object v0

    invoke-virtual {v0, p1}, Lcom/bytedance/sdk/openadsdk/component/reward/rAx;->Fj(Ljava/lang/String;)V

    return-void
.end method

.method public ex(Lcom/bytedance/sdk/openadsdk/AdSlot;)V
    .locals 2

    if-eqz p1, :cond_0

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/AdSlot;->getBidAdm()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-nez v0, :cond_0

    return-void

    :cond_0
    const/4 v0, 0x1

    const/4 v1, 0x0

    invoke-direct {p0, p1, v0, v1}, Lcom/bytedance/sdk/openadsdk/component/reward/UYd;->Fj(Lcom/bytedance/sdk/openadsdk/AdSlot;ZLcom/bytedance/sdk/openadsdk/common/hjc;)V

    return-void
.end method

.method public finalize()V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Throwable;
        }
    .end annotation

    invoke-super {p0}, Ljava/lang/Object;->finalize()V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/UYd;->Ubf:Lcom/bytedance/sdk/component/svN/BcC;

    if-eqz v0, :cond_0

    :try_start_0
    invoke-static {}, Lcom/bytedance/sdk/component/utils/BcC;->Fj()Landroid/os/Handler;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/UYd;->Ubf:Lcom/bytedance/sdk/component/svN/BcC;

    invoke-virtual {v0, v1}, Landroid/os/Handler;->removeCallbacks(Ljava/lang/Runnable;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    :catch_0
    const/4 v0, 0x0

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/UYd;->Ubf:Lcom/bytedance/sdk/component/svN/BcC;

    :cond_0
    invoke-direct {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/UYd;->hjc()V

    return-void
.end method
