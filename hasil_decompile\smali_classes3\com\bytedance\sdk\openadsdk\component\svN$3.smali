.class Lcom/bytedance/sdk/openadsdk/component/svN$3;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/openadsdk/component/WR$hjc;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/svN;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/AdSlot;Z)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Z

.field final synthetic ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

.field final synthetic hjc:Lcom/bytedance/sdk/openadsdk/component/svN;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/svN;ZLcom/bytedance/sdk/openadsdk/core/model/Ql;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/svN$3;->hjc:Lcom/bytedance/sdk/openadsdk/component/svN;

    iput-boolean p2, p0, Lcom/bytedance/sdk/openadsdk/component/svN$3;->Fj:Z

    iput-object p3, p0, Lcom/bytedance/sdk/openadsdk/component/svN$3;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj()V
    .locals 4

    iget-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/component/svN$3;->Fj:Z

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/svN$3;->hjc:Lcom/bytedance/sdk/openadsdk/component/svN;

    const/4 v1, 0x4

    invoke-static {v0, v1}, Lcom/bytedance/sdk/openadsdk/component/svN;->Fj(Lcom/bytedance/sdk/openadsdk/component/svN;I)I

    new-instance v0, Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;

    const/16 v1, 0x64

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/component/svN$3;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    const/4 v3, 0x1

    invoke-direct {v0, v3, v1, v2}, Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;-><init>(IILcom/bytedance/sdk/openadsdk/core/model/Ql;)V

    invoke-virtual {v0, v3}, Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;->Fj(Z)V

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/svN$3;->hjc:Lcom/bytedance/sdk/openadsdk/component/svN;

    invoke-static {v1, v0}, Lcom/bytedance/sdk/openadsdk/component/svN;->Fj(Lcom/bytedance/sdk/openadsdk/component/svN;Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;)V

    :cond_0
    return-void
.end method

.method public Fj(ILjava/lang/String;)V
    .locals 4

    iget-boolean p1, p0, Lcom/bytedance/sdk/openadsdk/component/svN$3;->Fj:Z

    if-eqz p1, :cond_0

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/svN$3;->hjc:Lcom/bytedance/sdk/openadsdk/component/svN;

    const/4 p2, 0x5

    invoke-static {p1, p2}, Lcom/bytedance/sdk/openadsdk/component/svN;->Fj(Lcom/bytedance/sdk/openadsdk/component/svN;I)I

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/svN$3;->hjc:Lcom/bytedance/sdk/openadsdk/component/svN;

    new-instance p2, Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;

    const/16 v0, 0x64

    const/16 v1, 0x2713

    invoke-static {v1}, Lcom/bytedance/sdk/openadsdk/core/svN;->Fj(I)Ljava/lang/String;

    move-result-object v2

    const/4 v3, 0x2

    invoke-direct {p2, v3, v0, v1, v2}, Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;-><init>(IIILjava/lang/String;)V

    invoke-static {p1, p2}, Lcom/bytedance/sdk/openadsdk/component/svN;->Fj(Lcom/bytedance/sdk/openadsdk/component/svN;Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;)V

    :cond_0
    return-void
.end method
