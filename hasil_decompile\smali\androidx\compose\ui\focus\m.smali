.class public interface abstract Landroidx/compose/ui/focus/m;
.super Ljava/lang/Object;


# annotations
.annotation runtime Lkotlin/Metadata;
.end annotation


# virtual methods
.method public abstract getNext()Landroidx/compose/ui/focus/FocusRequester;
.end method

.method public abstract l()Landroidx/compose/ui/focus/FocusRequester;
.end method

.method public abstract m()Landroidx/compose/ui/focus/FocusRequester;
.end method

.method public abstract o()Landroidx/compose/ui/focus/FocusRequester;
.end method

.method public abstract p()Landroidx/compose/ui/focus/FocusRequester;
.end method

.method public abstract q()Lkotlin/jvm/functions/Function1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlin/jvm/functions/Function1<",
            "Landroidx/compose/ui/focus/b;",
            "Landroidx/compose/ui/focus/FocusRequester;",
            ">;"
        }
    .end annotation
.end method

.method public abstract r()Landroidx/compose/ui/focus/FocusRequester;
.end method

.method public abstract s()Landroidx/compose/ui/focus/FocusRequester;
.end method

.method public abstract t(Z)V
.end method

.method public abstract u()Lkotlin/jvm/functions/Function1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlin/jvm/functions/Function1<",
            "Landroidx/compose/ui/focus/b;",
            "Landroidx/compose/ui/focus/FocusRequester;",
            ">;"
        }
    .end annotation
.end method

.method public abstract v()Z
.end method

.method public abstract w()Landroidx/compose/ui/focus/FocusRequester;
.end method
