<?xml version="1.0" encoding="utf-8"?>
<layer-list
  xmlns:android="http://schemas.android.com/apk/res/android">
    <item>
        <shape android:shape="rectangle">
            <padding android:top="1.0dip" />
            <solid android:color="#00cccccc" />
            <corners android:topLeftRadius="8.0dip" android:topRightRadius="8.0dip" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <padding android:top="1.0dip" />
            <solid android:color="#10cccccc" />
            <corners android:radius="5.0dip" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <padding android:top="1.0dip" />
            <solid android:color="#20cccccc" />
            <corners android:topLeftRadius="8.0dip" android:topRightRadius="8.0dip" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <padding android:top="1.0dip" />
            <solid android:color="#30cccccc" />
            <corners android:topLeftRadius="8.0dip" android:topRightRadius="8.0dip" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <padding android:top="1.0dip" />
            <solid android:color="#50cccccc" />
            <corners android:topLeftRadius="8.0dip" android:topRightRadius="8.0dip" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle" android:useLevel="false">
            <solid android:color="@color/cl38" />
            <corners android:topLeftRadius="8.0dip" android:topRightRadius="8.0dip" />
            <size android:height="32.0dip" android:width="32.0dip" />
        </shape>
    </item>
</layer-list>
