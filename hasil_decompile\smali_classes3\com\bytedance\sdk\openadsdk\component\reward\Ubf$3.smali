.class Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$3;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/openadsdk/core/Ql$Fj;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;->Fj(Lcom/bytedance/sdk/openadsdk/AdSlot;ZLcom/bytedance/sdk/openadsdk/utils/lv;Lcom/bytedance/sdk/openadsdk/common/hjc;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Z

.field final synthetic Ubf:Lcom/bytedance/sdk/openadsdk/utils/lv;

.field final synthetic WR:Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;

.field final synthetic eV:J

.field final synthetic ex:Lcom/bytedance/sdk/openadsdk/common/hjc;

.field final synthetic hjc:Lcom/bytedance/sdk/openadsdk/AdSlot;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;ZLcom/bytedance/sdk/openadsdk/common/hjc;Lcom/bytedance/sdk/openadsdk/AdSlot;JLcom/bytedance/sdk/openadsdk/utils/lv;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$3;->WR:Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;

    iput-boolean p2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$3;->Fj:Z

    iput-object p3, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$3;->ex:Lcom/bytedance/sdk/openadsdk/common/hjc;

    iput-object p4, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$3;->hjc:Lcom/bytedance/sdk/openadsdk/AdSlot;

    iput-wide p5, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$3;->eV:J

    iput-object p7, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$3;->Ubf:Lcom/bytedance/sdk/openadsdk/utils/lv;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj(ILjava/lang/String;)V
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$3;->Fj:Z

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$3;->ex:Lcom/bytedance/sdk/openadsdk/common/hjc;

    if-eqz v0, :cond_0

    invoke-interface {v0, p1, p2}, Lcom/bytedance/sdk/openadsdk/common/hjc;->onError(ILjava/lang/String;)V

    :cond_0
    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/openadsdk/core/model/Fj;Lcom/bytedance/sdk/openadsdk/core/model/ex;)V
    .locals 11

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/model/Fj;->hjc()Ljava/util/List;

    move-result-object v0

    if-eqz v0, :cond_3

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/model/Fj;->hjc()Ljava/util/List;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_3

    new-instance p2, Lcom/bytedance/sdk/openadsdk/component/reward/Tc;

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$3;->WR:Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;)Landroid/content/Context;

    move-result-object v0

    invoke-direct {p2, v0, p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Tc;-><init>(Landroid/content/Context;Lcom/bytedance/sdk/openadsdk/core/model/Fj;)V

    iget-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$3;->Fj:Z

    if-nez v0, :cond_1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$3;->hjc:Lcom/bytedance/sdk/openadsdk/AdSlot;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/AdSlot;->getBidAdm()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-nez v0, :cond_0

    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v0

    iget-wide v2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$3;->eV:J

    sub-long/2addr v0, v2

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/model/Fj;->Ubf()Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    move-result-object v2

    invoke-static {v2, v0, v1}, Lcom/bytedance/sdk/openadsdk/Ko/hjc;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;J)V

    :cond_0
    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/model/Fj;->WR()Z

    move-result v0

    if-nez v0, :cond_1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$3;->ex:Lcom/bytedance/sdk/openadsdk/common/hjc;

    instance-of v0, v0, Lcom/bytedance/sdk/openadsdk/api/interstitial/PAGInterstitialAdLoadListener;

    if-eqz v0, :cond_1

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/JU;->eV()Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;

    move-result-object v0

    invoke-interface {v0}, Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;->mE()I

    move-result v0

    if-nez v0, :cond_1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$3;->ex:Lcom/bytedance/sdk/openadsdk/common/hjc;

    check-cast v0, Lcom/bytedance/sdk/openadsdk/api/interstitial/PAGInterstitialAdLoadListener;

    invoke-virtual {p2}, Lcom/bytedance/sdk/openadsdk/component/reward/Tc;->Fj()Lcom/bytedance/sdk/openadsdk/component/reward/WR;

    move-result-object v1

    invoke-interface {v0, v1}, Lcom/bytedance/sdk/openadsdk/api/PAGLoadListener;->onAdLoaded(Ljava/lang/Object;)V

    :cond_1
    new-instance v0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$Fj;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$3;->ex:Lcom/bytedance/sdk/openadsdk/common/hjc;

    const/4 v2, 0x0

    invoke-direct {v0, v1, p1, v2}, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$Fj;-><init>(Lcom/bytedance/sdk/openadsdk/common/hjc;Lcom/bytedance/sdk/openadsdk/core/model/Fj;Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$1;)V

    const/4 v1, 0x0

    const/4 v10, 0x0

    :goto_0
    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/model/Fj;->hjc()Ljava/util/List;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    if-ge v10, v1, :cond_2

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$3;->WR:Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/model/Fj;->hjc()Ljava/util/List;

    move-result-object v2

    invoke-interface {v2, v10}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    move-object v3, v2

    check-cast v3, Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    iget-object v5, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$3;->hjc:Lcom/bytedance/sdk/openadsdk/AdSlot;

    iget-boolean v6, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$3;->Fj:Z

    iget-object v7, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$3;->Ubf:Lcom/bytedance/sdk/openadsdk/utils/lv;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/model/Fj;->WR()Z

    move-result v9

    move-object v2, p1

    move-object v4, p2

    move-object v8, v0

    invoke-static/range {v1 .. v9}, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;Lcom/bytedance/sdk/openadsdk/core/model/Fj;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/component/reward/Tc;Lcom/bytedance/sdk/openadsdk/AdSlot;ZLcom/bytedance/sdk/openadsdk/utils/lv;Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$Fj;Z)V

    add-int/lit8 v10, v10, 0x1

    goto :goto_0

    :cond_2
    return-void

    :cond_3
    iget-boolean p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$3;->Fj:Z

    if-nez p1, :cond_4

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$3;->ex:Lcom/bytedance/sdk/openadsdk/common/hjc;

    if-eqz p1, :cond_4

    const/4 v0, -0x3

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/svN;->Fj(I)Ljava/lang/String;

    move-result-object v1

    invoke-interface {p1, v0, v1}, Lcom/bytedance/sdk/openadsdk/common/hjc;->onError(ILjava/lang/String;)V

    invoke-virtual {p2, v0}, Lcom/bytedance/sdk/openadsdk/core/model/ex;->Fj(I)V

    invoke-static {p2}, Lcom/bytedance/sdk/openadsdk/core/model/ex;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/ex;)V

    :cond_4
    return-void
.end method
