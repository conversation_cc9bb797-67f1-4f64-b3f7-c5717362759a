.class Lcom/bytedance/sdk/openadsdk/core/model/JW$7;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/openadsdk/ex/mSE;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/core/model/JW;->BcC()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/core/model/JW;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/core/model/JW;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/JW$7;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/JW;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj(I)V
    .locals 8

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/JW$7;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/JW;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/model/JW;->svN(Lcom/bytedance/sdk/openadsdk/core/model/JW;)I

    move-result v1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/JW$7;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/JW;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/model/JW;->BcC(Lcom/bytedance/sdk/openadsdk/core/model/JW;)I

    move-result v2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/JW$7;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/JW;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/model/JW;->mSE(Lcom/bytedance/sdk/openadsdk/core/model/JW;)I

    move-result v3

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/JW$7;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/JW;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/model/JW;->Ko(Lcom/bytedance/sdk/openadsdk/core/model/JW;)I

    move-result v0

    iget-object v4, p0, Lcom/bytedance/sdk/openadsdk/core/model/JW$7;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/JW;

    invoke-static {v4}, Lcom/bytedance/sdk/openadsdk/core/model/JW;->mSE(Lcom/bytedance/sdk/openadsdk/core/model/JW;)I

    move-result v4

    sub-int v4, v0, v4

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/JW$7;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/JW;

    iget-object v5, v0, Lcom/bytedance/sdk/openadsdk/core/model/JW;->svN:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/model/JW;->eV(Lcom/bytedance/sdk/openadsdk/core/model/JW;)Ljava/lang/String;

    move-result-object v6

    move v7, p1

    invoke-static/range {v1 .. v7}, Lcom/bytedance/sdk/openadsdk/ex/hjc$Fj;->Fj(IIIILcom/bytedance/sdk/openadsdk/core/model/Ql;Ljava/lang/String;I)V

    return-void
.end method
