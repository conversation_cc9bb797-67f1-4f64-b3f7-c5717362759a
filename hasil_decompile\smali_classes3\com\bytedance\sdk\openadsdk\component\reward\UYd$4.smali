.class Lcom/bytedance/sdk/openadsdk/component/reward/UYd$4;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/openadsdk/core/video/ex/Fj$Fj;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/reward/UYd;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Fj;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/component/reward/JW;Lcom/bytedance/sdk/openadsdk/AdSlot;ZLcom/bytedance/sdk/openadsdk/component/reward/UYd$Fj;Z)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/component/reward/UYd;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/reward/UYd;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/UYd$4;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/UYd;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
