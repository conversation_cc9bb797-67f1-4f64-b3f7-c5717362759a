.class public final synthetic Landroidx/compose/runtime/j;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/util/Comparator;


# direct methods
.method public synthetic constructor <init>()V
    .locals 0

    invoke-direct {p0}, <PERSON>java/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final compare(Ljava/lang/Object;Ljava/lang/Object;)I
    .locals 0

    check-cast p1, Landroidx/compose/runtime/o0;

    check-cast p2, Landroidx/compose/runtime/o0;

    invoke-static {p1, p2}, Landroidx/compose/runtime/k;->a(Landroidx/compose/runtime/o0;Landroidx/compose/runtime/o0;)I

    move-result p1

    return p1
.end method
