.class public final Lcom/transsion/payment/lib/dialog/a;
.super Landroidx/lifecycle/u0;


# annotations
.annotation runtime Lkotlin/Metadata;
.end annotation


# instance fields
.field public a:Lcom/transsion/payment/lib/b;


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Landroidx/lifecycle/u0;-><init>()V

    return-void
.end method


# virtual methods
.method public final b()Lcom/transsion/payment/lib/b;
    .locals 1

    iget-object v0, p0, Lcom/transsion/payment/lib/dialog/a;->a:Lcom/transsion/payment/lib/b;

    return-object v0
.end method

.method public final c(Lcom/transsion/payment/lib/b;)V
    .locals 0

    iput-object p1, p0, Lcom/transsion/payment/lib/dialog/a;->a:Lcom/transsion/payment/lib/b;

    return-void
.end method
