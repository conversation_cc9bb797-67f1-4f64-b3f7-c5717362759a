.class public Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;
.super Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;


# instance fields
.field private final Af:Lcom/bytedance/sdk/component/svN/BcC;

.field private BcC:Landroid/content/Context;

.field private JU:Lcom/bytedance/sdk/openadsdk/core/Vq;

.field private final JW:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Lcom/com/bytedance/overseas/sdk/Fj/hjc;",
            ">;"
        }
    .end annotation
.end field

.field private Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

.field private Ql:Lcom/bytedance/sdk/component/adexpress/ex/svN;

.field private Tc:Lcom/bytedance/sdk/openadsdk/ex/Ko;

.field private UYd:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

.field private dG:Ljava/lang/String;

.field private final mC:Ljava/lang/Runnable;

.field private mE:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/BcC;

.field private mSE:Ljava/lang/String;

.field private rAx:Lorg/json/JSONObject;

.field private rS:Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;

.field svN:Lcom/bytedance/sdk/openadsdk/utils/Fj;

.field private volatile vYf:I


# direct methods
.method public constructor <init>(Landroid/content/Context;Lcom/bytedance/sdk/component/adexpress/ex/dG;Lcom/bytedance/sdk/component/adexpress/theme/ThemeStatusBroadcastReceiver;Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;Lcom/bytedance/sdk/openadsdk/core/model/Ql;)V
    .locals 2

    invoke-direct {p0, p1, p2, p3}, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;-><init>(Landroid/content/Context;Lcom/bytedance/sdk/component/adexpress/ex/dG;Lcom/bytedance/sdk/component/adexpress/theme/ThemeStatusBroadcastReceiver;)V

    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    invoke-static {v0}, Ljava/util/Collections;->synchronizedMap(Ljava/util/Map;)Ljava/util/Map;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->JW:Ljava/util/Map;

    const/4 v0, 0x0

    iput v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->vYf:I

    new-instance v0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql$1;

    const-string v1, "webviewrender_template"

    invoke-direct {v0, p0, v1}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql$1;-><init>(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;Ljava/lang/String;)V

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->Af:Lcom/bytedance/sdk/component/svN/BcC;

    new-instance v0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql$2;

    invoke-direct {v0, p0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql$2;-><init>(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;)V

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->mC:Ljava/lang/Runnable;

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->hjc:Lcom/bytedance/sdk/component/widget/SSWebView;

    if-nez v0, :cond_0

    return-void

    :cond_0
    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->BcC:Landroid/content/Context;

    invoke-virtual {p2}, Lcom/bytedance/sdk/component/adexpress/ex/dG;->eV()Ljava/lang/String;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->mSE:Ljava/lang/String;

    iput-object p5, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    iput-object p4, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->UYd:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    invoke-virtual {p3, p0}, Lcom/bytedance/sdk/component/adexpress/theme/ThemeStatusBroadcastReceiver;->Fj(Lcom/bytedance/sdk/component/adexpress/theme/Fj;)V

    invoke-direct {p0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->Ql()V

    return-void
.end method

.method public static synthetic BcC(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;)Lcom/bytedance/sdk/component/adexpress/ex/svN;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->Ql:Lcom/bytedance/sdk/component/adexpress/ex/svN;

    return-object p0
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;Lcom/bytedance/sdk/component/adexpress/Fj/hjc/ex;)Lcom/bytedance/sdk/component/adexpress/Fj/hjc/ex;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->Ubf:Lcom/bytedance/sdk/component/adexpress/Fj/hjc/ex;

    return-object p1
.end method

.method public static Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Ljava/lang/String;
    .locals 0

    if-eqz p0, :cond_0

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->uM()Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;

    move-result-object p0

    if-eqz p0, :cond_0

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;->UYd()Ljava/lang/String;

    move-result-object p0

    goto :goto_0

    :cond_0
    const/4 p0, 0x0

    :goto_0
    invoke-static {p0}, Lcom/bytedance/sdk/component/adexpress/Fj/ex/ex;->eV(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;)Ljava/util/concurrent/atomic/AtomicBoolean;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->WR:Ljava/util/concurrent/atomic/AtomicBoolean;

    return-object p0
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;Lorg/json/JSONObject;)Lorg/json/JSONObject;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->rAx:Lorg/json/JSONObject;

    return-object p1
.end method

.method private Fj(Lcom/bytedance/sdk/component/widget/SSWebView;)V
    .locals 4

    if-nez p1, :cond_0

    return-void

    :cond_0
    :try_start_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->BcC:Landroid/content/Context;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/widget/Fj/hjc;->Fj(Landroid/content/Context;)Lcom/bytedance/sdk/openadsdk/core/widget/Fj/hjc;

    move-result-object v0

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/core/widget/Fj/hjc;->Fj(Z)Lcom/bytedance/sdk/openadsdk/core/widget/Fj/hjc;

    move-result-object v0

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/widget/SSWebView;->getWebView()Landroid/webkit/WebView;

    move-result-object v2

    invoke-virtual {v0, v2}, Lcom/bytedance/sdk/openadsdk/core/widget/Fj/hjc;->Fj(Landroid/webkit/WebView;)V

    invoke-virtual {p1, v1}, Landroid/view/View;->setVerticalScrollBarEnabled(Z)V

    invoke-virtual {p1, v1}, Landroid/view/View;->setHorizontalScrollBarEnabled(Z)V

    const/4 v0, 0x1

    invoke-virtual {p1, v0}, Lcom/bytedance/sdk/component/widget/SSWebView;->Fj(Z)V

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/widget/SSWebView;->Ko()V

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/widget/SSWebView;->getWebView()Landroid/webkit/WebView;

    move-result-object v2

    const/16 v3, 0x1710

    invoke-static {v2, v3}, Lcom/bytedance/sdk/openadsdk/utils/dG;->Fj(Landroid/webkit/WebView;I)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {p1, v2}, Lcom/bytedance/sdk/component/widget/SSWebView;->setUserAgentString(Ljava/lang/String;)V

    invoke-virtual {p1, v1}, Lcom/bytedance/sdk/component/widget/SSWebView;->setMixedContentMode(I)V

    invoke-virtual {p1, v0}, Lcom/bytedance/sdk/component/widget/SSWebView;->setJavaScriptEnabled(Z)V

    invoke-virtual {p1, v0}, Lcom/bytedance/sdk/component/widget/SSWebView;->setJavaScriptCanOpenWindowsAutomatically(Z)V

    invoke-virtual {p1, v0}, Lcom/bytedance/sdk/component/widget/SSWebView;->setDomStorageEnabled(Z)V

    invoke-virtual {p1, v0}, Lcom/bytedance/sdk/component/widget/SSWebView;->setDatabaseEnabled(Z)V

    invoke-virtual {p1, v0}, Lcom/bytedance/sdk/component/widget/SSWebView;->setAppCacheEnabled(Z)V

    invoke-virtual {p1, v1}, Lcom/bytedance/sdk/component/widget/SSWebView;->setAllowFileAccess(Z)V

    invoke-virtual {p1, v0}, Lcom/bytedance/sdk/component/widget/SSWebView;->setSupportZoom(Z)V

    invoke-virtual {p1, v0}, Lcom/bytedance/sdk/component/widget/SSWebView;->setBuiltInZoomControls(Z)V

    sget-object v1, Landroid/webkit/WebSettings$LayoutAlgorithm;->NARROW_COLUMNS:Landroid/webkit/WebSettings$LayoutAlgorithm;

    invoke-virtual {p1, v1}, Lcom/bytedance/sdk/component/widget/SSWebView;->setLayoutAlgorithm(Landroid/webkit/WebSettings$LayoutAlgorithm;)V

    invoke-virtual {p1, v0}, Lcom/bytedance/sdk/component/widget/SSWebView;->setUseWideViewPort(Z)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return-void

    :catch_0
    move-exception p1

    const-string v0, "TTAD.WebViewRender"

    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {v0, p1}, Lcom/bytedance/sdk/component/utils/dG;->Fj(Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;Lcom/bytedance/sdk/component/adexpress/ex/svN;)V
    .locals 0

    invoke-super {p0, p1}, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->Fj(Lcom/bytedance/sdk/component/adexpress/ex/svN;)V

    return-void
.end method

.method public static synthetic Ko(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;)Lcom/bytedance/sdk/component/widget/SSWebView;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->hjc:Lcom/bytedance/sdk/component/widget/SSWebView;

    return-object p0
.end method

.method private Ql()V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->hjc:Lcom/bytedance/sdk/component/widget/SSWebView;

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/widget/SSWebView;->getWebView()Landroid/webkit/WebView;

    move-result-object v0

    if-eqz v0, :cond_0

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/utils/uy;->Ubf()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-direct {p0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->rS()V

    return-void

    :cond_0
    const/4 v0, 0x1

    iput v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->vYf:I

    new-instance v0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql$3;

    invoke-direct {v0, p0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql$3;-><init>(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;)V

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/utils/uy;->Fj(Ljava/lang/Runnable;)V

    return-void
.end method

.method public static synthetic Ubf(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;)V
    .locals 0

    invoke-direct {p0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->Ql()V

    return-void
.end method

.method public static synthetic WR(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;)Ljava/lang/Runnable;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->mC:Ljava/lang/Runnable;

    return-object p0
.end method

.method public static synthetic eV(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;)I
    .locals 0

    iget p0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->vYf:I

    return p0
.end method

.method public static synthetic ex(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;)Lcom/bytedance/sdk/openadsdk/core/model/Ql;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    return-object p0
.end method

.method public static ex(Ljava/lang/String;)Z
    .locals 1

    const-string v0, "banner_call"

    invoke-virtual {v0, p0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_1

    const-string v0, "banner_ad"

    invoke-virtual {v0, p0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_1

    const-string v0, "slide_banner_ad"

    invoke-virtual {v0, p0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_1

    const-string v0, "banner_ad_landingpage"

    invoke-virtual {v0, p0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p0

    if-eqz p0, :cond_0

    goto :goto_0

    :cond_0
    const/4 p0, 0x0

    return p0

    :cond_1
    :goto_0
    const/4 p0, 0x1

    return p0
.end method

.method public static synthetic hjc(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;)Lorg/json/JSONObject;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->rAx:Lorg/json/JSONObject;

    return-object p0
.end method

.method private hjc(Z)V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->JU:Lcom/bytedance/sdk/openadsdk/core/Vq;

    if-eqz v0, :cond_1

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->hjc:Lcom/bytedance/sdk/component/widget/SSWebView;

    if-nez v0, :cond_0

    goto :goto_0

    :cond_0
    :try_start_0
    new-instance v0, Lorg/json/JSONObject;

    invoke-direct {v0}, Lorg/json/JSONObject;-><init>()V

    const-string v1, "adVisible"

    invoke-virtual {v0, v1, p1}, Lorg/json/JSONObject;->put(Ljava/lang/String;Z)Lorg/json/JSONObject;

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->JU:Lcom/bytedance/sdk/openadsdk/core/Vq;

    const-string v1, "expressAdShow"

    invoke-virtual {p1, v1, v0}, Lcom/bytedance/sdk/openadsdk/core/Vq;->Fj(Ljava/lang/String;Lorg/json/JSONObject;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    :catch_0
    :cond_1
    :goto_0
    return-void
.end method

.method public static synthetic mSE(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;)V
    .locals 0

    invoke-direct {p0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->rS()V

    return-void
.end method

.method private rS()V
    .locals 3

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->hjc:Lcom/bytedance/sdk/component/widget/SSWebView;

    if-eqz v0, :cond_2

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/widget/SSWebView;->getWebView()Landroid/webkit/WebView;

    move-result-object v0

    if-nez v0, :cond_0

    goto :goto_0

    :cond_0
    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->vYf:I

    const/4 v1, 0x2

    if-ne v0, v1, :cond_1

    return-void

    :cond_1
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->dG:Ljava/lang/String;

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->hjc:Lcom/bytedance/sdk/component/widget/SSWebView;

    const/4 v2, 0x0

    invoke-virtual {v0, v2}, Lcom/bytedance/sdk/component/widget/SSWebView;->setDisplayZoomControls(Z)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->dG:Ljava/lang/String;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/utils/rf;->Fj(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->Fj(Ljava/lang/String;)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->dG()V

    new-instance v0, Lcom/bytedance/sdk/openadsdk/core/Vq;

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->BcC:Landroid/content/Context;

    invoke-direct {v0, v2}, Lcom/bytedance/sdk/openadsdk/core/Vq;-><init>(Landroid/content/Context;)V

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->JU:Lcom/bytedance/sdk/openadsdk/core/Vq;

    const/4 v2, 0x1

    invoke-virtual {v0, v2}, Lcom/bytedance/sdk/openadsdk/core/Vq;->eV(Z)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->Tc()V

    iput v1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->vYf:I

    :cond_2
    :goto_0
    return-void
.end method

.method public static synthetic svN(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;)Ljava/util/concurrent/atomic/AtomicBoolean;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->WR:Ljava/util/concurrent/atomic/AtomicBoolean;

    return-object p0
.end method


# virtual methods
.method public BcC()V
    .locals 3

    const-string v0, "expressShow"

    invoke-super {p0}, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->BcC()V

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->JU:Lcom/bytedance/sdk/openadsdk/core/Vq;

    if-nez v1, :cond_0

    return-void

    :cond_0
    :try_start_0
    new-instance v1, Lorg/json/JSONObject;

    invoke-direct {v1}, Lorg/json/JSONObject;-><init>()V

    const/4 v2, 0x1

    invoke-virtual {v1, v0, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;Z)Lorg/json/JSONObject;

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->JU:Lcom/bytedance/sdk/openadsdk/core/Vq;

    invoke-virtual {v2, v0, v1}, Lcom/bytedance/sdk/openadsdk/core/Vq;->Fj(Ljava/lang/String;Lorg/json/JSONObject;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    :catch_0
    return-void
.end method

.method public Fj()Lcom/bytedance/sdk/component/widget/SSWebView;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->hjc:Lcom/bytedance/sdk/component/widget/SSWebView;

    return-object v0
.end method

.method public Fj(I)V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->JU:Lcom/bytedance/sdk/openadsdk/core/Vq;

    if-nez v0, :cond_0

    return-void

    :cond_0
    new-instance v0, Lorg/json/JSONObject;

    invoke-direct {v0}, Lorg/json/JSONObject;-><init>()V

    :try_start_0
    const-string v1, "status"

    invoke-virtual {v0, v1, p1}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;
    :try_end_0
    .catch Lorg/json/JSONException; {:try_start_0 .. :try_end_0} :catch_0

    :catch_0
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->JU:Lcom/bytedance/sdk/openadsdk/core/Vq;

    const-string v1, "themeChange"

    invoke-virtual {p1, v1, v0}, Lcom/bytedance/sdk/openadsdk/core/Vq;->Fj(Ljava/lang/String;Lorg/json/JSONObject;)V

    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/component/adexpress/ex/Tc;)V
    .locals 3

    invoke-super {p0, p1}, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->Fj(Lcom/bytedance/sdk/component/adexpress/ex/Tc;)V

    iget-boolean p1, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->ex:Z

    if-nez p1, :cond_0

    return-void

    :cond_0
    invoke-static {}, Lcom/bytedance/sdk/component/utils/BcC;->ex()Landroid/os/Handler;

    move-result-object p1

    new-instance v0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql$4;

    invoke-direct {v0, p0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql$4;-><init>(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;)V

    const-wide/16 v1, 0x7d0

    invoke-virtual {p1, v0, v1, v2}, Landroid/os/Handler;->postDelayed(Ljava/lang/Runnable;J)Z

    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/component/adexpress/ex/svN;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->Ql:Lcom/bytedance/sdk/component/adexpress/ex/svN;

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->Af:Lcom/bytedance/sdk/component/svN/BcC;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/utils/uy;->ex(Lcom/bytedance/sdk/component/svN/BcC;)V

    return-void
.end method

.method public JU()Lcom/bytedance/sdk/openadsdk/core/Vq;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->JU:Lcom/bytedance/sdk/openadsdk/core/Vq;

    return-object v0
.end method

.method public JW()Lcom/bytedance/sdk/openadsdk/core/nativeexpress/BcC;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->mE:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/BcC;

    return-object v0
.end method

.method public Ko()V
    .locals 1

    invoke-super {p0}, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->Ko()V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->svN:Lcom/bytedance/sdk/openadsdk/utils/Fj;

    if-eqz v0, :cond_0

    invoke-virtual {v0, p0}, Lcom/bytedance/sdk/openadsdk/utils/Fj;->ex(Lcom/bytedance/sdk/component/adexpress/Fj;)Z

    :cond_0
    return-void
.end method

.method public Tc()V
    .locals 5

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->hjc:Lcom/bytedance/sdk/component/widget/SSWebView;

    if-eqz v0, :cond_2

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/widget/SSWebView;->getWebView()Landroid/webkit/WebView;

    move-result-object v0

    if-nez v0, :cond_0

    goto :goto_0

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->hjc:Lcom/bytedance/sdk/component/widget/SSWebView;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/widget/SSWebView;->setBackgroundColor(I)V

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->hjc:Lcom/bytedance/sdk/component/widget/SSWebView;

    const v2, 0x106000d

    invoke-virtual {v0, v2}, Landroid/view/View;->setBackgroundResource(I)V

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->hjc:Lcom/bytedance/sdk/component/widget/SSWebView;

    invoke-direct {p0, v0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->Fj(Lcom/bytedance/sdk/component/widget/SSWebView;)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->Fj()Lcom/bytedance/sdk/component/widget/SSWebView;

    move-result-object v0

    if-eqz v0, :cond_1

    new-instance v0, Lcom/bytedance/sdk/openadsdk/ex/Ko;

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->Fj()Lcom/bytedance/sdk/component/widget/SSWebView;

    move-result-object v3

    invoke-virtual {v3}, Lcom/bytedance/sdk/component/widget/SSWebView;->getWebView()Landroid/webkit/WebView;

    move-result-object v3

    invoke-direct {v0, v2, v3}, Lcom/bytedance/sdk/openadsdk/ex/Ko;-><init>(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Landroid/webkit/WebView;)V

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/ex/Ko;->Fj(Z)Lcom/bytedance/sdk/openadsdk/ex/Ko;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->Tc:Lcom/bytedance/sdk/openadsdk/ex/Ko;

    :cond_1
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->Tc:Lcom/bytedance/sdk/openadsdk/ex/Ko;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->UYd:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/ex/Ko;->Fj(Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;)V

    new-instance v0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/BcC;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->BcC:Landroid/content/Context;

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->JU:Lcom/bytedance/sdk/openadsdk/core/Vq;

    iget-object v3, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    iget-object v4, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->Tc:Lcom/bytedance/sdk/openadsdk/ex/Ko;

    invoke-direct {v0, v1, v2, v3, v4}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/BcC;-><init>(Landroid/content/Context;Lcom/bytedance/sdk/openadsdk/core/Vq;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/ex/Ko;)V

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->mE:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/BcC;

    iget-object v1, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->hjc:Lcom/bytedance/sdk/component/widget/SSWebView;

    invoke-virtual {v1, v0}, Lcom/bytedance/sdk/component/widget/SSWebView;->setWebViewClient(Landroid/webkit/WebViewClient;)V

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->hjc:Lcom/bytedance/sdk/component/widget/SSWebView;

    new-instance v1, Lcom/bytedance/sdk/openadsdk/core/widget/Fj/eV;

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->JU:Lcom/bytedance/sdk/openadsdk/core/Vq;

    iget-object v3, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->Tc:Lcom/bytedance/sdk/openadsdk/ex/Ko;

    invoke-direct {v1, v2, v3}, Lcom/bytedance/sdk/openadsdk/core/widget/Fj/eV;-><init>(Lcom/bytedance/sdk/openadsdk/core/Vq;Lcom/bytedance/sdk/openadsdk/ex/Ko;)V

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/widget/SSWebView;->setWebChromeClient(Landroid/webkit/WebChromeClient;)V

    invoke-static {}, Lcom/bytedance/sdk/component/adexpress/Ubf/Ubf;->Fj()Lcom/bytedance/sdk/component/adexpress/Ubf/Ubf;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->hjc:Lcom/bytedance/sdk/component/widget/SSWebView;

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->JU:Lcom/bytedance/sdk/openadsdk/core/Vq;

    invoke-virtual {v0, v1, v2}, Lcom/bytedance/sdk/component/adexpress/Ubf/Ubf;->Fj(Lcom/bytedance/sdk/component/widget/SSWebView;Lcom/bytedance/sdk/component/adexpress/Ubf/ex;)V

    :cond_2
    :goto_0
    return-void
.end method

.method public UYd()V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->hjc:Lcom/bytedance/sdk/component/widget/SSWebView;

    if-eqz v0, :cond_1

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/widget/SSWebView;->getWebView()Landroid/webkit/WebView;

    move-result-object v0

    if-eqz v0, :cond_1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->JU:Lcom/bytedance/sdk/openadsdk/core/Vq;

    if-nez v0, :cond_0

    goto :goto_0

    :cond_0
    iget-object v1, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->hjc:Lcom/bytedance/sdk/component/widget/SSWebView;

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/core/Vq;->ex(Lcom/bytedance/sdk/component/widget/SSWebView;)Lcom/bytedance/sdk/openadsdk/core/Vq;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/core/Vq;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Lcom/bytedance/sdk/openadsdk/core/Vq;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->cs()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/core/Vq;->hjc(Ljava/lang/String;)Lcom/bytedance/sdk/openadsdk/core/Vq;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->Jq()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/core/Vq;->eV(Ljava/lang/String;)Lcom/bytedance/sdk/openadsdk/core/Vq;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->mSE:Ljava/lang/String;

    invoke-static {v1}, Lcom/bytedance/sdk/openadsdk/utils/rXP;->Fj(Ljava/lang/String;)I

    move-result v1

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/core/Vq;->ex(I)Lcom/bytedance/sdk/openadsdk/core/Vq;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->tc()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/core/Vq;->Ubf(Ljava/lang/String;)Lcom/bytedance/sdk/openadsdk/core/Vq;

    move-result-object v0

    invoke-virtual {v0, p0}, Lcom/bytedance/sdk/openadsdk/core/Vq;->Fj(Lcom/bytedance/sdk/component/adexpress/ex/rAx;)Lcom/bytedance/sdk/openadsdk/core/Vq;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->rAx:Lorg/json/JSONObject;

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/core/Vq;->Fj(Lorg/json/JSONObject;)Lcom/bytedance/sdk/openadsdk/core/Vq;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->hjc:Lcom/bytedance/sdk/component/widget/SSWebView;

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/core/Vq;->Fj(Lcom/bytedance/sdk/component/widget/SSWebView;)Lcom/bytedance/sdk/openadsdk/core/Vq;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->UYd:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/core/Vq;->Fj(Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;)Lcom/bytedance/sdk/openadsdk/core/Vq;

    :cond_1
    :goto_0
    return-void
.end method

.method public WR()V
    .locals 1

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->Fj()Lcom/bytedance/sdk/component/widget/SSWebView;

    move-result-object v0

    if-nez v0, :cond_0

    return-void

    :cond_0
    :try_start_0
    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->Fj()Lcom/bytedance/sdk/component/widget/SSWebView;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/widget/SSWebView;->getWebView()Landroid/webkit/WebView;

    move-result-object v0

    invoke-virtual {v0}, Landroid/webkit/WebView;->resumeTimers()V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    :catch_0
    return-void
.end method

.method public dG()V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->uM()Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;

    move-result-object v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->uM()Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->rS:Lcom/bytedance/sdk/openadsdk/core/model/Ql$Fj;

    :cond_0
    return-void
.end method

.method public eV()V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->WR:Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-virtual {v0}, Ljava/util/concurrent/atomic/AtomicBoolean;->get()Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->JU:Lcom/bytedance/sdk/openadsdk/core/Vq;

    if-eqz v0, :cond_1

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/Vq;->ex()V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->JU:Lcom/bytedance/sdk/openadsdk/core/Vq;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/Vq;->UYd()V

    const/4 v0, 0x0

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->JU:Lcom/bytedance/sdk/openadsdk/core/Vq;

    :cond_1
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->Tc:Lcom/bytedance/sdk/openadsdk/ex/Ko;

    if-eqz v0, :cond_2

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/ex/Ko;->hjc(Z)V

    :cond_2
    invoke-super {p0}, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->eV()V

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/dG;->hjc()Landroid/os/Handler;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->mC:Ljava/lang/Runnable;

    invoke-virtual {v0, v1}, Landroid/os/Handler;->removeCallbacks(Ljava/lang/Runnable;)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->JW:Ljava/util/Map;

    invoke-interface {v0}, Ljava/util/Map;->clear()V

    return-void
.end method

.method public ex(I)V
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->eV:I

    if-ne p1, v0, :cond_0

    return-void

    :cond_0
    iput p1, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->eV:I

    if-nez p1, :cond_1

    const/4 p1, 0x1

    goto :goto_0

    :cond_1
    const/4 p1, 0x0

    :goto_0
    invoke-direct {p0, p1}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->hjc(Z)V

    return-void
.end method

.method public mSE()V
    .locals 1

    invoke-super {p0}, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->mSE()V

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/Tc;->Fj()Lcom/bytedance/sdk/openadsdk/core/Tc;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/Tc;->Ubf()Lcom/bytedance/sdk/openadsdk/utils/Fj;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->svN:Lcom/bytedance/sdk/openadsdk/utils/Fj;

    invoke-virtual {v0, p0}, Lcom/bytedance/sdk/openadsdk/utils/Fj;->Fj(Lcom/bytedance/sdk/component/adexpress/Fj;)V

    return-void
.end method

.method public svN()V
    .locals 3

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ql;->JU:Lcom/bytedance/sdk/openadsdk/core/Vq;

    if-nez v0, :cond_0

    return-void

    :cond_0
    const-string v1, "expressWebviewRecycle"

    const/4 v2, 0x0

    invoke-virtual {v0, v1, v2}, Lcom/bytedance/sdk/openadsdk/core/Vq;->Fj(Ljava/lang/String;Lorg/json/JSONObject;)V

    return-void
.end method
