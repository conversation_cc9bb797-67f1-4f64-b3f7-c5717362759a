.class public final Lcom/transsion/play/detail/PlayDetailBottomRecHelper$c;
.super Landroidx/viewpager2/widget/ViewPager2$OnPageChangeCallback;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->n()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# instance fields
.field public final synthetic a:Lcom/transsion/play/detail/PlayDetailBottomRecHelper;


# direct methods
.method public constructor <init>(Lcom/transsion/play/detail/PlayDetailBottomRecHelper;)V
    .locals 0

    iput-object p1, p0, Lcom/transsion/play/detail/PlayDetailBottomRecHelper$c;->a:Lcom/transsion/play/detail/PlayDetailBottomRecHelper;

    invoke-direct {p0}, Landroidx/viewpager2/widget/ViewPager2$OnPageChangeCallback;-><init>()V

    return-void
.end method


# virtual methods
.method public onPageScrollStateChanged(I)V
    .locals 1

    invoke-super {p0, p1}, Landroidx/viewpager2/widget/ViewPager2$OnPageChangeCallback;->onPageScrollStateChanged(I)V

    iget-object v0, p0, Lcom/transsion/play/detail/PlayDetailBottomRecHelper$c;->a:Lcom/transsion/play/detail/PlayDetailBottomRecHelper;

    invoke-static {v0}, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->d(Lcom/transsion/play/detail/PlayDetailBottomRecHelper;)Lnet/lucode/hackware/magicindicator/MagicIndicator;

    move-result-object v0

    if-eqz v0, :cond_0

    invoke-virtual {v0, p1}, Lnet/lucode/hackware/magicindicator/MagicIndicator;->onPageScrollStateChanged(I)V

    :cond_0
    return-void
.end method

.method public onPageScrolled(IFI)V
    .locals 1

    invoke-super {p0, p1, p2, p3}, Landroidx/viewpager2/widget/ViewPager2$OnPageChangeCallback;->onPageScrolled(IFI)V

    iget-object v0, p0, Lcom/transsion/play/detail/PlayDetailBottomRecHelper$c;->a:Lcom/transsion/play/detail/PlayDetailBottomRecHelper;

    invoke-static {v0}, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->d(Lcom/transsion/play/detail/PlayDetailBottomRecHelper;)Lnet/lucode/hackware/magicindicator/MagicIndicator;

    move-result-object v0

    if-eqz v0, :cond_0

    invoke-virtual {v0, p1, p2, p3}, Lnet/lucode/hackware/magicindicator/MagicIndicator;->onPageScrolled(IFI)V

    :cond_0
    return-void
.end method

.method public onPageSelected(I)V
    .locals 1

    invoke-super {p0, p1}, Landroidx/viewpager2/widget/ViewPager2$OnPageChangeCallback;->onPageSelected(I)V

    iget-object v0, p0, Lcom/transsion/play/detail/PlayDetailBottomRecHelper$c;->a:Lcom/transsion/play/detail/PlayDetailBottomRecHelper;

    invoke-static {v0}, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->d(Lcom/transsion/play/detail/PlayDetailBottomRecHelper;)Lnet/lucode/hackware/magicindicator/MagicIndicator;

    move-result-object v0

    if-eqz v0, :cond_0

    invoke-virtual {v0, p1}, Lnet/lucode/hackware/magicindicator/MagicIndicator;->onPageSelected(I)V

    :cond_0
    iget-object v0, p0, Lcom/transsion/play/detail/PlayDetailBottomRecHelper$c;->a:Lcom/transsion/play/detail/PlayDetailBottomRecHelper;

    invoke-static {v0, p1}, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->l(Lcom/transsion/play/detail/PlayDetailBottomRecHelper;I)V

    return-void
.end method
