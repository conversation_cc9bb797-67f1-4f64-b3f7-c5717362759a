.class Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC$1;
.super Lcom/bytedance/sdk/openadsdk/core/ex/Ubf;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC;Landroid/content/Context;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Ljava/lang/String;I)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC;

    invoke-direct {p0, p2, p3, p4, p5}, Lcom/bytedance/sdk/openadsdk/core/ex/Ubf;-><init>(Landroid/content/Context;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Ljava/lang/String;I)V

    return-void
.end method


# virtual methods
.method public Fj(Landroid/view/View;FFFFLandroid/util/SparseArray;IIIZ)V
    .locals 13
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/view/View;",
            "FFFF",
            "Landroid/util/SparseArray<",
            "Lcom/bytedance/sdk/openadsdk/core/ex/hjc$Fj;",
            ">;IIIZ)V"
        }
    .end annotation

    move-object v1, p0

    move-object v3, p1

    iget-object v0, v1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object v0

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->zf()Z

    move-result v0

    if-eqz v0, :cond_0

    if-eqz v3, :cond_0

    const v0, 0x22000001

    invoke-virtual {p1, v0}, Landroid/view/View;->getTag(I)Ljava/lang/Object;

    move-result-object v0

    instance-of v2, v0, Ljava/lang/String;

    if-eqz v2, :cond_0

    check-cast v0, Ljava/lang/String;

    invoke-virtual {p0, v0}, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->Fj(Ljava/lang/String;)V

    :cond_0
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    iget-object v2, v1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC;

    invoke-static {v2}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object v2

    iget-object v2, v2, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->kF:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;

    invoke-virtual {v2}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->svN()J

    move-result-wide v4

    invoke-static {v4, v5}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v2

    const-string v4, "duration"

    invoke-interface {v0, v4, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    iget-object v2, v1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC;

    invoke-static {v2}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object v2

    iget-object v2, v2, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Af:Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-virtual {v2}, Ljava/util/concurrent/atomic/AtomicBoolean;->get()Z

    move-result v2

    const-string v4, "click_scence"

    if-eqz v2, :cond_1

    const/4 v2, 0x2

    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    invoke-interface {v0, v4, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    :cond_1
    iget-object v2, v1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC;

    invoke-static {v2}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object v2

    iget-object v2, v2, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {v2}, Lcom/bytedance/sdk/openadsdk/core/model/mE;->rAx(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v2

    if-eqz v2, :cond_2

    const/4 v2, 0x3

    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    invoke-interface {v0, v4, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    :cond_2
    const/4 v2, 0x1

    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    invoke-interface {v0, v4, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :goto_0
    invoke-virtual {p0, v0}, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->Fj(Ljava/util/Map;)V

    iget-object v0, v1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object v0

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Kk:Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;

    invoke-interface {v0}, Lcom/bytedance/sdk/openadsdk/core/video/hjc/ex;->rS()V

    invoke-virtual {p1}, Landroid/view/View;->getId()I

    move-result v0

    sget v2, Lcom/bytedance/sdk/openadsdk/utils/Ko;->We:I

    if-ne v0, v2, :cond_4

    iget-object v0, v1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object v0

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/model/mE;->rAx(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v0

    if-eqz v0, :cond_4

    new-instance v2, Lorg/json/JSONObject;

    invoke-direct {v2}, Lorg/json/JSONObject;-><init>()V

    iget-object v0, v1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object v0

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->spi()Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

    move-result-object v0

    if-eqz v0, :cond_3

    :try_start_0
    const-string v0, "playable_url"

    iget-object v4, v1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC;

    invoke-static {v4}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object v4

    iget-object v4, v4, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v4}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->spi()Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

    move-result-object v4

    invoke-virtual {v4}, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->UYd()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v2, v0, v4}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;
    :try_end_0
    .catch Lorg/json/JSONException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_1

    :catch_0
    move-exception v0

    const-string v4, "TTAD.RFReportManager"

    const-string v5, "onRewardBarClick json error"

    invoke-static {v4, v5, v0}, Lcom/bytedance/sdk/component/utils/dG;->Fj(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)V

    :cond_3
    :goto_1
    iget-object v0, v1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object v0

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    iget-object v4, v1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC;

    invoke-static {v4}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object v4

    iget-object v4, v4, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->svN:Ljava/lang/String;

    const-string v5, "click_playable_download_button_loading"

    invoke-static {v0, v4, v5, v2}, Lcom/bytedance/sdk/openadsdk/ex/hjc;->ex(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Ljava/lang/String;Ljava/lang/String;Lorg/json/JSONObject;)V

    :cond_4
    iget-object v0, v1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object v0

    iget-object v2, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->uM:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/eV;

    new-instance v12, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC$1$1;

    invoke-direct {v12, p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC$1$1;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC$1;)V

    move-object v3, p1

    move v4, p2

    move/from16 v5, p3

    move/from16 v6, p4

    move/from16 v7, p5

    move-object/from16 v8, p6

    move/from16 v9, p7

    move/from16 v10, p8

    move/from16 v11, p9

    invoke-virtual/range {v2 .. v12}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/eV;->Fj(Landroid/view/View;FFFFLandroid/util/SparseArray;IIILcom/bytedance/sdk/openadsdk/component/reward/Fj/eV$Fj;)V

    iget-object v0, v1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object v0

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    const/16 v2, 0x9

    invoke-static {v0, v2}, Lcom/bytedance/sdk/openadsdk/Tc/Fj/Ubf;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;I)V

    return-void
.end method
