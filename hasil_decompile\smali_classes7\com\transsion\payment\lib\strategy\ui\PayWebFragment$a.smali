.class public final Lcom/transsion/payment/lib/strategy/ui/PayWebFragment$a;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/transsion/payment/lib/strategy/ui/PayWebFragment;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    invoke-direct {p0}, Lcom/transsion/payment/lib/strategy/ui/PayWebFragment$a;-><init>()V

    return-void
.end method


# virtual methods
.method public final a()Lcom/transsion/payment/lib/strategy/ui/PayWebFragment;
    .locals 1

    new-instance v0, Lcom/transsion/payment/lib/strategy/ui/PayWebFragment;

    invoke-direct {v0}, Lcom/transsion/payment/lib/strategy/ui/PayWebFragment;-><init>()V

    return-object v0
.end method
