.class public interface abstract Landroidx/compose/runtime/d2;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/compose/runtime/l;


# annotations
.annotation runtime L<PERSON><PERSON>/Metadata;
.end annotation


# virtual methods
.method public abstract deactivate()V
.end method

.method public abstract p(Lkotlin/jvm/functions/Function2;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function2<",
            "-",
            "Landroidx/compose/runtime/i;",
            "-",
            "Ljava/lang/Integer;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation
.end method
