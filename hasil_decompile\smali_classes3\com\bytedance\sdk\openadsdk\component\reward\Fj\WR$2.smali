.class Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$2;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/openadsdk/api/PAGExpressAdWrapperListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;->Fj([FLcom/bytedance/sdk/openadsdk/core/video/hjc/ex;Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/core/video/hjc/ex;

.field final synthetic ex:Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;

.field final synthetic hjc:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;Lcom/bytedance/sdk/openadsdk/core/video/hjc/ex;Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$2;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;

    iput-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$2;->Fj:Lcom/bytedance/sdk/openadsdk/core/video/hjc/ex;

    iput-object p3, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$2;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public onAdClicked()V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$2;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$2;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object v0

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$2;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object v0

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->UF()Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$2;->Fj:Lcom/bytedance/sdk/openadsdk/core/video/hjc/ex;

    invoke-interface {v0}, Lcom/bytedance/sdk/openadsdk/core/video/hjc/ex;->rS()V

    :cond_0
    return-void
.end method

.method public onAdDismissed()V
    .locals 0

    return-void
.end method

.method public onAdShow(Landroid/view/View;I)V
    .locals 0

    return-void
.end method

.method public onRenderFail(Landroid/view/View;Ljava/lang/String;I)V
    .locals 0

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$2;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object p1

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->vLw()Z

    move-result p1

    if-nez p1, :cond_0

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$2;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object p1

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->qPr:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;

    const/4 p2, 0x1

    invoke-virtual {p1, p2}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Fj(Z)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$2;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object p1

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->qPr:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ubf()V

    :cond_0
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$2;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object p1

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->spi:Lcom/bytedance/sdk/openadsdk/component/reward/view/hjc;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/view/hjc;->Ubf()Landroid/os/Handler;

    move-result-object p1

    new-instance p2, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$2$1;

    invoke-direct {p2, p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$2$1;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$2;)V

    invoke-virtual {p1, p2}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$2;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object p1

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Obv:Lcom/bytedance/sdk/openadsdk/component/reward/view/Ubf;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/view/Ubf;->dG()V

    return-void
.end method

.method public onRenderSuccess(Landroid/view/View;FF)V
    .locals 0

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$2;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object p1

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/core/model/mE;->rAx(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result p1

    if-eqz p1, :cond_0

    return-void

    :cond_0
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$2;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object p1

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->vLw()Z

    move-result p1

    if-nez p1, :cond_3

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$2;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object p1

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->spi:Lcom/bytedance/sdk/openadsdk/component/reward/view/hjc;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/view/hjc;->BcC()Z

    move-result p1

    const/4 p2, 0x1

    if-eqz p1, :cond_1

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$2;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;

    invoke-virtual {p1, p2}, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Fj;->Fj(Z)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$2;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object p1

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->kF:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;

    const/4 p3, 0x0

    invoke-virtual {p1, p3}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Ubf(Z)V

    goto :goto_0

    :cond_1
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$2;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object p1

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->kF:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;

    invoke-virtual {p1, p2}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->Ubf(Z)V

    :goto_0
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$2;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object p1

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Obv:Lcom/bytedance/sdk/openadsdk/component/reward/view/Ubf;

    const/16 p3, 0x8

    invoke-virtual {p1, p3}, Lcom/bytedance/sdk/openadsdk/component/reward/view/Ubf;->ex(I)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$2;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object p1

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->qPr:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;

    invoke-virtual {p1, p2}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Fj(Z)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$2;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object p1

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->qPr:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ubf()V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$2;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object p1

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->spi:Lcom/bytedance/sdk/openadsdk/component/reward/view/hjc;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/view/hjc;->BcC()Z

    move-result p1

    if-eqz p1, :cond_2

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$2;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object p1

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->spi:Lcom/bytedance/sdk/openadsdk/component/reward/view/hjc;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/view/hjc;->ex()Landroid/widget/FrameLayout;

    move-result-object p1

    const/high16 p2, -0x1000000

    invoke-virtual {p1, p2}, Landroid/view/View;->setBackgroundColor(I)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$2;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$2;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;

    invoke-static {p2}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object p2

    iget-object p2, p2, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Obv:Lcom/bytedance/sdk/openadsdk/component/reward/view/Ubf;

    invoke-virtual {p2}, Lcom/bytedance/sdk/openadsdk/component/reward/view/Ubf;->WR()Landroid/widget/FrameLayout;

    move-result-object p2

    invoke-virtual {p1, p2}, Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;->Fj(Landroid/widget/FrameLayout;)V

    goto :goto_1

    :cond_2
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$2;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object p1

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->spi()Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

    move-result-object p1

    if-eqz p1, :cond_3

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$2;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Fj;->Fj()Z

    move-result p1

    if-eqz p1, :cond_3

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$2;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object p1

    iput-boolean p2, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->mSE:Z

    :cond_3
    :goto_1
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$2;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;->rf()V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$2;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;

    instance-of p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Ubf;

    if-eqz p1, :cond_4

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$2;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object p1

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$2;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;

    invoke-static {p2}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object p2

    iget-object p2, p2, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->spi:Lcom/bytedance/sdk/openadsdk/component/reward/view/hjc;

    invoke-virtual {p2}, Lcom/bytedance/sdk/openadsdk/component/reward/view/hjc;->BcC()Z

    move-result p2

    iget-object p3, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$2;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;

    invoke-static {p3}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object p3

    iget-object p3, p3, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->svN:Ljava/lang/String;

    invoke-static {p1, p2, p3}, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Ubf;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;ZLjava/lang/String;)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$2;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object p1

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->spi:Lcom/bytedance/sdk/openadsdk/component/reward/view/hjc;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/view/hjc;->BcC()Z

    move-result p1

    if-nez p1, :cond_4

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$2;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object p1

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Kk:Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;

    invoke-interface {p1}, Lcom/bytedance/sdk/openadsdk/core/video/hjc/ex;->svN()V

    :cond_4
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$2;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object p1

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Obv:Lcom/bytedance/sdk/openadsdk/component/reward/view/Ubf;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/view/Ubf;->dG()V

    return-void
.end method
