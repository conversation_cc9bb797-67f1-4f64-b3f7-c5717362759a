.class public Lcom/bytedance/sdk/openadsdk/core/model/eV;
.super Ljava/lang/Object;


# instance fields
.field private Fj:I

.field private ex:I

.field private hjc:J


# direct methods
.method public constructor <init>()V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x2

    iput v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/eV;->Fj:I

    const/4 v0, 0x1

    iput v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/eV;->ex:I

    return-void
.end method


# virtual methods
.method public Fj()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/eV;->Fj:I

    return v0
.end method

.method public Fj(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/eV;->Fj:I

    return-void
.end method

.method public Fj(J)V
    .locals 0

    iput-wide p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/eV;->hjc:J

    return-void
.end method

.method public ex()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/eV;->ex:I

    return v0
.end method

.method public ex(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/eV;->ex:I

    return-void
.end method

.method public hjc()J
    .locals 2

    iget-wide v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/eV;->hjc:J

    return-wide v0
.end method
