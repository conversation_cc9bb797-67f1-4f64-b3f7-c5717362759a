.class Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ko$2$1;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ko$2;->eV()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:[Landroid/os/MessageQueue;

.field final synthetic ex:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ko$2;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ko$2;[Landroid/os/MessageQueue;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ko$2$1;->ex:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ko$2;

    iput-object p2, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ko$2$1;->Fj:[Landroid/os/MessageQueue;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 3

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ko$2$1;->Fj:[Landroid/os/MessageQueue;

    invoke-static {}, Landroid/os/Looper;->myQueue()Landroid/os/MessageQueue;

    move-result-object v1

    const/4 v2, 0x0

    aput-object v1, v0, v2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ko$2$1;->ex:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ko$2;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ko$2$1;->Fj:[Landroid/os/MessageQueue;

    aget-object v1, v1, v2

    invoke-static {v0, v1}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ko$2;->Fj(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ko$2;Landroid/os/MessageQueue;)V

    return-void
.end method
