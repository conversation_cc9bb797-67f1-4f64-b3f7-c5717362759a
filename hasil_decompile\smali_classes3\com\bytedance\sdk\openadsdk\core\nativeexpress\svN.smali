.class public Lcom/bytedance/sdk/openadsdk/core/nativeexpress/svN;
.super Lcom/bytedance/sdk/openadsdk/core/ex/ex;


# direct methods
.method public constructor <init>(Landroid/content/Context;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Ljava/lang/String;I)V
    .locals 0
    .param p1    # Landroid/content/Context;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p2    # Lcom/bytedance/sdk/openadsdk/core/model/Ql;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p3    # Ljava/lang/String;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    invoke-direct {p0, p1, p2, p3, p4}, Lcom/bytedance/sdk/openadsdk/core/ex/ex;-><init>(Landroid/content/Context;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Ljava/lang/String;I)V

    return-void
.end method


# virtual methods
.method public Fj(FFFFLandroid/util/SparseArray;JJLandroid/view/View;Landroid/view/View;Ljava/lang/String;FIFILorg/json/JSONObject;)Lcom/bytedance/sdk/openadsdk/core/model/mSE;
    .locals 16
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(FFFF",
            "Landroid/util/SparseArray<",
            "Lcom/bytedance/sdk/openadsdk/core/ex/hjc$Fj;",
            ">;JJ",
            "Landroid/view/View;",
            "Landroid/view/View;",
            "Ljava/lang/String;",
            "FIFI",
            "Lorg/json/JSONObject;",
            ")",
            "Lcom/bytedance/sdk/openadsdk/core/model/mSE;"
        }
    .end annotation

    move-object/from16 v0, p0

    invoke-static/range {p10 .. p10}, Lcom/bytedance/sdk/openadsdk/utils/eh;->Fj(Landroid/view/View;)[I

    move-result-object v1

    const/4 v2, 0x2

    const/4 v3, 0x1

    const/4 v4, 0x0

    if-eqz v1, :cond_1

    array-length v5, v1

    if-ne v5, v2, :cond_1

    aget v5, v1, v4

    aget v6, v1, v3

    iget v7, v0, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->vYf:I

    if-nez v7, :cond_0

    iget-object v7, v0, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->hjc:Landroid/content/Context;

    move/from16 v8, p1

    invoke-static {v7, v8}, Lcom/bytedance/sdk/openadsdk/utils/eh;->ex(Landroid/content/Context;F)I

    move-result v7

    add-int/2addr v7, v5

    int-to-float v7, v7

    const/high16 v8, 0x3f000000    # 0.5f

    sub-float/2addr v7, v8

    iget-object v9, v0, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->hjc:Landroid/content/Context;

    move/from16 v10, p2

    invoke-static {v9, v10}, Lcom/bytedance/sdk/openadsdk/utils/eh;->ex(Landroid/content/Context;F)I

    move-result v9

    add-int/2addr v9, v6

    int-to-float v9, v9

    sub-float/2addr v9, v8

    iget-object v10, v0, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->hjc:Landroid/content/Context;

    move/from16 v11, p3

    invoke-static {v10, v11}, Lcom/bytedance/sdk/openadsdk/utils/eh;->ex(Landroid/content/Context;F)I

    move-result v10

    add-int/2addr v10, v5

    int-to-float v10, v10

    sub-float/2addr v10, v8

    iget-object v11, v0, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->hjc:Landroid/content/Context;

    move/from16 v12, p4

    invoke-static {v11, v12}, Lcom/bytedance/sdk/openadsdk/utils/eh;->ex(Landroid/content/Context;F)I

    move-result v11

    add-int/2addr v11, v6

    int-to-float v11, v11

    sub-float v8, v11, v8

    move v12, v8

    move v11, v10

    move v8, v7

    move v10, v9

    goto :goto_0

    :cond_0
    move/from16 v8, p1

    move/from16 v10, p2

    move/from16 v11, p3

    move/from16 v12, p4

    goto :goto_0

    :cond_1
    move/from16 v8, p1

    move/from16 v10, p2

    move/from16 v11, p3

    move/from16 v12, p4

    const/4 v5, 0x0

    const/4 v6, 0x0

    :goto_0
    new-array v7, v2, [I

    new-array v9, v2, [I

    iget-object v13, v0, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->eV:Lcom/bytedance/sdk/openadsdk/core/model/dG;

    if-eqz v13, :cond_4

    iget-wide v14, v13, Lcom/bytedance/sdk/openadsdk/core/model/dG;->Ubf:J

    iget-wide v2, v13, Lcom/bytedance/sdk/openadsdk/core/model/dG;->WR:J

    iget v4, v0, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->vYf:I

    if-nez v4, :cond_2

    iget-object v4, v0, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->hjc:Landroid/content/Context;

    iget v13, v13, Lcom/bytedance/sdk/openadsdk/core/model/dG;->svN:I

    int-to-float v13, v13

    invoke-static {v4, v13}, Lcom/bytedance/sdk/openadsdk/utils/eh;->ex(Landroid/content/Context;F)I

    move-result v4

    add-int/2addr v4, v5

    const/4 v5, 0x0

    aput v4, v7, v5

    iget-object v4, v0, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->hjc:Landroid/content/Context;

    iget-object v5, v0, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->eV:Lcom/bytedance/sdk/openadsdk/core/model/dG;

    iget v5, v5, Lcom/bytedance/sdk/openadsdk/core/model/dG;->BcC:I

    int-to-float v5, v5

    invoke-static {v4, v5}, Lcom/bytedance/sdk/openadsdk/utils/eh;->ex(Landroid/content/Context;F)I

    move-result v4

    add-int/2addr v4, v6

    const/4 v5, 0x1

    aput v4, v7, v5

    iget-object v4, v0, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->hjc:Landroid/content/Context;

    iget-object v5, v0, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->eV:Lcom/bytedance/sdk/openadsdk/core/model/dG;

    iget v5, v5, Lcom/bytedance/sdk/openadsdk/core/model/dG;->mSE:I

    int-to-float v5, v5

    invoke-static {v4, v5}, Lcom/bytedance/sdk/openadsdk/utils/eh;->ex(Landroid/content/Context;F)I

    move-result v4

    iget-object v5, v0, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->hjc:Landroid/content/Context;

    iget-object v6, v0, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->eV:Lcom/bytedance/sdk/openadsdk/core/model/dG;

    iget v6, v6, Lcom/bytedance/sdk/openadsdk/core/model/dG;->Ko:I

    int-to-float v6, v6

    invoke-static {v5, v6}, Lcom/bytedance/sdk/openadsdk/utils/eh;->ex(Landroid/content/Context;F)I

    move-result v5

    move v13, v5

    const/4 v5, 0x0

    const/4 v6, 0x1

    goto :goto_1

    :cond_2
    iget v4, v13, Lcom/bytedance/sdk/openadsdk/core/model/dG;->svN:I

    const/4 v5, 0x0

    aput v4, v7, v5

    iget v4, v13, Lcom/bytedance/sdk/openadsdk/core/model/dG;->BcC:I

    const/4 v6, 0x1

    aput v4, v7, v6

    iget v4, v13, Lcom/bytedance/sdk/openadsdk/core/model/dG;->mSE:I

    iget v13, v13, Lcom/bytedance/sdk/openadsdk/core/model/dG;->Ko:I

    :goto_1
    aput v4, v9, v5

    aput v13, v9, v6

    if-nez v4, :cond_3

    if-nez v13, :cond_3

    if-eqz p11, :cond_3

    invoke-static/range {p11 .. p11}, Lcom/bytedance/sdk/openadsdk/utils/eh;->Fj(Landroid/view/View;)[I

    move-result-object v7

    invoke-static/range {p11 .. p11}, Lcom/bytedance/sdk/openadsdk/utils/eh;->hjc(Landroid/view/View;)[I

    move-result-object v9

    :cond_3
    const/4 v4, 0x0

    goto :goto_2

    :cond_4
    const/4 v6, 0x1

    move-wide/from16 v14, p6

    move-wide/from16 v2, p8

    :goto_2
    iput v4, v0, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->vYf:I

    new-instance v4, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    invoke-direct {v4}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;-><init>()V

    invoke-virtual {v4, v8}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->WR(F)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    move-result-object v4

    invoke-virtual {v4, v10}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->Ubf(F)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    move-result-object v4

    invoke-virtual {v4, v11}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->eV(F)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    move-result-object v4

    invoke-virtual {v4, v12}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->hjc(F)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    move-result-object v4

    invoke-virtual {v4, v14, v15}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->ex(J)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    move-result-object v4

    invoke-virtual {v4, v2, v3}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->Fj(J)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    move-result-object v2

    invoke-virtual {v2, v1}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->ex([I)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    move-result-object v1

    invoke-virtual {v1, v7}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->Fj([I)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    move-result-object v1

    invoke-static/range {p10 .. p10}, Lcom/bytedance/sdk/openadsdk/utils/eh;->hjc(Landroid/view/View;)[I

    move-result-object v2

    invoke-virtual {v1, v2}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->hjc([I)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    move-result-object v1

    invoke-virtual {v1, v9}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->eV([I)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    move-result-object v1

    iget v2, v0, Lcom/bytedance/sdk/openadsdk/core/ex/hjc;->Moo:I

    invoke-virtual {v1, v2}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->eV(I)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    move-result-object v1

    iget v2, v0, Lcom/bytedance/sdk/openadsdk/core/ex/hjc;->rf:I

    invoke-virtual {v1, v2}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->Ubf(I)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    move-result-object v1

    iget v2, v0, Lcom/bytedance/sdk/openadsdk/core/ex/hjc;->uy:I

    invoke-virtual {v1, v2}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->WR(I)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    move-result-object v1

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/BcC;->ex()Lcom/bytedance/sdk/openadsdk/core/BcC;

    move-result-object v2

    invoke-virtual {v2}, Lcom/bytedance/sdk/openadsdk/core/BcC;->Fj()Z

    move-result v2

    if-eqz v2, :cond_5

    const/4 v2, 0x1

    goto :goto_3

    :cond_5
    const/4 v2, 0x2

    :goto_3
    invoke-virtual {v1, v2}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->ex(I)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    move-result-object v1

    move-object/from16 v2, p5

    invoke-virtual {v1, v2}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->Fj(Landroid/util/SparseArray;)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    move-result-object v1

    move-object/from16 v2, p12

    invoke-virtual {v1, v2}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->Fj(Ljava/lang/String;)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    move-result-object v1

    move/from16 v2, p16

    invoke-virtual {v1, v2}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->Fj(I)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    move-result-object v1

    move-object/from16 v2, p17

    invoke-virtual {v1, v2}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->Fj(Lorg/json/JSONObject;)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    move-result-object v1

    invoke-virtual {v1}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->Fj()Lcom/bytedance/sdk/openadsdk/core/model/mSE;

    move-result-object v1

    return-object v1
.end method

.method public Fj(Lcom/bytedance/sdk/openadsdk/core/model/dG;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->eV:Lcom/bytedance/sdk/openadsdk/core/model/dG;

    return-void
.end method
