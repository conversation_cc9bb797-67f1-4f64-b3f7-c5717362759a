.class public Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/sdk/openadsdk/core/model/mSE;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "Fj"
.end annotation


# instance fields
.field private Af:Lorg/json/JSONObject;

.field private BcC:F

.field Fj:F

.field private JU:I

.field private JW:I

.field private Ko:[I

.field private Ql:Landroid/util/SparseArray;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Landroid/util/SparseArray<",
            "Lcom/bytedance/sdk/openadsdk/core/ex/hjc$Fj;",
            ">;"
        }
    .end annotation
.end field

.field private Tc:I

.field private UYd:[I

.field private Ubf:J

.field private WR:F

.field private dG:[I

.field private eV:J

.field ex:I

.field hjc:F

.field private mE:I

.field private mSE:F

.field private rAx:[I

.field private rS:I

.field private svN:F

.field private vYf:Ljava/lang/String;


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static synthetic BcC(Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;)F
    .locals 0

    iget p0, p0, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->WR:F

    return p0
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;)[I
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->rAx:[I

    return-object p0
.end method

.method public static synthetic JU(Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;)Ljava/lang/String;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->vYf:Ljava/lang/String;

    return-object p0
.end method

.method public static synthetic JW(Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;)Landroid/util/SparseArray;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->Ql:Landroid/util/SparseArray;

    return-object p0
.end method

.method public static synthetic Ko(Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;)J
    .locals 2

    iget-wide v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->eV:J

    return-wide v0
.end method

.method public static synthetic Ql(Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;)I
    .locals 0

    iget p0, p0, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->mE:I

    return p0
.end method

.method public static synthetic Tc(Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;)I
    .locals 0

    iget p0, p0, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->rS:I

    return p0
.end method

.method public static synthetic UYd(Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;)I
    .locals 0

    iget p0, p0, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->JW:I

    return p0
.end method

.method public static synthetic Ubf(Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;)F
    .locals 0

    iget p0, p0, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->mSE:F

    return p0
.end method

.method public static synthetic WR(Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;)F
    .locals 0

    iget p0, p0, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->BcC:F

    return p0
.end method

.method public static synthetic dG(Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;)I
    .locals 0

    iget p0, p0, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->JU:I

    return p0
.end method

.method public static synthetic eV(Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;)[I
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->Ko:[I

    return-object p0
.end method

.method public static synthetic ex(Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;)[I
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->UYd:[I

    return-object p0
.end method

.method public static synthetic hjc(Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;)[I
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->dG:[I

    return-object p0
.end method

.method public static synthetic mSE(Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;)J
    .locals 2

    iget-wide v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->Ubf:J

    return-wide v0
.end method

.method public static synthetic rAx(Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;)I
    .locals 0

    iget p0, p0, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->Tc:I

    return p0
.end method

.method public static synthetic rS(Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;)Lorg/json/JSONObject;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->Af:Lorg/json/JSONObject;

    return-object p0
.end method

.method public static synthetic svN(Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;)F
    .locals 0

    iget p0, p0, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->svN:F

    return p0
.end method


# virtual methods
.method public Fj(F)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->Fj:F

    return-object p0
.end method

.method public Fj(I)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->mE:I

    return-object p0
.end method

.method public Fj(J)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;
    .locals 0

    iput-wide p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->eV:J

    return-object p0
.end method

.method public Fj(Landroid/util/SparseArray;)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/util/SparseArray<",
            "Lcom/bytedance/sdk/openadsdk/core/ex/hjc$Fj;",
            ">;)",
            "Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;"
        }
    .end annotation

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->Ql:Landroid/util/SparseArray;

    return-object p0
.end method

.method public Fj(Ljava/lang/String;)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->vYf:Ljava/lang/String;

    return-object p0
.end method

.method public Fj(Lorg/json/JSONObject;)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->Af:Lorg/json/JSONObject;

    return-object p0
.end method

.method public Fj([I)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->Ko:[I

    return-object p0
.end method

.method public Fj()Lcom/bytedance/sdk/openadsdk/core/model/mSE;
    .locals 2

    new-instance v0, Lcom/bytedance/sdk/openadsdk/core/model/mSE;

    const/4 v1, 0x0

    invoke-direct {v0, p0, v1}, Lcom/bytedance/sdk/openadsdk/core/model/mSE;-><init>(Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;Lcom/bytedance/sdk/openadsdk/core/model/mSE$1;)V

    return-object v0
.end method

.method public Ubf(F)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->BcC:F

    return-object p0
.end method

.method public Ubf(I)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->JW:I

    return-object p0
.end method

.method public WR(F)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->mSE:F

    return-object p0
.end method

.method public WR(I)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->JU:I

    return-object p0
.end method

.method public eV(F)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->svN:F

    return-object p0
.end method

.method public eV(I)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->Tc:I

    return-object p0
.end method

.method public eV([I)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->dG:[I

    return-object p0
.end method

.method public ex(F)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->hjc:F

    return-object p0
.end method

.method public ex(I)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->rS:I

    return-object p0
.end method

.method public ex(J)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;
    .locals 0

    iput-wide p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->Ubf:J

    return-object p0
.end method

.method public ex([I)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->rAx:[I

    return-object p0
.end method

.method public hjc(F)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->WR:F

    return-object p0
.end method

.method public hjc(I)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->ex:I

    return-object p0
.end method

.method public hjc([I)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->UYd:[I

    return-object p0
.end method
