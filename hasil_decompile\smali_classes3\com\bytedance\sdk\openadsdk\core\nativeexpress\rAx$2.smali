.class Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx$2;
.super Lcom/bytedance/sdk/component/svN/BcC;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;->svN()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx$2;->Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;

    invoke-direct {p0, p2}, Lcom/bytedance/sdk/component/svN/BcC;-><init>(Ljava/lang/String;)V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 4

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx$2;->Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;->Fj(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;)Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx$2;->Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;

    invoke-static {v1}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;->ex(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/rAx;)Ljava/lang/String;

    move-result-object v1

    const-string v2, "dynamic_backup_render"

    const/4 v3, 0x0

    invoke-static {v0, v1, v2, v3}, Lcom/bytedance/sdk/openadsdk/ex/hjc;->ex(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Ljava/lang/String;Ljava/lang/String;Lorg/json/JSONObject;)V

    return-void
.end method
