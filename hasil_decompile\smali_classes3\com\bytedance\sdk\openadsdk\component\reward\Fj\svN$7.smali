.class Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN$7;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/component/utils/HomeWatcherReceiver$Fj;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;->WR()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN$7;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj()V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN$7;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;

    const/4 v1, 0x1

    iput-boolean v1, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;->hjc:Z

    return-void
.end method

.method public ex()V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN$7;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;

    const/4 v1, 0x1

    iput-boolean v1, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;->hjc:Z

    return-void
.end method
