.class public final Lcom/transsion/ninegridview/video/NineGridVideoView;
.super Landroid/view/ViewGroup;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/transsion/ninegridview/video/NineGridVideoView$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation

.annotation build Lkotlin/jvm/internal/SourceDebugExtension;
.end annotation


# static fields
.field public static final Companion:Lcom/transsion/ninegridview/video/NineGridVideoView$a;

.field public static s:Leo/b;


# instance fields
.field public a:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/transsion/moviedetailapi/bean/Image;",
            ">;"
        }
    .end annotation
.end field

.field public b:I

.field public c:I

.field public d:F

.field public e:I

.field public f:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/transsion/ninegridview/video/NineGridItemView;",
            ">;"
        }
    .end annotation
.end field

.field public g:I

.field public h:I

.field public i:I

.field public j:I

.field public k:I

.field public l:I

.field public m:I

.field public n:I

.field public final o:F

.field public final p:F

.field public q:Lcom/transsion/ninegridview/video/NineGridVideoViewAdapter;

.field public r:Z


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lcom/transsion/ninegridview/video/NineGridVideoView$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lcom/transsion/ninegridview/video/NineGridVideoView$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lcom/transsion/ninegridview/video/NineGridVideoView;->Companion:Lcom/transsion/ninegridview/video/NineGridVideoView$a;

    new-instance v0, Lcom/transsion/ninegridview/c;

    invoke-direct {v0}, Lcom/transsion/ninegridview/c;-><init>()V

    sput-object v0, Lcom/transsion/ninegridview/video/NineGridVideoView;->s:Leo/b;

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 1

    const-string v0, "context"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    const/4 v0, 0x0

    invoke-direct {p0, p1, v0}, Lcom/transsion/ninegridview/video/NineGridVideoView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 1

    const-string v0, "context"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    const/4 v0, 0x0

    invoke-direct {p0, p1, p2, v0}, Lcom/transsion/ninegridview/video/NineGridVideoView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 2

    const-string v0, "context"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-direct {p0, p1, p2, p3}, Landroid/view/ViewGroup;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    const/4 p3, 0x4

    iput p3, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->b:I

    const/16 p3, 0xfa

    iput p3, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->c:I

    const/high16 p3, 0x3f800000    # 1.0f

    iput p3, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->d:F

    const/16 p3, 0x9

    iput p3, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->e:I

    new-instance p3, Ljava/util/ArrayList;

    invoke-direct {p3}, Ljava/util/ArrayList;-><init>()V

    iput-object p3, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->f:Ljava/util/List;

    const p3, 0x40071c72

    iput p3, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->o:F

    const/high16 p3, 0x3f400000    # 0.75f

    iput p3, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->p:F

    const/4 p3, 0x1

    iput-boolean p3, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->r:Z

    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    invoke-virtual {v0}, Landroid/content/res/Resources;->getDisplayMetrics()Landroid/util/DisplayMetrics;

    move-result-object v0

    iget v1, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->b:I

    int-to-float v1, v1

    invoke-static {p3, v1, v0}, Landroid/util/TypedValue;->applyDimension(IFLandroid/util/DisplayMetrics;)F

    move-result v1

    float-to-int v1, v1

    iput v1, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->b:I

    iget v1, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->c:I

    int-to-float v1, v1

    invoke-static {p3, v1, v0}, Landroid/util/TypedValue;->applyDimension(IFLandroid/util/DisplayMetrics;)F

    move-result v0

    float-to-int v0, v0

    iput v0, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->c:I

    sget-object v0, Lcom/transsion/ninegridview/R$styleable;->NineGridView:[I

    invoke-virtual {p1, p2, v0}, Landroid/content/Context;->obtainStyledAttributes(Landroid/util/AttributeSet;[I)Landroid/content/res/TypedArray;

    move-result-object p1

    const-string p2, "context.obtainStyledAttr\u2026R.styleable.NineGridView)"

    invoke-static {p1, p2}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    sget p2, Lcom/transsion/ninegridview/R$styleable;->NineGridView_ngv_gridSpacing:I

    iget v0, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->b:I

    int-to-float v0, v0

    invoke-virtual {p1, p2, v0}, Landroid/content/res/TypedArray;->getDimension(IF)F

    move-result p2

    float-to-int p2, p2

    iput p2, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->b:I

    sget p2, Lcom/transsion/ninegridview/R$styleable;->NineGridView_ngv_singleImageSize:I

    iget v0, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->c:I

    invoke-virtual {p1, p2, v0}, Landroid/content/res/TypedArray;->getDimensionPixelSize(II)I

    move-result p2

    iput p2, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->c:I

    sget p2, Lcom/transsion/ninegridview/R$styleable;->NineGridView_ngv_singleImageRatio:I

    iget v0, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->d:F

    invoke-virtual {p1, p2, v0}, Landroid/content/res/TypedArray;->getFloat(IF)F

    move-result p2

    iput p2, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->d:F

    sget p2, Lcom/transsion/ninegridview/R$styleable;->NineGridView_ngv_maxSize:I

    iget v0, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->e:I

    invoke-virtual {p1, p2, v0}, Landroid/content/res/TypedArray;->getInt(II)I

    move-result p2

    iput p2, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->e:I

    sget p2, Lcom/transsion/ninegridview/R$styleable;->NineGridView_enable_click:I

    invoke-virtual {p1, p2, p3}, Landroid/content/res/TypedArray;->getBoolean(IZ)Z

    move-result p2

    iput-boolean p2, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->r:Z

    invoke-virtual {p1}, Landroid/content/res/TypedArray;->recycle()V

    sget p1, Lcom/transsion/ninegridview/R$id;->nine_grid:I

    invoke-virtual {p0, p1}, Landroid/view/View;->setId(I)V

    return-void
.end method

.method public static synthetic a(Lcom/transsion/ninegridview/video/NineGridVideoView;Lcom/transsion/ninegridview/video/NineGridItemView;ILandroid/view/View;)V
    .locals 0

    invoke-static {p0, p1, p2, p3}, Lcom/transsion/ninegridview/video/NineGridVideoView;->i(Lcom/transsion/ninegridview/video/NineGridVideoView;Lcom/transsion/ninegridview/video/NineGridItemView;ILandroid/view/View;)V

    return-void
.end method

.method public static final synthetic access$getMImageLoader$cp()Leo/b;
    .locals 1

    sget-object v0, Lcom/transsion/ninegridview/video/NineGridVideoView;->s:Leo/b;

    return-object v0
.end method

.method public static final synthetic access$setMImageLoader$cp(Leo/b;)V
    .locals 0

    sput-object p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->s:Leo/b;

    return-void
.end method

.method public static final i(Lcom/transsion/ninegridview/video/NineGridVideoView;Lcom/transsion/ninegridview/video/NineGridItemView;ILandroid/view/View;)V
    .locals 1

    const-string p3, "this$0"

    invoke-static {p0, p3}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    const-string p3, "$this_apply"

    invoke-static {p1, p3}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    iget-object p3, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->q:Lcom/transsion/ninegridview/video/NineGridVideoViewAdapter;

    if-eqz p3, :cond_1

    invoke-virtual {p1}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object p1

    const-string v0, "context"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    iget-object v0, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->q:Lcom/transsion/ninegridview/video/NineGridVideoViewAdapter;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/transsion/ninegridview/video/NineGridVideoViewAdapter;->getImageList()Ljava/util/List;

    move-result-object v0

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    invoke-virtual {p3, p1, p0, p2, v0}, Lcom/transsion/ninegridview/video/NineGridVideoViewAdapter;->onItemClick(Landroid/content/Context;Lcom/transsion/ninegridview/video/NineGridVideoView;ILjava/util/List;)V

    :cond_1
    return-void
.end method


# virtual methods
.method public final b(Lcom/transsion/moviedetailapi/bean/Image;II)V
    .locals 4

    invoke-virtual {p1}, Lcom/transsion/moviedetailapi/bean/Image;->getWidth()Ljava/lang/Integer;

    move-result-object v0

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    move-result v0

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    invoke-virtual {p1}, Lcom/transsion/moviedetailapi/bean/Image;->getHeight()Ljava/lang/Integer;

    move-result-object p1

    if-eqz p1, :cond_1

    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    move-result p1

    goto :goto_1

    :cond_1
    const/4 p1, 0x0

    :goto_1
    if-lez v0, :cond_4

    if-lez p1, :cond_4

    invoke-virtual {p0, p2, p3}, Lcom/transsion/ninegridview/video/NineGridVideoView;->f(II)I

    move-result p2

    iput p2, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->i:I

    div-int p3, v0, p1

    int-to-float p3, p3

    iget v2, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->o:F

    cmpl-float v3, p3, v2

    if-lez v3, :cond_2

    :goto_2
    int-to-float p1, p2

    div-float/2addr p1, v2

    float-to-int p1, p1

    goto :goto_3

    :cond_2
    iget v2, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->p:F

    cmpg-float p3, p3, v2

    if-gez p3, :cond_3

    goto :goto_2

    :cond_3
    int-to-float p2, p2

    const/high16 p3, 0x3f800000    # 1.0f

    mul-float p2, p2, p3

    int-to-float p3, v0

    div-float/2addr p2, p3

    int-to-float p1, p1

    mul-float p2, p2, p1

    float-to-int p1, p2

    :goto_3
    iput p1, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->j:I

    iput v1, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->l:I

    iput v1, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->n:I

    :cond_4
    return-void
.end method

.method public final c(Lcom/transsion/moviedetailapi/bean/Image;II)V
    .locals 4

    invoke-virtual {p1}, Lcom/transsion/moviedetailapi/bean/Image;->getWidth()Ljava/lang/Integer;

    move-result-object v0

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    move-result v0

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    invoke-virtual {p1}, Lcom/transsion/moviedetailapi/bean/Image;->getHeight()Ljava/lang/Integer;

    move-result-object p1

    if-eqz p1, :cond_1

    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    move-result p1

    goto :goto_1

    :cond_1
    const/4 p1, 0x0

    :goto_1
    if-lez v0, :cond_4

    if-lez p1, :cond_4

    invoke-virtual {p0, p2, p3}, Lcom/transsion/ninegridview/video/NineGridVideoView;->f(II)I

    move-result p2

    iput p2, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->k:I

    div-int p3, v0, p1

    int-to-float p3, p3

    iget v2, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->o:F

    cmpl-float v3, p3, v2

    if-lez v3, :cond_2

    :goto_2
    int-to-float p1, p2

    div-float/2addr p1, v2

    float-to-int p1, p1

    goto :goto_3

    :cond_2
    iget v2, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->p:F

    cmpg-float p3, p3, v2

    if-gez p3, :cond_3

    goto :goto_2

    :cond_3
    int-to-float p2, p2

    const/high16 p3, 0x3f800000    # 1.0f

    mul-float p2, p2, p3

    int-to-float p3, v0

    div-float/2addr p2, p3

    int-to-float p1, p1

    mul-float p2, p2, p1

    float-to-int p1, p2

    :goto_3
    iput p1, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->l:I

    iput v1, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->n:I

    :cond_4
    return-void
.end method

.method public final currentGifVisible(I)Z
    .locals 7

    iget-object v0, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->a:Ljava/util/List;

    const/4 v1, 0x0

    if-eqz v0, :cond_3

    check-cast v0, Ljava/lang/Iterable;

    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object v0

    const/4 v2, 0x0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_3

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    add-int/lit8 v4, v2, 0x1

    if-gez v2, :cond_0

    invoke-static {}, Lkotlin/collections/CollectionsKt;->u()V

    :cond_0
    check-cast v3, Lcom/transsion/moviedetailapi/bean/Image;

    invoke-virtual {v3}, Lcom/transsion/moviedetailapi/bean/Image;->getGifBean()Lcom/transsion/moviedetailapi/bean/GifBean;

    move-result-object v3

    if-eqz v3, :cond_2

    invoke-virtual {v3}, Lcom/transsion/moviedetailapi/bean/GifBean;->getVideoUrl()Ljava/lang/String;

    move-result-object v3

    if-eqz v3, :cond_2

    invoke-interface {v3}, Ljava/lang/CharSequence;->length()I

    move-result v3

    if-lez v3, :cond_2

    if-ne p1, v2, :cond_2

    invoke-virtual {p0, v2}, Landroid/view/ViewGroup;->getChildAt(I)Landroid/view/View;

    move-result-object v2

    new-instance v3, Landroid/graphics/Rect;

    invoke-direct {v3}, Landroid/graphics/Rect;-><init>()V

    if-eqz v2, :cond_1

    invoke-virtual {v2, v3}, Landroid/view/View;->getGlobalVisibleRect(Landroid/graphics/Rect;)Z

    move-result v5

    invoke-static {v5}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v5

    goto :goto_1

    :cond_1
    const/4 v5, 0x0

    :goto_1
    sget-object v6, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    invoke-static {v5, v6}, Lkotlin/jvm/internal/Intrinsics;->b(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v5

    if-eqz v5, :cond_2

    invoke-virtual {v2}, Landroid/view/View;->getHeight()I

    move-result v2

    int-to-float v2, v2

    const/high16 v5, 0x3f800000    # 1.0f

    mul-float v2, v2, v5

    invoke-virtual {v3}, Landroid/graphics/Rect;->height()I

    move-result v3

    int-to-float v3, v3

    mul-float v3, v3, v5

    div-float/2addr v3, v2

    const/high16 v2, 0x3f000000    # 0.5f

    cmpl-float v2, v3, v2

    if-lez v2, :cond_2

    const/4 p1, 0x1

    return p1

    :cond_2
    move v2, v4

    goto :goto_0

    :cond_3
    return v1
.end method

.method public final d(Lcom/transsion/moviedetailapi/bean/Image;II)V
    .locals 3

    invoke-virtual {p1}, Lcom/transsion/moviedetailapi/bean/Image;->getWidth()Ljava/lang/Integer;

    move-result-object v0

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    move-result v0

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    invoke-virtual {p1}, Lcom/transsion/moviedetailapi/bean/Image;->getHeight()Ljava/lang/Integer;

    move-result-object p1

    if-eqz p1, :cond_1

    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    move-result v1

    :cond_1
    if-lez v0, :cond_4

    if-lez v1, :cond_4

    invoke-virtual {p0, p2, p3}, Lcom/transsion/ninegridview/video/NineGridVideoView;->f(II)I

    move-result p1

    iput p1, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->m:I

    div-int p2, v0, v1

    int-to-float p2, p2

    iget p3, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->o:F

    cmpl-float v2, p2, p3

    if-lez v2, :cond_2

    :goto_1
    int-to-float p1, p1

    div-float/2addr p1, p3

    :goto_2
    float-to-int p1, p1

    goto :goto_3

    :cond_2
    iget p3, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->p:F

    cmpg-float p2, p2, p3

    if-gez p2, :cond_3

    goto :goto_1

    :cond_3
    int-to-float p1, p1

    const/high16 p2, 0x3f800000    # 1.0f

    mul-float p1, p1, p2

    int-to-float p2, v0

    div-float/2addr p1, p2

    int-to-float p2, v1

    mul-float p1, p1, p2

    goto :goto_2

    :goto_3
    iput p1, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->n:I

    :cond_4
    return-void
.end method

.method public final e()V
    .locals 6

    invoke-virtual {p0}, Landroid/view/ViewGroup;->getChildCount()I

    move-result v0

    const/4 v1, 0x0

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v0, :cond_3

    invoke-virtual {p0, v2}, Landroid/view/ViewGroup;->getChildAt(I)Landroid/view/View;

    move-result-object v3

    if-eqz v3, :cond_2

    instance-of v4, v3, Lcom/transsion/ninegridview/video/NineGridItemView;

    if-eqz v4, :cond_2

    sget v4, Lcom/transsion/ninegridview/R$id;->video_container:I

    invoke-virtual {v3, v4}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    move-result-object v4

    check-cast v4, Landroid/widget/FrameLayout;

    if-eqz v4, :cond_0

    invoke-virtual {v4}, Landroid/view/ViewGroup;->removeAllViews()V

    :cond_0
    sget v4, Lcom/transsion/ninegridview/R$id;->video_cover:I

    invoke-virtual {v3, v4}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    move-result-object v4

    check-cast v4, Lcom/google/android/material/imageview/ShapeableImageView;

    if-eqz v4, :cond_1

    const-string v5, "findViewById<ShapeableImageView>(R.id.video_cover)"

    invoke-static {v4, v5}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-static {v4}, Lvi/c;->k(Landroid/view/View;)V

    invoke-virtual {v4, v1}, Landroidx/appcompat/widget/AppCompatImageView;->setImageResource(I)V

    :cond_1
    sget v4, Lcom/transsion/ninegridview/R$id;->label_gif:I

    invoke-virtual {v3, v4}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    move-result-object v3

    check-cast v3, Landroidx/appcompat/widget/AppCompatImageView;

    if-eqz v3, :cond_2

    const-string v4, "findViewById<AppCompatImageView>(R.id.label_gif)"

    invoke-static {v3, v4}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-static {v3}, Lvi/c;->g(Landroid/view/View;)V

    :cond_2
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_3
    return-void
.end method

.method public final f(II)I
    .locals 2

    iget v0, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->b:I

    add-int/lit8 v1, p2, -0x1

    mul-int v0, v0, v1

    sub-int/2addr p1, v0

    div-int/2addr p1, p2

    return p1
.end method

.method public final g(I)I
    .locals 4

    iget-object v0, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->a:Ljava/util/List;

    const/4 v1, 0x0

    if-eqz v0, :cond_4

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    const/4 v2, 0x1

    const/4 v3, 0x3

    packed-switch v0, :pswitch_data_0

    goto :goto_4

    :pswitch_0
    const/4 v0, 0x5

    if-ge p1, v0, :cond_0

    rem-int/2addr p1, v3

    goto :goto_0

    :cond_0
    add-int/lit8 p1, p1, -0x2

    rem-int/2addr p1, v3

    :goto_0
    return p1

    :pswitch_1
    const/4 v0, 0x4

    if-ge p1, v0, :cond_1

    rem-int/2addr p1, v3

    goto :goto_1

    :cond_1
    sub-int/2addr p1, v2

    rem-int/2addr p1, v3

    :goto_1
    return p1

    :pswitch_2
    if-ge p1, v3, :cond_2

    rem-int/lit8 p1, p1, 0x2

    goto :goto_2

    :cond_2
    sub-int/2addr p1, v2

    rem-int/lit8 p1, p1, 0x2

    :goto_2
    return p1

    :pswitch_3
    rem-int/lit8 p1, p1, 0x2

    if-nez p1, :cond_3

    goto :goto_3

    :cond_3
    const/4 v1, 0x1

    :goto_3
    return v1

    :pswitch_4
    rem-int/2addr p1, v3

    return p1

    :cond_4
    :goto_4
    return v1

    nop

    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_4
        :pswitch_4
        :pswitch_3
        :pswitch_3
        :pswitch_2
        :pswitch_4
        :pswitch_1
        :pswitch_0
        :pswitch_4
    .end packed-switch
.end method

.method public final getGridSpacing()I
    .locals 1

    iget v0, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->b:I

    return v0
.end method

.method public final getMaxImageSize()I
    .locals 1

    iget v0, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->e:I

    return v0
.end method

.method public final getSingleImageRatio()F
    .locals 1

    iget v0, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->d:F

    return v0
.end method

.method public final getSingleImageSize()I
    .locals 1

    iget v0, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->c:I

    return v0
.end method

.method public final h(I)Lcom/transsion/ninegridview/video/NineGridItemView;
    .locals 3

    iget-object v0, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->q:Lcom/transsion/ninegridview/video/NineGridVideoViewAdapter;

    if-eqz v0, :cond_5

    iget-object v1, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->f:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    if-ge p1, v1, :cond_2

    iget-object v0, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->f:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/transsion/ninegridview/video/NineGridItemView;

    sget v0, Lcom/transsion/ninegridview/R$id;->video_container:I

    invoke-virtual {p1, v0}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/FrameLayout;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Landroid/view/ViewGroup;->removeAllViews()V

    :cond_0
    sget v0, Lcom/transsion/ninegridview/R$id;->video_cover:I

    invoke-virtual {p1, v0}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Lcom/google/android/material/imageview/ShapeableImageView;

    if-eqz v0, :cond_1

    const-string v1, "findViewById<ShapeableImageView>(R.id.video_cover)"

    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Landroidx/appcompat/widget/AppCompatImageView;->setImageResource(I)V

    invoke-static {v0}, Lvi/c;->k(Landroid/view/View;)V

    :cond_1
    sget v0, Lcom/transsion/ninegridview/R$id;->label_gif:I

    invoke-virtual {p1, v0}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroidx/appcompat/widget/AppCompatImageView;

    if-eqz v0, :cond_4

    const-string v1, "findViewById<AppCompatImageView>(R.id.label_gif)"

    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-static {v0}, Lvi/c;->g(Landroid/view/View;)V

    goto :goto_0

    :cond_2
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v1

    const-string v2, "context"

    invoke-static {v1, v2}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {v0, v1}, Lcom/transsion/ninegridview/video/NineGridVideoViewAdapter;->generateItemView(Landroid/content/Context;)Lcom/transsion/ninegridview/video/NineGridItemView;

    move-result-object v0

    iget-boolean v1, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->r:Z

    if-eqz v1, :cond_3

    new-instance v1, Lfo/c;

    invoke-direct {v1, p0, v0, p1}, Lfo/c;-><init>(Lcom/transsion/ninegridview/video/NineGridVideoView;Lcom/transsion/ninegridview/video/NineGridItemView;I)V

    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    iget-object p1, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->f:Ljava/util/List;

    invoke-interface {p1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :cond_3
    move-object p1, v0

    :cond_4
    :goto_0
    return-object p1

    :cond_5
    const/4 p1, 0x0

    return-object p1
.end method

.method public final hasGifVisible()Z
    .locals 7

    iget-object v0, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->a:Ljava/util/List;

    const/4 v1, 0x0

    if-eqz v0, :cond_3

    check-cast v0, Ljava/lang/Iterable;

    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object v0

    const/4 v2, 0x0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_3

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    add-int/lit8 v4, v2, 0x1

    if-gez v2, :cond_0

    invoke-static {}, Lkotlin/collections/CollectionsKt;->u()V

    :cond_0
    check-cast v3, Lcom/transsion/moviedetailapi/bean/Image;

    invoke-virtual {v3}, Lcom/transsion/moviedetailapi/bean/Image;->getGifBean()Lcom/transsion/moviedetailapi/bean/GifBean;

    move-result-object v3

    if-eqz v3, :cond_2

    invoke-virtual {v3}, Lcom/transsion/moviedetailapi/bean/GifBean;->getVideoUrl()Ljava/lang/String;

    move-result-object v3

    if-eqz v3, :cond_2

    invoke-interface {v3}, Ljava/lang/CharSequence;->length()I

    move-result v3

    if-lez v3, :cond_2

    invoke-virtual {p0, v2}, Landroid/view/ViewGroup;->getChildAt(I)Landroid/view/View;

    move-result-object v2

    new-instance v3, Landroid/graphics/Rect;

    invoke-direct {v3}, Landroid/graphics/Rect;-><init>()V

    if-eqz v2, :cond_1

    invoke-virtual {v2, v3}, Landroid/view/View;->getGlobalVisibleRect(Landroid/graphics/Rect;)Z

    move-result v5

    invoke-static {v5}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v5

    goto :goto_1

    :cond_1
    const/4 v5, 0x0

    :goto_1
    sget-object v6, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    invoke-static {v5, v6}, Lkotlin/jvm/internal/Intrinsics;->b(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v5

    if-eqz v5, :cond_2

    invoke-virtual {v2}, Landroid/view/View;->getHeight()I

    move-result v2

    int-to-float v2, v2

    const/high16 v5, 0x3f800000    # 1.0f

    mul-float v2, v2, v5

    invoke-virtual {v3}, Landroid/graphics/Rect;->height()I

    move-result v3

    int-to-float v3, v3

    mul-float v3, v3, v5

    div-float/2addr v3, v2

    const/high16 v2, 0x3f000000    # 0.5f

    cmpl-float v2, v3, v2

    if-lez v2, :cond_2

    const/4 v0, 0x1

    return v0

    :cond_2
    move v2, v4

    goto :goto_0

    :cond_3
    return v1
.end method

.method public final j(I)I
    .locals 4

    iget-object v0, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->a:Ljava/util/List;

    if-eqz v0, :cond_a

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    const/4 v1, 0x1

    const/4 v2, 0x2

    const/4 v3, 0x3

    packed-switch v0, :pswitch_data_0

    goto :goto_6

    :pswitch_0
    if-ge p1, v3, :cond_0

    goto :goto_0

    :cond_0
    if-gt v3, p1, :cond_1

    const/4 v0, 0x6

    if-ge p1, v0, :cond_1

    const/4 v1, 0x2

    goto :goto_0

    :cond_1
    const/4 v1, 0x3

    :goto_0
    return v1

    :pswitch_1
    if-ge p1, v3, :cond_2

    goto :goto_1

    :cond_2
    if-gt v3, p1, :cond_3

    const/4 v0, 0x5

    if-ge p1, v0, :cond_3

    const/4 v1, 0x2

    goto :goto_1

    :cond_3
    const/4 v1, 0x3

    :goto_1
    return v1

    :pswitch_2
    if-ge p1, v3, :cond_4

    goto :goto_2

    :cond_4
    if-ne p1, v3, :cond_5

    const/4 v1, 0x2

    goto :goto_2

    :cond_5
    const/4 v1, 0x3

    :goto_2
    return v1

    :pswitch_3
    if-ge p1, v3, :cond_6

    goto :goto_3

    :cond_6
    const/4 v1, 0x2

    :goto_3
    return v1

    :pswitch_4
    if-ge p1, v2, :cond_7

    goto :goto_4

    :cond_7
    if-ne p1, v2, :cond_8

    const/4 v1, 0x2

    goto :goto_4

    :cond_8
    const/4 v1, 0x3

    :goto_4
    return v1

    :pswitch_5
    if-ge p1, v2, :cond_9

    goto :goto_5

    :cond_9
    const/4 v1, 0x2

    :goto_5
    :pswitch_6
    return v1

    :cond_a
    :goto_6
    const/4 p1, 0x0

    return p1

    nop

    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_6
        :pswitch_6
        :pswitch_5
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method

.method public final k(Lcom/transsion/moviedetailapi/bean/Image;Lcom/transsion/moviedetailapi/bean/Image;)Lcom/transsion/moviedetailapi/bean/Image;
    .locals 3

    invoke-virtual {p1}, Lcom/transsion/moviedetailapi/bean/Image;->getHeight()Ljava/lang/Integer;

    move-result-object v0

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    move-result v0

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    invoke-virtual {p2}, Lcom/transsion/moviedetailapi/bean/Image;->getHeight()Ljava/lang/Integer;

    move-result-object v2

    if-eqz v2, :cond_1

    invoke-virtual {v2}, Ljava/lang/Integer;->intValue()I

    move-result v1

    :cond_1
    if-gt v0, v1, :cond_2

    return-object p1

    :cond_2
    return-object p2
.end method

.method public final l(Lcom/transsion/moviedetailapi/bean/Image;Lcom/transsion/moviedetailapi/bean/Image;Lcom/transsion/moviedetailapi/bean/Image;)Lcom/transsion/moviedetailapi/bean/Image;
    .locals 0

    invoke-virtual {p0, p1, p2}, Lcom/transsion/ninegridview/video/NineGridVideoView;->k(Lcom/transsion/moviedetailapi/bean/Image;Lcom/transsion/moviedetailapi/bean/Image;)Lcom/transsion/moviedetailapi/bean/Image;

    move-result-object p1

    invoke-virtual {p0, p1, p3}, Lcom/transsion/ninegridview/video/NineGridVideoView;->k(Lcom/transsion/moviedetailapi/bean/Image;Lcom/transsion/moviedetailapi/bean/Image;)Lcom/transsion/moviedetailapi/bean/Image;

    move-result-object p1

    return-object p1
.end method

.method public final loadImage()V
    .locals 12

    invoke-virtual {p0}, Landroid/view/ViewGroup;->getChildCount()I

    move-result v0

    const/4 v1, 0x0

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v0, :cond_3

    invoke-virtual {p0, v2}, Landroid/view/ViewGroup;->getChildAt(I)Landroid/view/View;

    move-result-object v3

    const-string v4, "null cannot be cast to non-null type com.transsion.ninegridview.video.NineGridItemView"

    invoke-static {v3, v4}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/String;)V

    check-cast v3, Lcom/transsion/ninegridview/video/NineGridItemView;

    iget-object v4, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->a:Ljava/util/List;

    const/4 v11, 0x0

    if-eqz v4, :cond_0

    invoke-interface {v4, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lcom/transsion/moviedetailapi/bean/Image;

    goto :goto_1

    :cond_0
    move-object v4, v11

    :goto_1
    sget-object v5, Lcom/transsion/ninegridview/video/NineGridVideoView;->s:Leo/b;

    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v6

    invoke-virtual {v3}, Landroid/view/View;->getWidth()I

    move-result v9

    invoke-virtual {v3}, Landroid/view/View;->getHeight()I

    move-result v10

    move-object v7, v3

    move-object v8, v4

    invoke-interface/range {v5 .. v10}, Leo/b;->b(Landroid/content/Context;Lcom/transsion/ninegridview/video/NineGridItemView;Lcom/transsion/moviedetailapi/bean/Image;II)V

    sget v5, Lcom/transsion/ninegridview/R$id;->label_gif:I

    invoke-virtual {v3, v5}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    move-result-object v3

    check-cast v3, Landroidx/appcompat/widget/AppCompatImageView;

    if-eqz v3, :cond_2

    if-eqz v4, :cond_1

    invoke-virtual {v4}, Lcom/transsion/moviedetailapi/bean/Image;->getUrl()Ljava/lang/String;

    move-result-object v4

    if-eqz v4, :cond_1

    const-string v5, ".gif"

    const/4 v6, 0x2

    invoke-static {v4, v5, v1, v6, v11}, Lkotlin/text/StringsKt;->t(Ljava/lang/String;Ljava/lang/String;ZILjava/lang/Object;)Z

    move-result v4

    const/4 v5, 0x1

    if-ne v4, v5, :cond_1

    invoke-static {v3}, Lvi/c;->k(Landroid/view/View;)V

    goto :goto_2

    :cond_1
    invoke-static {v3}, Lvi/c;->g(Landroid/view/View;)V

    :cond_2
    :goto_2
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_3
    return-void
.end method

.method public final m(Lcom/transsion/ninegridview/video/NineGridItemView;IIIIII)V
    .locals 0

    invoke-virtual {p1, p2, p3, p4, p5}, Landroid/view/View;->layout(IIII)V

    sget p2, Lcom/transsion/ninegridview/R$id;->video_cover:I

    invoke-virtual {p1, p2}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    move-result-object p2

    check-cast p2, Lcom/google/android/material/imageview/ShapeableImageView;

    const/4 p3, 0x0

    invoke-virtual {p2, p3, p3, p6, p7}, Landroid/view/View;->layout(IIII)V

    sget p2, Lcom/transsion/ninegridview/R$id;->video_container:I

    invoke-virtual {p1, p2}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    move-result-object p2

    check-cast p2, Landroid/widget/FrameLayout;

    invoke-virtual {p2, p3, p3, p6, p7}, Landroid/view/View;->layout(IIII)V

    sget p2, Lcom/transsion/ninegridview/R$id;->player_view:I

    invoke-virtual {p1, p2}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    move-result-object p1

    check-cast p1, Lcom/transsion/player/ui/ORPlayerView;

    if-eqz p1, :cond_0

    invoke-virtual {p1, p3, p3, p6, p7}, Landroid/view/View;->layout(IIII)V

    :cond_0
    return-void
.end method

.method public onLayout(ZIIII)V
    .locals 8

    iget-object p1, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->a:Ljava/util/List;

    if-eqz p1, :cond_5

    if-eqz p1, :cond_0

    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result p1

    if-nez p1, :cond_0

    goto/16 :goto_2

    :cond_0
    iget-object p1, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->a:Ljava/util/List;

    const/4 p2, 0x0

    if-eqz p1, :cond_1

    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result p1

    goto :goto_0

    :cond_1
    const/4 p1, 0x0

    :goto_0
    if-ge p2, p1, :cond_5

    invoke-virtual {p0, p2}, Landroid/view/ViewGroup;->getChildAt(I)Landroid/view/View;

    move-result-object p3

    const-string p4, "null cannot be cast to non-null type com.transsion.ninegridview.video.NineGridItemView"

    invoke-static {p3, p4}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/String;)V

    move-object v1, p3

    check-cast v1, Lcom/transsion/ninegridview/video/NineGridItemView;

    invoke-virtual {p0, p2}, Lcom/transsion/ninegridview/video/NineGridVideoView;->j(I)I

    move-result p3

    invoke-virtual {p0, p2}, Lcom/transsion/ninegridview/video/NineGridVideoView;->g(I)I

    move-result p4

    const/4 p5, 0x1

    if-eq p3, p5, :cond_4

    const/4 p5, 0x2

    if-eq p3, p5, :cond_3

    const/4 p5, 0x3

    if-eq p3, p5, :cond_2

    goto :goto_1

    :cond_2
    iget p5, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->m:I

    iget v0, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->b:I

    add-int/2addr p5, v0

    mul-int p5, p5, p4

    invoke-virtual {p0}, Landroid/view/View;->getPaddingLeft()I

    move-result p4

    add-int v2, p5, p4

    iget p4, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->b:I

    add-int/lit8 p3, p3, -0x1

    mul-int p4, p4, p3

    iget p3, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->j:I

    add-int/2addr p4, p3

    iget p3, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->l:I

    add-int/2addr p4, p3

    invoke-virtual {p0}, Landroid/view/View;->getPaddingTop()I

    move-result p3

    add-int v3, p4, p3

    iget v6, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->m:I

    add-int v4, v2, v6

    iget v7, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->n:I

    add-int v5, v3, v7

    move-object v0, p0

    invoke-virtual/range {v0 .. v7}, Lcom/transsion/ninegridview/video/NineGridVideoView;->m(Lcom/transsion/ninegridview/video/NineGridItemView;IIIIII)V

    goto :goto_1

    :cond_3
    iget p5, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->k:I

    iget v0, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->b:I

    add-int/2addr p5, v0

    mul-int p5, p5, p4

    invoke-virtual {p0}, Landroid/view/View;->getPaddingLeft()I

    move-result p4

    add-int v2, p5, p4

    iget p4, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->b:I

    add-int/lit8 p3, p3, -0x1

    mul-int p4, p4, p3

    iget p3, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->j:I

    add-int/2addr p4, p3

    invoke-virtual {p0}, Landroid/view/View;->getPaddingTop()I

    move-result p3

    add-int v3, p4, p3

    iget v6, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->k:I

    add-int v4, v2, v6

    iget v7, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->l:I

    add-int v5, v3, v7

    move-object v0, p0

    invoke-virtual/range {v0 .. v7}, Lcom/transsion/ninegridview/video/NineGridVideoView;->m(Lcom/transsion/ninegridview/video/NineGridItemView;IIIIII)V

    goto :goto_1

    :cond_4
    iget p3, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->i:I

    iget p5, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->b:I

    add-int/2addr p3, p5

    mul-int p3, p3, p4

    invoke-virtual {p0}, Landroid/view/View;->getPaddingLeft()I

    move-result p4

    add-int v2, p3, p4

    invoke-virtual {p0}, Landroid/view/View;->getPaddingTop()I

    move-result v3

    iget v6, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->i:I

    add-int v4, v2, v6

    iget v7, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->j:I

    add-int v5, v3, v7

    move-object v0, p0

    invoke-virtual/range {v0 .. v7}, Lcom/transsion/ninegridview/video/NineGridVideoView;->m(Lcom/transsion/ninegridview/video/NineGridItemView;IIIIII)V

    :goto_1
    add-int/lit8 p2, p2, 0x1

    goto/16 :goto_0

    :cond_5
    :goto_2
    return-void
.end method

.method public onMeasure(II)V
    .locals 10

    invoke-super {p0, p1, p2}, Landroid/view/ViewGroup;->onMeasure(II)V

    invoke-static {p1}, Landroid/view/View$MeasureSpec;->getSize(I)I

    move-result v0

    invoke-virtual {p0}, Landroid/view/View;->getPaddingLeft()I

    move-result v1

    sub-int v1, v0, v1

    invoke-virtual {p0}, Landroid/view/View;->getPaddingRight()I

    move-result v2

    sub-int/2addr v1, v2

    invoke-virtual {p0, p1, p2}, Landroid/view/ViewGroup;->measureChildren(II)V

    iget-object p1, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->a:Ljava/util/List;

    const/4 p2, 0x0

    if-eqz p1, :cond_0

    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result v0

    const/4 v2, 0x7

    const/4 v3, 0x6

    const/4 v4, 0x5

    const/4 v5, 0x4

    const/4 v6, 0x1

    const/4 v7, 0x2

    const/4 v8, 0x3

    packed-switch v0, :pswitch_data_0

    goto/16 :goto_0

    :pswitch_0
    invoke-interface {p1, p2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lcom/transsion/moviedetailapi/bean/Image;

    invoke-interface {p1, v6}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/transsion/moviedetailapi/bean/Image;

    invoke-interface {p1, v7}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v7

    check-cast v7, Lcom/transsion/moviedetailapi/bean/Image;

    invoke-virtual {p0, p2, v0, v7}, Lcom/transsion/ninegridview/video/NineGridVideoView;->l(Lcom/transsion/moviedetailapi/bean/Image;Lcom/transsion/moviedetailapi/bean/Image;Lcom/transsion/moviedetailapi/bean/Image;)Lcom/transsion/moviedetailapi/bean/Image;

    move-result-object p2

    invoke-virtual {p0, p2, v1, v8}, Lcom/transsion/ninegridview/video/NineGridVideoView;->b(Lcom/transsion/moviedetailapi/bean/Image;II)V

    invoke-interface {p1, v8}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lcom/transsion/moviedetailapi/bean/Image;

    invoke-interface {p1, v5}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/transsion/moviedetailapi/bean/Image;

    invoke-interface {p1, v4}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lcom/transsion/moviedetailapi/bean/Image;

    invoke-virtual {p0, p2, v0, v4}, Lcom/transsion/ninegridview/video/NineGridVideoView;->l(Lcom/transsion/moviedetailapi/bean/Image;Lcom/transsion/moviedetailapi/bean/Image;Lcom/transsion/moviedetailapi/bean/Image;)Lcom/transsion/moviedetailapi/bean/Image;

    move-result-object p2

    invoke-virtual {p0, p2, v1, v8}, Lcom/transsion/ninegridview/video/NineGridVideoView;->c(Lcom/transsion/moviedetailapi/bean/Image;II)V

    invoke-interface {p1, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lcom/transsion/moviedetailapi/bean/Image;

    invoke-interface {p1, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/transsion/moviedetailapi/bean/Image;

    const/16 v2, 0x8

    invoke-interface {p1, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/transsion/moviedetailapi/bean/Image;

    invoke-virtual {p0, p2, v0, p1}, Lcom/transsion/ninegridview/video/NineGridVideoView;->l(Lcom/transsion/moviedetailapi/bean/Image;Lcom/transsion/moviedetailapi/bean/Image;Lcom/transsion/moviedetailapi/bean/Image;)Lcom/transsion/moviedetailapi/bean/Image;

    move-result-object p1

    invoke-virtual {p0, p1, v1, v8}, Lcom/transsion/ninegridview/video/NineGridVideoView;->d(Lcom/transsion/moviedetailapi/bean/Image;II)V

    goto/16 :goto_0

    :pswitch_1
    invoke-interface {p1, p2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lcom/transsion/moviedetailapi/bean/Image;

    invoke-interface {p1, v6}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/transsion/moviedetailapi/bean/Image;

    invoke-interface {p1, v7}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v9

    check-cast v9, Lcom/transsion/moviedetailapi/bean/Image;

    invoke-virtual {p0, p2, v0, v9}, Lcom/transsion/ninegridview/video/NineGridVideoView;->l(Lcom/transsion/moviedetailapi/bean/Image;Lcom/transsion/moviedetailapi/bean/Image;Lcom/transsion/moviedetailapi/bean/Image;)Lcom/transsion/moviedetailapi/bean/Image;

    move-result-object p2

    invoke-virtual {p0, p2, v1, v8}, Lcom/transsion/ninegridview/video/NineGridVideoView;->b(Lcom/transsion/moviedetailapi/bean/Image;II)V

    invoke-interface {p1, v8}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lcom/transsion/moviedetailapi/bean/Image;

    invoke-interface {p1, v5}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/transsion/moviedetailapi/bean/Image;

    invoke-virtual {p0, p2, v0}, Lcom/transsion/ninegridview/video/NineGridVideoView;->k(Lcom/transsion/moviedetailapi/bean/Image;Lcom/transsion/moviedetailapi/bean/Image;)Lcom/transsion/moviedetailapi/bean/Image;

    move-result-object p2

    invoke-virtual {p0, p2, v1, v7}, Lcom/transsion/ninegridview/video/NineGridVideoView;->c(Lcom/transsion/moviedetailapi/bean/Image;II)V

    invoke-interface {p1, v4}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lcom/transsion/moviedetailapi/bean/Image;

    invoke-interface {p1, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/transsion/moviedetailapi/bean/Image;

    invoke-interface {p1, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/transsion/moviedetailapi/bean/Image;

    invoke-virtual {p0, p2, v0, p1}, Lcom/transsion/ninegridview/video/NineGridVideoView;->l(Lcom/transsion/moviedetailapi/bean/Image;Lcom/transsion/moviedetailapi/bean/Image;Lcom/transsion/moviedetailapi/bean/Image;)Lcom/transsion/moviedetailapi/bean/Image;

    move-result-object p1

    invoke-virtual {p0, p1, v1, v8}, Lcom/transsion/ninegridview/video/NineGridVideoView;->d(Lcom/transsion/moviedetailapi/bean/Image;II)V

    goto/16 :goto_0

    :pswitch_2
    invoke-interface {p1, p2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lcom/transsion/moviedetailapi/bean/Image;

    invoke-interface {p1, v6}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/transsion/moviedetailapi/bean/Image;

    invoke-interface {p1, v7}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/transsion/moviedetailapi/bean/Image;

    invoke-virtual {p0, p2, v0, v2}, Lcom/transsion/ninegridview/video/NineGridVideoView;->l(Lcom/transsion/moviedetailapi/bean/Image;Lcom/transsion/moviedetailapi/bean/Image;Lcom/transsion/moviedetailapi/bean/Image;)Lcom/transsion/moviedetailapi/bean/Image;

    move-result-object p2

    invoke-virtual {p0, p2, v1, v8}, Lcom/transsion/ninegridview/video/NineGridVideoView;->b(Lcom/transsion/moviedetailapi/bean/Image;II)V

    invoke-interface {p1, v8}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lcom/transsion/moviedetailapi/bean/Image;

    invoke-virtual {p0, p2, v1, v6}, Lcom/transsion/ninegridview/video/NineGridVideoView;->c(Lcom/transsion/moviedetailapi/bean/Image;II)V

    invoke-interface {p1, v5}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lcom/transsion/moviedetailapi/bean/Image;

    invoke-interface {p1, v4}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/transsion/moviedetailapi/bean/Image;

    invoke-interface {p1, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/transsion/moviedetailapi/bean/Image;

    invoke-virtual {p0, p2, v0, p1}, Lcom/transsion/ninegridview/video/NineGridVideoView;->l(Lcom/transsion/moviedetailapi/bean/Image;Lcom/transsion/moviedetailapi/bean/Image;Lcom/transsion/moviedetailapi/bean/Image;)Lcom/transsion/moviedetailapi/bean/Image;

    move-result-object p1

    invoke-virtual {p0, p1, v1, v8}, Lcom/transsion/ninegridview/video/NineGridVideoView;->d(Lcom/transsion/moviedetailapi/bean/Image;II)V

    goto/16 :goto_0

    :pswitch_3
    invoke-interface {p1, p2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lcom/transsion/moviedetailapi/bean/Image;

    invoke-interface {p1, v6}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/transsion/moviedetailapi/bean/Image;

    invoke-interface {p1, v7}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/transsion/moviedetailapi/bean/Image;

    invoke-virtual {p0, p2, v0, v2}, Lcom/transsion/ninegridview/video/NineGridVideoView;->l(Lcom/transsion/moviedetailapi/bean/Image;Lcom/transsion/moviedetailapi/bean/Image;Lcom/transsion/moviedetailapi/bean/Image;)Lcom/transsion/moviedetailapi/bean/Image;

    move-result-object p2

    invoke-virtual {p0, p2, v1, v8}, Lcom/transsion/ninegridview/video/NineGridVideoView;->b(Lcom/transsion/moviedetailapi/bean/Image;II)V

    invoke-interface {p1, v8}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lcom/transsion/moviedetailapi/bean/Image;

    invoke-interface {p1, v5}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/transsion/moviedetailapi/bean/Image;

    invoke-interface {p1, v4}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/transsion/moviedetailapi/bean/Image;

    invoke-virtual {p0, p2, v0, p1}, Lcom/transsion/ninegridview/video/NineGridVideoView;->l(Lcom/transsion/moviedetailapi/bean/Image;Lcom/transsion/moviedetailapi/bean/Image;Lcom/transsion/moviedetailapi/bean/Image;)Lcom/transsion/moviedetailapi/bean/Image;

    move-result-object p1

    invoke-virtual {p0, p1, v1, v8}, Lcom/transsion/ninegridview/video/NineGridVideoView;->c(Lcom/transsion/moviedetailapi/bean/Image;II)V

    goto/16 :goto_0

    :pswitch_4
    invoke-interface {p1, p2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lcom/transsion/moviedetailapi/bean/Image;

    invoke-interface {p1, v6}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/transsion/moviedetailapi/bean/Image;

    invoke-virtual {p0, p2, v0}, Lcom/transsion/ninegridview/video/NineGridVideoView;->k(Lcom/transsion/moviedetailapi/bean/Image;Lcom/transsion/moviedetailapi/bean/Image;)Lcom/transsion/moviedetailapi/bean/Image;

    move-result-object p2

    invoke-virtual {p0, p2, v1, v7}, Lcom/transsion/ninegridview/video/NineGridVideoView;->b(Lcom/transsion/moviedetailapi/bean/Image;II)V

    invoke-interface {p1, v7}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lcom/transsion/moviedetailapi/bean/Image;

    invoke-virtual {p0, p2, v1, v6}, Lcom/transsion/ninegridview/video/NineGridVideoView;->c(Lcom/transsion/moviedetailapi/bean/Image;II)V

    invoke-interface {p1, v8}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lcom/transsion/moviedetailapi/bean/Image;

    invoke-interface {p1, v5}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/transsion/moviedetailapi/bean/Image;

    invoke-virtual {p0, p2, p1}, Lcom/transsion/ninegridview/video/NineGridVideoView;->k(Lcom/transsion/moviedetailapi/bean/Image;Lcom/transsion/moviedetailapi/bean/Image;)Lcom/transsion/moviedetailapi/bean/Image;

    move-result-object p1

    invoke-virtual {p0, p1, v1, v7}, Lcom/transsion/ninegridview/video/NineGridVideoView;->d(Lcom/transsion/moviedetailapi/bean/Image;II)V

    goto :goto_0

    :pswitch_5
    invoke-interface {p1, p2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lcom/transsion/moviedetailapi/bean/Image;

    invoke-interface {p1, v6}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/transsion/moviedetailapi/bean/Image;

    invoke-virtual {p0, p2, v0}, Lcom/transsion/ninegridview/video/NineGridVideoView;->k(Lcom/transsion/moviedetailapi/bean/Image;Lcom/transsion/moviedetailapi/bean/Image;)Lcom/transsion/moviedetailapi/bean/Image;

    move-result-object p2

    invoke-virtual {p0, p2, v1, v7}, Lcom/transsion/ninegridview/video/NineGridVideoView;->b(Lcom/transsion/moviedetailapi/bean/Image;II)V

    invoke-interface {p1, v7}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lcom/transsion/moviedetailapi/bean/Image;

    invoke-interface {p1, v8}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/transsion/moviedetailapi/bean/Image;

    invoke-virtual {p0, p2, p1}, Lcom/transsion/ninegridview/video/NineGridVideoView;->k(Lcom/transsion/moviedetailapi/bean/Image;Lcom/transsion/moviedetailapi/bean/Image;)Lcom/transsion/moviedetailapi/bean/Image;

    move-result-object p1

    invoke-virtual {p0, p1, v1, v7}, Lcom/transsion/ninegridview/video/NineGridVideoView;->c(Lcom/transsion/moviedetailapi/bean/Image;II)V

    goto :goto_0

    :pswitch_6
    invoke-interface {p1, p2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lcom/transsion/moviedetailapi/bean/Image;

    invoke-interface {p1, v6}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/transsion/moviedetailapi/bean/Image;

    invoke-virtual {p0, p2, v0}, Lcom/transsion/ninegridview/video/NineGridVideoView;->k(Lcom/transsion/moviedetailapi/bean/Image;Lcom/transsion/moviedetailapi/bean/Image;)Lcom/transsion/moviedetailapi/bean/Image;

    move-result-object p2

    invoke-virtual {p0, p2, v1, v7}, Lcom/transsion/ninegridview/video/NineGridVideoView;->b(Lcom/transsion/moviedetailapi/bean/Image;II)V

    invoke-interface {p1, v7}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/transsion/moviedetailapi/bean/Image;

    invoke-virtual {p0, p1, v1, v6}, Lcom/transsion/ninegridview/video/NineGridVideoView;->c(Lcom/transsion/moviedetailapi/bean/Image;II)V

    goto :goto_0

    :pswitch_7
    invoke-interface {p1, p2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lcom/transsion/moviedetailapi/bean/Image;

    invoke-interface {p1, v6}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/transsion/moviedetailapi/bean/Image;

    invoke-virtual {p0, p2, p1}, Lcom/transsion/ninegridview/video/NineGridVideoView;->k(Lcom/transsion/moviedetailapi/bean/Image;Lcom/transsion/moviedetailapi/bean/Image;)Lcom/transsion/moviedetailapi/bean/Image;

    move-result-object p1

    invoke-virtual {p0, p1, v1, v7}, Lcom/transsion/ninegridview/video/NineGridVideoView;->b(Lcom/transsion/moviedetailapi/bean/Image;II)V

    goto :goto_0

    :pswitch_8
    invoke-interface {p1, p2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/transsion/moviedetailapi/bean/Image;

    invoke-virtual {p0, p1, v1, v6}, Lcom/transsion/ninegridview/video/NineGridVideoView;->b(Lcom/transsion/moviedetailapi/bean/Image;II)V

    :goto_0
    invoke-virtual {p0}, Landroid/view/View;->getPaddingLeft()I

    move-result p1

    add-int/2addr v1, p1

    invoke-virtual {p0}, Landroid/view/View;->getPaddingRight()I

    move-result p1

    add-int v0, v1, p1

    iget p1, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->j:I

    iget p2, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->l:I

    add-int/2addr p1, p2

    iget p2, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->n:I

    add-int/2addr p1, p2

    iget p2, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->b:I

    iget v1, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->h:I

    sub-int/2addr v1, v6

    mul-int p2, p2, v1

    add-int/2addr p1, p2

    invoke-virtual {p0}, Landroid/view/View;->getPaddingTop()I

    move-result p2

    add-int/2addr p1, p2

    invoke-virtual {p0}, Landroid/view/View;->getPaddingBottom()I

    move-result p2

    add-int/2addr p2, p1

    :cond_0
    invoke-virtual {p0, v0, p2}, Landroid/view/View;->setMeasuredDimension(II)V

    return-void

    nop

    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_8
        :pswitch_7
        :pswitch_6
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method

.method public final setAdapter(Lcom/transsion/ninegridview/video/NineGridVideoViewAdapter;)V
    .locals 5

    const-string v0, "adapter"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    iput-object p1, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->q:Lcom/transsion/ninegridview/video/NineGridVideoViewAdapter;

    invoke-virtual {p1}, Lcom/transsion/ninegridview/video/NineGridVideoViewAdapter;->getImageList()Ljava/util/List;

    move-result-object p1

    invoke-interface {p1}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-static {p0}, Lvi/c;->g(Landroid/view/View;)V

    return-void

    :cond_0
    invoke-static {p0}, Lvi/c;->k(Landroid/view/View;)V

    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result v0

    iget v1, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->e:I

    const/4 v2, 0x0

    if-ge v1, v0, :cond_1

    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result v0

    invoke-interface {p1, v2, v0}, Ljava/util/List;->subList(II)Ljava/util/List;

    move-result-object p1

    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result v0

    :cond_1
    const/4 v1, 0x1

    if-ne v0, v1, :cond_2

    iput v1, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->g:I

    iput v1, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->h:I

    goto :goto_0

    :cond_2
    const/4 v3, 0x6

    const/4 v4, 0x2

    if-gt v4, v0, :cond_4

    if-ge v0, v3, :cond_4

    iput v4, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->g:I

    div-int/lit8 v3, v0, 0x2

    rem-int/lit8 v4, v0, 0x2

    if-nez v4, :cond_3

    const/4 v1, 0x0

    :cond_3
    add-int/2addr v3, v1

    iput v3, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->h:I

    goto :goto_0

    :cond_4
    if-gt v3, v0, :cond_6

    const/16 v3, 0xa

    if-ge v0, v3, :cond_6

    const/4 v3, 0x3

    iput v3, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->g:I

    div-int/lit8 v3, v0, 0x3

    rem-int/lit8 v4, v0, 0x3

    if-nez v4, :cond_5

    const/4 v1, 0x0

    :cond_5
    add-int/2addr v3, v1

    iput v3, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->h:I

    :cond_6
    :goto_0
    iget-object v1, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->a:Ljava/util/List;

    if-nez v1, :cond_7

    :goto_1
    if-ge v2, v0, :cond_b

    invoke-virtual {p0, v2}, Lcom/transsion/ninegridview/video/NineGridVideoView;->h(I)Lcom/transsion/ninegridview/video/NineGridItemView;

    move-result-object v1

    invoke-virtual {p0}, Landroid/view/ViewGroup;->generateDefaultLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object v3

    invoke-virtual {p0, v1, v3}, Landroid/view/ViewGroup;->addView(Landroid/view/View;Landroid/view/ViewGroup$LayoutParams;)V

    add-int/lit8 v2, v2, 0x1

    goto :goto_1

    :cond_7
    if-eqz v1, :cond_8

    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v2

    :cond_8
    if-le v2, v0, :cond_9

    sub-int/2addr v2, v0

    invoke-virtual {p0, v0, v2}, Landroid/view/ViewGroup;->removeViews(II)V

    invoke-virtual {p0}, Lcom/transsion/ninegridview/video/NineGridVideoView;->e()V

    goto :goto_3

    :cond_9
    if-ge v2, v0, :cond_a

    invoke-virtual {p0}, Lcom/transsion/ninegridview/video/NineGridVideoView;->e()V

    :goto_2
    if-ge v2, v0, :cond_b

    invoke-virtual {p0, v2}, Lcom/transsion/ninegridview/video/NineGridVideoView;->h(I)Lcom/transsion/ninegridview/video/NineGridItemView;

    move-result-object v1

    invoke-virtual {p0}, Landroid/view/ViewGroup;->generateDefaultLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object v3

    invoke-virtual {p0, v1, v3}, Landroid/view/ViewGroup;->addView(Landroid/view/View;Landroid/view/ViewGroup$LayoutParams;)V

    add-int/lit8 v2, v2, 0x1

    goto :goto_2

    :cond_a
    invoke-virtual {p0}, Lcom/transsion/ninegridview/video/NineGridVideoView;->e()V

    :cond_b
    :goto_3
    iput-object p1, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->a:Ljava/util/List;

    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    return-void
.end method

.method public final setGridSpacing(I)V
    .locals 0

    iput p1, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->b:I

    return-void
.end method

.method public final setMaxImageSize(I)V
    .locals 0

    iput p1, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->e:I

    return-void
.end method

.method public final setSingleImageRatio(F)V
    .locals 0

    iput p1, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->d:F

    return-void
.end method

.method public final setSingleImageSize(I)V
    .locals 0

    iput p1, p0, Lcom/transsion/ninegridview/video/NineGridVideoView;->c:I

    return-void
.end method
