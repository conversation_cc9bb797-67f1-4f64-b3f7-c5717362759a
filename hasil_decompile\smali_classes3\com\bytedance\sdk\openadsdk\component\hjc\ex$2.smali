.class Lcom/bytedance/sdk/openadsdk/component/hjc/ex$2;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/hjc/ex;->Fj(Lcom/bytedance/sdk/openadsdk/api/interstitial/PAGInterstitialAd;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/api/interstitial/PAGInterstitialAd;

.field final synthetic ex:Lcom/bytedance/sdk/openadsdk/component/hjc/ex;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/hjc/ex;Lcom/bytedance/sdk/openadsdk/api/interstitial/PAGInterstitialAd;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/hjc/ex$2;->ex:Lcom/bytedance/sdk/openadsdk/component/hjc/ex;

    iput-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/hjc/ex$2;->Fj:Lcom/bytedance/sdk/openadsdk/api/interstitial/PAGInterstitialAd;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/hjc/ex$2;->ex:Lcom/bytedance/sdk/openadsdk/component/hjc/ex;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/hjc/ex;->Fj:Lcom/bytedance/sdk/openadsdk/api/interstitial/PAGInterstitialAdLoadListener;

    if-eqz v0, :cond_0

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/hjc/ex$2;->Fj:Lcom/bytedance/sdk/openadsdk/api/interstitial/PAGInterstitialAd;

    invoke-interface {v0, v1}, Lcom/bytedance/sdk/openadsdk/api/PAGLoadListener;->onAdLoaded(Ljava/lang/Object;)V

    :cond_0
    return-void
.end method
