.class Lcom/bytedance/sdk/openadsdk/component/reward/Fj/mSE$1;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/reward/Fj/mSE;->Fj(Lcom/bytedance/sdk/component/utils/Vq;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/mSE;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/mSE;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/mSE$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/mSE;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 2

    :try_start_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/mSE$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/mSE;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/mSE;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/mSE;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object v0

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Kk:Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;

    invoke-virtual {v0}, Landroid/app/Activity;->getWindow()Landroid/view/Window;

    move-result-object v0

    invoke-virtual {v0}, Landroid/view/Window;->getDecorView()Landroid/view/View;

    move-result-object v0

    if-eqz v0, :cond_0

    const v1, 0x102002f

    invoke-virtual {v0, v1}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    move-result-object v0

    if-eqz v0, :cond_0

    const/16 v1, 0x8

    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    :catch_0
    :cond_0
    return-void
.end method
