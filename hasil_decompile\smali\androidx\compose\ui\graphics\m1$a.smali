.class public final Landroidx/compose/ui/graphics/m1$a;
.super Landroidx/compose/ui/graphics/a5;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Landroidx/compose/ui/graphics/m1;->a(Landroid/graphics/Shader;)Landroidx/compose/ui/graphics/a5;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# instance fields
.field public final synthetic e:Landroid/graphics/Shader;


# direct methods
.method public constructor <init>(Landroid/graphics/Shader;)V
    .locals 0

    iput-object p1, p0, Landroidx/compose/ui/graphics/m1$a;->e:Landroid/graphics/Shader;

    invoke-direct {p0}, Landroidx/compose/ui/graphics/a5;-><init>()V

    return-void
.end method


# virtual methods
.method public b(J)Landroid/graphics/Shader;
    .locals 0

    iget-object p1, p0, Landroidx/compose/ui/graphics/m1$a;->e:Landroid/graphics/Shader;

    return-object p1
.end method
