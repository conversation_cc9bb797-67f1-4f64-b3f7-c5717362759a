.class Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$7;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/component/utils/cB$Fj;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$7;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj(Landroid/content/Context;Landroid/content/Intent;ZI)V
    .locals 0

    if-nez p3, :cond_0

    return-void

    :cond_0
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$7;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;->ex(Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;)Lcom/bytedance/sdk/component/svN/BcC;

    move-result-object p1

    if-nez p1, :cond_1

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$7;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;

    new-instance p2, Lcom/bytedance/sdk/openadsdk/component/reward/ex;

    const-string p3, "fsv net connect task"

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;->hjc(Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;)Ljava/util/List;

    move-result-object p4

    invoke-direct {p2, p3, p4}, Lcom/bytedance/sdk/openadsdk/component/reward/ex;-><init>(Ljava/lang/String;Ljava/util/List;)V

    invoke-static {p1, p2}, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;Lcom/bytedance/sdk/component/svN/BcC;)Lcom/bytedance/sdk/component/svN/BcC;

    :cond_1
    invoke-static {}, Lcom/bytedance/sdk/component/utils/BcC;->Fj()Landroid/os/Handler;

    move-result-object p1

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$7;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;

    invoke-static {p2}, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;->ex(Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;)Lcom/bytedance/sdk/component/svN/BcC;

    move-result-object p2

    invoke-virtual {p1, p2}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    return-void
.end method
