.class public interface abstract Landroidx/compose/ui/layout/t0;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/compose/ui/layout/t0$a;
    }
.end annotation

.annotation runtime L<PERSON><PERSON>/Metadata;
.end annotation


# virtual methods
.method public abstract a(Landroidx/compose/ui/layout/t0$a;)V
.end method

.method public abstract b(Ljava/lang/Object;Ljava/lang/Object;)Z
.end method
