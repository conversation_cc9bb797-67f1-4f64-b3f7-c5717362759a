.class Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN$8;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/openadsdk/mSE/hjc;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN$8;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj()V
    .locals 3

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN$8;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;->hjc(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object v0

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Kk:Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;

    invoke-virtual {v0}, Landroid/app/Activity;->isFinishing()Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN$8;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;->hjc(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object v0

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->zYh()Z

    move-result v0

    if-nez v0, :cond_1

    return-void

    :cond_1
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN$8;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;->hjc(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object v0

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/model/mE;->mSE(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v0

    if-eqz v0, :cond_2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN$8;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;->ex(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;)Landroid/os/Handler;

    move-result-object v0

    const/16 v1, 0x320

    invoke-virtual {v0, v1}, Landroid/os/Handler;->removeMessages(I)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN$8;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;->ex(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;)Landroid/os/Handler;

    move-result-object v0

    const/4 v1, 0x1

    const/4 v2, 0x0

    invoke-static {v1, v2}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;->Fj(II)Landroid/os/Message;

    move-result-object v1

    invoke-virtual {v0, v1}, Landroid/os/Handler;->sendMessage(Landroid/os/Message;)Z

    :cond_2
    return-void
.end method

.method public Fj(I)V
    .locals 0

    return-void
.end method

.method public ex()V
    .locals 0

    return-void
.end method
