.class public final Landroidx/media3/datasource/cronet/R$color;
.super Ljava/lang/Object;


# static fields
.field public static common_google_signin_btn_text_dark:I = 0x7f06013a

.field public static common_google_signin_btn_text_dark_default:I = 0x7f06013b

.field public static common_google_signin_btn_text_dark_disabled:I = 0x7f06013c

.field public static common_google_signin_btn_text_dark_focused:I = 0x7f06013d

.field public static common_google_signin_btn_text_dark_pressed:I = 0x7f06013e

.field public static common_google_signin_btn_text_light:I = 0x7f06013f

.field public static common_google_signin_btn_text_light_default:I = 0x7f060140

.field public static common_google_signin_btn_text_light_disabled:I = 0x7f060141

.field public static common_google_signin_btn_text_light_focused:I = 0x7f060142

.field public static common_google_signin_btn_text_light_pressed:I = 0x7f060143

.field public static common_google_signin_btn_tint:I = 0x7f060144

.field public static notification_action_color_filter:I = 0x7f0604c4

.field public static notification_icon_bg_color:I = 0x7f0604c6

.field public static ripple_material_light:I = 0x7f060659

.field public static secondary_text_default_material_light:I = 0x7f06065c


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
