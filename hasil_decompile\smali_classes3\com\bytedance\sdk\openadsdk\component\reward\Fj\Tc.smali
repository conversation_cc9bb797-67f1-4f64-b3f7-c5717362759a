.class public Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/openadsdk/mSE/WR;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$Fj;,
        Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$hjc;,
        Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$ex;,
        Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$eV;
    }
.end annotation


# instance fields
.field private Af:Z

.field private At:Ljava/lang/String;

.field protected BcC:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

.field private Bzy:Ljava/lang/String;

.field Fj:Lcom/bytedance/sdk/openadsdk/core/Vq;

.field private volatile HY:I

.field private JU:Lcom/bytedance/sdk/component/widget/SSWebView;

.field private JW:I

.field private Jq:I

.field private KZ:Z

.field private volatile Kk:I

.field private final Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field private Moo:F

.field private OK:I

.field private Obv:Lcom/bykv/vk/openvk/preload/falconx/loader/ILoader;

.field private PpV:J

.field private Ql:Lcom/bytedance/sdk/component/widget/SSWebView;

.field private Tc:I

.field private UYd:I

.field Ubf:Lcom/bytedance/sdk/openadsdk/ex/Ko;

.field private Vq:F

.field WR:I

.field private YH:Z

.field private cB:Landroid/view/View;

.field private cs:J

.field private final dG:Z

.field protected eV:Ljava/lang/String;

.field private efV:Z

.field private eh:Lcom/bytedance/sdk/openadsdk/common/svN;

.field ex:Lcom/bytedance/sdk/openadsdk/core/Vq;

.field private fj:F

.field private gXF:Z

.field private gci:I

.field protected hjc:Z

.field private iT:Z

.field private kF:Z

.field private lv:Z

.field private mC:Z

.field private mE:Z

.field mSE:Z

.field private mj:J

.field private nsB:Landroid/view/View;

.field private oX:Z

.field private volatile qPr:I

.field private qg:Z

.field private final rAx:Ljava/lang/String;

.field private rS:Z

.field private rXP:F

.field private rf:J

.field private spi:Lcom/bytedance/sdk/openadsdk/core/widget/Fj/Ubf;

.field svN:Ljava/lang/String;

.field private final uM:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

.field private uy:Landroid/util/SparseArray;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Landroid/util/SparseArray<",
            "Lcom/bytedance/sdk/openadsdk/core/ex/hjc$Fj;",
            ">;"
        }
    .end annotation
.end field

.field private final vYf:Ljava/util/concurrent/atomic/AtomicBoolean;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;)V
    .locals 4

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->hjc:Z

    const/4 v1, 0x0

    iput-boolean v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->rS:Z

    new-instance v2, Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-direct {v2, v0}, Ljava/util/concurrent/atomic/AtomicBoolean;-><init>(Z)V

    iput-object v2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->vYf:Ljava/util/concurrent/atomic/AtomicBoolean;

    iput v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->WR:I

    const-string v2, ""

    iput-object v2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->svN:Ljava/lang/String;

    iput-boolean v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->mSE:Z

    new-instance v2, Landroid/util/SparseArray;

    invoke-direct {v2}, Landroid/util/SparseArray;-><init>()V

    iput-object v2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->uy:Landroid/util/SparseArray;

    iput-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->lv:Z

    const/high16 v0, -0x40800000    # -1.0f

    iput v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->rXP:F

    iput v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->fj:F

    iput-boolean v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->gXF:Z

    const-wide/16 v2, -0x1

    iput-wide v2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->mj:J

    iput v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->qPr:I

    const/4 v0, -0x1

    iput v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->gci:I

    iput v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->HY:I

    iput v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Kk:I

    const-wide/16 v1, 0x0

    iput-wide v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->cs:J

    iput v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Jq:I

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->uM:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    iget-object v0, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->svN:Ljava/lang/String;

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->rAx:Ljava/lang/String;

    iget-boolean p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->WR:Z

    iput-boolean p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->dG:Z

    return-void
.end method

.method public static synthetic Af(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;)Z
    .locals 0

    iget-boolean p0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->kF:Z

    return p0
.end method

.method public static synthetic BcC(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;)I
    .locals 0

    iget p0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->qPr:I

    return p0
.end method

.method public static synthetic BcC(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;Z)Z
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->lv:Z

    return p1
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;F)F
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Vq:F

    return p1
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;I)I
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Jq:I

    return p1
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;J)J
    .locals 0

    iput-wide p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->rf:J

    return-wide p1
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;Landroid/util/SparseArray;)Landroid/util/SparseArray;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->uy:Landroid/util/SparseArray;

    return-object p1
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;Landroid/view/View;)Landroid/view/View;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->nsB:Landroid/view/View;

    return-object p1
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;)Lcom/bytedance/sdk/component/widget/SSWebView;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->JU:Lcom/bytedance/sdk/component/widget/SSWebView;

    return-object p0
.end method

.method private static Fj(Ljava/lang/String;Lcom/bytedance/sdk/openadsdk/core/model/Ql;III)Ljava/lang/String;
    .locals 4

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->HQ()F

    move-result v0

    invoke-static {p0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v1

    if-nez v1, :cond_3

    const/4 v1, 0x1

    const-string v2, "&"

    const-string v3, "?"

    if-ne p2, v1, :cond_1

    invoke-virtual {p0, v3}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result p2

    if-eqz p2, :cond_0

    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p2, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p2, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    goto :goto_0

    :cond_0
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p2, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    :goto_0
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p2, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string p0, "orientation=portrait"

    invoke-virtual {p2, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    :cond_1
    invoke-virtual {p0, v3}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result p2

    if-eqz p2, :cond_2

    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p2, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p2, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    goto :goto_1

    :cond_2
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p2, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    :goto_1
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p2, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string p0, "height="

    invoke-virtual {p2, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string p0, "&width="

    invoke-virtual {p2, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p2, p4}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string p0, "&aspect_ratio="

    invoke-virtual {p2, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(F)Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    :cond_3
    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/core/model/mE;->ex(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result p1

    if-nez p1, :cond_4

    invoke-static {p0}, Lcom/bytedance/sdk/openadsdk/utils/eV;->Fj(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p0

    :cond_4
    return-object p0
.end method

.method private Fj(Ljava/lang/String;Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$eV;)V
    .locals 11
    .annotation build Landroid/annotation/SuppressLint;
        value = {
            "ClickableViewAccessibility"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->JU:Lcom/bytedance/sdk/component/widget/SSWebView;

    if-eqz v0, :cond_7

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/widget/SSWebView;->getWebView()Landroid/webkit/WebView;

    move-result-object v0

    if-eqz v0, :cond_7

    new-instance v0, Lcom/bytedance/sdk/openadsdk/ex/Ko;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->JU:Lcom/bytedance/sdk/component/widget/SSWebView;

    invoke-virtual {v2}, Lcom/bytedance/sdk/component/widget/SSWebView;->getWebView()Landroid/webkit/WebView;

    move-result-object v2

    new-instance v3, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$4;

    invoke-direct {v3, p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$4;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;)V

    iget v4, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->gci:I

    invoke-direct {v0, v1, v2, v3, v4}, Lcom/bytedance/sdk/openadsdk/ex/Ko;-><init>(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Landroid/webkit/WebView;Lcom/bytedance/sdk/openadsdk/ex/mSE;I)V

    const/4 v1, 0x1

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/ex/Ko;->Fj(Z)Lcom/bytedance/sdk/openadsdk/ex/Ko;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ubf:Lcom/bytedance/sdk/openadsdk/ex/Ko;

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->JW()Z

    move-result v2

    if-eqz v2, :cond_0

    const-string p1, "landingpage_endcard"

    :cond_0
    invoke-virtual {v0, p1}, Lcom/bytedance/sdk/openadsdk/ex/Ko;->Fj(Ljava/lang/String;)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ubf:Lcom/bytedance/sdk/openadsdk/ex/Ko;

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->rAx:Ljava/lang/String;

    invoke-virtual {p1, v0}, Lcom/bytedance/sdk/openadsdk/ex/Ko;->ex(Ljava/lang/String;)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ubf:Lcom/bytedance/sdk/openadsdk/ex/Ko;

    invoke-virtual {p1, v1}, Lcom/bytedance/sdk/openadsdk/ex/Ko;->ex(Z)V

    new-instance p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$5;

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/JU;->Fj()Landroid/content/Context;

    move-result-object v4

    iget-object v5, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Fj:Lcom/bytedance/sdk/openadsdk/core/Vq;

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->cs()Ljava/lang/String;

    move-result-object v6

    iget-object v7, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ubf:Lcom/bytedance/sdk/openadsdk/ex/Ko;

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->eh()Z

    move-result v0

    const/4 v10, 0x0

    if-nez v0, :cond_2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/model/mE;->ex(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v0

    if-eqz v0, :cond_1

    goto :goto_0

    :cond_1
    const/4 v8, 0x0

    goto :goto_1

    :cond_2
    :goto_0
    const/4 v8, 0x1

    :goto_1
    move-object v2, p1

    move-object v3, p0

    move-object v9, p2

    invoke-direct/range {v2 .. v9}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$5;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;Landroid/content/Context;Lcom/bytedance/sdk/openadsdk/core/Vq;Ljava/lang/String;Lcom/bytedance/sdk/openadsdk/ex/Ko;ZLcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$eV;)V

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->spi:Lcom/bytedance/sdk/openadsdk/core/widget/Fj/Ubf;

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->JU:Lcom/bytedance/sdk/component/widget/SSWebView;

    invoke-virtual {v0, p1}, Lcom/bytedance/sdk/component/widget/SSWebView;->setWebViewClient(Landroid/webkit/WebViewClient;)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->spi:Lcom/bytedance/sdk/openadsdk/core/widget/Fj/Ubf;

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {p1, v0}, Lcom/bytedance/sdk/openadsdk/core/widget/Fj/Ubf;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->spi:Lcom/bytedance/sdk/openadsdk/core/widget/Fj/Ubf;

    iget-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->dG:Z

    if-eqz v0, :cond_3

    const-string v0, "rewarded_video"

    goto :goto_2

    :cond_3
    const-string v0, "fullscreen_interstitial_ad"

    :goto_2
    invoke-virtual {p1, v0}, Lcom/bytedance/sdk/openadsdk/core/widget/Fj/Ubf;->Fj(Ljava/lang/String;)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->eh()Z

    move-result p1

    if-eqz p1, :cond_4

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->JU:Lcom/bytedance/sdk/component/widget/SSWebView;

    if-eqz p1, :cond_4

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/widget/SSWebView;->getWebView()Landroid/webkit/WebView;

    move-result-object p1

    if-eqz p1, :cond_4

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->JU:Lcom/bytedance/sdk/component/widget/SSWebView;

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/widget/SSWebView;->getWebView()Landroid/webkit/WebView;

    move-result-object p1

    new-instance v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$6;

    invoke-direct {v0, p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$6;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;)V

    invoke-virtual {p1, v0}, Landroid/view/View;->setOnTouchListener(Landroid/view/View$OnTouchListener;)V

    :cond_4
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->JU:Lcom/bytedance/sdk/component/widget/SSWebView;

    if-eqz p1, :cond_5

    new-instance v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$7;

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Fj:Lcom/bytedance/sdk/openadsdk/core/Vq;

    iget-object v3, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ubf:Lcom/bytedance/sdk/openadsdk/ex/Ko;

    invoke-direct {v0, p0, v2, v3, p2}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$7;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;Lcom/bytedance/sdk/openadsdk/core/Vq;Lcom/bytedance/sdk/openadsdk/ex/Ko;Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$eV;)V

    invoke-virtual {p1, v0}, Lcom/bytedance/sdk/component/widget/SSWebView;->setWebChromeClient(Landroid/webkit/WebChromeClient;)V

    :cond_5
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->JU:Lcom/bytedance/sdk/component/widget/SSWebView;

    invoke-virtual {p0, p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Fj(Lcom/bytedance/sdk/component/widget/SSWebView;)V

    sget p1, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 p2, 0x18

    if-lt p1, p2, :cond_6

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->JU:Lcom/bytedance/sdk/component/widget/SSWebView;

    const/4 p2, 0x0

    invoke-virtual {p1, v1, p2}, Lcom/bytedance/sdk/component/widget/SSWebView;->setLayerType(ILandroid/graphics/Paint;)V

    :cond_6
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->JU:Lcom/bytedance/sdk/component/widget/SSWebView;

    const/4 p2, -0x1

    invoke-virtual {p1, p2}, Lcom/bytedance/sdk/component/widget/SSWebView;->setBackgroundColor(I)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->JU:Lcom/bytedance/sdk/component/widget/SSWebView;

    invoke-virtual {p1, v10}, Lcom/bytedance/sdk/component/widget/SSWebView;->setDisplayZoomControls(Z)V

    :cond_7
    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ubf()V

    return-void
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;Ljava/lang/String;)Z
    .locals 0

    invoke-direct {p0, p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Fj(Ljava/lang/String;)Z

    move-result p0

    return p0
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;Z)Z
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->gXF:Z

    return p1
.end method

.method private Fj(Ljava/lang/String;)Z
    .locals 2

    invoke-static {p1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    const/4 v1, 0x1

    if-eqz v0, :cond_0

    return v1

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->eh()Z

    move-result v0

    if-eqz v0, :cond_1

    const-string v0, ".mp4"

    invoke-virtual {p1, v0}, Ljava/lang/String;->endsWith(Ljava/lang/String;)Z

    move-result p1

    if-eqz p1, :cond_1

    return v1

    :cond_1
    const/4 p1, 0x0

    return p1
.end method

.method public static synthetic JU(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;)J
    .locals 2

    iget-wide v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->rf:J

    return-wide v0
.end method

.method public static synthetic JW(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;)Ljava/util/concurrent/atomic/AtomicBoolean;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->vYf:Ljava/util/concurrent/atomic/AtomicBoolean;

    return-object p0
.end method

.method private KZ()Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;
    .locals 4

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/model/mE;->ex(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 v0, 0x3

    goto :goto_0

    :cond_0
    const/4 v0, 0x2

    :goto_0
    new-instance v1, Lcom/bytedance/sdk/openadsdk/ex/JU;

    iget-boolean v2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->dG:Z

    if-eqz v2, :cond_1

    const-string v2, "rewarded_video"

    goto :goto_1

    :cond_1
    const-string v2, "fullscreen_interstitial_ad"

    :goto_1
    iget-object v3, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-direct {v1, v0, v2, v3}, Lcom/bytedance/sdk/openadsdk/ex/JU;-><init>(ILjava/lang/String;Lcom/bytedance/sdk/openadsdk/core/model/Ql;)V

    return-object v1
.end method

.method public static synthetic Ko(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;)I
    .locals 0

    iget p0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->HY:I

    return p0
.end method

.method public static synthetic Moo(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;)Landroid/view/View;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->nsB:Landroid/view/View;

    return-object p0
.end method

.method public static synthetic Ql(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;)F
    .locals 0

    iget p0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Vq:F

    return p0
.end method

.method public static synthetic Tc(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;)I
    .locals 2

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->qPr:I

    add-int/lit8 v1, v0, 0x1

    iput v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->qPr:I

    return v0
.end method

.method public static synthetic UYd(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;)Lcom/bykv/vk/openvk/preload/falconx/loader/ILoader;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Obv:Lcom/bykv/vk/openvk/preload/falconx/loader/ILoader;

    return-object p0
.end method

.method public static synthetic Ubf(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;)Lcom/bytedance/sdk/openadsdk/core/widget/Fj/Ubf;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->spi:Lcom/bytedance/sdk/openadsdk/core/widget/Fj/Ubf;

    return-object p0
.end method

.method public static synthetic Ubf(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;Z)Z
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Af:Z

    return p1
.end method

.method public static synthetic Vq(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;)Z
    .locals 0

    iget-boolean p0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->lv:Z

    return p0
.end method

.method public static synthetic WR(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;)Ljava/lang/String;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Bzy:Ljava/lang/String;

    return-object p0
.end method

.method public static synthetic WR(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;Z)Z
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->YH:Z

    return p1
.end method

.method public static synthetic cB(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;)Landroid/util/SparseArray;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->uy:Landroid/util/SparseArray;

    return-object p0
.end method

.method public static synthetic dG(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;)I
    .locals 2

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Kk:I

    add-int/lit8 v1, v0, 0x1

    iput v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Kk:I

    return v0
.end method

.method public static synthetic eV(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;F)F
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->fj:F

    return p1
.end method

.method public static synthetic eV(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;)Lcom/bytedance/sdk/openadsdk/core/model/Ql;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    return-object p0
.end method

.method public static synthetic eV(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;Z)Z
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->KZ:Z

    return p1
.end method

.method public static synthetic ex(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;F)F
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Moo:F

    return p1
.end method

.method public static synthetic ex(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->uM:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    return-object p0
.end method

.method public static synthetic ex(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;Z)Z
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->iT:Z

    return p1
.end method

.method public static synthetic hjc(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;F)F
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->rXP:F

    return p1
.end method

.method public static synthetic hjc(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;)Z
    .locals 0

    iget-boolean p0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->gXF:Z

    return p0
.end method

.method public static synthetic hjc(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;Z)Z
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->qg:Z

    return p1
.end method

.method public static synthetic mC(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;)Lcom/bytedance/sdk/openadsdk/common/svN;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->eh:Lcom/bytedance/sdk/openadsdk/common/svN;

    return-object p0
.end method

.method public static synthetic mE(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;)F
    .locals 0

    iget p0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->fj:F

    return p0
.end method

.method public static synthetic mSE(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;)I
    .locals 0

    iget p0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Kk:I

    return p0
.end method

.method public static synthetic mSE(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;Z)Z
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->rS:Z

    return p1
.end method

.method public static synthetic nsB(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;)Z
    .locals 0

    iget-boolean p0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->rS:Z

    return p0
.end method

.method private oX()V
    .locals 4

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->ex:Lcom/bytedance/sdk/openadsdk/core/Vq;

    const-string v1, "showPlayableEndCardOverlay"

    const/4 v2, 0x0

    invoke-virtual {v0, v1, v2}, Lcom/bytedance/sdk/openadsdk/core/Vq;->Fj(Ljava/lang/String;Lorg/json/JSONObject;)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->uM:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->YH:Lcom/bytedance/sdk/component/utils/Vq;

    const/16 v1, 0x258

    const-wide/16 v2, 0x3e8

    invoke-virtual {v0, v1, v2, v3}, Landroid/os/Handler;->sendEmptyMessageDelayed(IJ)Z

    return-void
.end method

.method public static synthetic rAx(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;)I
    .locals 2

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->HY:I

    add-int/lit8 v1, v0, 0x1

    iput v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->HY:I

    return v0
.end method

.method public static synthetic rS(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;)F
    .locals 0

    iget p0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Moo:F

    return p0
.end method

.method public static synthetic rf(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;)Landroid/view/View;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->cB:Landroid/view/View;

    return-object p0
.end method

.method public static synthetic svN(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;)I
    .locals 0

    iget p0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->OK:I

    return p0
.end method

.method public static synthetic svN(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;Z)Z
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->mE:Z

    return p1
.end method

.method public static synthetic uy(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;)Z
    .locals 0

    iget-boolean p0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->dG:Z

    return p0
.end method

.method public static synthetic vYf(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;)F
    .locals 0

    iget p0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->rXP:F

    return p0
.end method


# virtual methods
.method public Af()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->svN:Ljava/lang/String;

    return-object v0
.end method

.method public BcC()Lcom/bytedance/sdk/component/widget/SSWebView;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->JU:Lcom/bytedance/sdk/component/widget/SSWebView;

    return-object v0
.end method

.method public Fj()V
    .locals 4

    iget-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->mC:Z

    if-eqz v0, :cond_0

    return-void

    :cond_0
    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->mC:Z

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->uM:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget v1, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Ko:I

    iput v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->UYd:I

    iget v1, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->UYd:I

    iput v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Tc:I

    iget v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->dG:I

    iput v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->JW:I

    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    move-result-wide v0

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->ex()V

    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    move-result-wide v2

    sub-long/2addr v2, v0

    iput-wide v2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->cs:J

    return-void
.end method

.method public Fj(F)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->JU:Lcom/bytedance/sdk/component/widget/SSWebView;

    invoke-static {v0, p1}, Lcom/bytedance/sdk/openadsdk/utils/eh;->Fj(Landroid/view/View;F)V

    return-void
.end method

.method public Fj(I)V
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Jq:I

    if-gtz v0, :cond_0

    if-lez p1, :cond_0

    const/4 v0, 0x0

    invoke-virtual {p0, v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->eV(Z)V

    goto :goto_0

    :cond_0
    if-lez v0, :cond_1

    if-nez p1, :cond_1

    const/4 v0, 0x1

    invoke-virtual {p0, v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->eV(Z)V

    :cond_1
    :goto_0
    iput p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Jq:I

    return-void
.end method

.method public Fj(II)V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Fj:Lcom/bytedance/sdk/openadsdk/core/Vq;

    if-eqz v0, :cond_1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->uM:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Kk:Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;

    invoke-virtual {v0}, Landroid/app/Activity;->isFinishing()Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    :try_start_0
    new-instance v0, Lorg/json/JSONObject;

    invoke-direct {v0}, Lorg/json/JSONObject;-><init>()V

    const-string v1, "width"

    invoke-virtual {v0, v1, p1}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    const-string p1, "height"

    invoke-virtual {v0, p1, p2}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Fj:Lcom/bytedance/sdk/openadsdk/core/Vq;

    const-string p2, "resize"

    invoke-virtual {p1, p2, v0}, Lcom/bytedance/sdk/openadsdk/core/Vq;->Fj(Ljava/lang/String;Lorg/json/JSONObject;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return-void

    :catch_0
    move-exception p1

    const-string p2, "TTAD.RFWVM"

    const-string v0, ""

    invoke-static {p2, v0, p1}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)I

    :cond_1
    :goto_0
    return-void
.end method

.method public Fj(Landroid/webkit/DownloadListener;)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->JU:Lcom/bytedance/sdk/component/widget/SSWebView;

    if-eqz v0, :cond_1

    if-nez p1, :cond_0

    goto :goto_0

    :cond_0
    invoke-virtual {v0, p1}, Lcom/bytedance/sdk/component/widget/SSWebView;->setDownloadListener(Landroid/webkit/DownloadListener;)V

    :cond_1
    :goto_0
    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/component/widget/SSWebView;)V
    .locals 3

    if-nez p1, :cond_0

    return-void

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->uM:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Kk:Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/widget/Fj/hjc;->Fj(Landroid/content/Context;)Lcom/bytedance/sdk/openadsdk/core/widget/Fj/hjc;

    move-result-object v0

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/core/widget/Fj/hjc;->Fj(Z)Lcom/bytedance/sdk/openadsdk/core/widget/Fj/hjc;

    move-result-object v0

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/core/widget/Fj/hjc;->ex(Z)Lcom/bytedance/sdk/openadsdk/core/widget/Fj/hjc;

    move-result-object v0

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/widget/SSWebView;->getWebView()Landroid/webkit/WebView;

    move-result-object v2

    invoke-virtual {v0, v2}, Lcom/bytedance/sdk/openadsdk/core/widget/Fj/hjc;->Fj(Landroid/webkit/WebView;)V

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/widget/SSWebView;->getWebView()Landroid/webkit/WebView;

    move-result-object v0

    const/16 v2, 0x1710

    invoke-static {v0, v2}, Lcom/bytedance/sdk/openadsdk/utils/dG;->Fj(Landroid/webkit/WebView;I)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1, v0}, Lcom/bytedance/sdk/component/widget/SSWebView;->setUserAgentString(Ljava/lang/String;)V

    invoke-virtual {p1, v1}, Lcom/bytedance/sdk/component/widget/SSWebView;->setMixedContentMode(I)V

    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/openadsdk/common/svN;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->eh:Lcom/bytedance/sdk/openadsdk/common/svN;

    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/openadsdk/core/Vq;Z)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Fj:Lcom/bytedance/sdk/openadsdk/core/Vq;

    if-eqz v0, :cond_1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->uM:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Kk:Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;

    invoke-virtual {v0}, Landroid/app/Activity;->isFinishing()Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    invoke-virtual {p1, p2}, Lcom/bytedance/sdk/openadsdk/core/Vq;->ex(Z)Lcom/bytedance/sdk/openadsdk/core/Vq;

    :cond_1
    :goto_0
    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/openadsdk/core/Vq;ZZ)V
    .locals 2

    :try_start_0
    new-instance v0, Lorg/json/JSONObject;

    invoke-direct {v0}, Lorg/json/JSONObject;-><init>()V

    const-string v1, "endcard_mute"

    invoke-virtual {v0, v1, p2}, Lorg/json/JSONObject;->put(Ljava/lang/String;Z)Lorg/json/JSONObject;

    const-string p2, "endcard_show"

    invoke-virtual {v0, p2, p3}, Lorg/json/JSONObject;->put(Ljava/lang/String;Z)Lorg/json/JSONObject;

    const-string p2, "endcard_control_event"

    invoke-virtual {p1, p2, v0}, Lcom/bytedance/sdk/openadsdk/core/Vq;->Fj(Ljava/lang/String;Lorg/json/JSONObject;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    :catch_0
    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/openadsdk/mSE/eV;Ljava/lang/String;Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;)V
    .locals 9

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->JU:Lcom/bytedance/sdk/component/widget/SSWebView;

    if-nez v0, :cond_0

    return-void

    :cond_0
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {v1}, Lcom/bytedance/sdk/openadsdk/core/model/mE;->ex(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v1

    const/4 v2, 0x2

    const-string v3, "click_scence"

    if-eqz v1, :cond_1

    const/4 v1, 0x3

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    invoke-interface {v0, v3, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    :cond_1
    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    invoke-interface {v0, v3, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :goto_0
    invoke-direct {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->KZ()Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    move-result-object v1

    iput-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->BcC:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    new-instance v1, Lcom/bytedance/sdk/openadsdk/core/Vq;

    iget-object v4, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->uM:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v4, v4, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Kk:Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;

    invoke-direct {v1, v4}, Lcom/bytedance/sdk/openadsdk/core/Vq;-><init>(Landroid/content/Context;)V

    iput-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Fj:Lcom/bytedance/sdk/openadsdk/core/Vq;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->tc()Ljava/lang/String;

    move-result-object v1

    iget-object v4, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Fj:Lcom/bytedance/sdk/openadsdk/core/Vq;

    iget-object v5, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->JU:Lcom/bytedance/sdk/component/widget/SSWebView;

    invoke-virtual {v4, v5}, Lcom/bytedance/sdk/openadsdk/core/Vq;->ex(Lcom/bytedance/sdk/component/widget/SSWebView;)Lcom/bytedance/sdk/openadsdk/core/Vq;

    move-result-object v4

    iget-object v5, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v4, v5}, Lcom/bytedance/sdk/openadsdk/core/Vq;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Lcom/bytedance/sdk/openadsdk/core/Vq;

    move-result-object v4

    iget-object v5, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v5}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->cs()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v4, v5}, Lcom/bytedance/sdk/openadsdk/core/Vq;->hjc(Ljava/lang/String;)Lcom/bytedance/sdk/openadsdk/core/Vq;

    move-result-object v4

    iget-object v5, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v5}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->Jq()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v4, v5}, Lcom/bytedance/sdk/openadsdk/core/Vq;->eV(Ljava/lang/String;)Lcom/bytedance/sdk/openadsdk/core/Vq;

    move-result-object v4

    invoke-virtual {p3}, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->JW()Z

    move-result v5

    const/4 v6, 0x5

    const/4 v7, 0x7

    if-eqz v5, :cond_2

    const/4 v5, 0x7

    goto :goto_1

    :cond_2
    const/4 v5, 0x5

    :goto_1
    invoke-virtual {v4, v5}, Lcom/bytedance/sdk/openadsdk/core/Vq;->ex(I)Lcom/bytedance/sdk/openadsdk/core/Vq;

    move-result-object v4

    new-instance v5, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$ex;

    iget-object v8, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->JU:Lcom/bytedance/sdk/component/widget/SSWebView;

    invoke-direct {v5, v8}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$ex;-><init>(Landroid/view/View;)V

    invoke-virtual {v4, v5}, Lcom/bytedance/sdk/openadsdk/core/Vq;->Fj(Lcom/bytedance/sdk/openadsdk/mSE/Fj;)Lcom/bytedance/sdk/openadsdk/core/Vq;

    move-result-object v4

    invoke-virtual {v4, v1}, Lcom/bytedance/sdk/openadsdk/core/Vq;->Ubf(Ljava/lang/String;)Lcom/bytedance/sdk/openadsdk/core/Vq;

    move-result-object v4

    iget-object v5, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->JU:Lcom/bytedance/sdk/component/widget/SSWebView;

    invoke-virtual {v4, v5}, Lcom/bytedance/sdk/openadsdk/core/Vq;->Fj(Lcom/bytedance/sdk/component/widget/SSWebView;)Lcom/bytedance/sdk/openadsdk/core/Vq;

    move-result-object v4

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->JW()Z

    move-result v5

    const-string v8, "landingpage_endcard"

    if-eqz v5, :cond_3

    move-object v5, v8

    goto :goto_2

    :cond_3
    move-object v5, p2

    :goto_2
    invoke-virtual {v4, v5}, Lcom/bytedance/sdk/openadsdk/core/Vq;->ex(Ljava/lang/String;)Lcom/bytedance/sdk/openadsdk/core/Vq;

    move-result-object v4

    invoke-virtual {v4, v0}, Lcom/bytedance/sdk/openadsdk/core/Vq;->Fj(Ljava/util/Map;)Lcom/bytedance/sdk/openadsdk/core/Vq;

    move-result-object v0

    iget-object v4, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->BcC:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    invoke-virtual {v0, v4}, Lcom/bytedance/sdk/openadsdk/core/Vq;->Fj(Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;)Lcom/bytedance/sdk/openadsdk/core/Vq;

    move-result-object v0

    new-instance v4, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$11;

    invoke-direct {v4, p0, p3}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$11;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;)V

    invoke-virtual {v0, v4}, Lcom/bytedance/sdk/openadsdk/core/Vq;->Fj(Lcom/bytedance/sdk/openadsdk/core/widget/ex;)Lcom/bytedance/sdk/openadsdk/core/Vq;

    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    iget-object v4, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {v4}, Lcom/bytedance/sdk/openadsdk/core/model/mE;->hjc(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v4

    if-eqz v4, :cond_4

    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    invoke-interface {v0, v3, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_4
    new-instance v2, Lcom/bytedance/sdk/openadsdk/core/Vq;

    iget-object v3, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->uM:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v3, v3, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Kk:Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;

    invoke-direct {v2, v3}, Lcom/bytedance/sdk/openadsdk/core/Vq;-><init>(Landroid/content/Context;)V

    iput-object v2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->ex:Lcom/bytedance/sdk/openadsdk/core/Vq;

    iget-object v3, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ql:Lcom/bytedance/sdk/component/widget/SSWebView;

    invoke-virtual {v2, v3}, Lcom/bytedance/sdk/openadsdk/core/Vq;->ex(Lcom/bytedance/sdk/component/widget/SSWebView;)Lcom/bytedance/sdk/openadsdk/core/Vq;

    move-result-object v2

    iget-object v3, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v2, v3}, Lcom/bytedance/sdk/openadsdk/core/Vq;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Lcom/bytedance/sdk/openadsdk/core/Vq;

    move-result-object v2

    iget-object v3, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v3}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->cs()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Lcom/bytedance/sdk/openadsdk/core/Vq;->hjc(Ljava/lang/String;)Lcom/bytedance/sdk/openadsdk/core/Vq;

    move-result-object v2

    iget-object v3, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v3}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->Jq()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Lcom/bytedance/sdk/openadsdk/core/Vq;->eV(Ljava/lang/String;)Lcom/bytedance/sdk/openadsdk/core/Vq;

    move-result-object v2

    invoke-virtual {p3}, Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;->JW()Z

    move-result p3

    if-eqz p3, :cond_5

    const/4 v6, 0x7

    :cond_5
    invoke-virtual {v2, v6}, Lcom/bytedance/sdk/openadsdk/core/Vq;->ex(I)Lcom/bytedance/sdk/openadsdk/core/Vq;

    move-result-object p3

    new-instance v2, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$ex;

    iget-object v3, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ql:Lcom/bytedance/sdk/component/widget/SSWebView;

    invoke-direct {v2, v3}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$ex;-><init>(Landroid/view/View;)V

    invoke-virtual {p3, v2}, Lcom/bytedance/sdk/openadsdk/core/Vq;->Fj(Lcom/bytedance/sdk/openadsdk/mSE/Fj;)Lcom/bytedance/sdk/openadsdk/core/Vq;

    move-result-object p3

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ql:Lcom/bytedance/sdk/component/widget/SSWebView;

    invoke-virtual {p3, v2}, Lcom/bytedance/sdk/openadsdk/core/Vq;->Fj(Lcom/bytedance/sdk/component/widget/SSWebView;)Lcom/bytedance/sdk/openadsdk/core/Vq;

    move-result-object p3

    invoke-virtual {p3, v1}, Lcom/bytedance/sdk/openadsdk/core/Vq;->Ubf(Ljava/lang/String;)Lcom/bytedance/sdk/openadsdk/core/Vq;

    move-result-object p3

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->JW()Z

    move-result v1

    if-eqz v1, :cond_6

    move-object p2, v8

    :cond_6
    invoke-virtual {p3, p2}, Lcom/bytedance/sdk/openadsdk/core/Vq;->ex(Ljava/lang/String;)Lcom/bytedance/sdk/openadsdk/core/Vq;

    move-result-object p2

    invoke-virtual {p2, v0}, Lcom/bytedance/sdk/openadsdk/core/Vq;->Fj(Ljava/util/Map;)Lcom/bytedance/sdk/openadsdk/core/Vq;

    move-result-object p2

    iget-object p3, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->BcC:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    invoke-virtual {p2, p3}, Lcom/bytedance/sdk/openadsdk/core/Vq;->Fj(Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;)Lcom/bytedance/sdk/openadsdk/core/Vq;

    move-result-object p2

    new-instance p3, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$13;

    invoke-direct {p3, p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$13;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;)V

    invoke-virtual {p2, p3}, Lcom/bytedance/sdk/openadsdk/core/Vq;->Fj(Lcom/bytedance/sdk/openadsdk/core/widget/ex;)Lcom/bytedance/sdk/openadsdk/core/Vq;

    move-result-object p2

    new-instance p3, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$12;

    invoke-direct {p3, p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$12;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;)V

    invoke-virtual {p2, p3}, Lcom/bytedance/sdk/openadsdk/core/Vq;->Fj(Lcom/bytedance/sdk/openadsdk/core/Vq$Fj;)V

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Fj:Lcom/bytedance/sdk/openadsdk/core/Vq;

    new-instance p3, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$hjc;

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->JU:Lcom/bytedance/sdk/component/widget/SSWebView;

    const/4 v1, 0x0

    invoke-direct {p3, v0, v1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$hjc;-><init>(Lcom/bytedance/sdk/component/widget/SSWebView;Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$1;)V

    invoke-virtual {p2, p3}, Lcom/bytedance/sdk/openadsdk/core/Vq;->Fj(Lcom/bytedance/sdk/openadsdk/mSE/BcC;)Lcom/bytedance/sdk/openadsdk/core/Vq;

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->ex:Lcom/bytedance/sdk/openadsdk/core/Vq;

    new-instance p3, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$hjc;

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ql:Lcom/bytedance/sdk/component/widget/SSWebView;

    invoke-direct {p3, v0, v1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$hjc;-><init>(Lcom/bytedance/sdk/component/widget/SSWebView;Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$1;)V

    invoke-virtual {p2, p3}, Lcom/bytedance/sdk/openadsdk/core/Vq;->Fj(Lcom/bytedance/sdk/openadsdk/mSE/BcC;)Lcom/bytedance/sdk/openadsdk/core/Vq;

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Fj:Lcom/bytedance/sdk/openadsdk/core/Vq;

    iget-object p3, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->uM:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object p3, p3, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Obv:Lcom/bytedance/sdk/openadsdk/component/reward/view/Ubf;

    invoke-virtual {p3}, Lcom/bytedance/sdk/openadsdk/component/reward/view/Ubf;->Ko()Landroid/view/View;

    move-result-object p3

    invoke-virtual {p2, p3}, Lcom/bytedance/sdk/openadsdk/core/Vq;->Fj(Landroid/view/View;)Lcom/bytedance/sdk/openadsdk/core/Vq;

    move-result-object p2

    iget-object p3, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->uM:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-boolean p3, p3, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Ql:Z

    invoke-virtual {p2, p3}, Lcom/bytedance/sdk/openadsdk/core/Vq;->Fj(Z)Lcom/bytedance/sdk/openadsdk/core/Vq;

    move-result-object p2

    invoke-virtual {p2, p1}, Lcom/bytedance/sdk/openadsdk/core/Vq;->Fj(Lcom/bytedance/sdk/openadsdk/mSE/eV;)Lcom/bytedance/sdk/openadsdk/core/Vq;

    move-result-object p1

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->uM:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object p2, p2, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->gXF:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;

    invoke-virtual {p2}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;->JU()Lcom/bytedance/sdk/openadsdk/mSE/hjc;

    move-result-object p2

    invoke-virtual {p1, p2}, Lcom/bytedance/sdk/openadsdk/core/Vq;->Fj(Lcom/bytedance/sdk/openadsdk/mSE/hjc;)Lcom/bytedance/sdk/openadsdk/core/Vq;

    move-result-object p1

    new-instance p2, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$14;

    invoke-direct {p2, p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$14;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;)V

    invoke-virtual {p1, p2}, Lcom/bytedance/sdk/openadsdk/core/Vq;->Fj(Lcom/bytedance/sdk/openadsdk/mSE/ex;)Lcom/bytedance/sdk/openadsdk/core/Vq;

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Fj:Lcom/bytedance/sdk/openadsdk/core/Vq;

    iget-boolean p2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->efV:Z

    invoke-virtual {p1, p2}, Lcom/bytedance/sdk/openadsdk/core/Vq;->Ubf(Z)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->ex:Lcom/bytedance/sdk/openadsdk/core/Vq;

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->uM:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object p2, p2, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Obv:Lcom/bytedance/sdk/openadsdk/component/reward/view/Ubf;

    invoke-virtual {p2}, Lcom/bytedance/sdk/openadsdk/component/reward/view/Ubf;->Ko()Landroid/view/View;

    move-result-object p2

    invoke-virtual {p1, p2}, Lcom/bytedance/sdk/openadsdk/core/Vq;->Fj(Landroid/view/View;)Lcom/bytedance/sdk/openadsdk/core/Vq;

    move-result-object p1

    new-instance p2, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$15;

    invoke-direct {p2, p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$15;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;)V

    invoke-virtual {p1, p2}, Lcom/bytedance/sdk/openadsdk/core/Vq;->Fj(Lcom/bytedance/sdk/openadsdk/mSE/ex;)Lcom/bytedance/sdk/openadsdk/core/Vq;

    return-void
.end method

.method public Fj(Ljava/lang/String;Lcom/bytedance/sdk/openadsdk/core/video/hjc/ex;)V
    .locals 1

    new-instance v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$8;

    invoke-direct {v0, p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$8;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;)V

    invoke-direct {p0, p1, v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Fj(Ljava/lang/String;Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$eV;)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->uM:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/core/model/mE;->rAx(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result p1

    if-eqz p1, :cond_0

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ql:Lcom/bytedance/sdk/component/widget/SSWebView;

    invoke-virtual {p0, p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Fj(Lcom/bytedance/sdk/component/widget/SSWebView;)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->uM:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->gXF:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;

    new-instance v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$9;

    invoke-direct {v0, p0, p2}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$9;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;Lcom/bytedance/sdk/openadsdk/core/video/hjc/ex;)V

    invoke-virtual {p1, v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;->Fj(Landroid/webkit/DownloadListener;)V

    :cond_0
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->uM:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->gXF:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;

    iget-boolean p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Ql:Z

    invoke-virtual {v0, p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;->Fj(Z)V

    new-instance p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$10;

    invoke-direct {p1, p0, p2}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$10;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;Lcom/bytedance/sdk/openadsdk/core/video/hjc/ex;)V

    invoke-virtual {p0, p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Fj(Landroid/webkit/DownloadListener;)V

    return-void
.end method

.method public Fj(Z)V
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->hjc:Z

    return-void
.end method

.method public Fj(ZILjava/lang/String;)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->BcC:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    if-nez v0, :cond_0

    return-void

    :cond_0
    if-eqz p1, :cond_1

    invoke-interface {v0}, Lcom/bytedance/sdk/openadsdk/ex/hjc/eV;->ex()V

    return-void

    :cond_1
    invoke-interface {v0, p2, p3}, Lcom/bytedance/sdk/openadsdk/ex/hjc/eV;->Fj(ILjava/lang/String;)V

    return-void
.end method

.method public Fj(ZZ)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Fj:Lcom/bytedance/sdk/openadsdk/core/Vq;

    invoke-virtual {p0, v0, p1, p2}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Fj(Lcom/bytedance/sdk/openadsdk/core/Vq;ZZ)V

    return-void
.end method

.method public JU()Z
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->vYf:Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-virtual {v0}, Ljava/util/concurrent/atomic/AtomicBoolean;->get()Z

    move-result v0

    return v0
.end method

.method public JW()Z
    .locals 3

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->eV:Ljava/lang/String;

    const/4 v1, 0x0

    if-nez v0, :cond_0

    return v1

    :cond_0
    :try_start_0
    invoke-static {v0}, Landroid/net/Uri;->parse(Ljava/lang/String;)Landroid/net/Uri;

    move-result-object v0

    invoke-virtual {v0}, Landroid/net/Uri;->getQueryParameterNames()Ljava/util/Set;

    move-result-object v0

    const-string v2, "show_landingpage"

    invoke-interface {v0, v2}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result v0
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return v0

    :catch_0
    return v1
.end method

.method public Ko()Lcom/bytedance/sdk/openadsdk/core/Vq;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Fj:Lcom/bytedance/sdk/openadsdk/core/Vq;

    return-object v0
.end method

.method public Moo()V
    .locals 3

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ubf:Lcom/bytedance/sdk/openadsdk/ex/Ko;

    if-eqz v0, :cond_0

    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v1

    invoke-virtual {v0, v1, v2}, Lcom/bytedance/sdk/openadsdk/ex/Ko;->Fj(J)V

    :cond_0
    return-void
.end method

.method public Ql()V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->BcC:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    if-eqz v0, :cond_0

    invoke-interface {v0}, Lcom/bytedance/sdk/openadsdk/ex/hjc/hjc;->mSE()V

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ubf:Lcom/bytedance/sdk/openadsdk/ex/Ko;

    if-eqz v0, :cond_1

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/ex/Ko;->BcC()V

    :cond_1
    return-void
.end method

.method public Tc()V
    .locals 7

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->JU:Lcom/bytedance/sdk/component/widget/SSWebView;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/widget/SSWebView;->getWebView()Landroid/webkit/WebView;

    move-result-object v0

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/uy;->Fj(Landroid/webkit/WebView;)V

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ql:Lcom/bytedance/sdk/component/widget/SSWebView;

    if-eqz v0, :cond_1

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/widget/SSWebView;->getWebView()Landroid/webkit/WebView;

    move-result-object v0

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/uy;->Fj(Landroid/webkit/WebView;)V

    :cond_1
    iget-wide v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->PpV:J

    const-wide/16 v2, 0x0

    cmp-long v4, v0, v2

    if-lez v4, :cond_3

    iget-wide v4, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->mj:J

    cmp-long v6, v4, v2

    if-lez v6, :cond_2

    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    move-result-wide v2

    iget-wide v4, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->mj:J

    sub-long/2addr v2, v4

    add-long/2addr v0, v2

    iput-wide v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->PpV:J

    :cond_2
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->uM:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v1, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->rAx:Ljava/lang/String;

    const-string v3, "second_endcard_duration"

    const/4 v4, 0x0

    iget-wide v5, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->PpV:J

    invoke-static/range {v1 .. v6}, Lcom/bytedance/sdk/openadsdk/ex/hjc;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Ljava/lang/String;Ljava/lang/String;Lorg/json/JSONObject;J)V

    :cond_3
    const/4 v0, 0x0

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->JU:Lcom/bytedance/sdk/component/widget/SSWebView;

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->BcC:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    const/4 v1, 0x1

    if-eqz v0, :cond_4

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/model/JW;->hjc(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v0

    if-nez v0, :cond_4

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->hjc(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v0

    if-nez v0, :cond_4

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->BcC:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    invoke-interface {v0, v1}, Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;->Fj(Z)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->BcC:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    invoke-interface {v0}, Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;->UYd()V

    :cond_4
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Fj:Lcom/bytedance/sdk/openadsdk/core/Vq;

    if-eqz v0, :cond_5

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/Vq;->UYd()V

    :cond_5
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->ex:Lcom/bytedance/sdk/openadsdk/core/Vq;

    if-eqz v0, :cond_6

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/Vq;->UYd()V

    :cond_6
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ubf:Lcom/bytedance/sdk/openadsdk/ex/Ko;

    if-eqz v0, :cond_9

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v2}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->eh()Z

    move-result v2

    if-nez v2, :cond_8

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {v2}, Lcom/bytedance/sdk/openadsdk/core/model/mE;->ex(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v2

    if-eqz v2, :cond_7

    goto :goto_0

    :cond_7
    const/4 v1, 0x0

    :cond_8
    :goto_0
    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/ex/Ko;->hjc(Z)V

    :cond_9
    invoke-static {p0}, Lcom/bytedance/sdk/openadsdk/utils/DeviceUtils$AudioInfoReceiver;->ex(Lcom/bytedance/sdk/openadsdk/mSE/WR;)V

    return-void
.end method

.method public UYd()Lcom/bytedance/sdk/openadsdk/ex/Ko;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ubf:Lcom/bytedance/sdk/openadsdk/ex/Ko;

    return-object v0
.end method

.method public Ubf()V
    .locals 8

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->uM:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->cs:Landroid/content/Context;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {v0, v1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->Fj(Landroid/content/Context;Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-nez v0, :cond_0

    return-void

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    if-eqz v0, :cond_3

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->Xl()Z

    move-result v0

    if-nez v0, :cond_3

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Obv:Lcom/bykv/vk/openvk/preload/falconx/loader/ILoader;

    if-nez v0, :cond_3

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->JW()Z

    move-result v0

    if-eqz v0, :cond_3

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->ei()Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Bzy:Ljava/lang/String;

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/eV/ex;->Fj()Lcom/bytedance/sdk/openadsdk/eV/ex;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/eV/ex;->ex()Lcom/bykv/vk/openvk/preload/falconx/loader/ILoader;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Obv:Lcom/bykv/vk/openvk/preload/falconx/loader/ILoader;

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/eV/ex;->Fj()Lcom/bytedance/sdk/openadsdk/eV/ex;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Obv:Lcom/bykv/vk/openvk/preload/falconx/loader/ILoader;

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Bzy:Ljava/lang/String;

    invoke-virtual {v0, v1, v2}, Lcom/bytedance/sdk/openadsdk/eV/ex;->Fj(Lcom/bykv/vk/openvk/preload/falconx/loader/ILoader;Ljava/lang/String;)I

    move-result v0

    iput v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->OK:I

    if-lez v0, :cond_1

    const/4 v0, 0x2

    goto :goto_0

    :cond_1
    const/4 v0, 0x0

    :goto_0
    iput v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->gci:I

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Bzy:Ljava/lang/String;

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-nez v0, :cond_3

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ubf:Lcom/bytedance/sdk/openadsdk/ex/Ko;

    if-eqz v0, :cond_2

    iget v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->gci:I

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/ex/Ko;->Fj(I)V

    :cond_2
    iget-wide v2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->cs:J

    iget-object v4, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    const-string v5, "landingpage_endcard"

    iget-object v6, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Obv:Lcom/bykv/vk/openvk/preload/falconx/loader/ILoader;

    iget-object v7, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Bzy:Ljava/lang/String;

    invoke-static/range {v2 .. v7}, Lcom/bytedance/sdk/openadsdk/ex/hjc$Fj;->Fj(JLcom/bytedance/sdk/openadsdk/core/model/Ql;Ljava/lang/String;Lcom/bykv/vk/openvk/preload/falconx/loader/ILoader;Ljava/lang/String;)V

    :cond_3
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->eV:Ljava/lang/String;

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    const/4 v1, 0x1

    if-nez v0, :cond_4

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->eV:Ljava/lang/String;

    const-string v2, "play.google.com/store"

    invoke-virtual {v0, v2}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result v0

    if-nez v0, :cond_5

    :cond_4
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/model/JW;->eV(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v0

    if-eqz v0, :cond_6

    :cond_5
    iput-boolean v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->mSE:Z

    return-void

    :cond_6
    iget-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->hjc:Z

    if-eqz v0, :cond_a

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->JU:Lcom/bytedance/sdk/component/widget/SSWebView;

    if-eqz v0, :cond_9

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->eV:Ljava/lang/String;

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-nez v0, :cond_9

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->ex(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v0

    if-eqz v0, :cond_9

    iget-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->YH:Z

    if-eqz v0, :cond_7

    return-void

    :cond_7
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->eV:Ljava/lang/String;

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v2, "&is_pre_render=1"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ubf:Lcom/bytedance/sdk/openadsdk/ex/Ko;

    if-eqz v2, :cond_8

    invoke-virtual {v2}, Lcom/bytedance/sdk/openadsdk/ex/Ko;->eV()V

    :cond_8
    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->JU:Lcom/bytedance/sdk/component/widget/SSWebView;

    invoke-static {v2, v0}, Lcom/bytedance/sdk/openadsdk/utils/JW;->Fj(Lcom/bytedance/sdk/component/widget/SSWebView;Ljava/lang/String;)V

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->uM:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v2, v2, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->gXF:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;

    invoke-virtual {v2, v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;->hjc(Ljava/lang/String;)V

    iput-boolean v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->YH:Z

    return-void

    :cond_9
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->hjc(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v0

    if-eqz v0, :cond_a

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->uM:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->OK:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->hjc()V

    :cond_a
    return-void
.end method

.method public Vq()V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->BcC:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    if-eqz v0, :cond_0

    invoke-interface {v0}, Lcom/bytedance/sdk/openadsdk/ex/hjc/hjc;->svN()V

    :cond_0
    return-void
.end method

.method public WR()V
    .locals 9

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->spi()Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

    move-result-object v0

    if-nez v0, :cond_0

    return-void

    :cond_0
    invoke-virtual {v0}, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->UYd()Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->At:Ljava/lang/String;

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-eqz v0, :cond_1

    return-void

    :cond_1
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->At:Ljava/lang/String;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    iget v2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->UYd:I

    iget v3, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->JW:I

    iget v4, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Tc:I

    invoke-static {v0, v1, v2, v3, v4}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Fj(Ljava/lang/String;Lcom/bytedance/sdk/openadsdk/core/model/Ql;III)Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->At:Ljava/lang/String;

    new-instance v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$2;

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/JU;->Fj()Landroid/content/Context;

    move-result-object v3

    iget-object v4, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->ex:Lcom/bytedance/sdk/openadsdk/core/Vq;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->cs()Ljava/lang/String;

    move-result-object v5

    iget-object v6, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ubf:Lcom/bytedance/sdk/openadsdk/ex/Ko;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->eh()Z

    move-result v1

    const/4 v8, 0x1

    if-nez v1, :cond_3

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {v1}, Lcom/bytedance/sdk/openadsdk/core/model/mE;->ex(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v1

    if-eqz v1, :cond_2

    goto :goto_0

    :cond_2
    const/4 v1, 0x0

    const/4 v7, 0x0

    goto :goto_1

    :cond_3
    :goto_0
    const/4 v7, 0x1

    :goto_1
    move-object v1, v0

    move-object v2, p0

    invoke-direct/range {v1 .. v7}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$2;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;Landroid/content/Context;Lcom/bytedance/sdk/openadsdk/core/Vq;Ljava/lang/String;Lcom/bytedance/sdk/openadsdk/ex/Ko;Z)V

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ql:Lcom/bytedance/sdk/component/widget/SSWebView;

    invoke-virtual {v1, v0}, Lcom/bytedance/sdk/component/widget/SSWebView;->setWebViewClient(Landroid/webkit/WebViewClient;)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ql:Lcom/bytedance/sdk/component/widget/SSWebView;

    new-instance v1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$3;

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->ex:Lcom/bytedance/sdk/openadsdk/core/Vq;

    iget-object v3, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ubf:Lcom/bytedance/sdk/openadsdk/ex/Ko;

    invoke-direct {v1, p0, v2, v3}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$3;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;Lcom/bytedance/sdk/openadsdk/core/Vq;Lcom/bytedance/sdk/openadsdk/ex/Ko;)V

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/widget/SSWebView;->setWebChromeClient(Landroid/webkit/WebChromeClient;)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ql:Lcom/bytedance/sdk/component/widget/SSWebView;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->At:Ljava/lang/String;

    invoke-static {v0, v1}, Lcom/bytedance/sdk/openadsdk/utils/JW;->Fj(Lcom/bytedance/sdk/component/widget/SSWebView;Ljava/lang/String;)V

    iput-boolean v8, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Af:Z

    return-void
.end method

.method public cB()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->efV:Z

    if-eqz v0, :cond_0

    iget-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->oX:Z

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->vYf:Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-virtual {v0}, Ljava/util/concurrent/atomic/AtomicBoolean;->get()Z

    move-result v0

    if-eqz v0, :cond_0

    iget-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->mE:Z

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    return v0

    :cond_0
    const/4 v0, 0x0

    return v0
.end method

.method public dG()V
    .locals 5

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/model/mE;->WR(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->eV:Ljava/lang/String;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    iget v2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->UYd:I

    iget v3, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->JW:I

    iget v4, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Tc:I

    invoke-static {v0, v1, v2, v3, v4}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Fj(Ljava/lang/String;Lcom/bytedance/sdk/openadsdk/core/model/Ql;III)Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->eV:Ljava/lang/String;

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->eV:Ljava/lang/String;

    const-string v1, "use_second_endcard=1"

    invoke-virtual {v0, v1}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result v0

    iput-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->efV:Z

    :cond_0
    return-void
.end method

.method public eV(Z)V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Fj:Lcom/bytedance/sdk/openadsdk/core/Vq;

    if-eqz v0, :cond_1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->uM:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Kk:Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;

    invoke-virtual {v0}, Landroid/app/Activity;->isFinishing()Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->uM:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->gXF:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;

    invoke-virtual {v0, p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;->Ubf(Z)V

    :try_start_0
    new-instance v0, Lorg/json/JSONObject;

    invoke-direct {v0}, Lorg/json/JSONObject;-><init>()V

    const-string v1, "endcard_mute"

    invoke-virtual {v0, v1, p1}, Lorg/json/JSONObject;->put(Ljava/lang/String;Z)Lorg/json/JSONObject;

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Fj:Lcom/bytedance/sdk/openadsdk/core/Vq;

    const-string v1, "volumeChange"

    invoke-virtual {p1, v1, v0}, Lcom/bytedance/sdk/openadsdk/core/Vq;->Fj(Ljava/lang/String;Lorg/json/JSONObject;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    :catch_0
    :cond_1
    :goto_0
    return-void
.end method

.method public eV()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->oX:Z

    return v0
.end method

.method public efV()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->iT:Z

    return v0
.end method

.method public eh()V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ql:Lcom/bytedance/sdk/component/widget/SSWebView;

    const/16 v1, 0x8

    invoke-static {v0, v1}, Lcom/bytedance/sdk/openadsdk/utils/eh;->Fj(Landroid/view/View;I)V

    return-void
.end method

.method public ex()V
    .locals 3

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->uM:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Kk:Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;

    const v1, 0x1020002

    invoke-virtual {v0, v1}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->cB:Landroid/view/View;

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->uM:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-boolean v1, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->vYf:Z

    iput-boolean v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->kF:Z

    const/16 v2, 0x8

    if-eqz v1, :cond_0

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->eh:Lcom/bytedance/sdk/openadsdk/common/svN;

    if-eqz v1, :cond_0

    invoke-virtual {v1}, Lcom/bytedance/sdk/openadsdk/common/svN;->eV()Lcom/bytedance/sdk/component/widget/SSWebView;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->JU:Lcom/bytedance/sdk/component/widget/SSWebView;

    goto :goto_0

    :cond_0
    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Kk:Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;

    sget v1, Lcom/bytedance/sdk/openadsdk/utils/Ko;->Tc:I

    invoke-virtual {v0, v1}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Lcom/bytedance/sdk/component/widget/SSWebView;

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->JU:Lcom/bytedance/sdk/component/widget/SSWebView;

    if-eqz v0, :cond_1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->ex(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v0

    if-eqz v0, :cond_1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->JU:Lcom/bytedance/sdk/component/widget/SSWebView;

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/widget/SSWebView;->d_()V

    goto :goto_0

    :cond_1
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->JU:Lcom/bytedance/sdk/component/widget/SSWebView;

    invoke-static {v0, v2}, Lcom/bytedance/sdk/openadsdk/utils/eh;->Fj(Landroid/view/View;I)V

    :goto_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->uM:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Kk:Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;

    sget v1, Lcom/bytedance/sdk/openadsdk/utils/Ko;->JU:I

    invoke-virtual {v0, v1}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Lcom/bytedance/sdk/component/widget/SSWebView;

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ql:Lcom/bytedance/sdk/component/widget/SSWebView;

    if-eqz v0, :cond_2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/model/mE;->ex(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v0

    if-eqz v0, :cond_2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ql:Lcom/bytedance/sdk/component/widget/SSWebView;

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/widget/SSWebView;->d_()V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ql:Lcom/bytedance/sdk/component/widget/SSWebView;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/widget/SSWebView;->setDisplayZoomControls(Z)V

    goto :goto_1

    :cond_2
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ql:Lcom/bytedance/sdk/component/widget/SSWebView;

    invoke-static {v0, v2}, Lcom/bytedance/sdk/openadsdk/utils/eh;->Fj(Landroid/view/View;I)V

    :goto_1
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->JU:Lcom/bytedance/sdk/component/widget/SSWebView;

    if-eqz v0, :cond_3

    invoke-virtual {v0}, Landroid/view/View;->getViewTreeObserver()Landroid/view/ViewTreeObserver;

    move-result-object v0

    new-instance v1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$1;

    invoke-direct {v1, p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$1;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;)V

    invoke-virtual {v0, v1}, Landroid/view/ViewTreeObserver;->addOnGlobalLayoutListener(Landroid/view/ViewTreeObserver$OnGlobalLayoutListener;)V

    :cond_3
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ql:Lcom/bytedance/sdk/component/widget/SSWebView;

    if-eqz v0, :cond_5

    const/4 v1, 0x1

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/widget/SSWebView;->setLandingPage(Z)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ql:Lcom/bytedance/sdk/component/widget/SSWebView;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {v1}, Lcom/bytedance/sdk/openadsdk/core/model/mE;->ex(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v1

    if-eqz v1, :cond_4

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->rAx:Ljava/lang/String;

    goto :goto_2

    :cond_4
    const-string v1, "landingpage_endcard"

    :goto_2
    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/widget/SSWebView;->setTag(Ljava/lang/String;)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ql:Lcom/bytedance/sdk/component/widget/SSWebView;

    new-instance v1, Lcom/bytedance/sdk/component/widget/SSWebView$Fj;

    invoke-direct {v1}, Lcom/bytedance/sdk/component/widget/SSWebView$Fj;-><init>()V

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/widget/SSWebView;->setWebViewClient(Landroid/webkit/WebViewClient;)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ql:Lcom/bytedance/sdk/component/widget/SSWebView;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->jID()Lcom/bytedance/sdk/component/widget/ex/Fj;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/widget/SSWebView;->setMaterialMeta(Lcom/bytedance/sdk/component/widget/ex/Fj;)V

    :cond_5
    return-void
.end method

.method public ex(I)V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->JU:Lcom/bytedance/sdk/component/widget/SSWebView;

    invoke-static {v0, p1}, Lcom/bytedance/sdk/openadsdk/utils/eh;->Fj(Landroid/view/View;I)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->JU:Lcom/bytedance/sdk/component/widget/SSWebView;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/widget/SSWebView;->getWebView()Landroid/webkit/WebView;

    move-result-object v0

    invoke-static {v0, p1}, Lcom/bytedance/sdk/openadsdk/utils/eh;->Fj(Landroid/view/View;I)V

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->JU:Lcom/bytedance/sdk/component/widget/SSWebView;

    if-eqz v0, :cond_3

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->eh()Z

    move-result v0

    if-nez v0, :cond_1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/model/mE;->ex(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v0

    if-eqz v0, :cond_3

    :cond_1
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->JU:Lcom/bytedance/sdk/component/widget/SSWebView;

    const/4 v1, 0x1

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/widget/SSWebView;->setLandingPage(Z)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->JU:Lcom/bytedance/sdk/component/widget/SSWebView;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {v1}, Lcom/bytedance/sdk/openadsdk/core/model/mE;->ex(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v1

    if-eqz v1, :cond_2

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->rAx:Ljava/lang/String;

    goto :goto_0

    :cond_2
    const-string v1, "landingpage_endcard"

    :goto_0
    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/widget/SSWebView;->setTag(Ljava/lang/String;)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->JU:Lcom/bytedance/sdk/component/widget/SSWebView;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->jID()Lcom/bytedance/sdk/component/widget/ex/Fj;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/widget/SSWebView;->setMaterialMeta(Lcom/bytedance/sdk/component/widget/ex/Fj;)V

    :cond_3
    if-nez p1, :cond_4

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/core/model/mE;->hjc(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result p1

    if-eqz p1, :cond_4

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->WR()V

    :cond_4
    return-void
.end method

.method public ex(Lcom/bytedance/sdk/openadsdk/core/Vq;Z)V
    .locals 2

    :try_start_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->uM:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->gXF:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;

    invoke-virtual {v0, p2}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;->eV(Z)V

    new-instance v0, Lorg/json/JSONObject;

    invoke-direct {v0}, Lorg/json/JSONObject;-><init>()V

    const-string v1, "viewStatus"

    invoke-virtual {v0, v1, p2}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    const-string p2, "viewableChange"

    invoke-virtual {p1, p2, v0}, Lcom/bytedance/sdk/openadsdk/core/Vq;->Fj(Ljava/lang/String;Lorg/json/JSONObject;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    :catch_0
    return-void
.end method

.method public ex(Z)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Fj:Lcom/bytedance/sdk/openadsdk/core/Vq;

    invoke-virtual {p0, v0, p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Fj(Lcom/bytedance/sdk/openadsdk/core/Vq;Z)V

    return-void
.end method

.method public fj()V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->JU:Lcom/bytedance/sdk/component/widget/SSWebView;

    const/4 v1, 0x0

    invoke-static {v0, v1}, Lcom/bytedance/sdk/openadsdk/utils/eh;->Fj(Landroid/view/View;I)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ql:Lcom/bytedance/sdk/component/widget/SSWebView;

    const/16 v1, 0x8

    invoke-static {v0, v1}, Lcom/bytedance/sdk/openadsdk/utils/eh;->Fj(Landroid/view/View;I)V

    return-void
.end method

.method public gXF()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->qg:Z

    return v0
.end method

.method public hjc(Z)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Fj:Lcom/bytedance/sdk/openadsdk/core/Vq;

    invoke-virtual {p0, v0, p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->ex(Lcom/bytedance/sdk/openadsdk/core/Vq;Z)V

    return-void
.end method

.method public hjc()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->KZ:Z

    return v0
.end method

.method public iT()V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ubf:Lcom/bytedance/sdk/openadsdk/ex/Ko;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/ex/Ko;->Ubf()V

    :cond_0
    return-void
.end method

.method public kF()V
    .locals 4

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->rAx:Ljava/lang/String;

    const-string v2, "use_second_endcard"

    const/4 v3, 0x0

    invoke-static {v0, v1, v2, v3}, Lcom/bytedance/sdk/openadsdk/ex/hjc;->ex(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Ljava/lang/String;Ljava/lang/String;Lorg/json/JSONObject;)V

    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->oX:Z

    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    move-result-wide v0

    iput-wide v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->mj:J

    :try_start_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Fj:Lcom/bytedance/sdk/openadsdk/core/Vq;

    const-string v1, "click_endcard_close"

    invoke-virtual {v0, v1, v3}, Lcom/bytedance/sdk/openadsdk/core/Vq;->Fj(Ljava/lang/String;Lorg/json/JSONObject;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    :catch_0
    return-void
.end method

.method public lv()V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->BcC:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    if-eqz v0, :cond_0

    invoke-interface {v0}, Lcom/bytedance/sdk/openadsdk/ex/hjc/hjc;->Ko()V

    :cond_0
    return-void
.end method

.method public mC()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->eV:Ljava/lang/String;

    return-object v0
.end method

.method public mE()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->WR:I

    return v0
.end method

.method public mSE()Lcom/bytedance/sdk/component/widget/SSWebView;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ql:Lcom/bytedance/sdk/component/widget/SSWebView;

    return-object v0
.end method

.method public nsB()V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->BcC:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    if-eqz v0, :cond_0

    invoke-interface {v0}, Lcom/bytedance/sdk/openadsdk/ex/hjc/hjc;->BcC()V

    :cond_0
    return-void
.end method

.method public rAx()Lcom/bytedance/sdk/openadsdk/core/Vq;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->ex:Lcom/bytedance/sdk/openadsdk/core/Vq;

    return-object v0
.end method

.method public rS()V
    .locals 8

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->JU:Lcom/bytedance/sdk/component/widget/SSWebView;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/widget/SSWebView;->rAx()V

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ql:Lcom/bytedance/sdk/component/widget/SSWebView;

    if-eqz v0, :cond_1

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/widget/SSWebView;->rAx()V

    :cond_1
    iget-wide v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->mj:J

    const-wide/16 v2, 0x0

    cmp-long v4, v0, v2

    if-lez v4, :cond_2

    iget-wide v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->PpV:J

    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    move-result-wide v4

    iget-wide v6, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->mj:J

    sub-long/2addr v4, v6

    add-long/2addr v0, v4

    iput-wide v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->PpV:J

    iput-wide v2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->mj:J

    :cond_2
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Fj:Lcom/bytedance/sdk/openadsdk/core/Vq;

    const/4 v1, 0x1

    const/4 v2, 0x0

    if-eqz v0, :cond_3

    invoke-virtual {v0, v2}, Lcom/bytedance/sdk/openadsdk/core/Vq;->ex(Z)Lcom/bytedance/sdk/openadsdk/core/Vq;

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Fj:Lcom/bytedance/sdk/openadsdk/core/Vq;

    invoke-virtual {p0, v0, v2}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->ex(Lcom/bytedance/sdk/openadsdk/core/Vq;Z)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Fj:Lcom/bytedance/sdk/openadsdk/core/Vq;

    invoke-virtual {p0, v0, v1, v2}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Fj(Lcom/bytedance/sdk/openadsdk/core/Vq;ZZ)V

    :cond_3
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->ex:Lcom/bytedance/sdk/openadsdk/core/Vq;

    if-eqz v0, :cond_4

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/model/mE;->hjc(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v0

    if-eqz v0, :cond_4

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->ex:Lcom/bytedance/sdk/openadsdk/core/Vq;

    invoke-virtual {v0, v2}, Lcom/bytedance/sdk/openadsdk/core/Vq;->ex(Z)Lcom/bytedance/sdk/openadsdk/core/Vq;

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->ex:Lcom/bytedance/sdk/openadsdk/core/Vq;

    invoke-virtual {p0, v0, v2}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->ex(Lcom/bytedance/sdk/openadsdk/core/Vq;Z)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->ex:Lcom/bytedance/sdk/openadsdk/core/Vq;

    invoke-virtual {p0, v0, v1, v2}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Fj(Lcom/bytedance/sdk/openadsdk/core/Vq;ZZ)V

    :cond_4
    return-void
.end method

.method public rXP()Z
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Fj:Lcom/bytedance/sdk/openadsdk/core/Vq;

    if-nez v0, :cond_0

    const/4 v0, 0x0

    return v0

    :cond_0
    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/Vq;->mSE()Z

    move-result v0

    return v0
.end method

.method public rf()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->mSE:Z

    return v0
.end method

.method public spi()Z
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->JU:Lcom/bytedance/sdk/component/widget/SSWebView;

    if-eqz v0, :cond_1

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/widget/SSWebView;->getWebView()Landroid/webkit/WebView;

    move-result-object v0

    if-nez v0, :cond_0

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    return v0

    :cond_1
    :goto_0
    const/4 v0, 0x1

    return v0
.end method

.method public svN()V
    .locals 3

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->vYf:Ljava/util/concurrent/atomic/AtomicBoolean;

    iget-boolean v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Af:Z

    invoke-virtual {v0, v1}, Ljava/util/concurrent/atomic/AtomicBoolean;->set(Z)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ql:Lcom/bytedance/sdk/component/widget/SSWebView;

    invoke-virtual {v0}, Landroid/view/View;->getVisibility()I

    move-result v0

    if-nez v0, :cond_0

    iget-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Af:Z

    if-eqz v0, :cond_0

    invoke-direct {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->oX()V

    return-void

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->uM:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->gci:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Ko;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Ko;->hjc(Z)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->uM:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->gXF:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;->Ubf()V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Fj:Lcom/bytedance/sdk/openadsdk/core/Vq;

    const/4 v2, 0x1

    invoke-virtual {p0, v0, v2, v1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Fj(Lcom/bytedance/sdk/openadsdk/core/Vq;ZZ)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Fj:Lcom/bytedance/sdk/openadsdk/core/Vq;

    invoke-virtual {p0, v0, v1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->ex(Lcom/bytedance/sdk/openadsdk/core/Vq;Z)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Fj:Lcom/bytedance/sdk/openadsdk/core/Vq;

    invoke-virtual {p0, v0, v1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Fj(Lcom/bytedance/sdk/openadsdk/core/Vq;Z)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->JU:Lcom/bytedance/sdk/component/widget/SSWebView;

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/widget/SSWebView;->UYd()V

    iget-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Af:Z

    if-eqz v0, :cond_1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ql:Lcom/bytedance/sdk/component/widget/SSWebView;

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/widget/SSWebView;->setVisibility(I)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->ex:Lcom/bytedance/sdk/openadsdk/core/Vq;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->uM:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-boolean v1, v1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->eV:Z

    invoke-virtual {p0, v0, v1, v2}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Fj(Lcom/bytedance/sdk/openadsdk/core/Vq;ZZ)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->ex:Lcom/bytedance/sdk/openadsdk/core/Vq;

    invoke-virtual {p0, v0, v2}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->ex(Lcom/bytedance/sdk/openadsdk/core/Vq;Z)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->ex:Lcom/bytedance/sdk/openadsdk/core/Vq;

    invoke-virtual {p0, v0, v2}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Fj(Lcom/bytedance/sdk/openadsdk/core/Vq;Z)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->uM:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->YH:Lcom/bytedance/sdk/component/utils/Vq;

    const/16 v1, 0x258

    invoke-virtual {v0, v1}, Landroid/os/Handler;->removeMessages(I)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->uM:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v1, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Bzy:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Ubf;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->At:Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;

    invoke-virtual {v1, v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Ubf;->ex(Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;)Z

    move-result v0

    if-nez v0, :cond_3

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->uM:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->At:Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;->Vq()V

    goto :goto_0

    :cond_1
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->uM:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->gXF:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;->vYf()Z

    move-result v0

    if-eqz v0, :cond_2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->uM:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->gXF:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;

    const/4 v1, 0x5

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/svN;->ex(I)V

    :cond_2
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->uM:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Bzy:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Ubf;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Ubf;->eV()V

    :cond_3
    :goto_0
    iput-boolean v2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->qg:Z

    return-void
.end method

.method public uM()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->mE:Z

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->vYf:Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-virtual {v0}, Ljava/util/concurrent/atomic/AtomicBoolean;->get()Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    return v0

    :cond_0
    const/4 v0, 0x0

    return v0
.end method

.method public uy()V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->BcC:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    if-eqz v0, :cond_0

    invoke-interface {v0}, Lcom/bytedance/sdk/openadsdk/ex/hjc/ex;->hjc()V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->BcC:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    invoke-interface {v0}, Lcom/bytedance/sdk/openadsdk/ex/hjc/hjc;->eV()V

    :cond_0
    return-void
.end method

.method public vYf()V
    .locals 5

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->JU:Lcom/bytedance/sdk/component/widget/SSWebView;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/widget/SSWebView;->mSE()V

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ql:Lcom/bytedance/sdk/component/widget/SSWebView;

    if-eqz v0, :cond_1

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/widget/SSWebView;->mSE()V

    :cond_1
    iget-wide v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->mj:J

    const-wide/16 v2, 0x0

    cmp-long v4, v0, v2

    if-nez v4, :cond_2

    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    move-result-wide v0

    iput-wide v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->mj:J

    :cond_2
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Fj:Lcom/bytedance/sdk/openadsdk/core/Vq;

    const/4 v1, 0x0

    const/4 v2, 0x1

    if-eqz v0, :cond_4

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/Vq;->rAx()V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->JU:Lcom/bytedance/sdk/component/widget/SSWebView;

    if-eqz v0, :cond_4

    invoke-virtual {v0}, Landroid/view/View;->getVisibility()I

    move-result v0

    if-nez v0, :cond_3

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Fj:Lcom/bytedance/sdk/openadsdk/core/Vq;

    invoke-virtual {v0, v2}, Lcom/bytedance/sdk/openadsdk/core/Vq;->ex(Z)Lcom/bytedance/sdk/openadsdk/core/Vq;

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Fj:Lcom/bytedance/sdk/openadsdk/core/Vq;

    invoke-virtual {p0, v0, v2}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->ex(Lcom/bytedance/sdk/openadsdk/core/Vq;Z)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Fj:Lcom/bytedance/sdk/openadsdk/core/Vq;

    invoke-virtual {p0, v0, v1, v2}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Fj(Lcom/bytedance/sdk/openadsdk/core/Vq;ZZ)V

    goto :goto_0

    :cond_3
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Fj:Lcom/bytedance/sdk/openadsdk/core/Vq;

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/core/Vq;->ex(Z)Lcom/bytedance/sdk/openadsdk/core/Vq;

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Fj:Lcom/bytedance/sdk/openadsdk/core/Vq;

    invoke-virtual {p0, v0, v1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->ex(Lcom/bytedance/sdk/openadsdk/core/Vq;Z)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Fj:Lcom/bytedance/sdk/openadsdk/core/Vq;

    invoke-virtual {p0, v0, v2, v1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Fj(Lcom/bytedance/sdk/openadsdk/core/Vq;ZZ)V

    :cond_4
    :goto_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->ex:Lcom/bytedance/sdk/openadsdk/core/Vq;

    if-eqz v0, :cond_6

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ko:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/model/mE;->hjc(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v0

    if-eqz v0, :cond_6

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->ex:Lcom/bytedance/sdk/openadsdk/core/Vq;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/Vq;->rAx()V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ql:Lcom/bytedance/sdk/component/widget/SSWebView;

    if-eqz v0, :cond_6

    invoke-virtual {v0}, Landroid/view/View;->getVisibility()I

    move-result v0

    if-nez v0, :cond_5

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->ex:Lcom/bytedance/sdk/openadsdk/core/Vq;

    invoke-virtual {v0, v2}, Lcom/bytedance/sdk/openadsdk/core/Vq;->ex(Z)Lcom/bytedance/sdk/openadsdk/core/Vq;

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->ex:Lcom/bytedance/sdk/openadsdk/core/Vq;

    invoke-virtual {p0, v0, v2}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->ex(Lcom/bytedance/sdk/openadsdk/core/Vq;Z)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->ex:Lcom/bytedance/sdk/openadsdk/core/Vq;

    invoke-virtual {p0, v0, v1, v2}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Fj(Lcom/bytedance/sdk/openadsdk/core/Vq;ZZ)V

    iget-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->iT:Z

    if-nez v0, :cond_6

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->uM:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->LZ()Z

    move-result v0

    if-eqz v0, :cond_6

    invoke-direct {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->oX()V

    goto :goto_1

    :cond_5
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->ex:Lcom/bytedance/sdk/openadsdk/core/Vq;

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/core/Vq;->ex(Z)Lcom/bytedance/sdk/openadsdk/core/Vq;

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->ex:Lcom/bytedance/sdk/openadsdk/core/Vq;

    invoke-virtual {p0, v0, v1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->ex(Lcom/bytedance/sdk/openadsdk/core/Vq;Z)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->ex:Lcom/bytedance/sdk/openadsdk/core/Vq;

    invoke-virtual {p0, v0, v2, v1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Fj(Lcom/bytedance/sdk/openadsdk/core/Vq;ZZ)V

    :cond_6
    :goto_1
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ubf:Lcom/bytedance/sdk/openadsdk/ex/Ko;

    if-eqz v0, :cond_7

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/ex/Ko;->svN()V

    :cond_7
    return-void
.end method
