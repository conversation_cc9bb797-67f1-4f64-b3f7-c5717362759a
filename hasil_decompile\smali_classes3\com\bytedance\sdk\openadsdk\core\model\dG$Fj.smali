.class public Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/sdk/openadsdk/core/model/dG;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "Fj"
.end annotation


# instance fields
.field private BcC:I

.field protected Fj:Landroid/util/SparseArray;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Landroid/util/SparseArray<",
            "Lcom/bytedance/sdk/openadsdk/core/ex/hjc$Fj;",
            ">;"
        }
    .end annotation
.end field

.field private JU:Z

.field private JW:I

.field private Ko:I

.field private Tc:Lorg/json/JSONObject;

.field private UYd:Ljava/lang/String;

.field private Ubf:F

.field private WR:F

.field private dG:I

.field private eV:F

.field private ex:J

.field private hjc:J

.field private mSE:I

.field private rAx:I

.field private svN:F


# direct methods
.method public constructor <init>()V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Landroid/util/SparseArray;

    invoke-direct {v0}, Landroid/util/SparseArray;-><init>()V

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->Fj:Landroid/util/SparseArray;

    return-void
.end method

.method public static synthetic BcC(Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;)I
    .locals 0

    iget p0, p0, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->mSE:I

    return p0
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;)F
    .locals 0

    iget p0, p0, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->svN:F

    return p0
.end method

.method public static synthetic JW(Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;)I
    .locals 0

    iget p0, p0, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->JW:I

    return p0
.end method

.method public static synthetic Ko(Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;)I
    .locals 0

    iget p0, p0, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->rAx:I

    return p0
.end method

.method public static synthetic Tc(Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;)Lorg/json/JSONObject;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->Tc:Lorg/json/JSONObject;

    return-object p0
.end method

.method public static synthetic UYd(Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;)Z
    .locals 0

    iget-boolean p0, p0, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->JU:Z

    return p0
.end method

.method public static synthetic Ubf(Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;)J
    .locals 2

    iget-wide v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->hjc:J

    return-wide v0
.end method

.method public static synthetic WR(Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;)J
    .locals 2

    iget-wide v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->ex:J

    return-wide v0
.end method

.method public static synthetic dG(Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;)I
    .locals 0

    iget p0, p0, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->dG:I

    return p0
.end method

.method public static synthetic eV(Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;)F
    .locals 0

    iget p0, p0, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->eV:F

    return p0
.end method

.method public static synthetic ex(Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;)F
    .locals 0

    iget p0, p0, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->WR:F

    return p0
.end method

.method public static synthetic hjc(Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;)F
    .locals 0

    iget p0, p0, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->Ubf:F

    return p0
.end method

.method public static synthetic mSE(Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;)I
    .locals 0

    iget p0, p0, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->Ko:I

    return p0
.end method

.method public static synthetic rAx(Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;)Ljava/lang/String;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->UYd:Ljava/lang/String;

    return-object p0
.end method

.method public static synthetic svN(Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;)I
    .locals 0

    iget p0, p0, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->BcC:I

    return p0
.end method


# virtual methods
.method public Fj(F)Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->eV:F

    return-object p0
.end method

.method public Fj(I)Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->JW:I

    return-object p0
.end method

.method public Fj(J)Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;
    .locals 0

    iput-wide p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->ex:J

    return-object p0
.end method

.method public Fj(Landroid/util/SparseArray;)Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/util/SparseArray<",
            "Lcom/bytedance/sdk/openadsdk/core/ex/hjc$Fj;",
            ">;)",
            "Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;"
        }
    .end annotation

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->Fj:Landroid/util/SparseArray;

    return-object p0
.end method

.method public Fj(Ljava/lang/String;)Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->UYd:Ljava/lang/String;

    return-object p0
.end method

.method public Fj(Lorg/json/JSONObject;)Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->Tc:Lorg/json/JSONObject;

    return-object p0
.end method

.method public Fj(Z)Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->JU:Z

    return-object p0
.end method

.method public Fj()Lcom/bytedance/sdk/openadsdk/core/model/dG;
    .locals 2

    new-instance v0, Lcom/bytedance/sdk/openadsdk/core/model/dG;

    const/4 v1, 0x0

    invoke-direct {v0, p0, v1}, Lcom/bytedance/sdk/openadsdk/core/model/dG;-><init>(Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;Lcom/bytedance/sdk/openadsdk/core/model/dG$1;)V

    return-object v0
.end method

.method public Ubf(I)Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->Ko:I

    return-object p0
.end method

.method public WR(I)Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->rAx:I

    return-object p0
.end method

.method public eV(F)Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->svN:F

    return-object p0
.end method

.method public eV(I)Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->mSE:I

    return-object p0
.end method

.method public ex(F)Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->Ubf:F

    return-object p0
.end method

.method public ex(I)Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->dG:I

    return-object p0
.end method

.method public ex(J)Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;
    .locals 0

    iput-wide p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->hjc:J

    return-object p0
.end method

.method public hjc(F)Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->WR:F

    return-object p0
.end method

.method public hjc(I)Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/dG$Fj;->BcC:I

    return-object p0
.end method
