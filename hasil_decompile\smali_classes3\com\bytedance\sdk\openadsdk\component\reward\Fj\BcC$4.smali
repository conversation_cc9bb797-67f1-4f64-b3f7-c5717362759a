.class Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC$4;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/openadsdk/core/ex/Fj$Fj;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC;->hjc()Lcom/bytedance/sdk/openadsdk/core/ex/Ubf;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC$4;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public getVideoProgress()J
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC$4;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/BcC;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object v0

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->kF:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->svN()J

    move-result-wide v0

    return-wide v0
.end method
