<?xml version="1.0" encoding="utf-8"?>
<layer-list
  xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:id="@android:id/background">
        <shape android:shape="oval" android:innerRadiusRatio="2.25" android:thickness="2.0dip">
            <stroke android:width="2.0dip" android:color="@color/white_30" />
        </shape>
    </item>
    <item android:id="@android:id/progress">
        <rotate android:fromDegrees="270.0" android:toDegrees="270.0">
            <shape android:shape="ring" android:innerRadiusRatio="2.25" android:useLevel="true" android:thickness="2.0dip">
                <solid android:color="@color/white" />
            </shape>
        </rotate>
    </item>
</layer-list>
