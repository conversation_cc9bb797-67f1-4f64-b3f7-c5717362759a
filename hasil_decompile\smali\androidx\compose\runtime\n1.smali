.class public interface abstract Landroidx/compose/runtime/n1;
.super Ljava/lang/Object;

# interfaces
.implements Lx/g;
.implements Landroidx/compose/runtime/s;
.implements Landroidx/compose/runtime/r;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/compose/runtime/n1$a;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lx/g<",
        "Landroidx/compose/runtime/q<",
        "Ljava/lang/Object;",
        ">;",
        "Landroidx/compose/runtime/l3<",
        "Ljava/lang/Object;",
        ">;>;",
        "Landroidx/compose/runtime/s;",
        "Landroidx/compose/runtime/r;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# virtual methods
.method public abstract builder()Landroidx/compose/runtime/n1$a;
.end method

.method public abstract f(Landroidx/compose/runtime/q;Landroidx/compose/runtime/l3;)Landroidx/compose/runtime/n1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/compose/runtime/q<",
            "Ljava/lang/Object;",
            ">;",
            "Landroidx/compose/runtime/l3<",
            "Ljava/lang/Object;",
            ">;)",
            "Landroidx/compose/runtime/n1;"
        }
    .end annotation
.end method
