.class public final synthetic Landroidx/compose/ui/input/nestedscroll/a;
.super Ljava/lang/Object;


# direct methods
.method public static a(Landroidx/compose/ui/input/nestedscroll/b;JJLkotlin/coroutines/Continuation;)Ljava/lang/Object;
    .locals 0

    invoke-static/range {p0 .. p5}, Landroidx/compose/ui/input/nestedscroll/a;->e(Landroidx/compose/ui/input/nestedscroll/b;JJLkotlin/coroutines/Continuation;)Ljava/lang/Object;

    move-result-object p0

    return-object p0
.end method

.method public static b(Landroidx/compose/ui/input/nestedscroll/b;JJI)J
    .locals 0

    sget-object p0, Ld0/g;->b:Ld0/g$a;

    invoke-virtual {p0}, Ld0/g$a;->c()J

    move-result-wide p0

    return-wide p0
.end method

.method public static c(Landroidx/compose/ui/input/nestedscroll/b;JLkotlin/coroutines/Continuation;)Ljava/lang/Object;
    .locals 0

    invoke-static {p0, p1, p2, p3}, Landroidx/compose/ui/input/nestedscroll/a;->f(Landroidx/compose/ui/input/nestedscroll/b;JLkotlin/coroutines/Continuation;)Ljava/lang/Object;

    move-result-object p0

    return-object p0
.end method

.method public static d(Landroidx/compose/ui/input/nestedscroll/b;JI)J
    .locals 0

    sget-object p0, Ld0/g;->b:Ld0/g$a;

    invoke-virtual {p0}, Ld0/g$a;->c()J

    move-result-wide p0

    return-wide p0
.end method

.method public static synthetic e(Landroidx/compose/ui/input/nestedscroll/b;JJLkotlin/coroutines/Continuation;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/compose/ui/input/nestedscroll/b;",
            "JJ",
            "Lkotlin/coroutines/Continuation<",
            "-",
            "Lv0/z;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    sget-object p0, Lv0/z;->b:Lv0/z$a;

    invoke-virtual {p0}, Lv0/z$a;->a()J

    move-result-wide p0

    invoke-static {p0, p1}, Lv0/z;->b(J)Lv0/z;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic f(Landroidx/compose/ui/input/nestedscroll/b;JLkotlin/coroutines/Continuation;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/compose/ui/input/nestedscroll/b;",
            "J",
            "Lkotlin/coroutines/Continuation<",
            "-",
            "Lv0/z;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    sget-object p0, Lv0/z;->b:Lv0/z$a;

    invoke-virtual {p0}, Lv0/z$a;->a()J

    move-result-wide p0

    invoke-static {p0, p1}, Lv0/z;->b(J)Lv0/z;

    move-result-object p0

    return-object p0
.end method
