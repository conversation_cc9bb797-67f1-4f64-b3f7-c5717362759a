.class Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx$1;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/openadsdk/core/ugen/eV/Fj;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->hjc()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj()V
    .locals 3

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;

    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    move-result-wide v1

    invoke-static {v0, v1, v2}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;J)J

    return-void
.end method

.method public Fj(ILjava/lang/String;)V
    .locals 10

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;)Ljava/util/concurrent/atomic/AtomicBoolean;

    move-result-object v0

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/util/concurrent/atomic/AtomicBoolean;->set(Z)V

    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    move-result-wide v0

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;

    iget-object v3, v2, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {v2}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->ex(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;)Ljava/lang/String;

    move-result-object v4

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;

    invoke-static {v2}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->eV(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;)J

    move-result-wide v5

    sub-long v5, v0, v5

    const/4 v9, 0x0

    move v7, p1

    move-object v8, p2

    invoke-static/range {v3 .. v9}, Lcom/bytedance/sdk/openadsdk/ex/hjc;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Ljava/lang/String;JILjava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public Fj(ILjava/lang/String;Ljava/lang/String;)V
    .locals 10

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;

    invoke-static {v0, p3}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;Ljava/lang/String;)Ljava/lang/String;

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;)Ljava/util/concurrent/atomic/AtomicBoolean;

    move-result-object v0

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/util/concurrent/atomic/AtomicBoolean;->set(Z)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;

    iget-object v1, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->ex(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;)Ljava/lang/String;

    move-result-object v2

    const-string v3, "fail"

    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    move-result-wide v4

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->hjc(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;)J

    move-result-wide v6

    sub-long/2addr v4, v6

    const-string v7, "endcard"

    move-object v6, p3

    move v8, p1

    move-object v9, p2

    invoke-static/range {v1 .. v9}, Lcom/bytedance/sdk/openadsdk/ex/hjc;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Ljava/lang/String;Ljava/lang/String;JLjava/lang/String;Ljava/lang/String;ILjava/lang/String;)V

    return-void
.end method

.method public Fj(Lcom/bytedance/adsdk/ugeno/component/ex;)V
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/bytedance/adsdk/ugeno/component/ex<",
            "Landroid/view/View;",
            ">;)V"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;

    invoke-static {v0, p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;Lcom/bytedance/adsdk/ugeno/component/ex;)Lcom/bytedance/adsdk/ugeno/component/ex;

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;)Ljava/util/concurrent/atomic/AtomicBoolean;

    move-result-object p1

    const/4 v0, 0x1

    invoke-virtual {p1, v0}, Ljava/util/concurrent/atomic/AtomicBoolean;->set(Z)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;

    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    move-result-wide v0

    invoke-static {p1, v0, v1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->ex(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;J)J

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->svN()V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;

    iget-object v0, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->ex(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;)Ljava/lang/String;

    move-result-object p1

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;

    invoke-static {v1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->Ubf(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;)J

    move-result-wide v1

    iget-object v3, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;

    invoke-static {v3}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->eV(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;)J

    move-result-wide v3

    sub-long/2addr v1, v3

    invoke-static {v0, p1, v1, v2}, Lcom/bytedance/sdk/openadsdk/ex/hjc;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Ljava/lang/String;J)V

    return-void
.end method

.method public Fj(Ljava/lang/String;)V
    .locals 10

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;

    invoke-static {v0, p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;Ljava/lang/String;)Ljava/lang/String;

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;

    iget-object v1, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->ex(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;)Ljava/lang/String;

    move-result-object v2

    const-string v3, "success"

    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    move-result-wide v4

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->hjc(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;)J

    move-result-wide v6

    sub-long/2addr v4, v6

    const-string v7, "endcard"

    const/4 v8, 0x0

    const/4 v9, 0x0

    move-object v6, p1

    invoke-static/range {v1 .. v9}, Lcom/bytedance/sdk/openadsdk/ex/hjc;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Ljava/lang/String;Ljava/lang/String;JLjava/lang/String;Ljava/lang/String;ILjava/lang/String;)V

    return-void
.end method

.method public ex()V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->eV()V

    return-void
.end method
