.class Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$2;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/component/adexpress/ex/hjc;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;->Fj(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/NativeExpressView;Lcom/bytedance/sdk/openadsdk/core/model/Ql;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/NativeExpressView;

.field final synthetic ex:Ljava/lang/String;

.field final synthetic hjc:Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;Lcom/bytedance/sdk/openadsdk/core/nativeexpress/NativeExpressView;Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$2;->hjc:Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;

    iput-object p2, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$2;->Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/NativeExpressView;

    iput-object p3, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$2;->ex:Ljava/lang/String;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj(Landroid/view/ViewGroup;I)Z
    .locals 2

    :try_start_0
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$2;->Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/NativeExpressView;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/NativeExpressView;->UYd()V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$2;->hjc:Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;->hjc:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->zf()Z

    move-result p1

    if-eqz p1, :cond_0

    new-instance p1, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/VastBannerBackupView;

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$2;->Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/NativeExpressView;

    invoke-virtual {p2}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object p2

    invoke-direct {p1, p2}, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/VastBannerBackupView;-><init>(Landroid/content/Context;)V

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$2;->ex:Ljava/lang/String;

    invoke-virtual {p1, p2}, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/VastBannerBackupView;->setClosedListenerKey(Ljava/lang/String;)V

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$2;->hjc:Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;

    iget-object v0, p2, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;->hjc:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$2;->Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/NativeExpressView;

    invoke-static {p2}, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;->ex(Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;)Lcom/com/bytedance/overseas/sdk/Fj/hjc;

    move-result-object p2

    invoke-virtual {p1, v0, v1, p2}, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/VastBannerBackupView;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/core/nativeexpress/NativeExpressView;Lcom/com/bytedance/overseas/sdk/Fj/hjc;)V

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$2;->hjc:Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;

    invoke-static {p2}, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;->hjc(Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;)Lcom/bytedance/sdk/openadsdk/dislike/ex;

    move-result-object p2

    invoke-virtual {p1, p2}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/BackupView;->setDislikeInner(Lcom/bytedance/sdk/openadsdk/core/Af;)V

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$2;->hjc:Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;

    iget-object p2, p2, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;->Ubf:Lcom/bytedance/sdk/openadsdk/TTDislikeDialogAbstract;

    invoke-virtual {p1, p2}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/BackupView;->setDislikeOuter(Lcom/bytedance/sdk/openadsdk/TTDislikeDialogAbstract;)V

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$2;->hjc:Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;

    invoke-static {p2}, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;->eV(Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;)Lcom/bytedance/sdk/openadsdk/api/banner/PAGBannerAdWrapperListener;

    move-result-object p2

    invoke-virtual {p1, p2}, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/VastBannerBackupView;->setAdInteractionListener(Lcom/bytedance/sdk/openadsdk/api/banner/PAGBannerAdWrapperListener;)V

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$2;->Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/NativeExpressView;

    invoke-virtual {p2, p1}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/NativeExpressView;->setVastVideoHelper(Lcom/bytedance/sdk/openadsdk/core/bannerexpress/VastBannerBackupView;)V

    goto :goto_0

    :cond_0
    new-instance p1, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/BannerExpressBackupView;

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$2;->Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/NativeExpressView;

    invoke-virtual {p2}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object p2

    invoke-direct {p1, p2}, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/BannerExpressBackupView;-><init>(Landroid/content/Context;)V

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$2;->ex:Ljava/lang/String;

    invoke-virtual {p1, p2}, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/BannerExpressBackupView;->setClosedListenerKey(Ljava/lang/String;)V

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$2;->hjc:Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;

    iget-object v0, p2, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;->hjc:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$2;->Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/NativeExpressView;

    invoke-static {p2}, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;->ex(Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;)Lcom/com/bytedance/overseas/sdk/Fj/hjc;

    move-result-object p2

    invoke-virtual {p1, v0, v1, p2}, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/BannerExpressBackupView;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/core/nativeexpress/NativeExpressView;Lcom/com/bytedance/overseas/sdk/Fj/hjc;)V

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$2;->hjc:Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;

    invoke-static {p2}, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;->hjc(Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;)Lcom/bytedance/sdk/openadsdk/dislike/ex;

    move-result-object p2

    invoke-virtual {p1, p2}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/BackupView;->setDislikeInner(Lcom/bytedance/sdk/openadsdk/core/Af;)V

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$2;->hjc:Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;

    iget-object p2, p2, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;->Ubf:Lcom/bytedance/sdk/openadsdk/TTDislikeDialogAbstract;

    invoke-virtual {p1, p2}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/BackupView;->setDislikeOuter(Lcom/bytedance/sdk/openadsdk/TTDislikeDialogAbstract;)V

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$2;->hjc:Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;

    invoke-static {p2}, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;->eV(Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;)Lcom/bytedance/sdk/openadsdk/api/banner/PAGBannerAdWrapperListener;

    move-result-object p2

    invoke-virtual {p1, p2}, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/BannerExpressBackupView;->setAdInteractionListener(Lcom/bytedance/sdk/openadsdk/api/banner/PAGBannerAdWrapperListener;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    :goto_0
    const/4 p1, 0x1

    return p1

    :catch_0
    const/4 p1, 0x0

    return p1
.end method
