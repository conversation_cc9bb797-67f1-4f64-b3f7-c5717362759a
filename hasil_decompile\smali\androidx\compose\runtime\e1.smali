.class public interface abstract Landroidx/compose/runtime/e1;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/compose/runtime/n0;
.implements Landroidx/compose/runtime/i1;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Landroidx/compose/runtime/n0;",
        "Landroidx/compose/runtime/i1<",
        "Ljava/lang/Integer;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# virtual methods
.method public abstract d()I
.end method

.method public abstract g(I)V
.end method

.method public abstract getValue()Ljava/lang/Integer;
.end method

.method public abstract i(I)V
.end method
