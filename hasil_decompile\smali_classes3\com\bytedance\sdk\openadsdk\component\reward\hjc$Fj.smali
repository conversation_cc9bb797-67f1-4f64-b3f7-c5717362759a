.class Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bykv/vk/openvk/component/video/api/Fj;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/sdk/openadsdk/component/reward/hjc;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "Fj"
.end annotation


# instance fields
.field private final BcC:Lcom/bytedance/sdk/openadsdk/ex/svN;

.field private final Fj:J

.field private Ubf:Lcom/bykv/vk/openvk/component/video/api/eV/hjc$Fj;

.field private WR:J

.field private eV:Landroid/os/CountDownTimer;

.field private ex:J

.field private hjc:I

.field private final svN:Lcom/bykv/vk/openvk/component/video/api/ex/Fj;


# direct methods
.method public constructor <init>(JLcom/bykv/vk/openvk/component/video/api/ex/Fj;Lcom/bytedance/sdk/openadsdk/ex/svN;)V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    iput v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;->hjc:I

    iput-wide p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;->Fj:J

    iput-object p3, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;->svN:Lcom/bykv/vk/openvk/component/video/api/ex/Fj;

    iput-object p4, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;->BcC:Lcom/bytedance/sdk/openadsdk/ex/svN;

    return-void
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;I)I
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;->hjc:I

    return p1
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;)J
    .locals 2

    iget-wide v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;->WR:J

    return-wide v0
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;J)J
    .locals 0

    iput-wide p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;->WR:J

    return-wide p1
.end method

.method public static synthetic Ubf(Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;)Lcom/bykv/vk/openvk/component/video/api/ex/Fj;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;->svN:Lcom/bykv/vk/openvk/component/video/api/ex/Fj;

    return-object p0
.end method

.method public static synthetic WR(Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;)Lcom/bytedance/sdk/openadsdk/ex/svN;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;->BcC:Lcom/bytedance/sdk/openadsdk/ex/svN;

    return-object p0
.end method

.method public static synthetic eV(Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;)Lcom/bykv/vk/openvk/component/video/api/eV/hjc$Fj;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;->Ubf:Lcom/bykv/vk/openvk/component/video/api/eV/hjc$Fj;

    return-object p0
.end method

.method public static synthetic ex(Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;)J
    .locals 2

    iget-wide v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;->Fj:J

    return-wide v0
.end method

.method public static synthetic hjc(Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;)J
    .locals 2

    iget-wide v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;->ex:J

    return-wide v0
.end method


# virtual methods
.method public BcC()Z
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;->hjc:I

    if-nez v0, :cond_0

    const/4 v0, 0x1

    return v0

    :cond_0
    const/4 v0, 0x0

    return v0
.end method

.method public Fj(J)V
    .locals 0

    iput-wide p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;->ex:J

    return-void
.end method

.method public Fj(Lcom/bykv/vk/openvk/component/video/api/eV/hjc$Fj;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;->Ubf:Lcom/bykv/vk/openvk/component/video/api/eV/hjc$Fj;

    return-void
.end method

.method public Fj()Z
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public JW()J
    .locals 2

    iget-wide v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;->WR:J

    return-wide v0
.end method

.method public Ko()V
    .locals 2

    const/4 v0, 0x2

    iput v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;->hjc:I

    iget-wide v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;->WR:J

    iput-wide v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;->ex:J

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;->eV:Landroid/os/CountDownTimer;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Landroid/os/CountDownTimer;->cancel()V

    const/4 v0, 0x0

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;->eV:Landroid/os/CountDownTimer;

    :cond_0
    return-void
.end method

.method public Tc()J
    .locals 2

    iget-wide v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;->Fj:J

    return-wide v0
.end method

.method public UYd()J
    .locals 2

    const-wide/16 v0, 0x0

    return-wide v0
.end method

.method public Ubf()I
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public WR()Z
    .locals 2

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;->hjc:I

    const/4 v1, 0x1

    if-ne v0, v1, :cond_0

    return v1

    :cond_0
    const/4 v0, 0x0

    return v0
.end method

.method public dG()I
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public eV()I
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public ex()Z
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public hjc()Z
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public mSE()V
    .locals 12

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;->hjc:I

    const/4 v1, 0x1

    if-ne v0, v1, :cond_0

    return-void

    :cond_0
    iput v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;->hjc:I

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;->Tc()J

    move-result-wide v10

    iget-wide v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;->ex:J

    sub-long v8, v10, v0

    new-instance v0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj$1;

    const-wide/16 v6, 0xc8

    move-object v2, v0

    move-object v3, p0

    move-wide v4, v8

    invoke-direct/range {v2 .. v11}, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj$1;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;JJJJ)V

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;->eV:Landroid/os/CountDownTimer;

    invoke-virtual {v0}, Landroid/os/CountDownTimer;->start()Landroid/os/CountDownTimer;

    return-void
.end method

.method public rAx()V
    .locals 2

    const/4 v0, 0x0

    iput v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;->hjc:I

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;->eV:Landroid/os/CountDownTimer;

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Landroid/os/CountDownTimer;->cancel()V

    iput-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;->eV:Landroid/os/CountDownTimer;

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;->Ubf:Lcom/bykv/vk/openvk/component/video/api/eV/hjc$Fj;

    if-eqz v0, :cond_1

    iput-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;->Ubf:Lcom/bykv/vk/openvk/component/video/api/eV/hjc$Fj;

    :cond_1
    return-void
.end method

.method public svN()Z
    .locals 2

    iget v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/hjc$Fj;->hjc:I

    const/4 v1, 0x2

    if-ne v0, v1, :cond_0

    const/4 v0, 0x1

    return v0

    :cond_0
    const/4 v0, 0x0

    return v0
.end method
