.class Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$ex$1;
.super Lcom/bykv/vk/openvk/component/video/api/Ubf/ex;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$ex;->run()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$ex;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$ex;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$ex$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$ex;

    invoke-direct {p0}, Lcom/bykv/vk/openvk/component/video/api/Ubf/ex;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj(Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;I)V
    .locals 1

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/JU;->Fj()Landroid/content/Context;

    move-result-object p1

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/eV;->Fj(Landroid/content/Context;)Lcom/bytedance/sdk/openadsdk/component/reward/eV;

    move-result-object p1

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$ex$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$ex;

    iget-object v0, p2, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$ex;->ex:Lcom/bytedance/sdk/openadsdk/AdSlot;

    iget-object p2, p2, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$ex;->hjc:Lcom/bytedance/sdk/openadsdk/core/model/Fj;

    invoke-virtual {p1, v0, p2}, Lcom/bytedance/sdk/openadsdk/component/reward/eV;->Fj(Lcom/bytedance/sdk/openadsdk/AdSlot;Lcom/bytedance/sdk/openadsdk/core/model/Fj;)V

    return-void
.end method

.method public Fj(Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;ILjava/lang/String;)V
    .locals 0

    return-void
.end method
