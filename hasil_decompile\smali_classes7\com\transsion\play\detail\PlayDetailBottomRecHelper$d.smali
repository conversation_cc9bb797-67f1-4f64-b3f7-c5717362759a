.class public final Lcom/transsion/play/detail/PlayDetailBottomRecHelper$d;
.super Landroidx/viewpager2/adapter/FragmentStateAdapter;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->r()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# instance fields
.field public final synthetic a:Lcom/transsion/play/detail/PlayDetailBottomRecHelper;


# direct methods
.method public constructor <init>(Lcom/transsion/play/detail/PlayDetailBottomRecHelper;Landroidx/fragment/app/Fragment;)V
    .locals 0

    iput-object p1, p0, Lcom/transsion/play/detail/PlayDetailBottomRecHelper$d;->a:Lcom/transsion/play/detail/PlayDetailBottomRecHelper;

    invoke-direct {p0, p2}, Landroidx/viewpager2/adapter/FragmentStateAdapter;-><init>(Landroidx/fragment/app/Fragment;)V

    return-void
.end method


# virtual methods
.method public createFragment(I)Landroidx/fragment/app/Fragment;
    .locals 9

    const-class v0, Lcom/transsion/videodetail/api/IStreamDetailService;

    const/4 v1, 0x0

    if-nez p1, :cond_3

    invoke-static {}, Lcom/alibaba/android/arouter/launcher/a;->d()Lcom/alibaba/android/arouter/launcher/a;

    move-result-object p1

    invoke-virtual {p1, v0}, Lcom/alibaba/android/arouter/launcher/a;->h(Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    move-object v2, p1

    check-cast v2, Lcom/transsion/videodetail/api/IStreamDetailService;

    iget-object p1, p0, Lcom/transsion/play/detail/PlayDetailBottomRecHelper$d;->a:Lcom/transsion/play/detail/PlayDetailBottomRecHelper;

    invoke-static {p1}, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->f(Lcom/transsion/play/detail/PlayDetailBottomRecHelper;)Lcom/transsion/moviedetailapi/bean/Subject;

    move-result-object p1

    if-eqz p1, :cond_0

    invoke-virtual {p1}, Lcom/transsion/moviedetailapi/bean/Subject;->getSubjectId()Ljava/lang/String;

    move-result-object p1

    move-object v3, p1

    goto :goto_0

    :cond_0
    move-object v3, v1

    :goto_0
    iget-object p1, p0, Lcom/transsion/play/detail/PlayDetailBottomRecHelper$d;->a:Lcom/transsion/play/detail/PlayDetailBottomRecHelper;

    invoke-static {p1}, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->f(Lcom/transsion/play/detail/PlayDetailBottomRecHelper;)Lcom/transsion/moviedetailapi/bean/Subject;

    move-result-object p1

    if-eqz p1, :cond_1

    invoke-virtual {p1}, Lcom/transsion/moviedetailapi/bean/Subject;->getSubjectType()Ljava/lang/Integer;

    move-result-object p1

    move-object v4, p1

    goto :goto_1

    :cond_1
    move-object v4, v1

    :goto_1
    iget-object p1, p0, Lcom/transsion/play/detail/PlayDetailBottomRecHelper$d;->a:Lcom/transsion/play/detail/PlayDetailBottomRecHelper;

    invoke-static {p1}, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->f(Lcom/transsion/play/detail/PlayDetailBottomRecHelper;)Lcom/transsion/moviedetailapi/bean/Subject;

    move-result-object p1

    if-eqz p1, :cond_2

    invoke-virtual {p1}, Lcom/transsion/moviedetailapi/bean/Subject;->getOps()Ljava/lang/String;

    move-result-object v1

    :cond_2
    move-object v5, v1

    iget-object p1, p0, Lcom/transsion/play/detail/PlayDetailBottomRecHelper$d;->a:Lcom/transsion/play/detail/PlayDetailBottomRecHelper;

    invoke-static {p1}, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->e(Lcom/transsion/play/detail/PlayDetailBottomRecHelper;)Ljava/lang/String;

    move-result-object v6

    iget-object p1, p0, Lcom/transsion/play/detail/PlayDetailBottomRecHelper$d;->a:Lcom/transsion/play/detail/PlayDetailBottomRecHelper;

    invoke-static {p1}, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->h(Lcom/transsion/play/detail/PlayDetailBottomRecHelper;)Z

    move-result v7

    iget-object p1, p0, Lcom/transsion/play/detail/PlayDetailBottomRecHelper$d;->a:Lcom/transsion/play/detail/PlayDetailBottomRecHelper;

    invoke-static {p1}, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->i(Lcom/transsion/play/detail/PlayDetailBottomRecHelper;)Z

    move-result v8

    invoke-interface/range {v2 .. v8}, Lcom/transsion/videodetail/api/IStreamDetailService;->d1(Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;ZZ)Landroidx/fragment/app/Fragment;

    move-result-object p1

    goto :goto_2

    :cond_3
    invoke-static {}, Lcom/alibaba/android/arouter/launcher/a;->d()Lcom/alibaba/android/arouter/launcher/a;

    move-result-object p1

    invoke-virtual {p1, v0}, Lcom/alibaba/android/arouter/launcher/a;->h(Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/transsion/videodetail/api/IStreamDetailService;

    iget-object v0, p0, Lcom/transsion/play/detail/PlayDetailBottomRecHelper$d;->a:Lcom/transsion/play/detail/PlayDetailBottomRecHelper;

    invoke-static {v0}, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->f(Lcom/transsion/play/detail/PlayDetailBottomRecHelper;)Lcom/transsion/moviedetailapi/bean/Subject;

    move-result-object v0

    if-eqz v0, :cond_4

    invoke-virtual {v0}, Lcom/transsion/moviedetailapi/bean/Subject;->getSubjectId()Ljava/lang/String;

    move-result-object v1

    :cond_4
    iget-object v0, p0, Lcom/transsion/play/detail/PlayDetailBottomRecHelper$d;->a:Lcom/transsion/play/detail/PlayDetailBottomRecHelper;

    invoke-static {v0}, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->e(Lcom/transsion/play/detail/PlayDetailBottomRecHelper;)Ljava/lang/String;

    move-result-object v0

    invoke-interface {p1, v1, v0}, Lcom/transsion/videodetail/api/IStreamDetailService;->X(Ljava/lang/String;Ljava/lang/String;)Landroidx/fragment/app/Fragment;

    move-result-object p1

    :goto_2
    return-object p1
.end method

.method public getItemCount()I
    .locals 1

    iget-object v0, p0, Lcom/transsion/play/detail/PlayDetailBottomRecHelper$d;->a:Lcom/transsion/play/detail/PlayDetailBottomRecHelper;

    invoke-static {v0}, Lcom/transsion/play/detail/PlayDetailBottomRecHelper;->g(Lcom/transsion/play/detail/PlayDetailBottomRecHelper;)Ljava/util/List;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    return v0
.end method
