.class public final Landroidx/compose/ui/graphics/layer/view/ViewLayerContainer;
.super Landroidx/compose/ui/graphics/layer/view/DrawChildContainer;


# annotations
.annotation runtime Lkotlin/Metadata;
.end annotation


# direct methods
.method public constructor <init>(Landroid/content/Context;)V
    .locals 0

    invoke-direct {p0, p1}, Landroidx/compose/ui/graphics/layer/view/DrawChildContainer;-><init>(Landroid/content/Context;)V

    return-void
.end method


# virtual methods
.method public dispatchDraw(Landroid/graphics/Canvas;)V
    .locals 0

    return-void
.end method
