.class public interface abstract Lcom/bytedance/sdk/openadsdk/component/reward/Fj/eV$Fj;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/sdk/openadsdk/component/reward/Fj/eV;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "Fj"
.end annotation


# virtual methods
.method public abstract Fj(Landroid/view/View;FFFFLandroid/util/SparseArray;III)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/view/View;",
            "FFFF",
            "Landroid/util/SparseArray<",
            "Lcom/bytedance/sdk/openadsdk/core/ex/hjc$Fj;",
            ">;III)V"
        }
    .end annotation
.end method

.method public abstract Fj(Ljava/lang/String;Lorg/json/JSONObject;)V
.end method
