.class Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$4;
.super Lcom/bytedance/sdk/openadsdk/core/nativeexpress/WR;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;->Fj([FLcom/bytedance/sdk/openadsdk/core/video/hjc/ex;Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/core/video/hjc/ex;

.field final synthetic ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;Landroid/content/Context;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Ljava/lang/String;ILcom/bytedance/sdk/openadsdk/core/video/hjc/ex;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$4;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;

    iput-object p6, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$4;->Fj:Lcom/bytedance/sdk/openadsdk/core/video/hjc/ex;

    invoke-direct {p0, p2, p3, p4, p5}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/WR;-><init>(Landroid/content/Context;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Ljava/lang/String;I)V

    return-void
.end method


# virtual methods
.method public Fj(Landroid/view/View;FFFFLandroid/util/SparseArray;Z)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/view/View;",
            "FFFF",
            "Landroid/util/SparseArray<",
            "Lcom/bytedance/sdk/openadsdk/core/ex/hjc$Fj;",
            ">;Z)V"
        }
    .end annotation

    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$4;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;

    invoke-static {v1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR;)Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    move-result-object v1

    iget-object v1, v1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->kF:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;

    invoke-virtual {v1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/dG;->svN()J

    move-result-wide v1

    invoke-static {v1, v2}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v1

    const-string v2, "duration"

    invoke-virtual {v0, v2, v1}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    invoke-virtual {p0, v0}, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->Fj(Ljava/util/Map;)V

    invoke-super/range {p0 .. p7}, Lcom/bytedance/sdk/openadsdk/core/ex/Fj;->Fj(Landroid/view/View;FFFFLandroid/util/SparseArray;Z)V

    invoke-virtual {p0, p1, p7}, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->Fj(Landroid/view/View;Z)Z

    move-result p1

    if-nez p1, :cond_0

    return-void

    :cond_0
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/WR$4;->Fj:Lcom/bytedance/sdk/openadsdk/core/video/hjc/ex;

    invoke-interface {p1}, Lcom/bytedance/sdk/openadsdk/core/video/hjc/ex;->rS()V

    return-void
.end method
