.class Lcom/bytedance/sdk/openadsdk/component/reward/BcC$1;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/component/utils/ex$Fj;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/reward/BcC;->show(Landroid/app/Activity;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

.field final synthetic ex:Lcom/bytedance/sdk/openadsdk/component/reward/BcC;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/reward/BcC;Lcom/bytedance/sdk/openadsdk/core/model/Ql;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/BcC$1;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/BcC;

    iput-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/BcC$1;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj()V
    .locals 0

    return-void
.end method

.method public Fj(Ljava/lang/Throwable;)V
    .locals 2

    const-string v0, "TTRewardVideoAdImpl"

    const-string v1, "show reward video error: "

    invoke-static {v0, v1, p1}, Lcom/bytedance/sdk/component/utils/dG;->Fj(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/BcC$1;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    const-string v0, "fullscreen_interstitial_ad"

    const-string v1, "activity start  fail "

    invoke-static {p1, v0, v1}, Lcom/bytedance/sdk/openadsdk/ex/hjc;->ex(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method
