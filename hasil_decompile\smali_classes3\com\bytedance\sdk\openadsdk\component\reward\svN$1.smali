.class Lcom/bytedance/sdk/openadsdk/component/reward/svN$1;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/reward/svN;->onError(ILjava/lang/String;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:I

.field final synthetic ex:Ljava/lang/String;

.field final synthetic hjc:Lcom/bytedance/sdk/openadsdk/component/reward/svN;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/reward/svN;ILjava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/svN$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/svN;

    iput p2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/svN$1;->Fj:I

    iput-object p3, p0, Lcom/bytedance/sdk/openadsdk/component/reward/svN$1;->ex:Ljava/lang/String;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 3

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/svN$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/svN;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/svN;->Fj:Lcom/bytedance/sdk/openadsdk/api/reward/PAGRewardedAdLoadListener;

    if-eqz v0, :cond_0

    iget v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/svN$1;->Fj:I

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/svN$1;->ex:Ljava/lang/String;

    invoke-interface {v0, v1, v2}, Lcom/bytedance/sdk/openadsdk/api/PAGLoadListener;->onError(ILjava/lang/String;)V

    :cond_0
    return-void
.end method
