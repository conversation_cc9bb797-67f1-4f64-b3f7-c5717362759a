.class Lcom/bytedance/sdk/openadsdk/component/svN$4;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/openadsdk/component/WR$ex;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/svN;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Z)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Z

.field final synthetic ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

.field final synthetic hjc:Lcom/bytedance/sdk/openadsdk/component/svN;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/svN;ZLcom/bytedance/sdk/openadsdk/core/model/Ql;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/svN$4;->hjc:Lcom/bytedance/sdk/openadsdk/component/svN;

    iput-boolean p2, p0, Lcom/bytedance/sdk/openadsdk/component/svN$4;->Fj:Z

    iput-object p3, p0, Lcom/bytedance/sdk/openadsdk/component/svN$4;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj()V
    .locals 6

    iget-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/component/svN$4;->Fj:Z

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/svN$4;->hjc:Lcom/bytedance/sdk/openadsdk/component/svN;

    const/4 v1, 0x5

    invoke-static {v0, v1}, Lcom/bytedance/sdk/openadsdk/component/svN;->Fj(Lcom/bytedance/sdk/openadsdk/component/svN;I)I

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/svN$4;->hjc:Lcom/bytedance/sdk/openadsdk/component/svN;

    new-instance v1, Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;

    const/16 v2, 0x64

    const/16 v3, 0x2713

    invoke-static {v3}, Lcom/bytedance/sdk/openadsdk/core/svN;->Fj(I)Ljava/lang/String;

    move-result-object v4

    const/4 v5, 0x2

    invoke-direct {v1, v5, v2, v3, v4}, Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;-><init>(IIILjava/lang/String;)V

    invoke-static {v0, v1}, Lcom/bytedance/sdk/openadsdk/component/svN;->Fj(Lcom/bytedance/sdk/openadsdk/component/svN;Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;)V

    :cond_0
    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/openadsdk/rAx/Fj/ex;)V
    .locals 3

    iget-boolean p1, p0, Lcom/bytedance/sdk/openadsdk/component/svN$4;->Fj:Z

    if-eqz p1, :cond_0

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/svN$4;->hjc:Lcom/bytedance/sdk/openadsdk/component/svN;

    const/4 v0, 0x4

    invoke-static {p1, v0}, Lcom/bytedance/sdk/openadsdk/component/svN;->Fj(Lcom/bytedance/sdk/openadsdk/component/svN;I)I

    new-instance p1, Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;

    const/16 v0, 0x64

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/svN$4;->ex:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    const/4 v2, 0x1

    invoke-direct {p1, v2, v0, v1}, Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;-><init>(IILcom/bytedance/sdk/openadsdk/core/model/Ql;)V

    invoke-virtual {p1, v2}, Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;->Fj(Z)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/svN$4;->hjc:Lcom/bytedance/sdk/openadsdk/component/svN;

    invoke-static {v0, p1}, Lcom/bytedance/sdk/openadsdk/component/svN;->Fj(Lcom/bytedance/sdk/openadsdk/component/svN;Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;)V

    :cond_0
    return-void
.end method
