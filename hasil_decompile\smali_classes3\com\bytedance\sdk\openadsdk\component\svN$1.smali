.class Lcom/bytedance/sdk/openadsdk/component/svN$1;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/openadsdk/core/Ql$Fj;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/svN;->ex(Lcom/bytedance/sdk/openadsdk/AdSlot;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/AdSlot;

.field final synthetic ex:Lcom/bytedance/sdk/openadsdk/utils/lv;

.field final synthetic hjc:Lcom/bytedance/sdk/openadsdk/component/svN;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/svN;Lcom/bytedance/sdk/openadsdk/AdSlot;Lcom/bytedance/sdk/openadsdk/utils/lv;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/svN$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/svN;

    iput-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/svN$1;->Fj:Lcom/bytedance/sdk/openadsdk/AdSlot;

    iput-object p3, p0, Lcom/bytedance/sdk/openadsdk/component/svN$1;->ex:Lcom/bytedance/sdk/openadsdk/utils/lv;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj(ILjava/lang/String;)V
    .locals 4

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/svN$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/svN;

    const/4 v1, 0x3

    invoke-static {v0, v1}, Lcom/bytedance/sdk/openadsdk/component/svN;->Fj(Lcom/bytedance/sdk/openadsdk/component/svN;I)I

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/svN$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/svN;

    new-instance v1, Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;

    const/4 v2, 0x2

    const/16 v3, 0x64

    invoke-direct {v1, v2, v3, p1, p2}, Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;-><init>(IIILjava/lang/String;)V

    invoke-static {v0, v1}, Lcom/bytedance/sdk/openadsdk/component/svN;->Fj(Lcom/bytedance/sdk/openadsdk/component/svN;Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;)V

    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/openadsdk/core/model/Fj;Lcom/bytedance/sdk/openadsdk/core/model/ex;)V
    .locals 9

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/svN$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/svN;

    const/4 v1, 0x2

    invoke-static {v0, v1}, Lcom/bytedance/sdk/openadsdk/component/svN;->Fj(Lcom/bytedance/sdk/openadsdk/component/svN;I)I

    const/4 v0, 0x3

    const/16 v2, 0x64

    if-eqz p1, :cond_8

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/model/Fj;->hjc()Ljava/util/List;

    move-result-object v3

    if-eqz v3, :cond_8

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/model/Fj;->hjc()Ljava/util/List;

    move-result-object v3

    invoke-interface {v3}, Ljava/util/List;->size()I

    move-result v3

    if-nez v3, :cond_0

    goto/16 :goto_2

    :cond_0
    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/model/Fj;->hjc()Ljava/util/List;

    move-result-object p1

    const/4 p2, 0x0

    invoke-interface {p1, p2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->UYd()J

    move-result-wide v3

    iget-object v5, p0, Lcom/bytedance/sdk/openadsdk/component/svN$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/svN;

    invoke-static {v5}, Lcom/bytedance/sdk/openadsdk/component/svN;->Fj(Lcom/bytedance/sdk/openadsdk/component/svN;)Lcom/bytedance/sdk/openadsdk/core/model/mC;

    move-result-object v5

    iput-wide v3, v5, Lcom/bytedance/sdk/openadsdk/core/model/mC;->ex:J

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->ES()Z

    move-result v5

    const/4 v6, 0x1

    if-eqz v5, :cond_1

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/svN$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/svN;

    new-instance v0, Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;

    invoke-direct {v0, v6, v2, p1}, Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;-><init>(IILcom/bytedance/sdk/openadsdk/core/model/Ql;)V

    invoke-static {p2, v0}, Lcom/bytedance/sdk/openadsdk/component/svN;->Fj(Lcom/bytedance/sdk/openadsdk/component/svN;Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;)V

    return-void

    :cond_1
    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->Ubf(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v5

    const-wide/16 v7, -0x1

    if-eqz v5, :cond_4

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/JU;->eV()Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;

    move-result-object v5

    invoke-interface {v5}, Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;->UYd()I

    move-result v5

    if-eq v5, v6, :cond_3

    if-ne v5, v0, :cond_2

    goto :goto_0

    :cond_2
    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/svN$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/svN;

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/svN$1;->Fj:Lcom/bytedance/sdk/openadsdk/AdSlot;

    invoke-static {p2}, Lcom/bytedance/sdk/openadsdk/component/svN;->Fj(Lcom/bytedance/sdk/openadsdk/component/svN;)Lcom/bytedance/sdk/openadsdk/core/model/mC;

    move-result-object v5

    iget-boolean v5, v5, Lcom/bytedance/sdk/openadsdk/core/model/mC;->Fj:Z

    xor-int/2addr v5, v6

    invoke-static {p2, p1, v0, v5}, Lcom/bytedance/sdk/openadsdk/component/svN;->Fj(Lcom/bytedance/sdk/openadsdk/component/svN;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/AdSlot;Z)V

    goto :goto_1

    :cond_3
    :goto_0
    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/svN$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/svN;

    invoke-static {v1}, Lcom/bytedance/sdk/openadsdk/component/svN;->Fj(Lcom/bytedance/sdk/openadsdk/component/svN;)Lcom/bytedance/sdk/openadsdk/core/model/mC;

    move-result-object v1

    iput-wide v7, v1, Lcom/bytedance/sdk/openadsdk/core/model/mC;->ex:J

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/svN$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/svN;

    invoke-static {v1}, Lcom/bytedance/sdk/openadsdk/component/svN;->Fj(Lcom/bytedance/sdk/openadsdk/component/svN;)Lcom/bytedance/sdk/openadsdk/core/model/mC;

    move-result-object v1

    invoke-virtual {v1, v0}, Lcom/bytedance/sdk/openadsdk/core/model/mC;->Fj(I)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/svN$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/svN;

    new-instance v1, Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;

    invoke-direct {v1, v6, v2, p1}, Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;-><init>(IILcom/bytedance/sdk/openadsdk/core/model/Ql;)V

    invoke-static {v0, v1}, Lcom/bytedance/sdk/openadsdk/component/svN;->Fj(Lcom/bytedance/sdk/openadsdk/component/svN;Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/svN$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/svN;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/svN$1;->Fj:Lcom/bytedance/sdk/openadsdk/AdSlot;

    invoke-static {v0, p1, v1, p2}, Lcom/bytedance/sdk/openadsdk/component/svN;->Fj(Lcom/bytedance/sdk/openadsdk/component/svN;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/AdSlot;Z)V

    return-void

    :cond_4
    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/JU;->eV()Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;

    move-result-object v5

    invoke-interface {v5}, Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;->rAx()I

    move-result v5

    if-ne v5, v6, :cond_5

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/svN$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/svN;

    invoke-static {v1}, Lcom/bytedance/sdk/openadsdk/component/svN;->Fj(Lcom/bytedance/sdk/openadsdk/component/svN;)Lcom/bytedance/sdk/openadsdk/core/model/mC;

    move-result-object v1

    iput-wide v7, v1, Lcom/bytedance/sdk/openadsdk/core/model/mC;->ex:J

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/svN$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/svN;

    invoke-static {v1}, Lcom/bytedance/sdk/openadsdk/component/svN;->Fj(Lcom/bytedance/sdk/openadsdk/component/svN;)Lcom/bytedance/sdk/openadsdk/core/model/mC;

    move-result-object v1

    invoke-virtual {v1, v0}, Lcom/bytedance/sdk/openadsdk/core/model/mC;->Fj(I)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/svN$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/svN;

    new-instance v1, Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;

    invoke-direct {v1, v6, v2, p1}, Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;-><init>(IILcom/bytedance/sdk/openadsdk/core/model/Ql;)V

    invoke-static {v0, v1}, Lcom/bytedance/sdk/openadsdk/component/svN;->Fj(Lcom/bytedance/sdk/openadsdk/component/svN;Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/svN$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/svN;

    invoke-static {v0, p1, p2}, Lcom/bytedance/sdk/openadsdk/component/svN;->Fj(Lcom/bytedance/sdk/openadsdk/component/svN;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Z)V

    return-void

    :cond_5
    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/svN$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/svN;

    invoke-static {p2}, Lcom/bytedance/sdk/openadsdk/component/svN;->Fj(Lcom/bytedance/sdk/openadsdk/component/svN;)Lcom/bytedance/sdk/openadsdk/core/model/mC;

    move-result-object v0

    iget-boolean v0, v0, Lcom/bytedance/sdk/openadsdk/core/model/mC;->Fj:Z

    xor-int/2addr v0, v6

    invoke-static {p2, p1, v0}, Lcom/bytedance/sdk/openadsdk/component/svN;->Fj(Lcom/bytedance/sdk/openadsdk/component/svN;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Z)V

    :goto_1
    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/svN$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/svN;

    invoke-static {p2}, Lcom/bytedance/sdk/openadsdk/component/svN;->Fj(Lcom/bytedance/sdk/openadsdk/component/svN;)Lcom/bytedance/sdk/openadsdk/core/model/mC;

    move-result-object p2

    iget-boolean p2, p2, Lcom/bytedance/sdk/openadsdk/core/model/mC;->Fj:Z

    if-eqz p2, :cond_7

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/svN$1;->ex:Lcom/bytedance/sdk/openadsdk/utils/lv;

    invoke-virtual {p2}, Lcom/bytedance/sdk/openadsdk/utils/lv;->hjc()J

    move-result-wide v7

    invoke-static {p1, v7, v8}, Lcom/bytedance/sdk/openadsdk/Ko/hjc;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;J)V

    const-wide/16 v7, 0x0

    cmp-long p2, v3, v7

    if-nez p2, :cond_6

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/svN$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/svN;

    invoke-static {p2}, Lcom/bytedance/sdk/openadsdk/component/svN;->Fj(Lcom/bytedance/sdk/openadsdk/component/svN;)Lcom/bytedance/sdk/openadsdk/core/model/mC;

    move-result-object p2

    invoke-virtual {p2, v1}, Lcom/bytedance/sdk/openadsdk/core/model/mC;->Fj(I)V

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/svN$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/svN;

    new-instance v0, Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;

    invoke-direct {v0, v6, v2, p1}, Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;-><init>(IILcom/bytedance/sdk/openadsdk/core/model/Ql;)V

    invoke-static {p2, v0}, Lcom/bytedance/sdk/openadsdk/component/svN;->Fj(Lcom/bytedance/sdk/openadsdk/component/svN;Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;)V

    return-void

    :cond_6
    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/dG;->ex()Landroid/os/Handler;

    move-result-object p2

    new-instance v0, Lcom/bytedance/sdk/openadsdk/component/svN$1$1;

    invoke-direct {v0, p0, p1}, Lcom/bytedance/sdk/openadsdk/component/svN$1$1;-><init>(Lcom/bytedance/sdk/openadsdk/component/svN$1;Lcom/bytedance/sdk/openadsdk/core/model/Ql;)V

    invoke-virtual {p2, v0, v3, v4}, Landroid/os/Handler;->postDelayed(Ljava/lang/Runnable;J)Z

    :cond_7
    return-void

    :cond_8
    :goto_2
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/svN$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/svN;

    invoke-static {p1, v0}, Lcom/bytedance/sdk/openadsdk/component/svN;->Fj(Lcom/bytedance/sdk/openadsdk/component/svN;I)I

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/svN$1;->hjc:Lcom/bytedance/sdk/openadsdk/component/svN;

    new-instance v0, Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;

    const/16 v3, 0x4e21

    invoke-static {v3}, Lcom/bytedance/sdk/openadsdk/core/svN;->Fj(I)Ljava/lang/String;

    move-result-object v4

    invoke-direct {v0, v1, v2, v3, v4}, Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;-><init>(IIILjava/lang/String;)V

    invoke-static {p1, v0}, Lcom/bytedance/sdk/openadsdk/component/svN;->Fj(Lcom/bytedance/sdk/openadsdk/component/svN;Lcom/bytedance/sdk/openadsdk/component/Ubf/ex;)V

    const/4 p1, -0x3

    invoke-virtual {p2, p1}, Lcom/bytedance/sdk/openadsdk/core/model/ex;->Fj(I)V

    invoke-static {p2}, Lcom/bytedance/sdk/openadsdk/core/model/ex;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/ex;)V

    return-void
.end method
