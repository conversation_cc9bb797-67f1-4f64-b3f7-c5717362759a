.class public interface abstract Lcom/bytedance/sdk/openadsdk/core/ex/Fj$Fj;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/sdk/openadsdk/core/ex/Fj;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "Fj"
.end annotation


# virtual methods
.method public abstract getVideoProgress()J
.end method
