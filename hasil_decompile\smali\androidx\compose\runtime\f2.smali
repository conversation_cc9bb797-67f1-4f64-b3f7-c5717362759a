.class public final Landroidx/compose/runtime/f2;
.super Ljava/lang/Object;


# annotations
.annotation runtime L<PERSON><PERSON>/Metadata;
.end annotation


# static fields
.field public static final a:Landroidx/compose/runtime/f2;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Landroidx/compose/runtime/f2;

    invoke-direct {v0}, Landroidx/compose/runtime/f2;-><init>()V

    sput-object v0, Landroidx/compose/runtime/f2;->a:Landroidx/compose/runtime/f2;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
