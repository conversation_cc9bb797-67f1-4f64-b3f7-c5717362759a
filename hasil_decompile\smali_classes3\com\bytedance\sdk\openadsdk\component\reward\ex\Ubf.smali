.class public Lcom/bytedance/sdk/openadsdk/component/reward/ex/Ubf;
.super Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;

# interfaces
.implements Lcom/bytedance/sdk/openadsdk/mSE/Ubf;


# instance fields
.field private JU:Z

.field private final JW:Lcom/bytedance/sdk/openadsdk/core/model/rS;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;)V
    .locals 0

    invoke-direct {p0, p1}, Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;)V

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    check-cast p1, Lcom/bytedance/sdk/openadsdk/core/model/rS;

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Ubf;->JW:Lcom/bytedance/sdk/openadsdk/core/model/rS;

    return-void
.end method

.method public static Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;ZLjava/lang/String;)V
    .locals 3

    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    invoke-static {p1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v1

    const-string v2, "is_backup"

    invoke-virtual {v0, v2, v1}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const-string v1, "choose_ad_start_show"

    invoke-static {v1, p0, p2, v0}, Lcom/bytedance/sdk/openadsdk/ex/hjc;->Fj(Ljava/lang/String;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Ljava/lang/String;Ljava/util/Map;)V

    new-instance p2, Lorg/json/JSONObject;

    invoke-direct {p2}, Lorg/json/JSONObject;-><init>()V

    :try_start_0
    const-string v0, "req_id"

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->qw()Ljava/lang/String;

    move-result-object p0

    invoke-virtual {p2, v0, p0}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    invoke-virtual {p2, v2, p1}, Lorg/json/JSONObject;->put(Ljava/lang/String;Z)Lorg/json/JSONObject;
    :try_end_0
    .catch Lorg/json/JSONException; {:try_start_0 .. :try_end_0} :catch_0

    :catch_0
    invoke-static {}, Lcom/bytedance/sdk/openadsdk/Ko/hjc;->Fj()Lcom/bytedance/sdk/openadsdk/Ko/hjc;

    move-result-object p0

    invoke-virtual {p0, v1, p2}, Lcom/bytedance/sdk/openadsdk/Ko/hjc;->Fj(Ljava/lang/String;Lorg/json/JSONObject;)V

    return-void
.end method


# virtual methods
.method public Fj(IJ)V
    .locals 3

    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Ubf;->JU:Z

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Ubf;->JW:Lcom/bytedance/sdk/openadsdk/core/model/rS;

    add-int/lit8 v1, p1, -0x1

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/core/model/rS;->efV(I)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Ubf;->JW:Lcom/bytedance/sdk/openadsdk/core/model/rS;

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v2, v2, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->svN:Ljava/lang/String;

    invoke-static {v0, v2, p1, p2, p3}, Lcom/bytedance/sdk/openadsdk/ex/hjc;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Ljava/lang/String;IJ)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Ubf;->JW:Lcom/bytedance/sdk/openadsdk/core/model/rS;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/model/rS;->pJO()Lcom/bytedance/sdk/openadsdk/core/model/Fj;

    move-result-object p1

    invoke-static {p1, v1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Fj;I)V

    new-instance p1, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Ubf$1;

    invoke-direct {p1, p0}, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Ubf$1;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/ex/Ubf;)V

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/utils/uy;->Fj(Ljava/lang/Runnable;)V

    return-void
.end method

.method public Fj(Landroid/widget/FrameLayout;)V
    .locals 0

    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/component/adexpress/ex/Tc;)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->spi:Lcom/bytedance/sdk/openadsdk/component/reward/view/hjc;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/view/hjc;->Fj()Lcom/bytedance/sdk/openadsdk/component/reward/view/FullRewardExpressView;

    move-result-object v0

    invoke-virtual {v0, p1}, Lcom/bytedance/sdk/openadsdk/component/reward/view/FullRewardExpressView;->ex(Lcom/bytedance/sdk/component/adexpress/ex/Tc;)V

    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/openadsdk/component/reward/view/RewardFullBaseLayout;)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Ubf;->JW:Lcom/bytedance/sdk/openadsdk/core/model/rS;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/ex/hjc;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Ubf;->JW:Lcom/bytedance/sdk/openadsdk/core/model/rS;

    invoke-static {v0, p1}, Lcom/bytedance/sdk/openadsdk/component/reward/ex/hjc;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Landroid/widget/FrameLayout;)V

    return-void

    :cond_0
    invoke-super {p0, p1}, Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/view/RewardFullBaseLayout;)V

    return-void
.end method

.method public Ubf()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public WR()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public ex(Z)V
    .locals 3

    iget-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Ubf;->JU:Z

    if-nez v0, :cond_0

    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    const-string v1, "has_focus"

    invoke-static {p1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object p1

    invoke-virtual {v0, v1, p1}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Ubf;->JW:Lcom/bytedance/sdk/openadsdk/core/model/rS;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v1, v1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->svN:Ljava/lang/String;

    const-string v2, "choose_ad_focus_changed"

    invoke-static {v2, p1, v1, v0}, Lcom/bytedance/sdk/openadsdk/ex/hjc;->Fj(Ljava/lang/String;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Ljava/lang/String;Ljava/util/Map;)V

    :cond_0
    return-void
.end method

.method public ex()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Ubf;->JU:Z

    return v0
.end method

.method public lv()Z
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public rf()V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Kk:Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;

    invoke-virtual {v0}, Landroid/app/Activity;->isFinishing()Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->spi:Lcom/bytedance/sdk/openadsdk/component/reward/view/hjc;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/view/hjc;->BcC()Z

    move-result v0

    if-nez v0, :cond_2

    iget-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Ubf;->JU:Z

    if-eqz v0, :cond_1

    goto :goto_0

    :cond_1
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->spi:Lcom/bytedance/sdk/openadsdk/component/reward/view/hjc;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/view/hjc;->Fj()Lcom/bytedance/sdk/openadsdk/component/reward/view/FullRewardExpressView;

    move-result-object v0

    if-eqz v0, :cond_3

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/NativeExpressView;->getJsObject()Lcom/bytedance/sdk/openadsdk/core/Vq;

    move-result-object v0

    if-eqz v0, :cond_3

    invoke-virtual {v0, p0}, Lcom/bytedance/sdk/openadsdk/core/Vq;->Fj(Lcom/bytedance/sdk/openadsdk/mSE/Ubf;)Lcom/bytedance/sdk/openadsdk/core/Vq;

    goto :goto_1

    :cond_2
    :goto_0
    invoke-super {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;->rf()V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->spi:Lcom/bytedance/sdk/openadsdk/component/reward/view/hjc;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/view/hjc;->BcC()Z

    move-result v0

    if-eqz v0, :cond_3

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;->uy()V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Bzy:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Ubf;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Ubf;->ex()V

    :cond_3
    :goto_1
    return-void
.end method

.method public svN()V
    .locals 0

    return-void
.end method
