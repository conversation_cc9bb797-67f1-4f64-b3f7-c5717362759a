.class public Lcom/bytedance/sdk/openadsdk/core/ex/hjc$Fj;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/sdk/openadsdk/core/ex/hjc;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "Fj"
.end annotation


# instance fields
.field public Fj:I

.field public eV:J

.field public ex:D

.field public hjc:D


# direct methods
.method public constructor <init>(IDDJ)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/ex/hjc$Fj;->Fj:I

    iput-wide p2, p0, Lcom/bytedance/sdk/openadsdk/core/ex/hjc$Fj;->ex:D

    iput-wide p4, p0, Lcom/bytedance/sdk/openadsdk/core/ex/hjc$Fj;->hjc:D

    iput-wide p6, p0, Lcom/bytedance/sdk/openadsdk/core/ex/hjc$Fj;->eV:J

    return-void
.end method
