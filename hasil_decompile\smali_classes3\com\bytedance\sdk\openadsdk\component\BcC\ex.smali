.class public Lcom/bytedance/sdk/openadsdk/component/BcC/ex;
.super Lcom/bytedance/sdk/openadsdk/core/video/hjc/Fj;


# direct methods
.method public constructor <init>(Landroid/content/Context;Landroid/view/ViewGroup;Lcom/bytedance/sdk/openadsdk/core/model/Ql;)V
    .locals 1

    const/4 v0, 0x0

    invoke-direct {p0, p1, p2, p3, v0}, Lcom/bytedance/sdk/openadsdk/core/video/hjc/Fj;-><init>(Landroid/content/Context;Landroid/view/ViewGroup;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/ex/svN;)V

    return-void
.end method


# virtual methods
.method public Fj(Lcom/bytedance/sdk/openadsdk/ex/eV/ex/JW$Fj;)V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/video/Fj/Fj;->WR:Lcom/bytedance/sdk/openadsdk/core/video/nativevideo/Ubf;

    const/4 v1, 0x0

    invoke-static {v0, p1, v1}, Lcom/bytedance/sdk/openadsdk/ex/eV/Fj/Fj;->Fj(Lcom/bykv/vk/openvk/component/video/api/ex/Fj;Lcom/bytedance/sdk/openadsdk/ex/eV/ex/JW$Fj;Lcom/bytedance/sdk/openadsdk/ex/svN;)V

    return-void
.end method

.method public rS()V
    .locals 3

    new-instance v0, Lcom/bytedance/sdk/openadsdk/ex/eV/ex/JW$Fj;

    invoke-direct {v0}, Lcom/bytedance/sdk/openadsdk/ex/eV/ex/JW$Fj;-><init>()V

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/video/Fj/Fj;->Ubf()J

    move-result-wide v1

    invoke-virtual {v0, v1, v2}, Lcom/bytedance/sdk/openadsdk/ex/eV/ex/JW$Fj;->Fj(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/video/Fj/Fj;->BcC()J

    move-result-wide v1

    invoke-virtual {v0, v1, v2}, Lcom/bytedance/sdk/openadsdk/ex/eV/ex/JW$Fj;->hjc(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/video/Fj/Fj;->WR()J

    move-result-wide v1

    invoke-virtual {v0, v1, v2}, Lcom/bytedance/sdk/openadsdk/ex/eV/ex/JW$Fj;->ex(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/core/video/Fj/Fj;->svN()I

    move-result v1

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/ex/eV/ex/JW$Fj;->eV(I)V

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/core/video/Fj/Fj;->WR:Lcom/bytedance/sdk/openadsdk/core/video/nativevideo/Ubf;

    invoke-static {v1, v0}, Lcom/bytedance/sdk/openadsdk/ex/eV/Fj/Fj;->Ubf(Lcom/bykv/vk/openvk/component/video/api/ex/Fj;Lcom/bytedance/sdk/openadsdk/ex/eV/ex/JW$Fj;)V

    return-void
.end method
