.class public final enum Lcom/transsion/player/enum/ScaleMode;
.super Ljava/lang/Enum;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lcom/transsion/player/enum/ScaleMode;",
        ">;"
    }
.end annotation

.annotation runtime L<PERSON><PERSON>/Metadata;
.end annotation


# static fields
.field private static final synthetic $VALUES:[Lcom/transsion/player/enum/ScaleMode;

.field public static final enum SCALE_ASPECT_FILL:Lcom/transsion/player/enum/ScaleMode;

.field public static final enum SCALE_ASPECT_FIT:Lcom/transsion/player/enum/ScaleMode;

.field public static final enum SCALE_TO_FILL:Lcom/transsion/player/enum/ScaleMode;


# instance fields
.field private final value:I


# direct methods
.method private static final synthetic $values()[Lcom/transsion/player/enum/ScaleMode;
    .locals 3

    const/4 v0, 0x3

    new-array v0, v0, [Lcom/transsion/player/enum/ScaleMode;

    const/4 v1, 0x0

    sget-object v2, Lcom/transsion/player/enum/ScaleMode;->SCALE_ASPECT_FIT:Lcom/transsion/player/enum/ScaleMode;

    aput-object v2, v0, v1

    const/4 v1, 0x1

    sget-object v2, Lcom/transsion/player/enum/ScaleMode;->SCALE_ASPECT_FILL:Lcom/transsion/player/enum/ScaleMode;

    aput-object v2, v0, v1

    const/4 v1, 0x2

    sget-object v2, Lcom/transsion/player/enum/ScaleMode;->SCALE_TO_FILL:Lcom/transsion/player/enum/ScaleMode;

    aput-object v2, v0, v1

    return-object v0
.end method

.method static constructor <clinit>()V
    .locals 3

    new-instance v0, Lcom/transsion/player/enum/ScaleMode;

    const-string v1, "SCALE_ASPECT_FIT"

    const/4 v2, 0x0

    invoke-direct {v0, v1, v2, v2}, Lcom/transsion/player/enum/ScaleMode;-><init>(Ljava/lang/String;II)V

    sput-object v0, Lcom/transsion/player/enum/ScaleMode;->SCALE_ASPECT_FIT:Lcom/transsion/player/enum/ScaleMode;

    new-instance v0, Lcom/transsion/player/enum/ScaleMode;

    const-string v1, "SCALE_ASPECT_FILL"

    const/4 v2, 0x1

    invoke-direct {v0, v1, v2, v2}, Lcom/transsion/player/enum/ScaleMode;-><init>(Ljava/lang/String;II)V

    sput-object v0, Lcom/transsion/player/enum/ScaleMode;->SCALE_ASPECT_FILL:Lcom/transsion/player/enum/ScaleMode;

    new-instance v0, Lcom/transsion/player/enum/ScaleMode;

    const-string v1, "SCALE_TO_FILL"

    const/4 v2, 0x2

    invoke-direct {v0, v1, v2, v2}, Lcom/transsion/player/enum/ScaleMode;-><init>(Ljava/lang/String;II)V

    sput-object v0, Lcom/transsion/player/enum/ScaleMode;->SCALE_TO_FILL:Lcom/transsion/player/enum/ScaleMode;

    invoke-static {}, Lcom/transsion/player/enum/ScaleMode;->$values()[Lcom/transsion/player/enum/ScaleMode;

    move-result-object v0

    sput-object v0, Lcom/transsion/player/enum/ScaleMode;->$VALUES:[Lcom/transsion/player/enum/ScaleMode;

    return-void
.end method

.method private constructor <init>(Ljava/lang/String;II)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I)V"
        }
    .end annotation

    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    iput p3, p0, Lcom/transsion/player/enum/ScaleMode;->value:I

    return-void
.end method

.method public static valueOf(Ljava/lang/String;)Lcom/transsion/player/enum/ScaleMode;
    .locals 1

    const-class v0, Lcom/transsion/player/enum/ScaleMode;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object p0

    check-cast p0, Lcom/transsion/player/enum/ScaleMode;

    return-object p0
.end method

.method public static values()[Lcom/transsion/player/enum/ScaleMode;
    .locals 1

    sget-object v0, Lcom/transsion/player/enum/ScaleMode;->$VALUES:[Lcom/transsion/player/enum/ScaleMode;

    invoke-virtual {v0}, [Ljava/lang/Object;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lcom/transsion/player/enum/ScaleMode;

    return-object v0
.end method


# virtual methods
.method public final getValue()I
    .locals 1

    iget v0, p0, Lcom/transsion/player/enum/ScaleMode;->value:I

    return v0
.end method
