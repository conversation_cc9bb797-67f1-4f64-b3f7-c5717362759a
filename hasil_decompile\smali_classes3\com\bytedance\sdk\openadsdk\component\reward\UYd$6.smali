.class Lcom/bytedance/sdk/openadsdk/component/reward/UYd$6;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/openadsdk/component/reward/rAx$Fj;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/reward/UYd;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Fj;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/component/reward/JW;Lcom/bytedance/sdk/openadsdk/AdSlot;ZLcom/bytedance/sdk/openadsdk/component/reward/UYd$Fj;Z)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lcom/bytedance/sdk/openadsdk/component/reward/rAx$Fj<",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation


# instance fields
.field final synthetic Fj:Z

.field final synthetic Ubf:Lcom/bytedance/sdk/openadsdk/AdSlot;

.field final synthetic WR:Lcom/bytedance/sdk/openadsdk/core/model/Fj;

.field final synthetic eV:Z

.field final synthetic ex:Lcom/bytedance/sdk/openadsdk/component/reward/JW;

.field final synthetic hjc:Lcom/bytedance/sdk/openadsdk/component/reward/UYd$Fj;

.field final synthetic svN:Lcom/bytedance/sdk/openadsdk/component/reward/UYd;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/reward/UYd;ZLcom/bytedance/sdk/openadsdk/component/reward/JW;Lcom/bytedance/sdk/openadsdk/component/reward/UYd$Fj;ZLcom/bytedance/sdk/openadsdk/AdSlot;Lcom/bytedance/sdk/openadsdk/core/model/Fj;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/UYd$6;->svN:Lcom/bytedance/sdk/openadsdk/component/reward/UYd;

    iput-boolean p2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/UYd$6;->Fj:Z

    iput-object p3, p0, Lcom/bytedance/sdk/openadsdk/component/reward/UYd$6;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/JW;

    iput-object p4, p0, Lcom/bytedance/sdk/openadsdk/component/reward/UYd$6;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/UYd$Fj;

    iput-boolean p5, p0, Lcom/bytedance/sdk/openadsdk/component/reward/UYd$6;->eV:Z

    iput-object p6, p0, Lcom/bytedance/sdk/openadsdk/component/reward/UYd$6;->Ubf:Lcom/bytedance/sdk/openadsdk/AdSlot;

    iput-object p7, p0, Lcom/bytedance/sdk/openadsdk/component/reward/UYd$6;->WR:Lcom/bytedance/sdk/openadsdk/core/model/Fj;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj(ZLjava/lang/Object;)V
    .locals 1

    if-eqz p1, :cond_0

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/UYd$6;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/JW;

    invoke-virtual {p2}, Lcom/bytedance/sdk/openadsdk/component/reward/JW;->ex()V

    :cond_0
    iget-boolean p2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/UYd$6;->Fj:Z

    if-nez p2, :cond_4

    const/4 p2, 0x1

    if-eqz p1, :cond_2

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/UYd$6;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/UYd$Fj;

    if-eqz p1, :cond_5

    iget-boolean p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/UYd$6;->eV:Z

    if-nez p1, :cond_1

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/JU;->eV()Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;

    move-result-object p1

    invoke-interface {p1}, Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;->mE()I

    move-result p1

    if-ne p1, p2, :cond_5

    :cond_1
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/UYd$6;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/UYd$Fj;

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/UYd$6;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/JW;

    invoke-virtual {p2}, Lcom/bytedance/sdk/openadsdk/component/reward/JW;->Fj()Lcom/bytedance/sdk/openadsdk/component/reward/BcC;

    move-result-object p2

    invoke-virtual {p1, p2}, Lcom/bytedance/sdk/openadsdk/component/reward/UYd$Fj;->Fj(Lcom/bytedance/sdk/openadsdk/api/reward/PAGRewardedAd;)V

    return-void

    :cond_2
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/UYd$6;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/UYd$Fj;

    if-eqz p1, :cond_5

    iget-boolean p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/UYd$6;->eV:Z

    if-nez p1, :cond_3

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/JU;->eV()Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;

    move-result-object p1

    invoke-interface {p1}, Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;->mE()I

    move-result p1

    if-ne p1, p2, :cond_5

    :cond_3
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/UYd$6;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/UYd$Fj;

    const/4 p2, -0x1

    const-string v0, ""

    invoke-virtual {p1, p2, v0}, Lcom/bytedance/sdk/openadsdk/component/reward/UYd$Fj;->onError(ILjava/lang/String;)V

    return-void

    :cond_4
    if-eqz p1, :cond_5

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/UYd$6;->svN:Lcom/bytedance/sdk/openadsdk/component/reward/UYd;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/UYd;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/UYd;)Landroid/content/Context;

    move-result-object p1

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/rAx;->Fj(Landroid/content/Context;)Lcom/bytedance/sdk/openadsdk/component/reward/rAx;

    move-result-object p1

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/UYd$6;->Ubf:Lcom/bytedance/sdk/openadsdk/AdSlot;

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/UYd$6;->WR:Lcom/bytedance/sdk/openadsdk/core/model/Fj;

    invoke-virtual {p1, p2, v0}, Lcom/bytedance/sdk/openadsdk/component/reward/rAx;->Fj(Lcom/bytedance/sdk/openadsdk/AdSlot;Lcom/bytedance/sdk/openadsdk/core/model/Fj;)V

    :cond_5
    return-void
.end method
