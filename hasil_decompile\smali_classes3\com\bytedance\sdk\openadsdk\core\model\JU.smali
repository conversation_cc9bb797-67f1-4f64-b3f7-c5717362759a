.class public Lcom/bytedance/sdk/openadsdk/core/model/JU;
.super Ljava/lang/Object;


# instance fields
.field private Fj:J

.field private ex:J

.field private hjc:Ljava/lang/String;


# direct methods
.method public constructor <init>()V
    .locals 2

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const-wide/16 v0, 0x2710

    iput-wide v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/JU;->Fj:J

    iput-wide v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/JU;->ex:J

    const-string v0, ""

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/JU;->hjc:Ljava/lang/String;

    return-void
.end method


# virtual methods
.method public Fj()J
    .locals 2

    iget-wide v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/JU;->Fj:J

    return-wide v0
.end method

.method public Fj(J)V
    .locals 3

    const-wide/16 v0, 0x0

    cmp-long v2, p1, v0

    if-gtz v2, :cond_0

    const-wide/16 p1, 0xa

    iput-wide p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/JU;->Fj:J

    return-void

    :cond_0
    iput-wide p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/JU;->Fj:J

    return-void
.end method

.method public Fj(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/JU;->hjc:Ljava/lang/String;

    return-void
.end method

.method public ex()J
    .locals 2

    iget-wide v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/JU;->ex:J

    return-wide v0
.end method

.method public ex(J)V
    .locals 3

    const-wide/16 v0, 0x0

    cmp-long v2, p1, v0

    if-gez v2, :cond_0

    const-wide/16 p1, 0x14

    iput-wide p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/JU;->ex:J

    return-void

    :cond_0
    iput-wide p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/JU;->ex:J

    return-void
.end method

.method public hjc()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/JU;->hjc:Ljava/lang/String;

    return-object v0
.end method
