.class public final Landroidx/media3/common/o;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/media3/common/o$b;
    }
.end annotation


# static fields
.field public static final e:Landroidx/media3/common/o;

.field public static final f:Ljava/lang/String;

.field public static final g:Ljava/lang/String;

.field public static final h:Ljava/lang/String;

.field public static final i:Ljava/lang/String;

.field public static final j:Landroidx/media3/common/i;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Landroidx/media3/common/i<",
            "Landroidx/media3/common/o;",
            ">;"
        }
    .end annotation

    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end field


# instance fields
.field public final a:I

.field public final b:I

.field public final c:I

.field public final d:Ljava/lang/String;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Landroidx/media3/common/o$b;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Landroidx/media3/common/o$b;-><init>(I)V

    invoke-virtual {v0}, Landroidx/media3/common/o$b;->e()Landroidx/media3/common/o;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/o;->e:Landroidx/media3/common/o;

    invoke-static {v1}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/o;->f:Ljava/lang/String;

    const/4 v0, 0x1

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/o;->g:Ljava/lang/String;

    const/4 v0, 0x2

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/o;->h:Ljava/lang/String;

    const/4 v0, 0x3

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/o;->i:Ljava/lang/String;

    new-instance v0, Landroidx/media3/common/b;

    invoke-direct {v0}, Landroidx/media3/common/b;-><init>()V

    sput-object v0, Landroidx/media3/common/o;->j:Landroidx/media3/common/i;

    return-void
.end method

.method public constructor <init>(Landroidx/media3/common/o$b;)V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    invoke-static {p1}, Landroidx/media3/common/o$b;->a(Landroidx/media3/common/o$b;)I

    move-result v0

    iput v0, p0, Landroidx/media3/common/o;->a:I

    invoke-static {p1}, Landroidx/media3/common/o$b;->b(Landroidx/media3/common/o$b;)I

    move-result v0

    iput v0, p0, Landroidx/media3/common/o;->b:I

    invoke-static {p1}, Landroidx/media3/common/o$b;->c(Landroidx/media3/common/o$b;)I

    move-result v0

    iput v0, p0, Landroidx/media3/common/o;->c:I

    invoke-static {p1}, Landroidx/media3/common/o$b;->d(Landroidx/media3/common/o$b;)Ljava/lang/String;

    move-result-object p1

    iput-object p1, p0, Landroidx/media3/common/o;->d:Ljava/lang/String;

    return-void
.end method

.method public synthetic constructor <init>(Landroidx/media3/common/o$b;Landroidx/media3/common/o$a;)V
    .locals 0

    invoke-direct {p0, p1}, Landroidx/media3/common/o;-><init>(Landroidx/media3/common/o$b;)V

    return-void
.end method


# virtual methods
.method public equals(Ljava/lang/Object;)Z
    .locals 4
    .param p1    # Ljava/lang/Object;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    :cond_0
    instance-of v1, p1, Landroidx/media3/common/o;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    :cond_1
    check-cast p1, Landroidx/media3/common/o;

    iget v1, p0, Landroidx/media3/common/o;->a:I

    iget v3, p1, Landroidx/media3/common/o;->a:I

    if-ne v1, v3, :cond_2

    iget v1, p0, Landroidx/media3/common/o;->b:I

    iget v3, p1, Landroidx/media3/common/o;->b:I

    if-ne v1, v3, :cond_2

    iget v1, p0, Landroidx/media3/common/o;->c:I

    iget v3, p1, Landroidx/media3/common/o;->c:I

    if-ne v1, v3, :cond_2

    iget-object v1, p0, Landroidx/media3/common/o;->d:Ljava/lang/String;

    iget-object p1, p1, Landroidx/media3/common/o;->d:Ljava/lang/String;

    invoke-static {v1, p1}, Le2/u0;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_2

    goto :goto_0

    :cond_2
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public hashCode()I
    .locals 2

    const/16 v0, 0x20f

    iget v1, p0, Landroidx/media3/common/o;->a:I

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget v1, p0, Landroidx/media3/common/o;->b:I

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget v1, p0, Landroidx/media3/common/o;->c:I

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget-object v1, p0, Landroidx/media3/common/o;->d:Ljava/lang/String;

    if-nez v1, :cond_0

    const/4 v1, 0x0

    goto :goto_0

    :cond_0
    invoke-virtual {v1}, Ljava/lang/String;->hashCode()I

    move-result v1

    :goto_0
    add-int/2addr v0, v1

    return v0
.end method
