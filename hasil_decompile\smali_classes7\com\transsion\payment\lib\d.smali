.class public final Lcom/transsion/payment/lib/d;
.super Ljava/lang/Object;


# annotations
.annotation runtime Lkotlin/Metadata;
.end annotation


# static fields
.field public static final a:Lcom/transsion/payment/lib/d;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Lcom/transsion/payment/lib/d;

    invoke-direct {v0}, Lcom/transsion/payment/lib/d;-><init>()V

    sput-object v0, Lcom/transsion/payment/lib/d;->a:Lcom/transsion/payment/lib/d;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Ljava/lang/String;)V
    .locals 3

    const-string v0, "msg"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    sget-object v0, Lxi/b;->a:Lxi/b$a;

    const-string v1, "co_pay"

    const/4 v2, 0x1

    invoke-virtual {v0, v1, p1, v2}, Lxi/b$a;->c(Ljava/lang/String;Ljava/lang/String;Z)V

    return-void
.end method

.method public final b(Ljava/lang/String;)V
    .locals 3

    const-string v0, "msg"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    sget-object v0, Lxi/b;->a:Lxi/b$a;

    const-string v1, "co_pay"

    const/4 v2, 0x1

    invoke-virtual {v0, v1, p1, v2}, Lxi/b$a;->h(Ljava/lang/String;Ljava/lang/String;Z)V

    return-void
.end method

.method public final c(Ljava/lang/String;)V
    .locals 3

    const-string v0, "msg"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    sget-object v0, Lxi/b;->a:Lxi/b$a;

    const-string v1, "co_pay"

    const/4 v2, 0x1

    invoke-virtual {v0, v1, p1, v2}, Lxi/b$a;->u(Ljava/lang/String;Ljava/lang/String;Z)V

    return-void
.end method
