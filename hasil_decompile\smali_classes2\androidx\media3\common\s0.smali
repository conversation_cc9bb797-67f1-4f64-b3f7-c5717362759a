.class public interface abstract Landroidx/media3/common/s0;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/media3/common/s0$a;
    }
.end annotation


# virtual methods
.method public abstract a(I)Landroidx/media3/common/r0;
.end method

.method public abstract c(Landroidx/media3/common/l0;)V
    .param p1    # Landroidx/media3/common/l0;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
.end method

.method public abstract d()I
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/common/VideoFrameProcessingException;
        }
    .end annotation
.end method

.method public abstract release()V
.end method
