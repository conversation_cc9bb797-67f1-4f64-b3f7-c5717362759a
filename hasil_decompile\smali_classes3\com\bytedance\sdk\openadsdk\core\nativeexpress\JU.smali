.class public Lcom/bytedance/sdk/openadsdk/core/nativeexpress/JU;
.super Landroid/view/GestureDetector;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bytedance/sdk/openadsdk/core/nativeexpress/JU$Fj;
    }
.end annotation


# instance fields
.field private final Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/JU$Fj;

.field private final ex:Lcom/bytedance/sdk/openadsdk/core/ex/WR;


# direct methods
.method public constructor <init>(Landroid/content/Context;)V
    .locals 1

    new-instance v0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/JU$Fj;

    invoke-direct {v0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/JU$Fj;-><init>()V

    invoke-direct {p0, p1, v0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/JU;-><init>(Landroid/content/Context;Lcom/bytedance/sdk/openadsdk/core/nativeexpress/JU$Fj;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Lcom/bytedance/sdk/openadsdk/core/nativeexpress/JU$Fj;)V
    .locals 0

    invoke-direct {p0, p1, p2}, Landroid/view/GestureDetector;-><init>(Landroid/content/Context;Landroid/view/GestureDetector$OnGestureListener;)V

    iput-object p2, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/JU;->Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/JU$Fj;

    new-instance p1, Lcom/bytedance/sdk/openadsdk/core/ex/WR;

    invoke-direct {p1}, Lcom/bytedance/sdk/openadsdk/core/ex/WR;-><init>()V

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/JU;->ex:Lcom/bytedance/sdk/openadsdk/core/ex/WR;

    const/4 p1, 0x0

    invoke-virtual {p0, p1}, Landroid/view/GestureDetector;->setIsLongpressEnabled(Z)V

    return-void
.end method


# virtual methods
.method public Fj(Landroid/content/Context;Landroid/view/View;Landroid/view/View;)Lcom/bytedance/sdk/openadsdk/core/model/mSE;
    .locals 3

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/JU;->ex:Lcom/bytedance/sdk/openadsdk/core/ex/WR;

    if-nez v0, :cond_0

    new-instance p1, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    invoke-direct {p1}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;-><init>()V

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->Fj()Lcom/bytedance/sdk/openadsdk/core/model/mSE;

    move-result-object p1

    return-object p1

    :cond_0
    new-instance v0, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    invoke-direct {v0}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;-><init>()V

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/JU;->ex:Lcom/bytedance/sdk/openadsdk/core/ex/WR;

    iget v1, v1, Lcom/bytedance/sdk/openadsdk/core/ex/WR;->Fj:F

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->WR(F)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/JU;->ex:Lcom/bytedance/sdk/openadsdk/core/ex/WR;

    iget v1, v1, Lcom/bytedance/sdk/openadsdk/core/ex/WR;->ex:F

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->Ubf(F)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/JU;->ex:Lcom/bytedance/sdk/openadsdk/core/ex/WR;

    iget v1, v1, Lcom/bytedance/sdk/openadsdk/core/ex/WR;->hjc:F

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->eV(F)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/JU;->ex:Lcom/bytedance/sdk/openadsdk/core/ex/WR;

    iget v1, v1, Lcom/bytedance/sdk/openadsdk/core/ex/WR;->eV:F

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->hjc(F)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/JU;->ex:Lcom/bytedance/sdk/openadsdk/core/ex/WR;

    iget-wide v1, v1, Lcom/bytedance/sdk/openadsdk/core/ex/WR;->Ubf:J

    invoke-virtual {v0, v1, v2}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->ex(J)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/JU;->ex:Lcom/bytedance/sdk/openadsdk/core/ex/WR;

    iget-wide v1, v1, Lcom/bytedance/sdk/openadsdk/core/ex/WR;->WR:J

    invoke-virtual {v0, v1, v2}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->Fj(J)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    move-result-object v0

    invoke-static {p2}, Lcom/bytedance/sdk/openadsdk/utils/eh;->Fj(Landroid/view/View;)[I

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->ex([I)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    move-result-object v0

    invoke-static {p3}, Lcom/bytedance/sdk/openadsdk/utils/eh;->Fj(Landroid/view/View;)[I

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->Fj([I)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    move-result-object v0

    invoke-static {p2}, Lcom/bytedance/sdk/openadsdk/utils/eh;->hjc(Landroid/view/View;)[I

    move-result-object p2

    invoke-virtual {v0, p2}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->hjc([I)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    move-result-object p2

    invoke-static {p3}, Lcom/bytedance/sdk/openadsdk/utils/eh;->hjc(Landroid/view/View;)[I

    move-result-object p3

    invoke-virtual {p2, p3}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->eV([I)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    move-result-object p2

    iget-object p3, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/JU;->ex:Lcom/bytedance/sdk/openadsdk/core/ex/WR;

    iget p3, p3, Lcom/bytedance/sdk/openadsdk/core/ex/WR;->svN:I

    invoke-virtual {p2, p3}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->eV(I)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    move-result-object p2

    iget-object p3, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/JU;->ex:Lcom/bytedance/sdk/openadsdk/core/ex/WR;

    iget p3, p3, Lcom/bytedance/sdk/openadsdk/core/ex/WR;->BcC:I

    invoke-virtual {p2, p3}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->Ubf(I)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    move-result-object p2

    iget-object p3, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/JU;->ex:Lcom/bytedance/sdk/openadsdk/core/ex/WR;

    iget p3, p3, Lcom/bytedance/sdk/openadsdk/core/ex/WR;->mSE:I

    invoke-virtual {p2, p3}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->WR(I)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    move-result-object p2

    iget-object p3, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/JU;->ex:Lcom/bytedance/sdk/openadsdk/core/ex/WR;

    iget-object p3, p3, Lcom/bytedance/sdk/openadsdk/core/ex/WR;->UYd:Landroid/util/SparseArray;

    invoke-virtual {p2, p3}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->Fj(Landroid/util/SparseArray;)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    move-result-object p2

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/BcC;->ex()Lcom/bytedance/sdk/openadsdk/core/BcC;

    move-result-object p3

    invoke-virtual {p3}, Lcom/bytedance/sdk/openadsdk/core/BcC;->Fj()Z

    move-result p3

    if-eqz p3, :cond_1

    const/4 p3, 0x1

    goto :goto_0

    :cond_1
    const/4 p3, 0x2

    :goto_0
    invoke-virtual {p2, p3}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->ex(I)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    move-result-object p2

    const-string p3, "vessel"

    invoke-virtual {p2, p3}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->Fj(Ljava/lang/String;)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    move-result-object p2

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/utils/eh;->Ubf(Landroid/content/Context;)F

    move-result p3

    invoke-virtual {p2, p3}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->Fj(F)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    move-result-object p2

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/utils/eh;->svN(Landroid/content/Context;)I

    move-result p3

    invoke-virtual {p2, p3}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->hjc(I)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    move-result-object p2

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/utils/eh;->WR(Landroid/content/Context;)F

    move-result p1

    invoke-virtual {p2, p1}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->ex(F)Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;

    move-result-object p1

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/model/mSE$Fj;->Fj()Lcom/bytedance/sdk/openadsdk/core/model/mSE;

    move-result-object p1

    return-object p1
.end method

.method public Fj()V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/JU;->Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/JU$Fj;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/JU$Fj;->Fj()V

    return-void
.end method

.method public ex()Z
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/JU;->Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/JU$Fj;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/JU$Fj;->ex()Z

    move-result v0

    return v0
.end method

.method public onTouchEvent(Landroid/view/MotionEvent;)Z
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/JU;->ex:Lcom/bytedance/sdk/openadsdk/core/ex/WR;

    invoke-virtual {v0, p1}, Lcom/bytedance/sdk/openadsdk/core/ex/WR;->Fj(Landroid/view/MotionEvent;)V

    invoke-super {p0, p1}, Landroid/view/GestureDetector;->onTouchEvent(Landroid/view/MotionEvent;)Z

    move-result p1

    return p1
.end method
