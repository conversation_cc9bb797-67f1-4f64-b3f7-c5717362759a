.class Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$3;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/openadsdk/core/EmptyView$Fj;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;->Fj(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/NativeExpressView;Lcom/bytedance/sdk/openadsdk/core/model/Ql;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

.field final synthetic Ubf:Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$Fj;

.field final synthetic WR:Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;

.field final synthetic eV:Ljava/lang/String;

.field final synthetic ex:Lcom/bytedance/sdk/openadsdk/core/EmptyView;

.field final synthetic hjc:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/NativeExpressView;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/core/EmptyView;Lcom/bytedance/sdk/openadsdk/core/nativeexpress/NativeExpressView;Ljava/lang/String;Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$Fj;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$3;->WR:Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;

    iput-object p2, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$3;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    iput-object p3, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$3;->ex:Lcom/bytedance/sdk/openadsdk/core/EmptyView;

    iput-object p4, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$3;->hjc:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/NativeExpressView;

    iput-object p5, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$3;->eV:Ljava/lang/String;

    iput-object p6, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$3;->Ubf:Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$Fj;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj()V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$3;->WR:Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;->Ubf(Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;)V

    return-void
.end method

.method public Fj(Landroid/view/View;)V
    .locals 7

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$3;->WR:Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;->WR(Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;)Ljava/util/concurrent/atomic/AtomicBoolean;

    move-result-object v0

    const/4 v1, 0x0

    const/4 v2, 0x1

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicBoolean;->compareAndSet(ZZ)Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$3;->WR:Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;

    iget-object v3, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$3;->hjc:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/NativeExpressView;

    iget-object v4, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$3;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    iget-object v5, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$3;->eV:Ljava/lang/String;

    iget-object v6, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$3;->Ubf:Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$Fj;

    move-object v2, p1

    invoke-static/range {v1 .. v6}, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;->Fj(Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;Landroid/view/View;Lcom/bytedance/sdk/openadsdk/core/nativeexpress/NativeExpressView;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Ljava/lang/String;Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$Fj;)V

    :cond_0
    return-void
.end method

.method public Fj(Z)V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$3;->WR:Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$3;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {v0, p1, v1}, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;->Fj(Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;ZLcom/bytedance/sdk/openadsdk/core/model/Ql;)V

    return-void
.end method

.method public ex()V
    .locals 4

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$3;->WR:Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$3;->ex:Lcom/bytedance/sdk/openadsdk/core/EmptyView;

    const/4 v2, 0x0

    iget-object v3, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$3;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {v0, v1, v2, v3}, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;->Fj(Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;Lcom/bytedance/sdk/openadsdk/core/EmptyView;ZLcom/bytedance/sdk/openadsdk/core/model/Ql;)V

    return-void
.end method
