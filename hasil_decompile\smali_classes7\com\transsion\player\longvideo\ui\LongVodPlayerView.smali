.class public final Lcom/transsion/player/longvideo/ui/LongVodPlayerView;
.super Landroidx/constraintlayout/widget/ConstraintLayout;

# interfaces
.implements Lmo/a;
.implements Lcom/transsion/baselib/report/RoomActivityLifecycleCallbacks$a;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/transsion/player/longvideo/ui/LongVodPlayerView$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation

.annotation build Lkotlin/jvm/internal/SourceDebugExtension;
.end annotation


# static fields
.field public static final Companion:Lcom/transsion/player/longvideo/ui/LongVodPlayerView$a;

.field public static final PK_NEW_PLAYER_UI_KEY:Ljava/lang/String; = "pk_player_ui_key"

.field public static final TAG:Ljava/lang/String; = "LongVodPlayerView"


# instance fields
.field public A:Z

.field public B:Z

.field public C:Z

.field public D:Z

.field public E:Z

.field public F:Z

.field public G:Lno/a;

.field public H:Lno/c;

.field public final H0:Lcom/transsion/player/longvideo/helper/h;

.field public I:Landroid/view/View;

.field public I0:Lcom/transsion/player/longvideo/helper/e;

.field public J:Landroid/view/View;

.field public J0:Lcom/transsion/player/longvideo/helper/a;

.field public K:Landroid/view/View;

.field public K0:Z

.field public L:Landroid/widget/FrameLayout;

.field public L0:F

.field public M:Landroid/os/Handler;

.field public final M0:Lkotlin/Lazy;

.field public N:Lcom/transsion/player/longvideo/helper/LongVodLandForwardViewControl;

.field public final N0:Lkotlin/Lazy;

.field public O:Lcom/transsion/postdetail/layer/local/k0;

.field public O0:Lqo/d;

.field public P:Landroid/view/View;

.field public P0:Lqo/b;

.field public Q:Lno/c;

.field public Q0:Z

.field public R:Lcom/transsion/player/ui/ORPlayerView;

.field public R0:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lno/b;",
            ">;"
        }
    .end annotation
.end field

.field public S:Lcom/transsion/postdetail/layer/local/n;

.field public S0:Z

.field public final T:Lkotlin/Lazy;

.field public final T0:Landroid/os/Handler;

.field public U:Lmo/a$b;

.field public U0:Lcom/transsion/player/MediaSource;

.field public V:Lcom/transsion/player/longvideo/helper/LongVodSubtitleHelper;

.field public final V0:Lkotlin/Lazy;

.field public W:Lcom/transsion/player/longvideo/helper/f;

.field public final W0:Lkotlin/Lazy;

.field public final X0:Lkotlinx/coroutines/k0;

.field public Y0:Lkotlin/jvm/functions/Function1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Lcom/transsion/moviedetailapi/bean/DubsInfo;",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation
.end field

.field public Z0:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/transsion/moviedetailapi/bean/DubsInfo;",
            ">;"
        }
    .end annotation
.end field

.field public final a:Loo/e;

.field public final a1:Lkotlin/Lazy;

.field public b:Lcom/transsion/player/longvideo/ui/dialog/LongVodPlayerViewAdHelper;

.field public final b1:Lcom/transsion/player/longvideo/ui/LongVodPlayerView$g;

.field public final c:I

.field public c1:J

.field public final d:I

.field public final d1:Ljava/lang/Runnable;

.field public final e:Lcom/transsion/player/ui/longvideo/a;

.field public final e1:Lcom/transsion/player/longvideo/ui/LongVodPlayerView$f;

.field public f:Landroid/view/ViewGroup;

.field public final f1:Ljava/lang/Runnable;

.field public g:Lcom/transsion/player/longvideo/constants/LongVodPageType;

.field public g1:Z

.field public h:Ljava/lang/String;

.field public final h1:Lkotlin/Lazy;

.field public i:Ljava/lang/String;

.field public j:Lcom/transsion/player/longvideo/ui/LongVodUiType;

.field public k:Lcom/transsion/player/longvideo/ui/LongVodUiType;

.field public l:I

.field public m:I

.field public n:J

.field public o:J

.field public p:J

.field public q:Ljava/lang/String;

.field public r:Z

.field public s:Z

.field public t:Z

.field public u:Z

.field public v:Z

.field public w:Z

.field public x:Z

.field public y:Z

.field public z:Z


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->Companion:Lcom/transsion/player/longvideo/ui/LongVodPlayerView$a;

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 1

    const-string v0, "context"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    const/4 v0, 0x0

    invoke-direct {p0, p1, v0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 1

    const-string v0, "context"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    const/4 v0, 0x0

    invoke-direct {p0, p1, p2, v0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 1

    const-string v0, "context"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-direct {p0, p1, p2, p3}, Landroidx/constraintlayout/widget/ConstraintLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    new-instance p1, Lcom/transsion/player/longvideo/ui/dialog/LongVodPlayerViewAdHelper;

    invoke-direct {p1}, Lcom/transsion/player/longvideo/ui/dialog/LongVodPlayerViewAdHelper;-><init>()V

    iput-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->b:Lcom/transsion/player/longvideo/ui/dialog/LongVodPlayerViewAdHelper;

    invoke-static {}, Lcom/blankj/utilcode/util/d0;->e()I

    move-result p1

    iput p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->c:I

    invoke-static {}, Lcom/blankj/utilcode/util/d0;->c()I

    move-result p1

    iput p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->d:I

    sget-object p1, Lcom/transsion/player/longvideo/ui/LongVodUiType;->MIDDLE:Lcom/transsion/player/longvideo/ui/LongVodUiType;

    iput-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->j:Lcom/transsion/player/longvideo/ui/LongVodUiType;

    iput-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->k:Lcom/transsion/player/longvideo/ui/LongVodUiType;

    const/4 p1, 0x1

    iput-boolean p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->z:Z

    iput-boolean p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->C:Z

    iput-boolean p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->E:Z

    new-instance p2, Landroid/os/Handler;

    invoke-static {}, Landroid/os/Looper;->getMainLooper()Landroid/os/Looper;

    move-result-object p3

    invoke-direct {p2, p3}, Landroid/os/Handler;-><init>(Landroid/os/Looper;)V

    iput-object p2, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->M:Landroid/os/Handler;

    sget-object p2, Lcom/transsion/player/longvideo/ui/LongVodPlayerView$videoDetailPlayDao$2;->INSTANCE:Lcom/transsion/player/longvideo/ui/LongVodPlayerView$videoDetailPlayDao$2;

    invoke-static {p2}, Lkotlin/LazyKt;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/Lazy;

    move-result-object p2

    iput-object p2, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->T:Lkotlin/Lazy;

    new-instance p2, Lcom/transsion/player/longvideo/ui/LongVodPlayerView$configViewModel$2;

    invoke-direct {p2, p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView$configViewModel$2;-><init>(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)V

    invoke-static {p2}, Lkotlin/LazyKt;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/Lazy;

    move-result-object p2

    iput-object p2, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->M0:Lkotlin/Lazy;

    new-instance p2, Lcom/transsion/player/longvideo/ui/LongVodPlayerView$speedViewModel$2;

    invoke-direct {p2, p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView$speedViewModel$2;-><init>(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)V

    invoke-static {p2}, Lkotlin/LazyKt;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/Lazy;

    move-result-object p2

    iput-object p2, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->N0:Lkotlin/Lazy;

    iput-boolean p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->Q0:Z

    new-instance p2, Ljava/util/ArrayList;

    invoke-direct {p2}, Ljava/util/ArrayList;-><init>()V

    iput-object p2, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->R0:Ljava/util/List;

    new-instance p2, Landroid/os/Handler;

    invoke-static {}, Landroid/os/Looper;->getMainLooper()Landroid/os/Looper;

    move-result-object p3

    invoke-direct {p2, p3}, Landroid/os/Handler;-><init>(Landroid/os/Looper;)V

    iput-object p2, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->T0:Landroid/os/Handler;

    sget-object p2, Lcom/transsion/player/longvideo/ui/LongVodPlayerView$playTimeoutStream$2;->INSTANCE:Lcom/transsion/player/longvideo/ui/LongVodPlayerView$playTimeoutStream$2;

    invoke-static {p2}, Lkotlin/LazyKt;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/Lazy;

    move-result-object p2

    iput-object p2, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->V0:Lkotlin/Lazy;

    sget-object p2, Lcom/transsion/player/longvideo/ui/LongVodPlayerView$pkStyle$2;->INSTANCE:Lcom/transsion/player/longvideo/ui/LongVodPlayerView$pkStyle$2;

    invoke-static {p2}, Lkotlin/LazyKt;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/Lazy;

    move-result-object p2

    iput-object p2, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->W0:Lkotlin/Lazy;

    invoke-static {}, Lkotlinx/coroutines/w0;->b()Lkotlinx/coroutines/CoroutineDispatcher;

    move-result-object p2

    invoke-static {p2}, Lkotlinx/coroutines/l0;->a(Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/k0;

    move-result-object p2

    iput-object p2, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->X0:Lkotlinx/coroutines/k0;

    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object p2

    invoke-static {p2}, Landroid/view/LayoutInflater;->from(Landroid/content/Context;)Landroid/view/LayoutInflater;

    move-result-object p2

    invoke-static {p2, p0}, Loo/e;->b(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)Loo/e;

    move-result-object p2

    const-string p3, "inflate(LayoutInflater.from(context), this)"

    invoke-static {p2, p3}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    iput-object p2, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object p2, p2, Loo/e;->q:Lcom/transsion/player/ui/longvideo/ORLongVodPlayerView;

    const-string p3, "viewBinding.orLongVodView"

    invoke-static {p2, p3}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    iput-object p2, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e:Lcom/transsion/player/ui/longvideo/a;

    new-instance p2, Lcom/transsion/player/longvideo/helper/h;

    invoke-direct {p2}, Lcom/transsion/player/longvideo/helper/h;-><init>()V

    iput-object p2, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->H0:Lcom/transsion/player/longvideo/helper/h;

    invoke-virtual {p0, p1}, Landroid/view/View;->setClickable(Z)V

    invoke-virtual {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->E()V

    new-instance p1, Lcom/transsion/player/longvideo/ui/LongVodPlayerView$screenHelper$2;

    invoke-direct {p1, p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView$screenHelper$2;-><init>(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)V

    invoke-static {p1}, Lkotlin/LazyKt;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/Lazy;

    move-result-object p1

    iput-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a1:Lkotlin/Lazy;

    new-instance p1, Lcom/transsion/player/longvideo/ui/LongVodPlayerView$g;

    invoke-direct {p1, p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView$g;-><init>(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)V

    iput-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->b1:Lcom/transsion/player/longvideo/ui/LongVodPlayerView$g;

    new-instance p1, Lcom/transsion/player/longvideo/ui/r;

    invoke-direct {p1, p0}, Lcom/transsion/player/longvideo/ui/r;-><init>(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)V

    iput-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->d1:Ljava/lang/Runnable;

    new-instance p1, Lcom/transsion/player/longvideo/ui/LongVodPlayerView$f;

    invoke-direct {p1, p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView$f;-><init>(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)V

    iput-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e1:Lcom/transsion/player/longvideo/ui/LongVodPlayerView$f;

    new-instance p1, Lcom/transsion/player/longvideo/ui/s;

    invoke-direct {p1, p0}, Lcom/transsion/player/longvideo/ui/s;-><init>(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)V

    iput-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->f1:Ljava/lang/Runnable;

    new-instance p1, Lcom/transsion/player/longvideo/ui/LongVodPlayerView$orientationEventListener$2;

    invoke-direct {p1, p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView$orientationEventListener$2;-><init>(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)V

    invoke-static {p1}, Lkotlin/LazyKt;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/Lazy;

    move-result-object p1

    iput-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->h1:Lkotlin/Lazy;

    return-void
.end method

.method public static synthetic B(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;ZILjava/lang/Object;)V
    .locals 0

    const/4 p3, 0x1

    and-int/2addr p2, p3

    if-eqz p2, :cond_0

    const/4 p1, 0x1

    :cond_0
    invoke-virtual {p0, p1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->A(Z)V

    return-void
.end method

.method private final C(ZZ)V
    .locals 0

    if-eqz p1, :cond_0

    invoke-direct {p0, p2}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->c1(Z)V

    goto :goto_0

    :cond_0
    invoke-virtual {p0, p2}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->hideBottomController(Z)V

    :goto_0
    return-void
.end method

.method private final C0(Z)V
    .locals 5

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->m:Loo/f;

    iget-object v0, v0, Loo/f;->c:Lpl/d;

    iget-object v0, v0, Lpl/d;->f:Landroid/widget/ProgressBar;

    const-string v1, "viewBinding.layoutLand.c\u2026ControlLayout.progressBar"

    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    const/16 v1, 0x8

    const/4 v2, 0x0

    if-eqz p1, :cond_0

    const/4 v3, 0x0

    goto :goto_0

    :cond_0
    const/16 v3, 0x8

    :goto_0
    invoke-virtual {v0, v3}, Landroid/view/View;->setVisibility(I)V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->m:Loo/f;

    iget-object v0, v0, Loo/f;->c:Lpl/d;

    iget-object v0, v0, Lpl/d;->d:Landroidx/appcompat/widget/AppCompatImageView;

    const-string v3, "viewBinding.layoutLand.c\u2026ntrolLayout.ivCenterPause"

    invoke-static {v0, v3}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    xor-int/lit8 v4, p1, 0x1

    if-eqz v4, :cond_1

    const/4 v4, 0x0

    goto :goto_1

    :cond_1
    const/16 v4, 0x8

    :goto_1
    invoke-virtual {v0, v4}, Landroid/view/View;->setVisibility(I)V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->n:Loo/g;

    iget-object v0, v0, Loo/g;->f:Loo/b;

    invoke-virtual {v0}, Loo/b;->b()Landroid/widget/LinearLayout;

    move-result-object v0

    const-string v4, "viewBinding.layoutMiddle.layoutLoading.root"

    invoke-static {v0, v4}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    if-eqz p1, :cond_2

    const/4 v1, 0x0

    :cond_2
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    if-eqz p1, :cond_3

    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object p1, p1, Loo/e;->m:Loo/f;

    iget-object p1, p1, Loo/f;->c:Lpl/d;

    iget-object p1, p1, Lpl/d;->d:Landroidx/appcompat/widget/AppCompatImageView;

    invoke-static {p1, v3}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-static {p1}, Lvi/c;->h(Landroid/view/View;)V

    :cond_3
    return-void
.end method

.method private final F()V
    .locals 6

    iget v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->L0:F

    const/4 v1, 0x0

    cmpl-float v0, v0, v1

    if-lez v0, :cond_1

    const/4 v0, 0x0

    invoke-virtual {p0, v0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->k1(Z)V

    iget v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->L0:F

    invoke-virtual {p0, v0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->setSpeed(F)V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->i:Ljava/lang/String;

    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    move-result-wide v2

    iget-wide v4, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->c1:J

    sub-long/2addr v2, v4

    iget-object v4, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->G:Lno/a;

    if-eqz v4, :cond_0

    invoke-virtual {v4}, Lno/a;->n()Ljava/lang/String;

    move-result-object v4

    goto :goto_0

    :cond_0
    const/4 v4, 0x0

    :goto_0
    iget v5, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->L0:F

    invoke-static {v5}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object v5

    invoke-static {v0, v2, v3, v4, v5}, Lcom/transsion/baselib/utils/j;->b(Ljava/lang/String;JLjava/lang/String;Ljava/lang/Float;)V

    iput v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->L0:F

    :cond_1
    return-void
.end method

.method private final G(ZZ)V
    .locals 2

    if-eqz p2, :cond_0

    invoke-direct {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->getLocalVideoLandForwardViewControl()Lcom/transsion/player/longvideo/helper/LongVodLandForwardViewControl;

    move-result-object p2

    if-eqz p2, :cond_0

    invoke-virtual {p2, p1}, Lcom/transsion/player/longvideo/helper/LongVodLandForwardViewControl;->b(Z)V

    :cond_0
    const/16 p2, 0x2710

    if-eqz p1, :cond_1

    iget-wide v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->p:J

    int-to-long p1, p2

    add-long/2addr v0, p1

    iget-wide p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->n:J

    invoke-static {v0, v1, p1, p2}, Lkotlin/ranges/RangesKt;->h(JJ)J

    move-result-wide p1

    goto :goto_0

    :cond_1
    iget-wide v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->p:J

    int-to-long p1, p2

    sub-long/2addr v0, p1

    const-wide/16 p1, 0x0

    invoke-static {v0, v1, p1, p2}, Lkotlin/ranges/RangesKt;->e(JJ)J

    move-result-wide p1

    :goto_0
    iput-wide p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->p:J

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e:Lcom/transsion/player/ui/longvideo/a;

    invoke-interface {v0, p1, p2}, Lcom/transsion/player/orplayer/f;->seekTo(J)V

    return-void
.end method

.method public static synthetic I(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;ZZILjava/lang/Object;)V
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    const/4 p2, 0x1

    :cond_0
    invoke-direct {p0, p1, p2}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->G(ZZ)V

    return-void
.end method

.method public static final J0(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)V
    .locals 6

    const-string v0, "this$0"

    invoke-static {p0, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->U0:Lcom/transsion/player/MediaSource;

    if-eqz v0, :cond_1

    iget-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e:Lcom/transsion/player/ui/longvideo/a;

    invoke-interface {v1}, Lcom/transsion/player/orplayer/f;->reset()V

    iget-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e:Lcom/transsion/player/ui/longvideo/a;

    invoke-interface {v1}, Lcom/transsion/player/orplayer/f;->stop()V

    iget-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->S:Lcom/transsion/postdetail/layer/local/n;

    const/4 v2, 0x1

    if-nez v1, :cond_0

    goto :goto_0

    :cond_0
    invoke-virtual {v1, v2}, Lcom/transsion/postdetail/layer/local/n;->J(Z)V

    :goto_0
    sget-object v1, Lxi/b;->a:Lxi/b$a;

    invoke-virtual {v0}, Lcom/transsion/player/MediaSource;->i()Ljava/lang/String;

    move-result-object v3

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    const-string v5, "playerTimeout,change 2 software decoder, path = "

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    const-string v4, "LongVodPlayerView"

    invoke-virtual {v1, v4, v3, v2}, Lxi/b$a;->c(Ljava/lang/String;Ljava/lang/String;Z)V

    iget-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e:Lcom/transsion/player/ui/longvideo/a;

    const/4 v2, 0x0

    invoke-interface {v1, v2}, Lcom/transsion/player/orplayer/f;->enableHardwareDecoder(Z)V

    iget-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e:Lcom/transsion/player/ui/longvideo/a;

    invoke-interface {v1, v0}, Lcom/transsion/player/orplayer/f;->setDataSource(Lcom/transsion/player/MediaSource;)V

    iget-object p0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e:Lcom/transsion/player/ui/longvideo/a;

    invoke-interface {p0}, Lcom/transsion/player/orplayer/f;->prepare()V

    :cond_1
    return-void
.end method

.method private final M0()V
    .locals 2

    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->w:Z

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->G:Lno/a;

    if-eqz v0, :cond_0

    invoke-virtual {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->O0()V

    iget-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e:Lcom/transsion/player/ui/longvideo/a;

    invoke-interface {v1}, Lcom/transsion/player/orplayer/f;->stop()V

    invoke-virtual {p0, v0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->setDataSource(Lno/a;)V

    :cond_0
    return-void
.end method

.method private final N0()V
    .locals 3

    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    const-string v1, "null cannot be cast to non-null type androidx.fragment.app.FragmentActivity"

    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/String;)V

    check-cast v0, Landroidx/fragment/app/FragmentActivity;

    invoke-virtual {v0}, Landroidx/fragment/app/FragmentActivity;->getSupportFragmentManager()Landroidx/fragment/app/FragmentManager;

    move-result-object v0

    invoke-virtual {v0}, Landroidx/fragment/app/FragmentManager;->getFragments()Ljava/util/List;

    move-result-object v0

    const-string v1, "context as FragmentActiv\u2026FragmentManager.fragments"

    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_0
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroidx/fragment/app/Fragment;

    instance-of v2, v1, Lcom/transsion/baseui/dialog/BaseDialog;

    if-eqz v2, :cond_0

    :try_start_0
    sget-object v2, Lkotlin/Result;->Companion:Lkotlin/Result$Companion;

    check-cast v1, Lcom/transsion/baseui/dialog/BaseDialog;

    invoke-virtual {v1}, Landroidx/fragment/app/DialogFragment;->dismissAllowingStateLoss()V

    sget-object v1, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-static {v1}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_0

    :catchall_0
    move-exception v1

    sget-object v2, Lkotlin/Result;->Companion:Lkotlin/Result$Companion;

    invoke-static {v1}, Lkotlin/ResultKt;->a(Ljava/lang/Throwable;)Ljava/lang/Object;

    move-result-object v1

    invoke-static {v1}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    :cond_1
    return-void
.end method

.method public static final O(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/View;)V
    .locals 2

    const-string p1, "this$0"

    invoke-static {p0, p1}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    const/4 p1, 0x0

    const/4 v0, 0x3

    const/4 v1, 0x0

    invoke-static {p0, v1, p1, v0, v1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->w1(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/MotionEvent;ZILjava/lang/Object;)V

    return-void
.end method

.method private final Q(Landroid/view/MotionEvent;Z)V
    .locals 3

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->j:Lcom/transsion/player/longvideo/ui/LongVodUiType;

    sget-object v1, Lcom/transsion/player/longvideo/ui/LongVodUiType;->LAND:Lcom/transsion/player/longvideo/ui/LongVodUiType;

    if-ne v0, v1, :cond_2

    if-eqz p2, :cond_2

    if-eqz p1, :cond_2

    invoke-static {}, Lcom/blankj/utilcode/util/d0;->e()I

    move-result p2

    div-int/lit8 p2, p2, 0x3

    invoke-virtual {p1}, Landroid/view/MotionEvent;->getRawX()F

    move-result p1

    float-to-int p1, p1

    const/4 v0, 0x0

    const/4 v1, 0x2

    const/4 v2, 0x0

    if-ltz p1, :cond_0

    if-gt p1, p2, :cond_0

    invoke-static {p0, v2, v2, v1, v0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->I(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;ZZILjava/lang/Object;)V

    goto :goto_0

    :cond_0
    mul-int/lit8 p2, p2, 0x2

    if-le p1, p2, :cond_1

    const/4 p1, 0x1

    invoke-static {p0, p1, v2, v1, v0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->I(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;ZZILjava/lang/Object;)V

    goto :goto_0

    :cond_1
    invoke-virtual {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->P()V

    goto :goto_0

    :cond_2
    invoke-virtual {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->P()V

    :goto_0
    return-void
.end method

.method private final Q0()V
    .locals 1

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->O:Lcom/transsion/postdetail/layer/local/k0;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/transsion/postdetail/layer/local/k0;->d()V

    :cond_0
    return-void
.end method

.method private final S()V
    .locals 3

    iget-boolean v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->r:Z

    const/16 v1, 0x8

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->o:Loo/d;

    invoke-virtual {v0}, Loo/d;->b()Landroidx/constraintlayout/widget/ConstraintLayout;

    move-result-object v0

    const-string v2, "viewBinding.layoutTopToolBar.root"

    invoke-static {v0, v2}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    :cond_0
    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->U:Lmo/a$b;

    if-eqz v0, :cond_1

    const/4 v2, 0x0

    invoke-interface {v0, v2}, Lmo/a$b;->f(Z)V

    :cond_1
    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->m:Loo/f;

    iget-object v0, v0, Loo/f;->l:Landroidx/appcompat/widget/AppCompatTextView;

    const-string v2, "viewBinding.layoutLand.ivLock"

    invoke-static {v0, v2}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->m:Loo/f;

    iget-object v0, v0, Loo/f;->d:Landroidx/constraintlayout/widget/ConstraintLayout;

    const-string v2, "viewBinding.layoutLand.clBottomControl"

    invoke-static {v0, v2}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->m:Loo/f;

    iget-object v0, v0, Loo/f;->c:Lpl/d;

    invoke-virtual {v0}, Lpl/d;->b()Landroidx/constraintlayout/widget/ConstraintLayout;

    move-result-object v0

    const-string v2, "viewBinding.layoutLand.centerControlLayout.root"

    invoke-static {v0, v2}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->m:Loo/f;

    iget-object v0, v0, Loo/f;->h:Landroidx/constraintlayout/widget/Group;

    const-string v2, "viewBinding.layoutLand.groupControlPk"

    invoke-static {v0, v2}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->m:Loo/f;

    iget-object v0, v0, Loo/f;->y:Landroidx/appcompat/widget/AppCompatTextView;

    const-string v2, "viewBinding.layoutLand.tvPlayNext"

    invoke-static {v0, v2}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->m:Loo/f;

    iget-object v0, v0, Loo/f;->p:Landroid/view/View;

    const-string v2, "viewBinding.layoutLand.landGradientTop"

    invoke-static {v0, v2}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->m:Loo/f;

    iget-object v0, v0, Loo/f;->o:Landroid/view/View;

    const-string v2, "viewBinding.layoutLand.landGradientBottom"

    invoke-static {v0, v2}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->n:Loo/g;

    iget-object v0, v0, Loo/g;->j:Landroid/view/View;

    const-string v2, "viewBinding.layoutMiddle.middleGradientTop"

    invoke-static {v0, v2}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->n:Loo/g;

    iget-object v0, v0, Loo/g;->i:Landroid/view/View;

    const-string v2, "viewBinding.layoutMiddle.middleGradientBottom"

    invoke-static {v0, v2}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->n:Loo/g;

    iget-object v0, v0, Loo/g;->h:Landroid/widget/LinearLayout;

    const-string v2, "viewBinding.layoutMiddle.llMiddleBottomController"

    invoke-static {v0, v2}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    return-void
.end method

.method private final T()V
    .locals 5

    invoke-direct {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->getPkStyle()Z

    move-result v0

    const/4 v1, 0x0

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->m:Loo/f;

    iget-object v0, v0, Loo/f;->l:Landroidx/appcompat/widget/AppCompatTextView;

    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->m:Loo/f;

    iget-object v0, v0, Loo/f;->x:Landroidx/appcompat/widget/AppCompatTextView;

    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->m:Loo/f;

    iget-object v0, v0, Loo/f;->z:Landroid/widget/TextView;

    const-string v2, "viewBinding.layoutLand.tvPlayScale"

    invoke-static {v0, v2}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-static {v0}, Lvi/c;->g(Landroid/view/View;)V

    :cond_0
    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->m:Loo/f;

    iget-object v0, v0, Loo/f;->A:Lcom/transsion/postdetail/ui/view/ImmSpeedView;

    const-string v2, "viewBinding.layoutLand.tvPlaySpeed"

    invoke-static {v0, v2}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    sget-object v2, Lcom/transsion/baselib/helper/LocalVideoPlayerConfigMmkv;->a:Lcom/transsion/baselib/helper/LocalVideoPlayerConfigMmkv;

    invoke-virtual {v2}, Lcom/transsion/baselib/helper/LocalVideoPlayerConfigMmkv;->c()F

    move-result v2

    const/4 v3, 0x0

    const/4 v4, 0x2

    invoke-static {v0, v2, v3, v4, v1}, Lcom/transsion/postdetail/ui/view/ImmSpeedView;->updateSpeed$default(Lcom/transsion/postdetail/ui/view/ImmSpeedView;FZILjava/lang/Object;)V

    return-void
.end method

.method private final U0()V
    .locals 2

    const/4 v0, 0x0

    iput-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->U0:Lcom/transsion/player/MediaSource;

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->S:Lcom/transsion/postdetail/layer/local/n;

    if-nez v0, :cond_0

    goto :goto_0

    :cond_0
    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Lcom/transsion/postdetail/layer/local/n;->J(Z)V

    :goto_0
    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->T0:Landroid/os/Handler;

    iget-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->f1:Ljava/lang/Runnable;

    invoke-virtual {v0, v1}, Landroid/os/Handler;->removeCallbacks(Ljava/lang/Runnable;)V

    return-void
.end method

.method private final V()V
    .locals 2

    invoke-virtual {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->A0()Z

    move-result v0

    if-nez v0, :cond_0

    return-void

    :cond_0
    new-instance v0, Lcom/transsion/player/longvideo/helper/a;

    iget-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    invoke-direct {v0, v1}, Lcom/transsion/player/longvideo/helper/a;-><init>(Loo/e;)V

    invoke-virtual {v0}, Lcom/transsion/player/longvideo/helper/a;->a()V

    iput-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->J0:Lcom/transsion/player/longvideo/helper/a;

    return-void
.end method

.method private final V0()V
    .locals 4

    iget-boolean v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->C:Z

    if-nez v0, :cond_0

    return-void

    :cond_0
    sget-object v0, Lxi/b;->a:Lxi/b$a;

    const-string v1, "LongVodPlayerView"

    const-string v2, "player resetPlayer"

    const/4 v3, 0x1

    invoke-virtual {v0, v1, v2, v3}, Lxi/b$a;->c(Ljava/lang/String;Ljava/lang/String;Z)V

    const/4 v0, 0x0

    iput-boolean v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->w:Z

    iput-boolean v3, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->Q0:Z

    iget-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->W:Lcom/transsion/player/longvideo/helper/f;

    if-eqz v1, :cond_1

    invoke-virtual {v1}, Lcom/transsion/player/longvideo/helper/f;->c()V

    :cond_1
    iget-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e:Lcom/transsion/player/ui/longvideo/a;

    invoke-interface {v1}, Lcom/transsion/player/orplayer/f;->reset()V

    invoke-direct {p0, v0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->setPauseViewStatus(Z)V

    return-void
.end method

.method private final W()V
    .locals 3

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->n:Loo/g;

    iget-object v0, v0, Loo/g;->e:Landroid/widget/RelativeLayout;

    new-instance v1, Lcom/transsion/player/longvideo/ui/t;

    invoke-direct {v1, p0}, Lcom/transsion/player/longvideo/ui/t;-><init>(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)V

    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->o:Loo/d;

    iget-object v0, v0, Loo/d;->b:Landroidx/appcompat/widget/AppCompatImageView;

    new-instance v1, Lcom/transsion/player/longvideo/ui/d;

    invoke-direct {v1, p0}, Lcom/transsion/player/longvideo/ui/d;-><init>(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)V

    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->n:Loo/g;

    iget-object v0, v0, Loo/g;->d:Landroidx/appcompat/widget/AppCompatImageView;

    new-instance v1, Lcom/transsion/player/longvideo/ui/e;

    invoke-direct {v1, p0}, Lcom/transsion/player/longvideo/ui/e;-><init>(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)V

    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->m:Loo/f;

    iget-object v0, v0, Loo/f;->k:Landroidx/appcompat/widget/AppCompatImageView;

    new-instance v1, Lcom/transsion/player/longvideo/ui/f;

    invoke-direct {v1, p0}, Lcom/transsion/player/longvideo/ui/f;-><init>(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)V

    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->o:Loo/d;

    iget-object v0, v0, Loo/d;->c:Landroidx/appcompat/widget/AppCompatTextView;

    new-instance v1, Lcom/transsion/player/longvideo/ui/g;

    invoke-direct {v1, p0}, Lcom/transsion/player/longvideo/ui/g;-><init>(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)V

    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->o:Loo/d;

    iget-object v0, v0, Loo/d;->d:Landroidx/appcompat/widget/AppCompatTextView;

    new-instance v1, Lcom/transsion/player/longvideo/ui/h;

    invoke-direct {v1, p0}, Lcom/transsion/player/longvideo/ui/h;-><init>(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)V

    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->m:Loo/f;

    iget-object v0, v0, Loo/f;->u:Landroid/widget/TextView;

    new-instance v1, Lcom/transsion/player/longvideo/ui/i;

    invoke-direct {v1, p0}, Lcom/transsion/player/longvideo/ui/i;-><init>(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)V

    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->m:Loo/f;

    iget-object v0, v0, Loo/f;->y:Landroidx/appcompat/widget/AppCompatTextView;

    new-instance v1, Lcom/transsion/player/longvideo/ui/j;

    invoke-direct {v1, p0}, Lcom/transsion/player/longvideo/ui/j;-><init>(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)V

    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->m:Loo/f;

    iget-object v0, v0, Loo/f;->c:Lpl/d;

    iget-object v0, v0, Lpl/d;->d:Landroidx/appcompat/widget/AppCompatImageView;

    new-instance v1, Lcom/transsion/player/longvideo/ui/k;

    invoke-direct {v1, p0}, Lcom/transsion/player/longvideo/ui/k;-><init>(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)V

    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->m:Loo/f;

    iget-object v0, v0, Loo/f;->c:Lpl/d;

    iget-object v0, v0, Lpl/d;->e:Landroidx/appcompat/widget/AppCompatImageView;

    new-instance v1, Lcom/transsion/player/longvideo/ui/m;

    invoke-direct {v1, p0}, Lcom/transsion/player/longvideo/ui/m;-><init>(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)V

    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->m:Loo/f;

    iget-object v0, v0, Loo/f;->c:Lpl/d;

    iget-object v0, v0, Lpl/d;->c:Landroidx/appcompat/widget/AppCompatImageView;

    new-instance v1, Lcom/transsion/player/longvideo/ui/u;

    invoke-direct {v1, p0}, Lcom/transsion/player/longvideo/ui/u;-><init>(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)V

    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->m:Loo/f;

    iget-object v0, v0, Loo/f;->n:Lcom/transsion/postdetail/ui/view/ImmScaleView;

    new-instance v1, Lcom/transsion/player/longvideo/ui/v;

    invoke-direct {v1, p0}, Lcom/transsion/player/longvideo/ui/v;-><init>(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)V

    invoke-virtual {v0, v1}, Lcom/transsion/postdetail/ui/view/ImmScaleView;->setOnModelChangeListener(Lcom/transsion/postdetail/ui/view/ImmScaleView$a;)V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->m:Loo/f;

    iget-object v0, v0, Loo/f;->r:Landroid/widget/LinearLayout;

    new-instance v1, Lcom/transsion/player/longvideo/ui/w;

    invoke-direct {v1, p0}, Lcom/transsion/player/longvideo/ui/w;-><init>(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)V

    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->m:Loo/f;

    iget-object v0, v0, Loo/f;->l:Landroidx/appcompat/widget/AppCompatTextView;

    new-instance v1, Lcom/transsion/player/longvideo/ui/b;

    invoke-direct {v1, p0}, Lcom/transsion/player/longvideo/ui/b;-><init>(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)V

    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    invoke-static {}, Lvi/c;->f()Z

    move-result v0

    iget-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e:Lcom/transsion/player/ui/longvideo/a;

    new-instance v2, Lcom/transsion/player/longvideo/ui/LongVodPlayerView$c;

    invoke-direct {v2, p0, v0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView$c;-><init>(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Z)V

    invoke-interface {v1, v2}, Lcom/transsion/player/ui/longvideo/a;->setGestureListener(Lcom/transsion/player/ui/longvideo/a$b;)V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->n:Loo/g;

    iget-object v0, v0, Loo/g;->c:Landroid/widget/RelativeLayout;

    const-string v1, "viewBinding.layoutMiddle.ivFloat"

    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-direct {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->z0()Z

    move-result v1

    if-eqz v1, :cond_0

    const/4 v1, 0x0

    goto :goto_0

    :cond_0
    const/16 v1, 0x8

    :goto_0
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->n:Loo/g;

    iget-object v0, v0, Loo/g;->c:Landroid/widget/RelativeLayout;

    new-instance v1, Lcom/transsion/player/longvideo/ui/c;

    invoke-direct {v1, p0}, Lcom/transsion/player/longvideo/ui/c;-><init>(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)V

    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    return-void
.end method

.method public static final X(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/View;)V
    .locals 3

    const-string p1, "this$0"

    invoke-static {p0, p1}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    sget-object p1, Llo/c;->a:Llo/c;

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->i:Ljava/lang/String;

    iget-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->q:Ljava/lang/String;

    const/4 v2, 0x0

    invoke-virtual {p1, v0, v1, v2}, Llo/c;->a(Ljava/lang/String;Ljava/lang/String;Z)V

    const/4 p1, 0x0

    const/4 v0, 0x3

    invoke-static {p0, p1, v2, v0, p1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->w1(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/MotionEvent;ZILjava/lang/Object;)V

    return-void
.end method

.method public static final X0(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)V
    .locals 1

    const-string v0, "this$0"

    invoke-static {p0, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-direct {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->S()V

    return-void
.end method

.method public static final Y(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/View;)V
    .locals 0

    const-string p1, "this$0"

    invoke-static {p0, p1}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->I0()V

    return-void
.end method

.method public static final Z(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/View;)V
    .locals 3

    const-string p1, "this$0"

    invoke-static {p0, p1}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object p1

    instance-of v0, p1, Landroidx/appcompat/app/AppCompatActivity;

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    check-cast p1, Landroidx/appcompat/app/AppCompatActivity;

    goto :goto_0

    :cond_0
    move-object p1, v1

    :goto_0
    if-eqz p1, :cond_1

    new-instance v0, Lcom/transsion/player/longvideo/ui/dialog/PlayerSettingDialog;

    invoke-direct {v0}, Lcom/transsion/player/longvideo/ui/dialog/PlayerSettingDialog;-><init>()V

    invoke-virtual {p1}, Landroidx/fragment/app/FragmentActivity;->getSupportFragmentManager()Landroidx/fragment/app/FragmentManager;

    move-result-object p1

    const-string v2, "it.supportFragmentManager"

    invoke-static {p1, v2}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    const-string v2, "PlayerSettingDialog"

    invoke-virtual {v0, p1, v2}, Lcom/transsion/baseui/dialog/BaseDialog;->show(Landroidx/fragment/app/FragmentManager;Ljava/lang/String;)V

    :cond_1
    new-instance p1, Ljava/util/LinkedHashMap;

    invoke-direct {p1}, Ljava/util/LinkedHashMap;-><init>()V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->G:Lno/a;

    if-eqz v0, :cond_2

    invoke-virtual {v0}, Lno/a;->n()Ljava/lang/String;

    move-result-object v0

    goto :goto_1

    :cond_2
    move-object v0, v1

    :goto_1
    const-string v2, "subject_id"

    invoke-interface {p1, v2, v0}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->G:Lno/a;

    if-eqz v0, :cond_3

    invoke-virtual {v0}, Lno/a;->p()Ljava/lang/String;

    move-result-object v0

    goto :goto_2

    :cond_3
    move-object v0, v1

    :goto_2
    const-string v2, "subject_ops"

    invoke-interface {p1, v2, v0}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->G:Lno/a;

    if-eqz v0, :cond_4

    invoke-virtual {v0}, Lno/a;->o()Ljava/lang/String;

    move-result-object v0

    goto :goto_3

    :cond_4
    move-object v0, v1

    :goto_3
    const-string v2, "subject_name"

    invoke-interface {p1, v2, v0}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->G:Lno/a;

    if-eqz v0, :cond_5

    invoke-virtual {v0}, Lno/a;->s()Ljava/lang/String;

    move-result-object v0

    goto :goto_4

    :cond_5
    move-object v0, v1

    :goto_4
    const-string v2, "title_name"

    invoke-interface {p1, v2, v0}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    iget-object p0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->G:Lno/a;

    if-eqz p0, :cond_6

    invoke-virtual {p0}, Lno/a;->q()Ljava/lang/Integer;

    move-result-object p0

    if-eqz p0, :cond_6

    invoke-virtual {p0}, Ljava/lang/Integer;->toString()Ljava/lang/String;

    move-result-object v1

    :cond_6
    const-string p0, "subject_type"

    invoke-interface {p1, p0, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    sget-object p0, Lcom/transsion/baselib/report/l;->a:Lcom/transsion/baselib/report/l;

    const-string v0, "LongVodPlayerView"

    const-string v1, "click"

    invoke-virtual {p0, v0, v1, p1}, Lcom/transsion/baselib/report/l;->l(Ljava/lang/String;Ljava/lang/String;Ljava/util/Map;)V

    return-void
.end method

.method public static synthetic a(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/View;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->l0(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/View;)V

    return-void
.end method

.method public static final a0(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/View;)V
    .locals 0

    const-string p1, "this$0"

    invoke-static {p0, p1}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->W0()V

    return-void
.end method

.method private final a1()V
    .locals 2

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->V:Lcom/transsion/player/longvideo/helper/LongVodSubtitleHelper;

    if-eqz v0, :cond_1

    iget-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->S:Lcom/transsion/postdetail/layer/local/n;

    if-eqz v1, :cond_0

    invoke-virtual {v1}, Lcom/transsion/postdetail/layer/local/n;->e()Ljava/util/Map;

    move-result-object v1

    if-eqz v1, :cond_0

    invoke-interface {v1}, Ljava/util/Map;->clear()V

    :cond_0
    iget-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->S:Lcom/transsion/postdetail/layer/local/n;

    if-eqz v1, :cond_1

    invoke-virtual {v1}, Lcom/transsion/postdetail/layer/local/n;->e()Ljava/util/Map;

    move-result-object v1

    if-eqz v1, :cond_1

    invoke-virtual {v0}, Lcom/transsion/player/longvideo/helper/LongVodSubtitleHelper;->r()Ljava/util/Map;

    move-result-object v0

    invoke-interface {v1, v0}, Ljava/util/Map;->putAll(Ljava/util/Map;)V

    :cond_1
    return-void
.end method

.method public static final synthetic access$autoScreenRotation(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Z)V
    .locals 0

    invoke-direct {p0, p1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->z(Z)V

    return-void
.end method

.method public static final synthetic access$bottomControllerVisibility(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;ZZ)V
    .locals 0

    invoke-direct {p0, p1, p2}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->C(ZZ)V

    return-void
.end method

.method public static final synthetic access$checkLandBottomLayout(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;I)V
    .locals 0

    invoke-virtual {p0, p1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->D(I)V

    return-void
.end method

.method public static final synthetic access$endLongPress(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)V
    .locals 0

    invoke-direct {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->F()V

    return-void
.end method

.method public static final synthetic access$getAdHelper$p(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)Lcom/transsion/player/longvideo/helper/a;
    .locals 0

    iget-object p0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->J0:Lcom/transsion/player/longvideo/helper/a;

    return-object p0
.end method

.method public static final synthetic access$getAudioSelectCallback$p(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)Lkotlin/jvm/functions/Function1;
    .locals 0

    iget-object p0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->Y0:Lkotlin/jvm/functions/Function1;

    return-object p0
.end method

.method public static final synthetic access$getCurBean$p(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)Lno/a;
    .locals 0

    iget-object p0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->G:Lno/a;

    return-object p0
.end method

.method public static final synthetic access$getCurPlayStream$p(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)Lno/c;
    .locals 0

    iget-object p0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->H:Lno/c;

    return-object p0
.end method

.method public static final synthetic access$getCurUiType$p(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)Lcom/transsion/player/longvideo/ui/LongVodUiType;
    .locals 0

    iget-object p0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->j:Lcom/transsion/player/longvideo/ui/LongVodUiType;

    return-object p0
.end method

.method public static final synthetic access$getHandler$p(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)Landroid/os/Handler;
    .locals 0

    iget-object p0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->M:Landroid/os/Handler;

    return-object p0
.end method

.method public static final synthetic access$getMobileDataLayout$p(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)Landroid/view/View;
    .locals 0

    iget-object p0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->K:Landroid/view/View;

    return-object p0
.end method

.method public static final synthetic access$getPageType$p(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)Lcom/transsion/player/longvideo/constants/LongVodPageType;
    .locals 0

    iget-object p0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->g:Lcom/transsion/player/longvideo/constants/LongVodPageType;

    return-object p0
.end method

.method public static final synthetic access$getPlayProgress$p(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)J
    .locals 2

    iget-wide v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->o:J

    return-wide v0
.end method

.method public static final synthetic access$getPlayerCallback$p(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)Lmo/a$b;
    .locals 0

    iget-object p0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->U:Lmo/a$b;

    return-object p0
.end method

.method public static final synthetic access$getPlayerControl$p(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)Lcom/transsion/player/ui/longvideo/a;
    .locals 0

    iget-object p0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e:Lcom/transsion/player/ui/longvideo/a;

    return-object p0
.end method

.method public static final synthetic access$getReplayLayout$p(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)Landroid/view/View;
    .locals 0

    iget-object p0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->J:Landroid/view/View;

    return-object p0
.end method

.method public static final synthetic access$getResolutionList$p(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)Ljava/util/List;
    .locals 0

    iget-object p0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->R0:Ljava/util/List;

    return-object p0
.end method

.method public static final synthetic access$getRunnable$p(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)Ljava/lang/Runnable;
    .locals 0

    iget-object p0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->d1:Ljava/lang/Runnable;

    return-object p0
.end method

.method public static final synthetic access$getScreenHeight$p(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)I
    .locals 0

    iget p0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->d:I

    return p0
.end method

.method public static final synthetic access$getScreenWidth$p(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)I
    .locals 0

    iget p0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->c:I

    return p0
.end method

.method public static final synthetic access$getSpaceHelper$p(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)Lcom/transsion/player/longvideo/helper/f;
    .locals 0

    iget-object p0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->W:Lcom/transsion/player/longvideo/helper/f;

    return-object p0
.end method

.method public static final synthetic access$getSubSelectId$p(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)Ljava/lang/String;
    .locals 0

    iget-object p0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->q:Ljava/lang/String;

    return-object p0
.end method

.method public static final synthetic access$getSubtitleHelper$p(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)Lcom/transsion/player/longvideo/helper/LongVodSubtitleHelper;
    .locals 0

    iget-object p0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->V:Lcom/transsion/player/longvideo/helper/LongVodSubtitleHelper;

    return-object p0
.end method

.method public static final synthetic access$getTotalDuration$p(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)J
    .locals 2

    iget-wide v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->n:J

    return-wide v0
.end method

.method public static final synthetic access$getVideoDetailPlayDao(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)Lcom/transsion/baselib/db/video/VideoDetailPlayDao;
    .locals 0

    invoke-direct {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->getVideoDetailPlayDao()Lcom/transsion/baselib/db/video/VideoDetailPlayDao;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic access$getVideoDot$p(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)Lcom/transsion/postdetail/layer/local/n;
    .locals 0

    iget-object p0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->S:Lcom/transsion/postdetail/layer/local/n;

    return-object p0
.end method

.method public static final synthetic access$getViewBinding$p(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)Loo/e;
    .locals 0

    iget-object p0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    return-object p0
.end method

.method public static final synthetic access$hideCoverBg(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)V
    .locals 0

    invoke-virtual {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->R()V

    return-void
.end method

.method public static final synthetic access$initStreamResolution(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)V
    .locals 0

    invoke-virtual {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->q0()V

    return-void
.end method

.method public static final synthetic access$isCloseAutoRotation(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)Z
    .locals 0

    invoke-direct {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->v0()Z

    move-result p0

    return p0
.end method

.method public static final synthetic access$isErrorShown(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)Z
    .locals 0

    invoke-direct {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->x0()Z

    move-result p0

    return p0
.end method

.method public static final synthetic access$isFirstSetTracks$p(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)Z
    .locals 0

    iget-boolean p0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->Q0:Z

    return p0
.end method

.method public static final synthetic access$isMobilePaused$p(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)Z
    .locals 0

    iget-boolean p0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->x:Z

    return p0
.end method

.method public static final synthetic access$isMultipleResChangeResolution$p(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)Z
    .locals 0

    iget-boolean p0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->B:Z

    return p0
.end method

.method public static final synthetic access$isMusic(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)Z
    .locals 0

    invoke-virtual {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->y0()Z

    move-result p0

    return p0
.end method

.method public static final synthetic access$isPageResumed$p(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)Z
    .locals 0

    iget-boolean p0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->z:Z

    return p0
.end method

.method public static final synthetic access$isPrepared$p(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)Z
    .locals 0

    iget-boolean p0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->F:Z

    return p0
.end method

.method public static final synthetic access$isPressedPause$p(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)Z
    .locals 0

    iget-boolean p0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->u:Z

    return p0
.end method

.method public static final synthetic access$keepScreenOn(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Z)V
    .locals 0

    invoke-virtual {p0, p1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->B0(Z)V

    return-void
.end method

.method public static final synthetic access$longPressSpeed(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)V
    .locals 0

    invoke-virtual {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->D0()V

    return-void
.end method

.method public static final synthetic access$onPrepare2PlayVideo(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)V
    .locals 0

    invoke-virtual {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->E0()V

    return-void
.end method

.method public static final synthetic access$onResolutionChange(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Lno/b;)V
    .locals 0

    invoke-virtual {p0, p1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->F0(Lno/b;)V

    return-void
.end method

.method public static final synthetic access$onSingleTapClick(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)V
    .locals 0

    invoke-virtual {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->G0()V

    return-void
.end method

.method public static final synthetic access$onSpeedChange(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Lno/b;)V
    .locals 0

    invoke-virtual {p0, p1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->H0(Lno/b;)V

    return-void
.end method

.method public static final synthetic access$removeError(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)V
    .locals 0

    invoke-virtual {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->O0()V

    return-void
.end method

.method public static final synthetic access$resetPlayTimeout(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)V
    .locals 0

    invoke-direct {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->U0()V

    return-void
.end method

.method public static final synthetic access$sendInfoToJS(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Lcom/transsion/baselib/db/video/VideoDetailPlayBean;)V
    .locals 0

    invoke-virtual {p0, p1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->Z0(Lcom/transsion/baselib/db/video/VideoDetailPlayBean;)V

    return-void
.end method

.method public static final synthetic access$setDashVideoTracksGroup$p(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Lqo/d;)V
    .locals 0

    iput-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->O0:Lqo/d;

    return-void
.end method

.method public static final synthetic access$setFirstSetTracks$p(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Z)V
    .locals 0

    iput-boolean p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->Q0:Z

    return-void
.end method

.method public static final synthetic access$setFormUserSeek$p(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Z)V
    .locals 0

    iput-boolean p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->v:Z

    return-void
.end method

.method public static final synthetic access$setMobilePaused$p(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Z)V
    .locals 0

    iput-boolean p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->x:Z

    return-void
.end method

.method public static final synthetic access$setMobilePausedHandRemoved$p(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Z)V
    .locals 0

    iput-boolean p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->y:Z

    return-void
.end method

.method public static final synthetic access$setMultipleResChangeResolution$p(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Z)V
    .locals 0

    iput-boolean p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->B:Z

    return-void
.end method

.method public static final synthetic access$setPauseViewStatus(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Z)V
    .locals 0

    invoke-direct {p0, p1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->setPauseViewStatus(Z)V

    return-void
.end method

.method public static final synthetic access$setPlayProgress$p(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;J)V
    .locals 0

    iput-wide p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->o:J

    return-void
.end method

.method public static final synthetic access$setPlayerDataSourceAdPrepare(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Lno/c;)V
    .locals 0

    invoke-direct {p0, p1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->setPlayerDataSourceAdPrepare(Lno/c;)V

    return-void
.end method

.method public static final synthetic access$setPrepared$p(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Z)V
    .locals 0

    iput-boolean p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->F:Z

    return-void
.end method

.method public static final synthetic access$setPressSeekProgress$p(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;J)V
    .locals 0

    iput-wide p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->p:J

    return-void
.end method

.method public static final synthetic access$setPressedPause$p(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Z)V
    .locals 0

    iput-boolean p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->u:Z

    return-void
.end method

.method public static final synthetic access$setShowToolbarAlways$p(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Z)V
    .locals 0

    iput-boolean p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->r:Z

    return-void
.end method

.method public static final synthetic access$setSubSelectId$p(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->q:Ljava/lang/String;

    return-void
.end method

.method public static final synthetic access$setTotalDuration$p(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;J)V
    .locals 0

    iput-wide p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->n:J

    return-void
.end method

.method public static final synthetic access$setVideoHeight$p(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;I)V
    .locals 0

    iput p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->l:I

    return-void
.end method

.method public static final synthetic access$setVideoWidth$p(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;I)V
    .locals 0

    iput p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->m:I

    return-void
.end method

.method public static final synthetic access$showBottomController(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Z)V
    .locals 0

    invoke-direct {p0, p1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->c1(Z)V

    return-void
.end method

.method public static final synthetic access$showCoverBg(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)V
    .locals 0

    invoke-virtual {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e1()V

    return-void
.end method

.method public static final synthetic access$showError(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Z)V
    .locals 0

    invoke-virtual {p0, p1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->f1(Z)V

    return-void
.end method

.method public static final synthetic access$showToast(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Ljava/lang/String;)V
    .locals 0

    invoke-direct {p0, p1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->n1(Ljava/lang/String;)V

    return-void
.end method

.method public static final synthetic access$showVideoLoading(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Z)V
    .locals 0

    invoke-direct {p0, p1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->o1(Z)V

    return-void
.end method

.method public static final synthetic access$updatePipParams(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Z)V
    .locals 0

    invoke-direct {p0, p1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->q1(Z)V

    return-void
.end method

.method public static final synthetic access$updateSeekbar(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;ZJ)V
    .locals 0

    invoke-direct {p0, p1, p2, p3}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->r1(ZJ)V

    return-void
.end method

.method public static final synthetic access$updateTime(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;J)V
    .locals 0

    invoke-direct {p0, p1, p2}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->t1(J)V

    return-void
.end method

.method public static final synthetic access$videoPauseOrPlayClick(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/MotionEvent;Z)V
    .locals 0

    invoke-virtual {p0, p1, p2}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->v1(Landroid/view/MotionEvent;Z)V

    return-void
.end method

.method public static synthetic b(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/View;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->Y(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/View;)V

    return-void
.end method

.method public static final b0(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/View;)V
    .locals 3

    const-string p1, "this$0"

    invoke-static {p0, p1}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    sget-object p1, Lcom/transsion/baselib/report/l;->a:Lcom/transsion/baselib/report/l;

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->i:Ljava/lang/String;

    if-nez v0, :cond_0

    const-string v0, ""

    :cond_0
    const-string v1, "click"

    invoke-direct {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->getReportNextMap()Ljava/util/Map;

    move-result-object v2

    invoke-virtual {p1, v0, v1, v2}, Lcom/transsion/baselib/report/l;->l(Ljava/lang/String;Ljava/lang/String;Ljava/util/Map;)V

    iget-object p0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->U:Lmo/a$b;

    if-eqz p0, :cond_1

    invoke-interface {p0}, Lmo/a$b;->a()V

    :cond_1
    return-void
.end method

.method public static synthetic c(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/View;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->m1(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/View;)V

    return-void
.end method

.method public static final c0(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/View;)V
    .locals 3

    const-string p1, "this$0"

    invoke-static {p0, p1}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    sget-object p1, Llo/c;->a:Llo/c;

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->i:Ljava/lang/String;

    iget-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->q:Ljava/lang/String;

    const/4 v2, 0x1

    invoke-virtual {p1, v0, v1, v2}, Llo/c;->a(Ljava/lang/String;Ljava/lang/String;Z)V

    const/4 p1, 0x0

    const/4 v0, 0x3

    const/4 v1, 0x0

    invoke-static {p0, v1, p1, v0, v1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->w1(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/MotionEvent;ZILjava/lang/Object;)V

    return-void
.end method

.method private final c1(Z)V
    .locals 5

    iget-boolean v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->r:Z

    if-eqz v0, :cond_0

    return-void

    :cond_0
    invoke-static {}, Lcom/blankj/utilcode/util/c;->j()Z

    move-result v0

    if-eqz v0, :cond_9

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->m:Loo/f;

    iget-object v0, v0, Loo/f;->l:Landroidx/appcompat/widget/AppCompatTextView;

    invoke-virtual {v0}, Landroid/view/View;->isSelected()Z

    move-result v0

    const/16 v1, 0x8

    const/4 v2, 0x1

    const-string v3, "viewBinding.layoutLand.ivLock"

    const/4 v4, 0x0

    if-eqz v0, :cond_3

    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object p1, p1, Loo/e;->m:Loo/f;

    iget-object p1, p1, Loo/f;->l:Landroidx/appcompat/widget/AppCompatTextView;

    invoke-static {p1, v3}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->m:Loo/f;

    iget-object v0, v0, Loo/f;->l:Landroidx/appcompat/widget/AppCompatTextView;

    invoke-static {v0, v3}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {v0}, Landroid/view/View;->getVisibility()I

    move-result v0

    if-nez v0, :cond_1

    const/4 v0, 0x1

    goto :goto_0

    :cond_1
    const/4 v0, 0x0

    :goto_0
    xor-int/2addr v0, v2

    if-eqz v0, :cond_2

    const/4 v1, 0x0

    :cond_2
    invoke-virtual {p1, v1}, Landroid/view/View;->setVisibility(I)V

    return-void

    :cond_3
    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->m:Loo/f;

    iget-object v0, v0, Loo/f;->l:Landroidx/appcompat/widget/AppCompatTextView;

    invoke-static {v0, v3}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {v0, v4}, Landroid/view/View;->setVisibility(I)V

    if-eqz p1, :cond_4

    invoke-virtual {p0, v4}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->hideBottomController(Z)V

    goto :goto_1

    :cond_4
    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->M:Landroid/os/Handler;

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->d1:Ljava/lang/Runnable;

    invoke-virtual {p1, v0}, Landroid/os/Handler;->removeCallbacks(Ljava/lang/Runnable;)V

    :goto_1
    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object p1, p1, Loo/e;->m:Loo/f;

    iget-object p1, p1, Loo/f;->d:Landroidx/constraintlayout/widget/ConstraintLayout;

    const-string v0, "viewBinding.layoutLand.clBottomControl"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {p1, v4}, Landroid/view/View;->setVisibility(I)V

    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object p1, p1, Loo/e;->m:Loo/f;

    iget-object p1, p1, Loo/f;->c:Lpl/d;

    invoke-virtual {p1}, Lpl/d;->b()Landroidx/constraintlayout/widget/ConstraintLayout;

    move-result-object p1

    const-string v0, "viewBinding.layoutLand.centerControlLayout.root"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {p1, v4}, Landroid/view/View;->setVisibility(I)V

    invoke-direct {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->getPkStyle()Z

    move-result p1

    if-eqz p1, :cond_5

    invoke-virtual {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->A0()Z

    move-result p1

    if-eqz p1, :cond_5

    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object p1, p1, Loo/e;->m:Loo/f;

    iget-object p1, p1, Loo/f;->h:Landroidx/constraintlayout/widget/Group;

    const-string v0, "viewBinding.layoutLand.groupControlPk"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {p1, v4}, Landroid/view/View;->setVisibility(I)V

    :cond_5
    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object p1, p1, Loo/e;->o:Loo/d;

    invoke-virtual {p1}, Loo/d;->b()Landroidx/constraintlayout/widget/ConstraintLayout;

    move-result-object p1

    const-string v0, "viewBinding.layoutTopToolBar.root"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {p1, v4}, Landroid/view/View;->setVisibility(I)V

    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->U:Lmo/a$b;

    if-eqz p1, :cond_6

    invoke-interface {p1, v2}, Lmo/a$b;->f(Z)V

    :cond_6
    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object p1, p1, Loo/e;->m:Loo/f;

    iget-object p1, p1, Loo/f;->p:Landroid/view/View;

    const-string v0, "viewBinding.layoutLand.landGradientTop"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {p1, v4}, Landroid/view/View;->setVisibility(I)V

    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object p1, p1, Loo/e;->m:Loo/f;

    iget-object p1, p1, Loo/f;->o:Landroid/view/View;

    const-string v0, "viewBinding.layoutLand.landGradientBottom"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {p1, v4}, Landroid/view/View;->setVisibility(I)V

    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object p1, p1, Loo/e;->m:Loo/f;

    iget-object p1, p1, Loo/f;->y:Landroidx/appcompat/widget/AppCompatTextView;

    const-string v0, "viewBinding.layoutLand.tvPlayNext"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    iget-boolean v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->A:Z

    if-eqz v0, :cond_7

    const/4 v0, 0x0

    goto :goto_2

    :cond_7
    const/16 v0, 0x8

    :goto_2
    invoke-virtual {p1, v0}, Landroid/view/View;->setVisibility(I)V

    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object p1, p1, Loo/e;->n:Loo/g;

    iget-object p1, p1, Loo/g;->j:Landroid/view/View;

    const-string v0, "viewBinding.layoutMiddle.middleGradientTop"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {p1, v4}, Landroid/view/View;->setVisibility(I)V

    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object p1, p1, Loo/e;->n:Loo/g;

    iget-object p1, p1, Loo/g;->h:Landroid/widget/LinearLayout;

    const-string v0, "viewBinding.layoutMiddle.llMiddleBottomController"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {p1, v4}, Landroid/view/View;->setVisibility(I)V

    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object p1, p1, Loo/e;->n:Loo/g;

    iget-object p1, p1, Loo/g;->i:Landroid/view/View;

    const-string v0, "viewBinding.layoutMiddle.middleGradientBottom"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {p1, v4}, Landroid/view/View;->setVisibility(I)V

    invoke-virtual {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->R0()V

    const p1, 0x102000b

    invoke-virtual {p0, p1}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    move-result-object p1

    if-nez p1, :cond_8

    goto :goto_3

    :cond_8
    invoke-virtual {p1, v1}, Landroid/view/View;->setVisibility(I)V

    :cond_9
    :goto_3
    return-void
.end method

.method public static synthetic d(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/View;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->i0(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/View;)V

    return-void
.end method

.method public static final d0(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/View;)V
    .locals 1

    const-string p1, "this$0"

    invoke-static {p0, p1}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    const-string p1, "forward"

    invoke-virtual {p0, p1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->S0(Ljava/lang/String;)V

    const/4 p1, 0x1

    const/4 v0, 0x0

    invoke-direct {p0, p1, v0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->G(ZZ)V

    invoke-virtual {p0, v0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->hideBottomController(Z)V

    return-void
.end method

.method public static synthetic d1(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;ZILjava/lang/Object;)V
    .locals 0

    const/4 p3, 0x1

    and-int/2addr p2, p3

    if-eqz p2, :cond_0

    const/4 p1, 0x1

    :cond_0
    invoke-direct {p0, p1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->c1(Z)V

    return-void
.end method

.method public static final e0(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/View;)V
    .locals 0

    const-string p1, "this$0"

    invoke-static {p0, p1}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    const-string p1, "backward"

    invoke-virtual {p0, p1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->S0(Ljava/lang/String;)V

    const/4 p1, 0x0

    invoke-virtual {p0, p1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->hideBottomController(Z)V

    invoke-direct {p0, p1, p1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->G(ZZ)V

    return-void
.end method

.method public static synthetic f(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/View;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->j1(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/View;)V

    return-void
.end method

.method public static final f0(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Lcom/transsion/player/enum/ScaleMode;Ljava/lang/String;)V
    .locals 1

    const-string v0, "this$0"

    invoke-static {p0, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    const-string v0, "scaleMode"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    const-string v0, "scaleModeName"

    invoke-static {p2, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {p0, p1, p2}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->y1(Lcom/transsion/player/enum/ScaleMode;Ljava/lang/String;)V

    return-void
.end method

.method public static synthetic g(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)V
    .locals 0

    invoke-static {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->u0(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)V

    return-void
.end method

.method public static final g0(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/View;)V
    .locals 0

    const-string p1, "this$0"

    invoke-static {p0, p1}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    iget-object p0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object p0, p0, Loo/e;->m:Loo/f;

    iget-object p0, p0, Loo/f;->n:Lcom/transsion/postdetail/ui/view/ImmScaleView;

    invoke-virtual {p0}, Landroid/view/View;->performClick()Z

    return-void
.end method

.method public static synthetic g1(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;ZILjava/lang/Object;)V
    .locals 0

    const/4 p3, 0x1

    and-int/2addr p2, p3

    if-eqz p2, :cond_0

    const/4 p1, 0x1

    :cond_0
    invoke-virtual {p0, p1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->f1(Z)V

    return-void
.end method

.method private final getConfigViewModel()Lcom/transsion/player/longvideo/ui/dialog/a;
    .locals 1

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->M0:Lkotlin/Lazy;

    invoke-interface {v0}, Lkotlin/Lazy;->getValue()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/transsion/player/longvideo/ui/dialog/a;

    return-object v0
.end method

.method private final getLocalVideoLandForwardViewControl()Lcom/transsion/player/longvideo/helper/LongVodLandForwardViewControl;
    .locals 3

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->N:Lcom/transsion/player/longvideo/helper/LongVodLandForwardViewControl;

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->m:Loo/f;

    iget-object v0, v0, Loo/f;->F:Landroid/view/ViewStub;

    invoke-virtual {v0}, Landroid/view/ViewStub;->inflate()Landroid/view/View;

    move-result-object v0

    if-eqz v0, :cond_0

    new-instance v1, Lcom/transsion/player/longvideo/helper/LongVodLandForwardViewControl;

    invoke-static {v0}, Loo/a;->a(Landroid/view/View;)Loo/a;

    move-result-object v0

    const-string v2, "bind(it)"

    invoke-static {v0, v2}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-direct {v1, v0}, Lcom/transsion/player/longvideo/helper/LongVodLandForwardViewControl;-><init>(Loo/a;)V

    iput-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->N:Lcom/transsion/player/longvideo/helper/LongVodLandForwardViewControl;

    :cond_0
    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->N:Lcom/transsion/player/longvideo/helper/LongVodLandForwardViewControl;

    return-object v0
.end method

.method private final getOrientationEventListener()Lcom/transsion/postdetail/util/g;
    .locals 1

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->h1:Lkotlin/Lazy;

    invoke-interface {v0}, Lkotlin/Lazy;->getValue()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/transsion/postdetail/util/g;

    return-object v0
.end method

.method private final getPendingIntentFlag()I
    .locals 2

    sget v0, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v1, 0x1f

    if-lt v0, v1, :cond_0

    const/high16 v0, 0x4000000

    goto :goto_0

    :cond_0
    const/high16 v0, 0x8000000

    :goto_0
    return v0
.end method

.method private final getPkStyle()Z
    .locals 1

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->W0:Lkotlin/Lazy;

    invoke-interface {v0}, Lkotlin/Lazy;->getValue()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/Boolean;

    invoke-virtual {v0}, Ljava/lang/Boolean;->booleanValue()Z

    move-result v0

    return v0
.end method

.method private final getPlayTimeoutStream()J
    .locals 2

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->V0:Lkotlin/Lazy;

    invoke-interface {v0}, Lkotlin/Lazy;->getValue()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/Number;

    invoke-virtual {v0}, Ljava/lang/Number;->longValue()J

    move-result-wide v0

    return-wide v0
.end method

.method private final getReportNextMap()Ljava/util/Map;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    const/4 v0, 0x2

    new-array v0, v0, [Lkotlin/Pair;

    new-instance v1, Lkotlin/Pair;

    const-string v2, "module_name"

    const-string v3, "play_next"

    invoke-direct {v1, v2, v3}, Lkotlin/Pair;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    const/4 v2, 0x0

    aput-object v1, v0, v2

    new-instance v1, Lkotlin/Pair;

    iget-object v2, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->G:Lno/a;

    if-eqz v2, :cond_0

    invoke-virtual {v2}, Lno/a;->n()Ljava/lang/String;

    move-result-object v2

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    :goto_0
    const-string v3, "subject_id"

    invoke-direct {v1, v3, v2}, Lkotlin/Pair;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    const/4 v2, 0x1

    aput-object v1, v0, v2

    invoke-static {v0}, Lkotlin/collections/MapsKt;->k([Lkotlin/Pair;)Ljava/util/Map;

    move-result-object v0

    return-object v0
.end method

.method private final getScreenHelper()Lcom/transsion/baselib/helper/ScreenRotationHelper;
    .locals 1

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a1:Lkotlin/Lazy;

    invoke-interface {v0}, Lkotlin/Lazy;->getValue()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/transsion/baselib/helper/ScreenRotationHelper;

    return-object v0
.end method

.method private final getSpeedViewModel()Lcom/transsion/postdetail/ui/dialog/j;
    .locals 1

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->N0:Lkotlin/Lazy;

    invoke-interface {v0}, Lkotlin/Lazy;->getValue()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/transsion/postdetail/ui/dialog/j;

    return-object v0
.end method

.method private final getVideoDetailPlayDao()Lcom/transsion/baselib/db/video/VideoDetailPlayDao;
    .locals 1

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->T:Lkotlin/Lazy;

    invoke-interface {v0}, Lkotlin/Lazy;->getValue()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/transsion/baselib/db/video/VideoDetailPlayDao;

    return-object v0
.end method

.method public static synthetic h(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)V
    .locals 0

    invoke-static {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->X0(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)V

    return-void
.end method

.method public static final h0(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/View;)V
    .locals 8

    const-string v0, "this$0"

    invoke-static {p0, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {p1}, Landroid/view/View;->isSelected()Z

    move-result v0

    const/4 v1, 0x1

    xor-int/2addr v0, v1

    invoke-virtual {p1, v0}, Landroid/view/View;->setSelected(Z)V

    invoke-virtual {p1}, Landroid/view/View;->isSelected()Z

    move-result v0

    const-string v2, "null cannot be cast to non-null type android.app.Activity"

    if-eqz v0, :cond_0

    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-static {v0, v2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/String;)V

    check-cast v0, Landroid/app/Activity;

    const/16 v2, 0xb

    invoke-virtual {v0, v2}, Landroid/app/Activity;->setRequestedOrientation(I)V

    invoke-virtual {p0, v1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->hideBottomController(Z)V

    const-string v0, "it"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-static {p1}, Lvi/c;->k(Landroid/view/View;)V

    goto :goto_0

    :cond_0
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-static {v0, v2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/String;)V

    check-cast v0, Landroid/app/Activity;

    const/4 v2, 0x6

    invoke-virtual {v0, v2}, Landroid/app/Activity;->setRequestedOrientation(I)V

    invoke-direct {p0, v1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->c1(Z)V

    :goto_0
    sget-object v0, Lcom/transsion/baselib/report/l;->a:Lcom/transsion/baselib/report/l;

    iget-object v2, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->i:Ljava/lang/String;

    const-string v3, ""

    if-nez v2, :cond_1

    move-object v2, v3

    :cond_1
    const/4 v4, 0x3

    new-array v4, v4, [Lkotlin/Pair;

    new-instance v5, Lkotlin/Pair;

    const-string v6, "module_name"

    const-string v7, "lock"

    invoke-direct {v5, v6, v7}, Lkotlin/Pair;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    const/4 v6, 0x0

    aput-object v5, v4, v6

    new-instance v5, Lkotlin/Pair;

    iget-object v6, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->G:Lno/a;

    if-eqz v6, :cond_3

    invoke-virtual {v6}, Lno/a;->n()Ljava/lang/String;

    move-result-object v6

    if-nez v6, :cond_2

    goto :goto_1

    :cond_2
    move-object v3, v6

    :cond_3
    :goto_1
    const-string v6, "subject_id"

    invoke-direct {v5, v6, v3}, Lkotlin/Pair;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    aput-object v5, v4, v1

    new-instance v3, Lkotlin/Pair;

    invoke-virtual {p1}, Landroid/view/View;->isSelected()Z

    move-result v5

    if-eqz v5, :cond_4

    const-string v5, "1"

    goto :goto_2

    :cond_4
    const-string v5, "0"

    :goto_2
    const-string v6, "type"

    invoke-direct {v3, v6, v5}, Lkotlin/Pair;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    const/4 v5, 0x2

    aput-object v3, v4, v5

    invoke-static {v4}, Lkotlin/collections/MapsKt;->l([Lkotlin/Pair;)Ljava/util/Map;

    move-result-object v3

    const-string v4, "click"

    invoke-virtual {v0, v2, v4, v3}, Lcom/transsion/baselib/report/l;->l(Ljava/lang/String;Ljava/lang/String;Ljava/util/Map;)V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->S:Lcom/transsion/postdetail/layer/local/n;

    if-nez v0, :cond_5

    goto :goto_3

    :cond_5
    invoke-virtual {p1}, Landroid/view/View;->isSelected()Z

    move-result v2

    invoke-virtual {v0, v2}, Lcom/transsion/postdetail/layer/local/n;->C(Z)V

    :goto_3
    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e:Lcom/transsion/player/ui/longvideo/a;

    invoke-virtual {p1}, Landroid/view/View;->isSelected()Z

    move-result v2

    xor-int/2addr v1, v2

    invoke-interface {v0, v1}, Lcom/transsion/player/ui/longvideo/a;->enableGesture(Z)V

    invoke-direct {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->getPkStyle()Z

    move-result v0

    if-eqz v0, :cond_7

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->m:Loo/f;

    iget-object v0, v0, Loo/f;->l:Landroidx/appcompat/widget/AppCompatTextView;

    invoke-virtual {p1}, Landroid/view/View;->isSelected()Z

    move-result p1

    if-eqz p1, :cond_6

    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object p0

    sget p1, Lcom/transsion/baseui/R$string;->play_tap_unlock:I

    invoke-virtual {p0, p1}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    move-result-object p0

    goto :goto_4

    :cond_6
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object p0

    sget p1, Lcom/transsion/baseui/R$string;->play_tap_lock:I

    invoke-virtual {p0, p1}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    move-result-object p0

    :goto_4
    invoke-virtual {v0, p0}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    :cond_7
    return-void
.end method

.method public static final h1(Landroid/view/View;)V
    .locals 0

    invoke-static {}, Lcom/blankj/utilcode/util/NetworkUtils;->w()V

    return-void
.end method

.method public static synthetic i(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/View;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->b0(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/View;)V

    return-void
.end method

.method public static final i0(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/View;)V
    .locals 0

    const-string p1, "this$0"

    invoke-static {p0, p1}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    iget-object p0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->U:Lmo/a$b;

    if-eqz p0, :cond_0

    sget-object p1, Lcom/transsion/videofloat/bean/FloatActionType;->ICON:Lcom/transsion/videofloat/bean/FloatActionType;

    invoke-interface {p0, p1}, Lmo/a$b;->c(Lcom/transsion/videofloat/bean/FloatActionType;)Z

    :cond_0
    return-void
.end method

.method public static final i1(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/View;)V
    .locals 1

    const-string p1, "this$0"

    invoke-static {p0, p1}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->U:Lmo/a$b;

    if-eqz p1, :cond_0

    invoke-interface {p1}, Lmo/a$b;->b()Z

    move-result p1

    const/4 v0, 0x1

    if-ne p1, v0, :cond_0

    return-void

    :cond_0
    sget-object p1, Lcom/tn/lib/util/networkinfo/f;->a:Lcom/tn/lib/util/networkinfo/f;

    invoke-virtual {p1}, Lcom/tn/lib/util/networkinfo/f;->e()Z

    move-result p1

    if-eqz p1, :cond_1

    invoke-direct {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->M0()V

    :cond_1
    return-void
.end method

.method public static synthetic j(Landroid/view/View;)V
    .locals 0

    invoke-static {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->h1(Landroid/view/View;)V

    return-void
.end method

.method public static final j0(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/View;)V
    .locals 0

    const-string p1, "this$0"

    invoke-static {p0, p1}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->J()V

    return-void
.end method

.method public static final j1(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/View;)V
    .locals 2

    const-string p1, "this$0"

    invoke-static {p0, p1}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    const/4 p1, 0x1

    const/4 v0, 0x0

    const/4 v1, 0x0

    invoke-static {p0, v1, p1, v0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->B(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;ZILjava/lang/Object;)V

    return-void
.end method

.method public static synthetic k(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/View;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->c0(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/View;)V

    return-void
.end method

.method public static final k0(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/View;)V
    .locals 2

    const-string p1, "this$0"

    invoke-static {p0, p1}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    const/4 p1, 0x1

    const/4 v0, 0x0

    const/4 v1, 0x0

    invoke-static {p0, v1, p1, v0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->B(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;ZILjava/lang/Object;)V

    return-void
.end method

.method public static synthetic l(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/View;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a0(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/View;)V

    return-void
.end method

.method public static final l0(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/View;)V
    .locals 2

    const-string p1, "this$0"

    invoke-static {p0, p1}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    const/4 p1, 0x0

    const/4 v0, 0x3

    const/4 v1, 0x0

    invoke-static {p0, v1, p1, v0, v1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->w1(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/MotionEvent;ZILjava/lang/Object;)V

    return-void
.end method

.method public static synthetic l1(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;ZILjava/lang/Object;)V
    .locals 0

    const/4 p3, 0x1

    and-int/2addr p2, p3

    if-eqz p2, :cond_0

    const/4 p1, 0x1

    :cond_0
    invoke-virtual {p0, p1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->k1(Z)V

    return-void
.end method

.method public static final m1(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/View;)V
    .locals 0

    const-string p1, "this$0"

    invoke-static {p0, p1}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-direct {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->x1()V

    return-void
.end method

.method public static synthetic n(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/View;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->d0(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/View;)V

    return-void
.end method

.method private final n1(Ljava/lang/String;)V
    .locals 9

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->P:Landroid/view/View;

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->z:Landroid/view/ViewStub;

    invoke-virtual {v0}, Landroid/view/ViewStub;->inflate()Landroid/view/View;

    move-result-object v0

    iput-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->P:Landroid/view/View;

    invoke-virtual {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->s0()V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->P:Landroid/view/View;

    invoke-static {v0}, Lkotlin/jvm/internal/Intrinsics;->d(Ljava/lang/Object;)V

    sget v1, Lcom/transsion/player/longvideo/R$id;->tv_toast_1:I

    invoke-virtual {v0, v1}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/TextView;

    iget-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->P:Landroid/view/View;

    invoke-static {v1}, Lkotlin/jvm/internal/Intrinsics;->d(Ljava/lang/Object;)V

    sget v2, Lcom/transsion/player/longvideo/R$id;->tv_toast_2:I

    invoke-virtual {v1, v2}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    move-result-object v1

    check-cast v1, Landroid/widget/TextView;

    new-instance v2, Lcom/transsion/postdetail/layer/local/k0;

    invoke-direct {v2, v0, v1}, Lcom/transsion/postdetail/layer/local/k0;-><init>(Landroid/widget/TextView;Landroid/widget/TextView;)V

    iput-object v2, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->O:Lcom/transsion/postdetail/layer/local/k0;

    :cond_0
    iget-object v3, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->O:Lcom/transsion/postdetail/layer/local/k0;

    if-eqz v3, :cond_1

    const-wide/16 v5, 0x0

    const/4 v7, 0x2

    const/4 v8, 0x0

    move-object v4, p1

    invoke-static/range {v3 .. v8}, Lcom/transsion/postdetail/layer/local/k0;->i(Lcom/transsion/postdetail/layer/local/k0;Ljava/lang/String;JILjava/lang/Object;)V

    :cond_1
    return-void
.end method

.method public static synthetic o(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/View;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->g0(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/View;)V

    return-void
.end method

.method private final o0()V
    .locals 2

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->m:Loo/f;

    iget-object v0, v0, Loo/f;->s:Lcom/tn/lib/view/SecondariesSeekBar;

    const-string v1, "viewBinding.layoutLand.seekBarLand"

    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {p0, v0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->p0(Lcom/tn/lib/view/SecondariesSeekBar;)V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->n:Loo/g;

    iget-object v0, v0, Loo/g;->l:Lcom/tn/lib/view/SecondariesSeekBar;

    const-string v1, "viewBinding.layoutMiddle.seekBarMiddle"

    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {p0, v0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->p0(Lcom/tn/lib/view/SecondariesSeekBar;)V

    return-void
.end method

.method private final o1(Z)V
    .locals 12

    iget-boolean v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->s:Z

    if-ne v0, p1, :cond_0

    return-void

    :cond_0
    iput-boolean p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->s:Z

    if-eqz p1, :cond_1

    iget-boolean p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->t:Z

    if-nez p1, :cond_2

    invoke-virtual {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->O0()V

    const/4 p1, 0x1

    invoke-direct {p0, p1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->C0(Z)V

    sget-object v0, Lxi/b;->a:Lxi/b$a;

    const-string v1, "LongVodPlayerView"

    const-string v2, "showVideoLoading visible"

    const/4 v3, 0x0

    const/4 v4, 0x4

    const/4 v5, 0x0

    invoke-static/range {v0 .. v5}, Lxi/b$a;->f(Lxi/b$a;Ljava/lang/String;Ljava/lang/String;ZILjava/lang/Object;)V

    goto :goto_0

    :cond_1
    sget-object v6, Lxi/b;->a:Lxi/b$a;

    const-string v7, "LongVodPlayerView"

    const-string v8, "showVideoLoading gone"

    const/4 v9, 0x0

    const/4 v10, 0x4

    const/4 v11, 0x0

    invoke-static/range {v6 .. v11}, Lxi/b$a;->f(Lxi/b$a;Ljava/lang/String;Ljava/lang/String;ZILjava/lang/Object;)V

    const/4 p1, 0x0

    invoke-direct {p0, p1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->C0(Z)V

    :cond_2
    :goto_0
    return-void
.end method

.method public static synthetic p(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/View;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->i1(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/View;)V

    return-void
.end method

.method public static synthetic q(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/View;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->X(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/View;)V

    return-void
.end method

.method private final q1(Z)V
    .locals 3

    :try_start_0
    sget-object v0, Lkotlin/Result;->Companion:Lkotlin/Result$Companion;

    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    instance-of v1, v0, Landroidx/fragment/app/FragmentActivity;

    if-eqz v1, :cond_0

    check-cast v0, Landroidx/fragment/app/FragmentActivity;

    goto :goto_0

    :catchall_0
    move-exception p1

    goto :goto_2

    :cond_0
    const/4 v0, 0x0

    :goto_0
    if-eqz v0, :cond_2

    invoke-virtual {v0}, Landroid/app/Activity;->isDestroyed()Z

    move-result v1

    if-nez v1, :cond_2

    invoke-virtual {v0}, Landroid/app/Activity;->isFinishing()Z

    move-result v1

    if-eqz v1, :cond_1

    goto :goto_1

    :cond_1
    sget-object v1, Lcom/transsion/videofloat/VideoPipManager;->a:Lcom/transsion/videofloat/VideoPipManager$Companion;

    invoke-virtual {v1}, Lcom/transsion/videofloat/VideoPipManager$Companion;->a()Lcom/transsion/videofloat/VideoPipManager;

    move-result-object v1

    const/4 v2, 0x0

    invoke-interface {v1, v0, v2, p1}, Lcom/transsion/videofloat/VideoPipManager;->b(Landroidx/fragment/app/FragmentActivity;ZZ)Landroid/app/PictureInPictureParams;

    move-result-object p1

    invoke-static {p1}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_3

    :cond_2
    :goto_1
    return-void

    :goto_2
    sget-object v0, Lkotlin/Result;->Companion:Lkotlin/Result$Companion;

    invoke-static {p1}, Lkotlin/ResultKt;->a(Ljava/lang/Throwable;)Ljava/lang/Object;

    move-result-object p1

    invoke-static {p1}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    :goto_3
    return-void
.end method

.method public static synthetic r(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/View;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->j0(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/View;)V

    return-void
.end method

.method private final r1(ZJ)V
    .locals 8

    iput-boolean p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->t:Z

    invoke-direct {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->x0()Z

    move-result v0

    const-string v1, "viewBinding.tvCenterProgress"

    if-eqz v0, :cond_0

    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object p1, p1, Loo/e;->r:Landroidx/appcompat/widget/AppCompatTextView;

    invoke-static {p1, v1}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-static {p1}, Lvi/c;->g(Landroid/view/View;)V

    return-void

    :cond_0
    if-eqz p1, :cond_2

    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object p1, p1, Loo/e;->r:Landroidx/appcompat/widget/AppCompatTextView;

    const/4 v0, 0x0

    invoke-virtual {p1, v0}, Landroid/view/View;->setVisibility(I)V

    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object p1, p1, Loo/e;->r:Landroidx/appcompat/widget/AppCompatTextView;

    invoke-static {}, Lcom/blankj/utilcode/util/Utils;->a()Landroid/app/Application;

    move-result-object v1

    sget v2, Lcom/transsion/player/longvideo/R$string;->long_vod_progress_tx_style:I

    const/4 v3, 0x2

    new-array v3, v3, [Ljava/lang/Object;

    invoke-static {p2, p3}, Lcom/transsion/postdetail/util/f;->d(J)Ljava/lang/String;

    move-result-object v4

    aput-object v4, v3, v0

    iget-wide v4, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->n:J

    invoke-static {v4, v5}, Lcom/transsion/postdetail/util/f;->d(J)Ljava/lang/String;

    move-result-object v4

    const/4 v5, 0x1

    aput-object v4, v3, v5

    invoke-virtual {v1, v2, v3}, Landroid/content/Context;->getString(I[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Landroid/text/Html;->fromHtml(Ljava/lang/String;)Landroid/text/Spanned;

    move-result-object v1

    invoke-virtual {p1, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->V:Lcom/transsion/player/longvideo/helper/LongVodSubtitleHelper;

    if-eqz p1, :cond_1

    invoke-virtual {p1, p2, p3}, Lcom/transsion/player/longvideo/helper/LongVodSubtitleHelper;->H(J)V

    :cond_1
    invoke-direct {p0, v0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->C0(Z)V

    sget-object v1, Lxi/b;->a:Lxi/b$a;

    const-string v2, "LongVodPlayerView"

    const-string v3, "updateSeekbar loading gone"

    const/4 v4, 0x0

    const/4 v5, 0x4

    const/4 v6, 0x0

    invoke-static/range {v1 .. v6}, Lxi/b$a;->f(Lxi/b$a;Ljava/lang/String;Ljava/lang/String;ZILjava/lang/Object;)V

    goto :goto_0

    :cond_2
    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->V:Lcom/transsion/player/longvideo/helper/LongVodSubtitleHelper;

    if-eqz p1, :cond_3

    invoke-virtual {p1}, Lcom/transsion/player/longvideo/helper/LongVodSubtitleHelper;->B()V

    :cond_3
    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object p1, p1, Loo/e;->r:Landroidx/appcompat/widget/AppCompatTextView;

    invoke-static {p1, v1}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-static {p1}, Lvi/c;->g(Landroid/view/View;)V

    sget-object v2, Lxi/b;->a:Lxi/b$a;

    const-string v3, "LongVodPlayerView"

    iget-boolean p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->s:Z

    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string p3, "updateSeekbar loading "

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    const/4 v5, 0x0

    const/4 v6, 0x4

    const/4 v7, 0x0

    invoke-static/range {v2 .. v7}, Lxi/b$a;->f(Lxi/b$a;Ljava/lang/String;Ljava/lang/String;ZILjava/lang/Object;)V

    iget-boolean p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->s:Z

    invoke-direct {p0, p1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->C0(Z)V

    :goto_0
    return-void
.end method

.method public static synthetic s(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/View;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e0(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/View;)V

    return-void
.end method

.method private final setPauseViewStatus(Z)V
    .locals 8

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->m:Loo/f;

    iget-object v0, v0, Loo/f;->k:Landroidx/appcompat/widget/AppCompatImageView;

    invoke-static {p1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v1

    invoke-virtual {v0, v1}, Landroid/view/View;->setTag(Ljava/lang/Object;)V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->n:Loo/g;

    iget-object v0, v0, Loo/g;->d:Landroidx/appcompat/widget/AppCompatImageView;

    invoke-static {p1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v1

    invoke-virtual {v0, v1}, Landroid/view/View;->setTag(Ljava/lang/Object;)V

    sget-object v2, Lxi/b;->a:Lxi/b$a;

    const-string v3, "LongVodPlayerView"

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "setPauseViewStatus playing = "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    const/4 v5, 0x0

    const/4 v6, 0x4

    const/4 v7, 0x0

    invoke-static/range {v2 .. v7}, Lxi/b$a;->f(Lxi/b$a;Ljava/lang/String;Ljava/lang/String;ZILjava/lang/Object;)V

    if-eqz p1, :cond_0

    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object p1, p1, Loo/e;->m:Loo/f;

    iget-object p1, p1, Loo/f;->k:Landroidx/appcompat/widget/AppCompatImageView;

    sget v0, Lcom/transsion/baseui/R$mipmap;->icon_player_pause:I

    invoke-virtual {p1, v0}, Landroidx/appcompat/widget/AppCompatImageView;->setImageResource(I)V

    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object p1, p1, Loo/e;->m:Loo/f;

    iget-object p1, p1, Loo/f;->c:Lpl/d;

    iget-object p1, p1, Lpl/d;->d:Landroidx/appcompat/widget/AppCompatImageView;

    sget v0, Lcom/transsion/baseui/R$drawable;->ic_player_pause:I

    invoke-virtual {p1, v0}, Landroidx/appcompat/widget/AppCompatImageView;->setImageResource(I)V

    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object p1, p1, Loo/e;->n:Loo/g;

    iget-object p1, p1, Loo/g;->d:Landroidx/appcompat/widget/AppCompatImageView;

    sget v0, Lcom/transsion/baseui/R$mipmap;->icon_player_pause:I

    invoke-virtual {p1, v0}, Landroidx/appcompat/widget/AppCompatImageView;->setImageResource(I)V

    goto :goto_0

    :cond_0
    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object p1, p1, Loo/e;->m:Loo/f;

    iget-object p1, p1, Loo/f;->k:Landroidx/appcompat/widget/AppCompatImageView;

    sget v0, Lcom/transsion/baseui/R$mipmap;->icon_player_play:I

    invoke-virtual {p1, v0}, Landroidx/appcompat/widget/AppCompatImageView;->setImageResource(I)V

    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object p1, p1, Loo/e;->m:Loo/f;

    iget-object p1, p1, Loo/f;->c:Lpl/d;

    iget-object p1, p1, Lpl/d;->d:Landroidx/appcompat/widget/AppCompatImageView;

    sget v0, Lcom/transsion/baseui/R$drawable;->ic_player_play:I

    invoke-virtual {p1, v0}, Landroidx/appcompat/widget/AppCompatImageView;->setImageResource(I)V

    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object p1, p1, Loo/e;->n:Loo/g;

    iget-object p1, p1, Loo/g;->d:Landroidx/appcompat/widget/AppCompatImageView;

    sget v0, Lcom/transsion/baseui/R$mipmap;->icon_player_play:I

    invoke-virtual {p1, v0}, Landroidx/appcompat/widget/AppCompatImageView;->setImageResource(I)V

    :goto_0
    return-void
.end method

.method private final setPlayerDataSourceAdPrepare(Lno/c;)V
    .locals 11

    iput-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->Q:Lno/c;

    invoke-virtual {p1}, Lno/c;->d()Ljava/lang/String;

    move-result-object v0

    invoke-interface {v0}, Ljava/lang/CharSequence;->length()I

    move-result v0

    const/4 v1, 0x0

    if-lez v0, :cond_0

    new-instance v0, Ljava/util/LinkedHashMap;

    invoke-direct {v0}, Ljava/util/LinkedHashMap;-><init>()V

    const-string v2, "cookie"

    invoke-virtual {p1}, Lno/c;->d()Ljava/lang/String;

    move-result-object v3

    invoke-interface {v0, v2, v3}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    :cond_0
    move-object v0, v1

    :goto_0
    new-instance v10, Lcom/transsion/player/MediaSource;

    invoke-virtual {p1}, Lno/c;->f()Ljava/lang/String;

    move-result-object v2

    invoke-static {v2}, Lcom/transsion/baselib/utils/h;->f(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/String;->hashCode()I

    move-result v2

    invoke-static {v2}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {p1}, Lno/c;->f()Ljava/lang/String;

    move-result-object v4

    const/4 v5, 0x0

    invoke-virtual {p1}, Lno/c;->a()Lcom/transsion/player/enum/PlayMimeType;

    move-result-object v6

    invoke-virtual {p0, p1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->L(Lno/c;)Lcom/transsion/player/mediasession/MediaItem;

    move-result-object v7

    const/4 v8, 0x4

    const/4 v9, 0x0

    move-object v2, v10

    invoke-direct/range {v2 .. v9}, Lcom/transsion/player/MediaSource;-><init>(Ljava/lang/String;Ljava/lang/String;ILcom/transsion/player/enum/PlayMimeType;Lcom/transsion/player/mediasession/MediaItem;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    sget-object v2, Lcom/transsion/baseui/music/MusicFloatManager;->g:Lcom/transsion/baseui/music/MusicFloatManager$a;

    invoke-virtual {v2}, Lcom/transsion/baseui/music/MusicFloatManager$a;->b()Lcom/transsion/baseui/music/MusicFloatManager;

    move-result-object v3

    invoke-virtual {v10}, Lcom/transsion/player/MediaSource;->d()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3, v4}, Lcom/transsion/baseui/music/MusicFloatManager;->v(Ljava/lang/String;)V

    invoke-virtual {v2}, Lcom/transsion/baseui/music/MusicFloatManager$a;->b()Lcom/transsion/baseui/music/MusicFloatManager;

    move-result-object v2

    invoke-virtual {v10}, Lcom/transsion/player/MediaSource;->g()Lcom/transsion/player/mediasession/MediaItem;

    move-result-object v3

    invoke-virtual {v2, v3}, Lcom/transsion/baseui/music/MusicFloatManager;->u(Lcom/transsion/player/mediasession/MediaItem;)V

    invoke-virtual {v10, v0}, Lcom/transsion/player/MediaSource;->l(Ljava/util/Map;)V

    sget-object v0, Lxi/b;->a:Lxi/b$a;

    iget-object v2, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->G:Lno/a;

    if-eqz v2, :cond_1

    invoke-virtual {v2}, Lno/a;->k()I

    move-result v2

    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    goto :goto_1

    :cond_1
    move-object v2, v1

    :goto_1
    iget-object v3, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->G:Lno/a;

    if-eqz v3, :cond_2

    invoke-virtual {v3}, Lno/a;->d()I

    move-result v1

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    :cond_2
    invoke-virtual {p1}, Lno/c;->c()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v10}, Lcom/transsion/player/MediaSource;->d()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {p1}, Lno/c;->f()Ljava/lang/String;

    move-result-object p1

    new-instance v5, Ljava/lang/StringBuilder;

    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    const-string v6, "setPlayerDataSourceAdPrepare,  se:"

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v2, " ep:"

    invoke-virtual {v5, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v1, ", resolution:"

    invoke-virtual {v5, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, "\uff0cid\uff1a"

    invoke-virtual {v5, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, ",url:"

    invoke-virtual {v5, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    const-string v1, "LongVodPlayerView"

    const/4 v2, 0x1

    invoke-virtual {v0, v1, p1, v2}, Lxi/b$a;->c(Ljava/lang/String;Ljava/lang/String;Z)V

    invoke-direct {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->U0()V

    iput-object v10, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->U0:Lcom/transsion/player/MediaSource;

    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e:Lcom/transsion/player/ui/longvideo/a;

    invoke-interface {p1}, Lcom/transsion/player/orplayer/f;->clearScreen()V

    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e:Lcom/transsion/player/ui/longvideo/a;

    invoke-interface {p1, v10}, Lcom/transsion/player/orplayer/f;->setDataSource(Lcom/transsion/player/MediaSource;)V

    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e:Lcom/transsion/player/ui/longvideo/a;

    invoke-interface {p1}, Lcom/transsion/player/orplayer/f;->prepare()V

    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->T0:Landroid/os/Handler;

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->f1:Ljava/lang/Runnable;

    invoke-direct {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->getPlayTimeoutStream()J

    move-result-wide v3

    invoke-virtual {p1, v0, v3, v4}, Landroid/os/Handler;->postDelayed(Ljava/lang/Runnable;J)Z

    sget-object p1, Lcom/tn/lib/util/networkinfo/f;->a:Lcom/tn/lib/util/networkinfo/f;

    invoke-static {}, Lcom/blankj/utilcode/util/Utils;->a()Landroid/app/Application;

    move-result-object v0

    const-string v1, "getApp()"

    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {p1, v0}, Lcom/tn/lib/util/networkinfo/f;->h(Landroid/content/Context;)Z

    move-result p1

    if-eqz p1, :cond_3

    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->H:Lno/c;

    if-eqz p1, :cond_3

    invoke-virtual {p1}, Lno/c;->h()Z

    move-result p1

    if-ne p1, v2, :cond_3

    invoke-virtual {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->N()V

    :cond_3
    return-void
.end method

.method public static synthetic t(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Lcom/transsion/player/enum/ScaleMode;Ljava/lang/String;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->f0(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Lcom/transsion/player/enum/ScaleMode;Ljava/lang/String;)V

    return-void
.end method

.method private final t0()V
    .locals 5

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->m:Loo/f;

    invoke-virtual {v0}, Loo/f;->b()Landroidx/constraintlayout/widget/ConstraintLayout;

    move-result-object v0

    const-string v1, "viewBinding.layoutLand.root"

    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-static {v0}, Lvi/c;->g(Landroid/view/View;)V

    new-instance v0, Lcom/transsion/player/longvideo/helper/f;

    iget-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    invoke-direct {v0, v1}, Lcom/transsion/player/longvideo/helper/f;-><init>(Loo/e;)V

    iput-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->W:Lcom/transsion/player/longvideo/helper/f;

    invoke-direct {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->getConfigViewModel()Lcom/transsion/player/longvideo/ui/dialog/a;

    move-result-object v0

    invoke-virtual {v0}, Lcom/transsion/player/longvideo/ui/dialog/a;->b()Landroidx/lifecycle/LiveData;

    move-result-object v0

    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v1

    const-string v2, "null cannot be cast to non-null type androidx.fragment.app.FragmentActivity"

    invoke-static {v1, v2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/String;)V

    check-cast v1, Landroidx/fragment/app/FragmentActivity;

    new-instance v3, Lcom/transsion/player/longvideo/ui/LongVodPlayerView$initViewData$1;

    invoke-direct {v3, p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView$initViewData$1;-><init>(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)V

    new-instance v4, Lcom/transsion/player/longvideo/ui/LongVodPlayerView$h;

    invoke-direct {v4, v3}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView$h;-><init>(Lkotlin/jvm/functions/Function1;)V

    invoke-virtual {v0, v1, v4}, Landroidx/lifecycle/LiveData;->j(Landroidx/lifecycle/u;Landroidx/lifecycle/d0;)V

    invoke-direct {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->getSpeedViewModel()Lcom/transsion/postdetail/ui/dialog/j;

    move-result-object v0

    invoke-virtual {v0}, Lcom/transsion/postdetail/ui/dialog/j;->b()Landroidx/lifecycle/LiveData;

    move-result-object v0

    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v1

    invoke-static {v1, v2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/String;)V

    check-cast v1, Landroidx/fragment/app/FragmentActivity;

    new-instance v2, Lcom/transsion/player/longvideo/ui/LongVodPlayerView$initViewData$2;

    invoke-direct {v2, p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView$initViewData$2;-><init>(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)V

    new-instance v3, Lcom/transsion/player/longvideo/ui/LongVodPlayerView$h;

    invoke-direct {v3, v2}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView$h;-><init>(Lkotlin/jvm/functions/Function1;)V

    invoke-virtual {v0, v1, v3}, Landroidx/lifecycle/LiveData;->j(Landroidx/lifecycle/u;Landroidx/lifecycle/d0;)V

    new-instance v0, Lcom/transsion/player/longvideo/ui/n;

    invoke-direct {v0, p0}, Lcom/transsion/player/longvideo/ui/n;-><init>(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)V

    invoke-virtual {p0, v0}, Landroid/view/View;->post(Ljava/lang/Runnable;)Z

    invoke-virtual {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->A0()Z

    move-result v0

    if-nez v0, :cond_0

    invoke-virtual {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->y0()Z

    move-result v0

    if-eqz v0, :cond_2

    :cond_0
    new-instance v0, Lcom/transsion/postdetail/layer/local/n;

    invoke-direct {v0}, Lcom/transsion/postdetail/layer/local/n;-><init>()V

    iput-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->S:Lcom/transsion/postdetail/layer/local/n;

    invoke-virtual {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->y0()Z

    move-result v0

    if-eqz v0, :cond_2

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->S:Lcom/transsion/postdetail/layer/local/n;

    if-nez v0, :cond_1

    goto :goto_0

    :cond_1
    const-string v1, "music_play"

    invoke-virtual {v0, v1}, Lcom/transsion/postdetail/layer/local/n;->B(Ljava/lang/String;)V

    :cond_2
    :goto_0
    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->S:Lcom/transsion/postdetail/layer/local/n;

    if-eqz v0, :cond_3

    invoke-virtual {v0}, Lcom/transsion/postdetail/layer/local/n;->g()V

    :cond_3
    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->S:Lcom/transsion/postdetail/layer/local/n;

    if-eqz v0, :cond_4

    iget-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e:Lcom/transsion/player/ui/longvideo/a;

    invoke-virtual {v0, v1}, Lcom/transsion/postdetail/layer/local/n;->F(Lcom/transsion/player/orplayer/f;)V

    :cond_4
    invoke-virtual {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->y0()Z

    move-result v0

    if-eqz v0, :cond_5

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->o:Loo/d;

    iget-object v0, v0, Loo/d;->d:Landroidx/appcompat/widget/AppCompatTextView;

    const/16 v1, 0x8

    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    :cond_5
    return-void
.end method

.method private final t1(J)V
    .locals 6

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->V:Lcom/transsion/player/longvideo/helper/LongVodSubtitleHelper;

    if-eqz v0, :cond_0

    invoke-virtual {v0, p1, p2}, Lcom/transsion/player/longvideo/helper/LongVodSubtitleHelper;->H(J)V

    :cond_0
    iget-wide v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->n:J

    const-wide/16 v2, 0x0

    cmp-long v4, v0, v2

    if-gtz v4, :cond_1

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e:Lcom/transsion/player/ui/longvideo/a;

    invoke-interface {v0}, Lcom/transsion/player/orplayer/f;->getDuration()J

    move-result-wide v0

    iput-wide v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->n:J

    :cond_1
    iget-boolean v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->v:Z

    if-nez v0, :cond_2

    iget-wide v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->n:J

    cmp-long v4, v0, v2

    if-lez v4, :cond_2

    long-to-float v4, p1

    long-to-float v5, v0

    div-float/2addr v4, v5

    long-to-float v0, v0

    mul-float v4, v4, v0

    float-to-int v0, v4

    iget-boolean v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->t:Z

    if-nez v1, :cond_2

    iget-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v1, v1, Loo/e;->m:Loo/f;

    iget-object v1, v1, Loo/f;->s:Lcom/tn/lib/view/SecondariesSeekBar;

    int-to-long v4, v0

    invoke-virtual {v1, v4, v5}, Lcom/tn/lib/view/SecondariesSeekBar;->setProgress(J)V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->n:Loo/g;

    iget-object v0, v0, Loo/g;->l:Lcom/tn/lib/view/SecondariesSeekBar;

    invoke-virtual {v0, v4, v5}, Lcom/tn/lib/view/SecondariesSeekBar;->setProgress(J)V

    :cond_2
    iget-wide v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->n:J

    cmp-long v4, v0, v2

    if-ltz v4, :cond_3

    invoke-static {p1, p2}, Lcom/transsion/postdetail/util/f;->d(J)Ljava/lang/String;

    move-result-object p1

    iget-wide v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->n:J

    invoke-static {v0, v1}, Lcom/transsion/postdetail/util/f;->d(J)Ljava/lang/String;

    move-result-object p2

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->m:Loo/f;

    iget-object v0, v0, Loo/f;->v:Landroid/widget/TextView;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->m:Loo/f;

    iget-object v0, v0, Loo/f;->w:Landroid/widget/TextView;

    invoke-virtual {v0, p2}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->n:Loo/g;

    iget-object v0, v0, Loo/g;->n:Landroid/widget/TextView;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string p1, "/"

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    const-string p2, "toString(...)"

    invoke-static {p1, p2}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    :cond_3
    return-void
.end method

.method public static synthetic u(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/View;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->k0(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/View;)V

    return-void
.end method

.method public static final u0(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)V
    .locals 1

    const-string v0, "this$0"

    invoke-static {p0, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->L0()V

    return-void
.end method

.method public static synthetic v(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/View;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->O(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/View;)V

    return-void
.end method

.method private final v0()Z
    .locals 1

    sget-object v0, Lcom/transsion/videofloat/VideoPipManager;->a:Lcom/transsion/videofloat/VideoPipManager$Companion;

    invoke-virtual {v0}, Lcom/transsion/videofloat/VideoPipManager$Companion;->a()Lcom/transsion/videofloat/VideoPipManager;

    move-result-object v0

    invoke-interface {v0}, Lcom/transsion/videofloat/VideoPipManager;->k()Z

    move-result v0

    return v0
.end method

.method public static synthetic w(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)V
    .locals 0

    invoke-static {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->J0(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)V

    return-void
.end method

.method private final w0()Z
    .locals 2

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->j:Lcom/transsion/player/longvideo/ui/LongVodUiType;

    sget-object v1, Lcom/transsion/player/longvideo/ui/LongVodUiType;->LAND:Lcom/transsion/player/longvideo/ui/LongVodUiType;

    if-ne v0, v1, :cond_0

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->m:Loo/f;

    iget-object v0, v0, Loo/f;->e:Landroid/widget/FrameLayout;

    invoke-virtual {v0}, Landroid/view/View;->getVisibility()I

    move-result v0

    if-nez v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    iput-boolean v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->K0:Z

    return v0
.end method

.method public static synthetic w1(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/MotionEvent;ZILjava/lang/Object;)V
    .locals 0

    and-int/lit8 p4, p3, 0x1

    if-eqz p4, :cond_0

    const/4 p1, 0x0

    :cond_0
    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_1

    const/4 p2, 0x0

    :cond_1
    invoke-virtual {p0, p1, p2}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->v1(Landroid/view/MotionEvent;Z)V

    return-void
.end method

.method public static synthetic x(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/View;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->h0(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/View;)V

    return-void
.end method

.method private final x0()Z
    .locals 1

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->I:Landroid/view/View;

    if-eqz v0, :cond_0

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Landroid/view/View;->getVisibility()I

    move-result v0

    if-nez v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method private final x1()V
    .locals 3

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->S:Lcom/transsion/postdetail/layer/local/n;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/transsion/postdetail/layer/local/n;->o()V

    :cond_0
    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->J:Landroid/view/View;

    if-eqz v0, :cond_1

    invoke-static {v0}, Lvi/c;->g(Landroid/view/View;)V

    :cond_1
    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e:Lcom/transsion/player/ui/longvideo/a;

    const-wide/16 v1, 0x0

    invoke-interface {v0, v1, v2}, Lcom/transsion/player/orplayer/f;->seekTo(J)V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e:Lcom/transsion/player/ui/longvideo/a;

    invoke-interface {v0}, Lcom/transsion/player/orplayer/f;->play()V

    return-void
.end method

.method public static synthetic y(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/View;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->Z(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/View;)V

    return-void
.end method

.method private final z(Z)V
    .locals 5

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->j:Lcom/transsion/player/longvideo/ui/LongVodUiType;

    iget-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->k:Lcom/transsion/player/longvideo/ui/LongVodUiType;

    iget-boolean v2, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->z:Z

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "\u65cb\u8f6cto\u7ad6\u5c4f: "

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, p1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    const-string v4, "\uff0ccurUiType\uff1a"

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v0, "\uff0ccurUiRation: "

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v0, ", resume:"

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->m:Loo/f;

    iget-object v0, v0, Loo/f;->l:Landroidx/appcompat/widget/AppCompatTextView;

    invoke-virtual {v0}, Landroid/view/View;->isSelected()Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    if-eqz p1, :cond_1

    sget-object v0, Lcom/transsion/player/longvideo/ui/LongVodUiType;->MIDDLE:Lcom/transsion/player/longvideo/ui/LongVodUiType;

    goto :goto_0

    :cond_1
    sget-object v0, Lcom/transsion/player/longvideo/ui/LongVodUiType;->LAND:Lcom/transsion/player/longvideo/ui/LongVodUiType;

    :goto_0
    iget-boolean v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->z:Z

    if-eqz v1, :cond_5

    iget-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->k:Lcom/transsion/player/longvideo/ui/LongVodUiType;

    if-eq v1, v0, :cond_5

    iget-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->j:Lcom/transsion/player/longvideo/ui/LongVodUiType;

    if-ne v0, v1, :cond_2

    goto :goto_3

    :cond_2
    iput-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->k:Lcom/transsion/player/longvideo/ui/LongVodUiType;

    sget-object v1, Lcom/transsion/baselib/report/l;->a:Lcom/transsion/baselib/report/l;

    new-instance v2, Lkotlin/Pair;

    sget-object v3, Lcom/transsion/player/longvideo/ui/LongVodUiType;->LAND:Lcom/transsion/player/longvideo/ui/LongVodUiType;

    if-ne v0, v3, :cond_3

    const-string v0, "land"

    goto :goto_1

    :cond_3
    const-string v0, "port"

    :goto_1
    const-string v3, "orientation"

    invoke-direct {v2, v3, v0}, Lkotlin/Pair;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    invoke-static {v2}, Lkotlin/collections/MapsKt;->f(Lkotlin/Pair;)Ljava/util/Map;

    move-result-object v0

    const-string v2, "auto_screen"

    invoke-virtual {v1, v2, v0}, Lcom/transsion/baselib/report/l;->m(Ljava/lang/String;Ljava/util/Map;)V

    if-eqz p1, :cond_4

    invoke-direct {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->N0()V

    const/4 p1, 0x0

    invoke-virtual {p0, p1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->A(Z)V

    goto :goto_2

    :cond_4
    invoke-virtual {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->J()V

    :goto_2
    return-void

    :cond_5
    :goto_3
    iput-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->k:Lcom/transsion/player/longvideo/ui/LongVodUiType;

    return-void
.end method

.method private final z0()Z
    .locals 2

    iget-boolean v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->E:Z

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->g:Lcom/transsion/player/longvideo/constants/LongVodPageType;

    sget-object v1, Lcom/transsion/player/longvideo/constants/LongVodPageType;->MUSIC:Lcom/transsion/player/longvideo/constants/LongVodPageType;

    if-eq v0, v1, :cond_0

    iget-boolean v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->S0:Z

    if-nez v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method


# virtual methods
.method public final A(Z)V
    .locals 3

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->j:Lcom/transsion/player/longvideo/ui/LongVodUiType;

    sget-object v1, Lcom/transsion/player/longvideo/ui/LongVodUiType;->MIDDLE:Lcom/transsion/player/longvideo/ui/LongVodUiType;

    const-string v2, "null cannot be cast to non-null type android.app.Activity"

    if-eq v0, v1, :cond_1

    sget-object p1, Lcom/transsion/player/longvideo/ui/LongVodUiType;->LAND:Lcom/transsion/player/longvideo/ui/LongVodUiType;

    if-ne v0, p1, :cond_0

    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object p1

    invoke-static {p1, v2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/String;)V

    check-cast p1, Landroid/app/Activity;

    invoke-static {p1}, Lcom/gyf/immersionbar/ImmersionBar;->with(Landroid/app/Activity;)Lcom/gyf/immersionbar/ImmersionBar;

    move-result-object v0

    sget-object v2, Lcom/gyf/immersionbar/BarHide;->FLAG_SHOW_BAR:Lcom/gyf/immersionbar/BarHide;

    invoke-virtual {v0, v2}, Lcom/gyf/immersionbar/ImmersionBar;->hideBar(Lcom/gyf/immersionbar/BarHide;)Lcom/gyf/immersionbar/ImmersionBar;

    move-result-object v0

    invoke-virtual {v0}, Lcom/gyf/immersionbar/ImmersionBar;->init()V

    const/4 v0, 0x1

    invoke-virtual {p1, v0}, Landroid/app/Activity;->setRequestedOrientation(I)V

    :cond_0
    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object p1, p1, Loo/e;->v:Landroid/widget/ImageView;

    const/4 v0, 0x0

    invoke-virtual {p1, v0, v0, v0, v0}, Landroid/view/View;->setPadding(IIII)V

    iput-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->j:Lcom/transsion/player/longvideo/ui/LongVodUiType;

    invoke-virtual {p0, v0, v1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->Y0(ZLcom/transsion/player/longvideo/ui/LongVodUiType;)V

    goto :goto_0

    :cond_1
    if-eqz p1, :cond_3

    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->U:Lmo/a$b;

    if-eqz p1, :cond_2

    sget-object v0, Lcom/transsion/videofloat/bean/FloatActionType;->BACK:Lcom/transsion/videofloat/bean/FloatActionType;

    invoke-interface {p1, v0}, Lmo/a$b;->c(Lcom/transsion/videofloat/bean/FloatActionType;)Z

    move-result p1

    if-nez p1, :cond_3

    :cond_2
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object p1

    invoke-static {p1, v2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/String;)V

    check-cast p1, Landroid/app/Activity;

    invoke-virtual {p1}, Landroid/app/Activity;->finish()V

    :cond_3
    :goto_0
    return-void
.end method

.method public final A0()Z
    .locals 2

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->g:Lcom/transsion/player/longvideo/constants/LongVodPageType;

    sget-object v1, Lcom/transsion/player/longvideo/constants/LongVodPageType;->STREAM:Lcom/transsion/player/longvideo/constants/LongVodPageType;

    if-eq v0, v1, :cond_1

    sget-object v1, Lcom/transsion/player/longvideo/constants/LongVodPageType;->LOCAL_VIDEO:Lcom/transsion/player/longvideo/constants/LongVodPageType;

    if-ne v0, v1, :cond_0

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v0, 0x1

    :goto_1
    return v0
.end method

.method public final B0(Z)V
    .locals 1

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    invoke-virtual {v0}, Loo/e;->getRoot()Landroid/view/View;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/view/View;->setKeepScreenOn(Z)V

    return-void
.end method

.method public final D(I)V
    .locals 0

    return-void
.end method

.method public final D0()V
    .locals 3

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e:Lcom/transsion/player/ui/longvideo/a;

    invoke-interface {v0}, Lcom/transsion/player/orplayer/f;->isPlaying()Z

    move-result v0

    if-eqz v0, :cond_2

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->m:Loo/f;

    iget-object v0, v0, Loo/f;->l:Landroidx/appcompat/widget/AppCompatTextView;

    invoke-virtual {v0}, Landroid/view/View;->isSelected()Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    sget-object v0, Lcom/transsion/baselib/helper/LocalVideoPlayerConfigMmkv;->a:Lcom/transsion/baselib/helper/LocalVideoPlayerConfigMmkv;

    invoke-virtual {v0}, Lcom/transsion/baselib/helper/LocalVideoPlayerConfigMmkv;->c()F

    move-result v0

    iput v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->L0:F

    iget-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e:Lcom/transsion/player/ui/longvideo/a;

    const/4 v2, 0x2

    int-to-float v2, v2

    mul-float v0, v0, v2

    invoke-interface {v1, v0}, Lcom/transsion/player/orplayer/f;->setSpeed(F)V

    const/4 v0, 0x0

    const/4 v1, 0x1

    const/4 v2, 0x0

    invoke-static {p0, v0, v1, v2}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->l1(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;ZILjava/lang/Object;)V

    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    move-result-wide v0

    iput-wide v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->c1:J

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->i:Ljava/lang/String;

    iget-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->G:Lno/a;

    if-eqz v1, :cond_1

    invoke-virtual {v1}, Lno/a;->n()Ljava/lang/String;

    move-result-object v2

    :cond_1
    iget v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->L0:F

    invoke-static {v1}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object v1

    invoke-static {v0, v2, v1}, Lcom/transsion/baselib/utils/j;->a(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Float;)V

    :cond_2
    :goto_0
    return-void
.end method

.method public final E()V
    .locals 3

    new-instance v0, Lcom/transsion/player/longvideo/helper/e;

    invoke-direct {v0}, Lcom/transsion/player/longvideo/helper/e;-><init>()V

    iget-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->i:Ljava/lang/String;

    iget-object v2, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    invoke-virtual {v0, v1, v2}, Lcom/transsion/player/longvideo/helper/e;->c(Ljava/lang/String;Loo/e;)V

    iput-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->I0:Lcom/transsion/player/longvideo/helper/e;

    return-void
.end method

.method public final E0()V
    .locals 5

    iget-boolean v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->w:Z

    if-nez v0, :cond_0

    iget-boolean v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->B:Z

    if-nez v0, :cond_0

    invoke-static {}, Lcom/blankj/utilcode/util/Utils;->a()Landroid/app/Application;

    move-result-object v0

    sget v1, Lcom/transsion/player/longvideo/R$string;->long_vod_analysing_from:I

    invoke-virtual {v0, v1}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    move-result-object v0

    invoke-static {}, Lcom/blankj/utilcode/util/Utils;->a()Landroid/app/Application;

    move-result-object v1

    invoke-virtual {v1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    move-result-object v1

    sget v2, Lcom/transsion/player/longvideo/R$array;->long_vod_analysing_array:I

    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getStringArray(I)[Ljava/lang/String;

    move-result-object v1

    const-string v2, "getApp().resources.getSt\u2026long_vod_analysing_array)"

    invoke-static {v1, v2}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    sget-object v2, Lkotlin/random/Random;->Default:Lkotlin/random/Random$Default;

    invoke-static {v1, v2}, Lkotlin/collections/ArraysKt;->p0([Ljava/lang/Object;Lkotlin/random/Random;)Ljava/lang/Object;

    move-result-object v1

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v0, " ["

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v0, "]"

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-direct {p0, v0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->n1(Ljava/lang/String;)V

    :cond_0
    sget-object v0, Lcom/tn/lib/util/networkinfo/f;->a:Lcom/tn/lib/util/networkinfo/f;

    invoke-static {}, Lcom/blankj/utilcode/util/Utils;->a()Landroid/app/Application;

    move-result-object v1

    const-string v2, "getApp()"

    invoke-static {v1, v2}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {v0, v1}, Lcom/tn/lib/util/networkinfo/f;->h(Landroid/content/Context;)Z

    move-result v0

    const/4 v1, 0x0

    const/4 v2, 0x1

    if-eqz v0, :cond_1

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->H:Lno/c;

    if-eqz v0, :cond_1

    invoke-virtual {v0}, Lno/c;->h()Z

    move-result v0

    if-ne v0, v2, :cond_1

    const/4 v0, 0x1

    goto :goto_0

    :cond_1
    const/4 v0, 0x0

    :goto_0
    const-wide/16 v3, 0x0

    invoke-direct {p0, v3, v4}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->t1(J)V

    invoke-virtual {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->K0()V

    iget-boolean v3, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->z:Z

    const-string v4, "LongVodPlayerView"

    if-nez v3, :cond_2

    sget-object v0, Lxi/b;->a:Lxi/b$a;

    const-string v3, "----onPrepare2PlayVideo, pause video"

    invoke-virtual {v0, v4, v3, v2}, Lxi/b$a;->c(Ljava/lang/String;Ljava/lang/String;Z)V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e:Lcom/transsion/player/ui/longvideo/a;

    invoke-interface {v0}, Lcom/transsion/player/orplayer/f;->pause()V

    invoke-direct {p0, v1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->o1(Z)V

    return-void

    :cond_2
    if-eqz v0, :cond_3

    iget-boolean v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->x:Z

    if-eqz v0, :cond_3

    sget-object v0, Lxi/b;->a:Lxi/b$a;

    const-string v1, "----onPrepare2PlayVideo, showMobileDataTips pause"

    invoke-virtual {v0, v4, v1, v2}, Lxi/b$a;->c(Ljava/lang/String;Ljava/lang/String;Z)V

    goto :goto_1

    :cond_3
    invoke-virtual {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->R()V

    sget-object v0, Lxi/b;->a:Lxi/b$a;

    const-string v1, "onPrepare2PlayVideo, play"

    invoke-virtual {v0, v4, v1, v2}, Lxi/b$a;->c(Ljava/lang/String;Ljava/lang/String;Z)V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e:Lcom/transsion/player/ui/longvideo/a;

    invoke-interface {v0}, Lcom/transsion/player/orplayer/f;->play()V

    :goto_1
    return-void
.end method

.method public final F0(Lno/b;)V
    .locals 8

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->R0:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    const/4 v2, 0x0

    if-eqz v1, :cond_1

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lno/b;

    invoke-virtual {v1}, Lno/b;->c()Z

    move-result v3

    if-eqz v3, :cond_0

    invoke-virtual {v1, v2}, Lno/b;->d(Z)V

    :cond_1
    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->R0:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    const/4 v1, 0x0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    const/4 v4, 0x1

    if-eqz v3, :cond_3

    add-int/lit8 v3, v1, 0x1

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lno/b;

    invoke-virtual {p1}, Lno/b;->b()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v5}, Lno/b;->b()Ljava/lang/String;

    move-result-object v7

    invoke-static {v6, v7}, Lkotlin/jvm/internal/Intrinsics;->b(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v6

    if-eqz v6, :cond_2

    invoke-virtual {v5, v4}, Lno/b;->d(Z)V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->m:Loo/f;

    iget-object v0, v0, Loo/f;->u:Landroid/widget/TextView;

    invoke-virtual {v5}, Lno/b;->b()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    move v2, v1

    goto :goto_1

    :cond_2
    move v1, v3

    goto :goto_0

    :cond_3
    :goto_1
    sget-object v0, Lxi/b;->a:Lxi/b$a;

    invoke-virtual {p1}, Lno/b;->b()Ljava/lang/String;

    move-result-object v1

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v5, "onResolutionChange, content = "

    invoke-virtual {v3, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, ", selectedIndex = "

    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    const-string v3, "LongVodPlayerView"

    invoke-virtual {v0, v3, v1, v4}, Lxi/b$a;->c(Ljava/lang/String;Ljava/lang/String;Z)V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->G:Lno/a;

    if-eqz v0, :cond_7

    iget-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->S:Lcom/transsion/postdetail/layer/local/n;

    if-nez v1, :cond_4

    goto :goto_2

    :cond_4
    invoke-virtual {p1}, Lno/b;->b()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v1, v3}, Lcom/transsion/postdetail/layer/local/n;->I(Ljava/lang/String;)V

    :goto_2
    invoke-virtual {v0}, Lno/a;->u()Z

    move-result v1

    if-eqz v1, :cond_6

    iget-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->H0:Lcom/transsion/player/longvideo/helper/h;

    invoke-virtual {v1, v0, p1}, Lcom/transsion/player/longvideo/helper/h;->d(Lno/a;Lno/b;)Lno/c;

    move-result-object p1

    if-eqz p1, :cond_7

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->H0:Lcom/transsion/player/longvideo/helper/h;

    invoke-virtual {p1}, Lno/c;->c()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/transsion/player/longvideo/helper/h;->f(Ljava/lang/String;)V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->G:Lno/a;

    if-nez v0, :cond_5

    goto :goto_3

    :cond_5
    iget-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e:Lcom/transsion/player/ui/longvideo/a;

    invoke-interface {v1}, Lcom/transsion/player/orplayer/f;->getCurrentPosition()J

    move-result-wide v1

    invoke-virtual {v0, v1, v2}, Lno/a;->v(J)V

    :goto_3
    iput-boolean v4, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->B:Z

    invoke-direct {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->V0()V

    invoke-direct {p0, p1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->setPlayerDataSourceAdPrepare(Lno/c;)V

    goto :goto_4

    :cond_6
    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->O0:Lqo/d;

    if-eqz p1, :cond_7

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e:Lcom/transsion/player/ui/longvideo/a;

    invoke-interface {v0, p1, v2}, Lcom/transsion/player/orplayer/f;->changeTrackSelection(Lqo/d;I)V

    :cond_7
    :goto_4
    return-void
.end method

.method public final G0()V
    .locals 14

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->o:Loo/d;

    invoke-virtual {v0}, Loo/d;->b()Landroidx/constraintlayout/widget/ConstraintLayout;

    move-result-object v0

    const-string v1, "viewBinding.layoutTopToolBar.root"

    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {v0}, Landroid/view/View;->getVisibility()I

    move-result v0

    const/4 v1, 0x1

    if-nez v0, :cond_0

    sget-object v2, Lxi/b;->a:Lxi/b$a;

    const-string v3, "LongVodPlayerView"

    const-string v4, "onSingleTap hideBottomController "

    const/4 v5, 0x0

    const/4 v6, 0x4

    const/4 v7, 0x0

    invoke-static/range {v2 .. v7}, Lxi/b$a;->f(Lxi/b$a;Ljava/lang/String;Ljava/lang/String;ZILjava/lang/Object;)V

    invoke-virtual {p0, v1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->hideBottomController(Z)V

    goto :goto_0

    :cond_0
    sget-object v8, Lxi/b;->a:Lxi/b$a;

    const-string v9, "LongVodPlayerView"

    const-string v10, "onSingleTap showBottomController "

    const/4 v11, 0x0

    const/4 v12, 0x4

    const/4 v13, 0x0

    invoke-static/range {v8 .. v13}, Lxi/b$a;->f(Lxi/b$a;Ljava/lang/String;Ljava/lang/String;ZILjava/lang/Object;)V

    const/4 v0, 0x0

    const/4 v2, 0x0

    invoke-static {p0, v0, v1, v2}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->d1(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;ZILjava/lang/Object;)V

    :goto_0
    return-void
.end method

.method public final H0(Lno/b;)V
    .locals 1

    invoke-virtual {p1}, Lno/b;->b()Ljava/lang/String;

    move-result-object p1

    invoke-static {p1}, Lkotlin/text/StringsKt;->j(Ljava/lang/String;)Ljava/lang/Float;

    move-result-object p1

    if-eqz p1, :cond_0

    invoke-virtual {p1}, Ljava/lang/Number;->floatValue()F

    move-result p1

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e:Lcom/transsion/player/ui/longvideo/a;

    invoke-interface {v0, p1}, Lcom/transsion/player/orplayer/f;->setSpeed(F)V

    :cond_0
    return-void
.end method

.method public final I0()V
    .locals 2

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->U:Lmo/a$b;

    if-eqz v0, :cond_0

    iget-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->G:Lno/a;

    invoke-interface {v0, v1}, Lmo/a$b;->g(Lno/a;)V

    :cond_0
    return-void
.end method

.method public final J()V
    .locals 3

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e:Lcom/transsion/player/ui/longvideo/a;

    invoke-interface {v0}, Lcom/transsion/player/orplayer/f;->isPlaying()Z

    move-result v0

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e:Lcom/transsion/player/ui/longvideo/a;

    invoke-interface {v0}, Lcom/transsion/player/orplayer/f;->isComplete()Z

    move-result v0

    if-nez v0, :cond_0

    invoke-direct {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->x0()Z

    move-result v0

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e:Lcom/transsion/player/ui/longvideo/a;

    invoke-interface {v0}, Lcom/transsion/player/orplayer/f;->play()V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->J:Landroid/view/View;

    if-eqz v0, :cond_0

    invoke-static {v0}, Lkotlin/jvm/internal/Intrinsics;->d(Ljava/lang/Object;)V

    invoke-static {v0}, Lvi/c;->i(Landroid/view/View;)Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->J:Landroid/view/View;

    if-eqz v0, :cond_0

    invoke-static {v0}, Lvi/c;->g(Landroid/view/View;)V

    :cond_0
    sget-object v0, Lcom/transsion/videofloat/a;->a:Lcom/transsion/videofloat/a;

    invoke-virtual {v0}, Lcom/transsion/videofloat/a;->a()V

    sget-object v0, Lcom/transsion/player/longvideo/ui/LongVodUiType;->LAND:Lcom/transsion/player/longvideo/ui/LongVodUiType;

    iput-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->j:Lcom/transsion/player/longvideo/ui/LongVodUiType;

    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    const-string v1, "null cannot be cast to non-null type androidx.fragment.app.FragmentActivity"

    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/String;)V

    check-cast v0, Landroidx/fragment/app/FragmentActivity;

    invoke-static {v0}, Lcom/gyf/immersionbar/ImmersionBar;->with(Landroid/app/Activity;)Lcom/gyf/immersionbar/ImmersionBar;

    move-result-object v1

    sget-object v2, Lcom/gyf/immersionbar/BarHide;->FLAG_HIDE_BAR:Lcom/gyf/immersionbar/BarHide;

    invoke-virtual {v1, v2}, Lcom/gyf/immersionbar/ImmersionBar;->hideBar(Lcom/gyf/immersionbar/BarHide;)Lcom/gyf/immersionbar/ImmersionBar;

    move-result-object v1

    invoke-virtual {v1}, Lcom/gyf/immersionbar/ImmersionBar;->init()V

    const/4 v1, 0x6

    invoke-virtual {v0, v1}, Landroid/app/Activity;->setRequestedOrientation(I)V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->v:Landroid/widget/ImageView;

    const/4 v1, 0x0

    invoke-virtual {v0, v1, v1, v1, v1}, Landroid/view/View;->setPadding(IIII)V

    const/4 v0, 0x1

    iget-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->j:Lcom/transsion/player/longvideo/ui/LongVodUiType;

    invoke-virtual {p0, v0, v1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->Y0(ZLcom/transsion/player/longvideo/ui/LongVodUiType;)V

    return-void
.end method

.method public final K(Lno/c;Ljava/lang/String;)Landroid/content/Intent;
    .locals 1

    new-instance p1, Landroid/content/Intent;

    invoke-direct {p1}, Landroid/content/Intent;-><init>()V

    const-string p2, "path"

    const-string v0, "/playvideo/music_detail"

    invoke-virtual {p1, p2, v0}, Landroid/content/Intent;->putExtra(Ljava/lang/String;Ljava/lang/String;)Landroid/content/Intent;

    iget-object p2, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->G:Lno/a;

    if-eqz p2, :cond_0

    invoke-virtual {p2}, Lno/a;->n()Ljava/lang/String;

    move-result-object p2

    goto :goto_0

    :cond_0
    const/4 p2, 0x0

    :goto_0
    const-string v0, "id"

    invoke-virtual {p1, v0, p2}, Landroid/content/Intent;->putExtra(Ljava/lang/String;Ljava/lang/String;)Landroid/content/Intent;

    const-string p2, "isMusicLikedFragment"

    iget-boolean v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->g1:Z

    invoke-virtual {p1, p2, v0}, Landroid/content/Intent;->putExtra(Ljava/lang/String;Z)Landroid/content/Intent;

    return-object p1
.end method

.method public final K0()V
    .locals 6

    iget-wide v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->o:J

    const-wide/16 v2, 0x0

    cmp-long v4, v0, v2

    if-lez v4, :cond_0

    goto :goto_0

    :cond_0
    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->G:Lno/a;

    if-eqz v0, :cond_1

    invoke-virtual {v0}, Lno/a;->j()J

    move-result-wide v0

    goto :goto_0

    :cond_1
    move-wide v0, v2

    :goto_0
    const-wide/16 v2, 0x3e8

    cmp-long v4, v0, v2

    if-ltz v4, :cond_2

    sget-object v2, Lxi/b;->a:Lxi/b$a;

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "prepareSeekTo,progress = "

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v0, v1}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    const/4 v4, 0x1

    const-string v5, "LongVodPlayerView"

    invoke-virtual {v2, v5, v3, v4}, Lxi/b$a;->c(Ljava/lang/String;Ljava/lang/String;Z)V

    iget-object v2, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e:Lcom/transsion/player/ui/longvideo/a;

    invoke-interface {v2, v0, v1}, Lcom/transsion/player/orplayer/f;->seekTo(J)V

    invoke-direct {p0, v0, v1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->t1(J)V

    iget-boolean v2, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->w:Z

    if-nez v2, :cond_2

    iget-boolean v2, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->B:Z

    if-nez v2, :cond_2

    invoke-static {}, Lcom/blankj/utilcode/util/Utils;->a()Landroid/app/Application;

    move-result-object v2

    sget v3, Lcom/transsion/player/longvideo/R$string;->long_vod_last_played_time:I

    invoke-virtual {v2, v3}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    move-result-object v2

    invoke-static {v0, v1}, Lcom/transsion/postdetail/util/f;->d(J)Ljava/lang/String;

    move-result-object v0

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v2, " "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-direct {p0, v0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->n1(Ljava/lang/String;)V

    :cond_2
    return-void
.end method

.method public final L(Lno/c;)Lcom/transsion/player/mediasession/MediaItem;
    .locals 21

    move-object/from16 v0, p0

    iget-object v1, v0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->g:Lcom/transsion/player/longvideo/constants/LongVodPageType;

    sget-object v2, Lcom/transsion/player/longvideo/constants/LongVodPageType;->MUSIC:Lcom/transsion/player/longvideo/constants/LongVodPageType;

    const/4 v3, 0x0

    if-eq v1, v2, :cond_0

    return-object v3

    :cond_0
    iget-object v1, v0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->G:Lno/a;

    if-eqz v1, :cond_1

    invoke-virtual {v1}, Lno/a;->s()Ljava/lang/String;

    move-result-object v1

    move-object v5, v1

    goto :goto_0

    :cond_1
    move-object v5, v3

    :goto_0
    iget-object v1, v0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->G:Lno/a;

    if-eqz v1, :cond_2

    invoke-virtual {v1}, Lno/a;->n()Ljava/lang/String;

    move-result-object v1

    move-object v13, v1

    goto :goto_1

    :cond_2
    move-object v13, v3

    :goto_1
    iget-object v1, v0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->G:Lno/a;

    if-eqz v1, :cond_3

    invoke-virtual {v1}, Lno/a;->c()Ljava/lang/String;

    move-result-object v1

    move-object v7, v1

    goto :goto_2

    :cond_3
    move-object v7, v3

    :goto_2
    invoke-virtual/range {p0 .. p1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->M(Lno/c;)Landroid/app/PendingIntent;

    move-result-object v11

    const-string v1, "media_music_float_notification"

    move-object/from16 v4, p1

    invoke-virtual {v0, v4, v1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->K(Lno/c;Ljava/lang/String;)Landroid/content/Intent;

    move-result-object v12

    sget-object v1, Lcom/transsion/baseui/music/MusicFloatManager;->g:Lcom/transsion/baseui/music/MusicFloatManager$a;

    invoke-virtual {v1}, Lcom/transsion/baseui/music/MusicFloatManager$a;->b()Lcom/transsion/baseui/music/MusicFloatManager;

    move-result-object v1

    iget-object v4, v0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->G:Lno/a;

    if-eqz v4, :cond_4

    invoke-virtual {v4}, Lno/a;->n()Ljava/lang/String;

    move-result-object v4

    goto :goto_3

    :cond_4
    move-object v4, v3

    :goto_3
    invoke-virtual {v1, v4}, Lcom/transsion/baseui/music/MusicFloatManager;->n(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v14

    iget-object v1, v0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->g:Lcom/transsion/player/longvideo/constants/LongVodPageType;

    if-ne v1, v2, :cond_5

    const/4 v1, 0x1

    goto :goto_4

    :cond_5
    const/4 v1, 0x0

    :goto_4
    iget-object v2, v0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->i:Ljava/lang/String;

    iget-object v4, v0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->G:Lno/a;

    if-eqz v4, :cond_6

    invoke-virtual {v4}, Lno/a;->p()Ljava/lang/String;

    move-result-object v3

    :cond_6
    move-object/from16 v18, v3

    new-instance v3, Lcom/transsion/player/mediasession/MediaItem;

    move-object v4, v3

    const-string v6, ""

    const/4 v8, 0x0

    const/4 v9, 0x0

    const/4 v10, 0x0

    invoke-static {v1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v15

    const/16 v17, 0x0

    const/16 v19, 0x1038

    const/16 v20, 0x0

    move-object/from16 v16, v2

    invoke-direct/range {v4 .. v20}, Lcom/transsion/player/mediasession/MediaItem;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Long;Ljava/lang/Long;Ljava/lang/String;Landroid/app/PendingIntent;Landroid/content/Intent;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Boolean;Ljava/lang/String;Ljava/lang/Boolean;Ljava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-object v3
.end method

.method public final L0()V
    .locals 2

    sget-object v0, Lcom/transsion/baselib/report/RoomActivityLifecycleCallbacks;->a:Lcom/transsion/baselib/report/RoomActivityLifecycleCallbacks;

    invoke-virtual {v0, p0}, Lcom/transsion/baselib/report/RoomActivityLifecycleCallbacks;->b(Lcom/transsion/baselib/report/RoomActivityLifecycleCallbacks$a;)Z

    sget-object v0, Lcom/tn/lib/util/networkinfo/f;->a:Lcom/tn/lib/util/networkinfo/f;

    iget-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e1:Lcom/transsion/player/longvideo/ui/LongVodPlayerView$f;

    invoke-virtual {v0, v1}, Lcom/tn/lib/util/networkinfo/f;->l(Lcom/tn/lib/util/networkinfo/g;)V

    return-void
.end method

.method public final M(Lno/c;)Landroid/app/PendingIntent;
    .locals 6

    invoke-static {}, Lcom/alibaba/android/arouter/launcher/a;->d()Lcom/alibaba/android/arouter/launcher/a;

    move-result-object v0

    const-class v1, Lcom/transsion/push/api/IPushProvider;

    invoke-virtual {v0, v1}, Lcom/alibaba/android/arouter/launcher/a;->h(Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/transsion/push/api/IPushProvider;

    new-instance v1, Ljava/lang/StringBuilder;

    const-string v2, "oneroom://com.community.oneroom?type="

    invoke-direct {v1, v2}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    const-string v2, "/movie/detail"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v2, "&"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v3, "extra_resource_id"

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v3, "="

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Lno/c;->b()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string p1, "subject_type"

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->G:Lno/a;

    const/4 v4, 0x0

    if-eqz p1, :cond_0

    invoke-virtual {p1}, Lno/a;->q()Ljava/lang/Integer;

    move-result-object p1

    goto :goto_0

    :cond_0
    move-object p1, v4

    :goto_0
    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string p1, "season"

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->G:Lno/a;

    const/4 v5, 0x0

    if-eqz p1, :cond_1

    invoke-virtual {p1}, Lno/a;->k()I

    move-result p1

    goto :goto_1

    :cond_1
    const/4 p1, 0x0

    :goto_1
    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string p1, "episode"

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->G:Lno/a;

    if-eqz p1, :cond_2

    invoke-virtual {p1}, Lno/a;->d()I

    move-result p1

    goto :goto_2

    :cond_2
    const/4 p1, 0x0

    :goto_2
    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string p1, "id"

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->G:Lno/a;

    if-eqz p1, :cond_3

    invoke-virtual {p1}, Lno/a;->n()Ljava/lang/String;

    move-result-object p1

    goto :goto_3

    :cond_3
    move-object p1, v4

    :goto_3
    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string p1, "ops"

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->G:Lno/a;

    if-eqz p1, :cond_4

    invoke-virtual {p1}, Lno/a;->p()Ljava/lang/String;

    move-result-object v4

    :cond_4
    invoke-virtual {v1, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string p1, "extra_page_from"

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->i:Ljava/lang/String;

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string p1, "is_music_liked_fragment"

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-boolean p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->g1:Z

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-static {}, Lcom/blankj/utilcode/util/Utils;->a()Landroid/app/Application;

    move-result-object p1

    const-string v2, "getApp()"

    invoke-static {p1, v2}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-interface {v0, p1}, Lcom/transsion/push/api/IPushProvider;->f1(Landroid/content/Context;)Landroid/content/Intent;

    move-result-object p1

    const/high16 v0, 0x24000000

    invoke-virtual {p1, v0}, Landroid/content/Intent;->addFlags(I)Landroid/content/Intent;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Landroid/net/Uri;->parse(Ljava/lang/String;)Landroid/net/Uri;

    move-result-object v0

    invoke-virtual {p1, v0}, Landroid/content/Intent;->setData(Landroid/net/Uri;)Landroid/content/Intent;

    invoke-static {}, Lcom/blankj/utilcode/util/Utils;->a()Landroid/app/Application;

    move-result-object v0

    invoke-direct {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->getPendingIntentFlag()I

    move-result v1

    invoke-static {v0, v5, p1, v1}, Landroid/app/PendingIntent;->getActivity(Landroid/content/Context;ILandroid/content/Intent;I)Landroid/app/PendingIntent;

    move-result-object p1

    return-object p1
.end method

.method public final N()V
    .locals 21

    move-object/from16 v0, p0

    iget-boolean v1, v0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->y:Z

    const-string v2, "LongVodPlayerView"

    const/4 v3, 0x1

    if-eqz v1, :cond_0

    sget-object v1, Lxi/b;->a:Lxi/b$a;

    const-string v4, "onPrepare2PlayVideo, is mobile , been shown~"

    invoke-virtual {v1, v2, v4, v3}, Lxi/b$a;->c(Ljava/lang/String;Ljava/lang/String;Z)V

    return-void

    :cond_0
    sget-object v1, Lxi/b;->a:Lxi/b$a;

    const-string v4, "onPrepare2PlayVideo, is mobile , pause video"

    invoke-virtual {v1, v2, v4, v3}, Lxi/b$a;->c(Ljava/lang/String;Ljava/lang/String;Z)V

    iput-boolean v3, v0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->x:Z

    invoke-virtual/range {p0 .. p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->P()V

    iget-object v1, v0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->K:Landroid/view/View;

    if-nez v1, :cond_1

    iget-object v1, v0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v1, v1, Loo/e;->x:Landroid/view/ViewStub;

    invoke-virtual {v1}, Landroid/view/ViewStub;->inflate()Landroid/view/View;

    move-result-object v1

    iput-object v1, v0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->K:Landroid/view/View;

    :cond_1
    iget-object v1, v0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->K:Landroid/view/View;

    if-eqz v1, :cond_2

    invoke-static {v1}, Lvi/c;->k(Landroid/view/View;)V

    :cond_2
    iget-object v1, v0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->K:Landroid/view/View;

    if-eqz v1, :cond_9

    sget v2, Lcom/transsion/player/longvideo/R$id;->or_long_vod_iv_mobile_data_bg:I

    invoke-virtual {v1, v2}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    move-result-object v2

    move-object v5, v2

    check-cast v5, Landroid/widget/ImageView;

    iget-object v2, v0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->G:Lno/a;

    const-string v3, ""

    if-eqz v2, :cond_4

    invoke-virtual {v2}, Lno/a;->l()Lcom/transsion/moviedetailapi/bean/Subject;

    move-result-object v2

    if-eqz v2, :cond_4

    invoke-virtual {v2}, Lcom/transsion/moviedetailapi/bean/Subject;->getTrailer()Lcom/transsion/moviedetailapi/bean/Trailer;

    move-result-object v2

    if-eqz v2, :cond_4

    invoke-virtual {v2}, Lcom/transsion/moviedetailapi/bean/Trailer;->getCover()Lcom/transsion/moviedetailapi/bean/Cover;

    move-result-object v2

    if-eqz v2, :cond_4

    invoke-virtual {v2}, Lcom/transsion/moviedetailapi/bean/Cover;->getUrl()Ljava/lang/String;

    move-result-object v2

    if-nez v2, :cond_3

    goto :goto_1

    :cond_3
    :goto_0
    move-object v6, v2

    goto :goto_2

    :cond_4
    :goto_1
    iget-object v2, v0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->G:Lno/a;

    if-eqz v2, :cond_5

    invoke-virtual {v2}, Lno/a;->c()Ljava/lang/String;

    move-result-object v2

    goto :goto_0

    :cond_5
    move-object v6, v3

    :goto_2
    iget-object v2, v0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->G:Lno/a;

    if-eqz v2, :cond_7

    invoke-virtual {v2}, Lno/a;->l()Lcom/transsion/moviedetailapi/bean/Subject;

    move-result-object v2

    if-eqz v2, :cond_7

    invoke-virtual {v2}, Lcom/transsion/moviedetailapi/bean/Subject;->getTrailer()Lcom/transsion/moviedetailapi/bean/Trailer;

    move-result-object v2

    if-eqz v2, :cond_7

    invoke-virtual {v2}, Lcom/transsion/moviedetailapi/bean/Trailer;->getCover()Lcom/transsion/moviedetailapi/bean/Cover;

    move-result-object v2

    if-eqz v2, :cond_7

    invoke-virtual {v2}, Lcom/transsion/moviedetailapi/bean/Cover;->getThumbnail()Ljava/lang/String;

    move-result-object v2

    if-nez v2, :cond_6

    goto :goto_4

    :cond_6
    :goto_3
    move-object v12, v2

    goto :goto_5

    :cond_7
    :goto_4
    iget-object v2, v0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->G:Lno/a;

    if-eqz v2, :cond_8

    invoke-virtual {v2}, Lno/a;->r()Ljava/lang/String;

    move-result-object v2

    goto :goto_3

    :cond_8
    move-object v12, v3

    :goto_5
    sget-object v3, Lcom/transsion/baseui/image/ImageHelper;->a:Lcom/transsion/baseui/image/ImageHelper$Companion;

    invoke-virtual {v1}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v2

    move-object v4, v2

    const-string v7, "context"

    invoke-static {v2, v7}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    const-string v2, "bgView"

    invoke-static {v5, v2}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    const/4 v7, 0x0

    const/4 v8, 0x0

    const/4 v9, 0x0

    const/4 v10, 0x0

    const/4 v11, 0x0

    const/4 v13, 0x0

    const/4 v14, 0x0

    const/4 v15, 0x0

    const/16 v16, 0x0

    const/16 v17, 0x0

    const/16 v18, 0x0

    const/16 v19, 0x7ef8

    const/16 v20, 0x0

    invoke-static/range {v3 .. v20}, Lcom/transsion/baseui/image/ImageHelper$Companion;->r(Lcom/transsion/baseui/image/ImageHelper$Companion;Landroid/content/Context;Landroid/widget/ImageView;Ljava/lang/String;IIIIZLjava/lang/String;ZZZZZIILjava/lang/Object;)V

    sget v2, Lcom/transsion/player/longvideo/R$id;->or_long_vod_mobile_btn:I

    invoke-virtual {v1, v2}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    move-result-object v1

    check-cast v1, Landroid/widget/TextView;

    new-instance v2, Lcom/transsion/player/longvideo/ui/o;

    invoke-direct {v2, v0}, Lcom/transsion/player/longvideo/ui/o;-><init>(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)V

    invoke-virtual {v1, v2}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    :cond_9
    return-void
.end method

.method public final O0()V
    .locals 1

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->I:Landroid/view/View;

    if-eqz v0, :cond_0

    invoke-static {v0}, Lvi/c;->g(Landroid/view/View;)V

    :cond_0
    return-void
.end method

.method public final P()V
    .locals 4

    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->u:Z

    sget-object v1, Lxi/b;->a:Lxi/b$a;

    const-string v2, "LongVodPlayerView"

    const-string v3, "----handlePause, pause video"

    invoke-virtual {v1, v2, v3, v0}, Lxi/b$a;->c(Ljava/lang/String;Ljava/lang/String;Z)V

    const/4 v0, 0x0

    invoke-direct {p0, v0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->setPauseViewStatus(Z)V

    iget-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e:Lcom/transsion/player/ui/longvideo/a;

    invoke-interface {v1}, Lcom/transsion/player/orplayer/f;->pause()V

    iget-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->j:Lcom/transsion/player/longvideo/ui/LongVodUiType;

    sget-object v2, Lcom/transsion/player/longvideo/ui/LongVodUiType;->MIDDLE:Lcom/transsion/player/longvideo/ui/LongVodUiType;

    if-ne v1, v2, :cond_0

    iget-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->J0:Lcom/transsion/player/longvideo/helper/a;

    if-eqz v1, :cond_0

    invoke-virtual {v1}, Lcom/transsion/player/longvideo/helper/a;->g()V

    :cond_0
    invoke-direct {p0, v0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->o1(Z)V

    iget-boolean v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->x:Z

    if-nez v0, :cond_1

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->b:Lcom/transsion/player/longvideo/ui/dialog/LongVodPlayerViewAdHelper;

    invoke-virtual {v0}, Lcom/transsion/player/longvideo/ui/dialog/LongVodPlayerViewAdHelper;->K()V

    const/high16 v0, 0x43480000    # 200.0f

    invoke-static {v0}, Lql/a;->a(F)I

    move-result v0

    invoke-virtual {p0, v0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->D(I)V

    :cond_1
    return-void
.end method

.method public final P0()V
    .locals 2

    invoke-virtual {p0}, Landroid/view/View;->getParent()Landroid/view/ViewParent;

    move-result-object v0

    if-eqz v0, :cond_1

    invoke-virtual {p0}, Landroid/view/View;->getParent()Landroid/view/ViewParent;

    move-result-object v0

    instance-of v1, v0, Landroid/view/ViewGroup;

    if-eqz v1, :cond_0

    check-cast v0, Landroid/view/ViewGroup;

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    if-eqz v0, :cond_1

    invoke-virtual {v0, p0}, Landroid/view/ViewGroup;->removeView(Landroid/view/View;)V

    :cond_1
    return-void
.end method

.method public final R()V
    .locals 2

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->G:Lno/a;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lno/a;->i()Ljava/util/List;

    move-result-object v0

    if-eqz v0, :cond_0

    invoke-static {v0}, Lkotlin/collections/CollectionsKt;->b0(Ljava/util/List;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lno/c;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lno/c;->g()Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->p:Landroidx/appcompat/widget/AppCompatImageView;

    const-string v1, "viewBinding.orLongVodIvBg"

    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-static {v0}, Lvi/c;->g(Landroid/view/View;)V

    return-void
.end method

.method public final R0()V
    .locals 4

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v1, v0, Loo/e;->m:Loo/f;

    iget-object v1, v1, Loo/f;->y:Landroidx/appcompat/widget/AppCompatTextView;

    const-string v2, "it.layoutLand.tvPlayNext"

    invoke-static {v1, v2}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {v1}, Landroid/view/View;->getVisibility()I

    move-result v1

    if-nez v1, :cond_1

    iget-object v0, v0, Loo/e;->m:Loo/f;

    invoke-virtual {v0}, Loo/f;->b()Landroidx/constraintlayout/widget/ConstraintLayout;

    move-result-object v0

    const-string v1, "it.layoutLand.root"

    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {v0}, Landroid/view/View;->getVisibility()I

    move-result v0

    if-nez v0, :cond_1

    sget-object v0, Lcom/transsion/baselib/report/l;->a:Lcom/transsion/baselib/report/l;

    iget-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->i:Ljava/lang/String;

    if-nez v1, :cond_0

    const-string v1, ""

    :cond_0
    const-string v2, "browse"

    invoke-direct {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->getReportNextMap()Ljava/util/Map;

    move-result-object v3

    invoke-virtual {v0, v1, v2, v3}, Lcom/transsion/baselib/report/l;->q(Ljava/lang/String;Ljava/lang/String;Ljava/util/Map;)V

    :cond_1
    return-void
.end method

.method public final S0(Ljava/lang/String;)V
    .locals 6

    sget-object v0, Lcom/transsion/baselib/report/l;->a:Lcom/transsion/baselib/report/l;

    iget-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->i:Ljava/lang/String;

    if-nez v1, :cond_0

    const-string v1, ""

    :cond_0
    const/4 v2, 0x3

    new-array v2, v2, [Lkotlin/Pair;

    new-instance v3, Lkotlin/Pair;

    const-string v4, "module_name"

    invoke-direct {v3, v4, p1}, Lkotlin/Pair;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    const/4 p1, 0x0

    aput-object v3, v2, p1

    new-instance p1, Lkotlin/Pair;

    iget-object v3, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->G:Lno/a;

    const/4 v4, 0x0

    if-eqz v3, :cond_1

    invoke-virtual {v3}, Lno/a;->n()Ljava/lang/String;

    move-result-object v3

    goto :goto_0

    :cond_1
    move-object v3, v4

    :goto_0
    const-string v5, "subject_id"

    invoke-direct {p1, v5, v3}, Lkotlin/Pair;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    const/4 v3, 0x1

    aput-object p1, v2, v3

    new-instance p1, Lkotlin/Pair;

    iget-object v3, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->G:Lno/a;

    if-eqz v3, :cond_2

    invoke-virtual {v3}, Lno/a;->i()Ljava/util/List;

    move-result-object v3

    if-eqz v3, :cond_2

    invoke-static {v3}, Lkotlin/collections/CollectionsKt;->b0(Ljava/util/List;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lno/c;

    if-eqz v3, :cond_2

    invoke-virtual {v3}, Lno/c;->b()Ljava/lang/String;

    move-result-object v4

    :cond_2
    const-string v3, "resource_id"

    invoke-direct {p1, v3, v4}, Lkotlin/Pair;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    const/4 v3, 0x2

    aput-object p1, v2, v3

    invoke-static {v2}, Lkotlin/collections/MapsKt;->k([Lkotlin/Pair;)Ljava/util/Map;

    move-result-object p1

    const-string v2, "click"

    invoke-virtual {v0, v1, v2, p1}, Lcom/transsion/baselib/report/l;->l(Ljava/lang/String;Ljava/lang/String;Ljava/util/Map;)V

    return-void
.end method

.method public final T0(Lno/a;Lno/c;)V
    .locals 73

    move-object/from16 v0, p0

    new-instance v15, Lcom/transsion/baselib/db/download/DownloadBean;

    move-object v1, v15

    invoke-virtual/range {p2 .. p2}, Lno/c;->f()Ljava/lang/String;

    move-result-object v2

    invoke-virtual/range {p2 .. p2}, Lno/c;->b()Ljava/lang/String;

    move-result-object v3

    invoke-virtual/range {p1 .. p1}, Lno/a;->o()Ljava/lang/String;

    move-result-object v4

    const-string v5, ""

    invoke-virtual/range {p2 .. p2}, Lno/c;->e()Ljava/lang/String;

    move-result-object v6

    invoke-static {v6}, Lkotlin/text/StringsKt;->m(Ljava/lang/String;)Ljava/lang/Long;

    move-result-object v6

    const-string v7, ""

    const/4 v8, 0x0

    const/4 v9, 0x0

    const/4 v10, 0x0

    const/4 v11, 0x0

    const-wide/16 v12, 0x0

    const/4 v14, 0x0

    const/16 v16, 0x0

    move-object/from16 v72, v15

    move/from16 v15, v16

    const-wide/16 v17, 0x0

    const-wide/16 v19, 0x0

    const/16 v21, 0x0

    const/16 v22, 0x0

    const/16 v23, 0x0

    const/16 v24, 0x0

    const/16 v25, 0x0

    const/16 v26, 0x0

    const/16 v27, 0x0

    const/16 v28, 0x0

    const/16 v29, 0x0

    const/16 v30, 0x0

    const/16 v31, 0x0

    const/16 v32, 0x0

    const/16 v33, 0x0

    const/16 v34, 0x0

    const/16 v35, 0x0

    const-wide/16 v36, 0x0

    const/16 v38, 0x0

    const/16 v39, 0x0

    const/16 v40, 0x0

    const/16 v41, 0x0

    const-wide/16 v42, 0x0

    const/16 v44, 0x0

    const/16 v45, 0x0

    const-wide/16 v46, 0x0

    const/16 v48, 0x0

    const-wide/16 v49, 0x0

    const/16 v51, 0x0

    const/16 v52, 0x0

    const/16 v53, 0x0

    const/16 v54, 0x0

    const/16 v55, 0x0

    const/16 v56, 0x0

    const/16 v57, 0x0

    const/16 v58, 0x0

    const/16 v59, 0x0

    const/16 v60, 0x0

    const/16 v61, 0x0

    const/16 v62, 0x0

    const/16 v63, 0x0

    const/16 v64, 0x0

    const/16 v65, 0x0

    const/16 v66, 0x0

    const/16 v67, 0x0

    const/16 v68, 0x0

    const/16 v69, -0x40

    const v70, 0xfffffff

    const/16 v71, 0x0

    invoke-direct/range {v1 .. v71}, Lcom/transsion/baselib/db/download/DownloadBean;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Long;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Long;Ljava/lang/Long;JIIIJJIIIIIIIIIIILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;JIILjava/lang/String;Ljava/lang/String;JLjava/lang/String;Ljava/lang/String;JIJLjava/lang/String;ILjava/lang/String;Ljava/lang/String;ZZILjava/lang/String;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    invoke-virtual/range {p1 .. p1}, Lno/a;->n()Ljava/lang/String;

    move-result-object v1

    move-object/from16 v2, v72

    invoke-virtual {v2, v1}, Lcom/transsion/baselib/db/download/DownloadBean;->setSubjectId(Ljava/lang/String;)V

    invoke-virtual/range {p1 .. p1}, Lno/a;->d()I

    move-result v1

    invoke-virtual {v2, v1}, Lcom/transsion/baselib/db/download/DownloadBean;->setEp(I)V

    invoke-virtual/range {p1 .. p1}, Lno/a;->k()I

    move-result v1

    invoke-virtual {v2, v1}, Lcom/transsion/baselib/db/download/DownloadBean;->setSe(I)V

    invoke-virtual/range {p1 .. p1}, Lno/a;->p()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v2, v1}, Lcom/transsion/baselib/db/download/DownloadBean;->setOps(Ljava/lang/String;)V

    iget-object v1, v0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->S:Lcom/transsion/postdetail/layer/local/n;

    if-eqz v1, :cond_0

    invoke-virtual {v1}, Lcom/transsion/postdetail/layer/local/n;->a()V

    :cond_0
    iget-object v1, v0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->S:Lcom/transsion/postdetail/layer/local/n;

    if-eqz v1, :cond_1

    invoke-virtual/range {p1 .. p1}, Lno/a;->g()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v1, v2, v3}, Lcom/transsion/postdetail/layer/local/n;->z(Lcom/transsion/baselib/db/download/DownloadBean;Ljava/lang/String;)V

    :cond_1
    invoke-virtual/range {p1 .. p1}, Lno/a;->t()Lcom/transsion/player/enum/PlayMimeType;

    move-result-object v1

    sget-object v2, Lcom/transsion/player/enum/PlayMimeType;->DASH:Lcom/transsion/player/enum/PlayMimeType;

    if-ne v1, v2, :cond_3

    iget-object v1, v0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->S:Lcom/transsion/postdetail/layer/local/n;

    if-nez v1, :cond_2

    goto :goto_1

    :cond_2
    const-string v2, "3"

    invoke-virtual {v1, v2}, Lcom/transsion/postdetail/layer/local/n;->H(Ljava/lang/String;)V

    goto :goto_1

    :cond_3
    invoke-virtual/range {p1 .. p1}, Lno/a;->t()Lcom/transsion/player/enum/PlayMimeType;

    move-result-object v1

    sget-object v2, Lcom/transsion/player/enum/PlayMimeType;->HLS:Lcom/transsion/player/enum/PlayMimeType;

    if-ne v1, v2, :cond_6

    iget-object v1, v0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->S:Lcom/transsion/postdetail/layer/local/n;

    if-nez v1, :cond_4

    goto :goto_1

    :cond_4
    invoke-virtual/range {p1 .. p1}, Lno/a;->u()Z

    move-result v2

    if-eqz v2, :cond_5

    const-string v2, "5"

    goto :goto_0

    :cond_5
    const-string v2, "6"

    :goto_0
    invoke-virtual {v1, v2}, Lcom/transsion/postdetail/layer/local/n;->H(Ljava/lang/String;)V

    goto :goto_1

    :cond_6
    invoke-virtual/range {p2 .. p2}, Lno/c;->h()Z

    move-result v1

    if-eqz v1, :cond_8

    iget-object v1, v0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->S:Lcom/transsion/postdetail/layer/local/n;

    if-nez v1, :cond_7

    goto :goto_1

    :cond_7
    const-string v2, "4"

    invoke-virtual {v1, v2}, Lcom/transsion/postdetail/layer/local/n;->H(Ljava/lang/String;)V

    goto :goto_1

    :cond_8
    iget-object v1, v0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->S:Lcom/transsion/postdetail/layer/local/n;

    if-nez v1, :cond_9

    goto :goto_1

    :cond_9
    const-string v2, "0"

    invoke-virtual {v1, v2}, Lcom/transsion/postdetail/layer/local/n;->H(Ljava/lang/String;)V

    :goto_1
    return-void
.end method

.method public final U()V
    .locals 3

    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    const-string v1, "null cannot be cast to non-null type androidx.fragment.app.FragmentActivity"

    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/String;)V

    check-cast v0, Landroidx/fragment/app/FragmentActivity;

    new-instance v1, Lcom/transsion/player/longvideo/ui/LongVodPlayerView$b;

    invoke-direct {v1, p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView$b;-><init>(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)V

    invoke-virtual {v0}, Landroidx/activity/ComponentActivity;->getOnBackPressedDispatcher()Landroidx/activity/OnBackPressedDispatcher;

    move-result-object v2

    invoke-virtual {v2, v0, v1}, Landroidx/activity/OnBackPressedDispatcher;->i(Landroidx/lifecycle/u;Landroidx/activity/p;)V

    return-void
.end method

.method public final W0()V
    .locals 4

    new-instance v0, Lcom/transsion/player/longvideo/ui/dialog/LongVdPlayerConfigDialog;

    invoke-direct {v0}, Lcom/transsion/player/longvideo/ui/dialog/LongVdPlayerConfigDialog;-><init>()V

    iget-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->i:Ljava/lang/String;

    sget-object v2, Lcom/transsion/player/longvideo/constants/LongVodPlayerConfigType;->BITRATE:Lcom/transsion/player/longvideo/constants/LongVodPlayerConfigType;

    iget-object v3, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->R0:Ljava/util/List;

    invoke-virtual {v0, v1, v2, v3}, Lcom/transsion/player/longvideo/ui/dialog/LongVdPlayerConfigDialog;->d0(Ljava/lang/String;Lcom/transsion/player/longvideo/constants/LongVodPlayerConfigType;Ljava/util/List;)V

    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v1

    const-string v2, "LongVdPlayerConfigDialog"

    invoke-virtual {v0, v1, v2}, Lcom/transsion/baseui/dialog/BaseDialog;->showDialog(Landroid/content/Context;Ljava/lang/String;)V

    return-void
.end method

.method public final Y0(ZLcom/transsion/player/longvideo/ui/LongVodUiType;)V
    .locals 7

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->b:Lcom/transsion/player/longvideo/ui/dialog/LongVodPlayerViewAdHelper;

    invoke-virtual {v0, p2}, Lcom/transsion/player/longvideo/ui/dialog/LongVodPlayerViewAdHelper;->X(Lcom/transsion/player/longvideo/ui/LongVodUiType;)V

    sget-object v0, Lcom/transsion/player/longvideo/ui/LongVodUiType;->LAND:Lcom/transsion/player/longvideo/ui/LongVodUiType;

    const/16 v1, 0x8

    const/4 v2, 0x0

    if-ne p2, v0, :cond_0

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->o:Loo/d;

    iget-object v0, v0, Loo/d;->d:Landroidx/appcompat/widget/AppCompatTextView;

    invoke-virtual {v0, v2}, Landroid/view/View;->setVisibility(I)V

    goto :goto_0

    :cond_0
    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->o:Loo/d;

    iget-object v0, v0, Loo/d;->d:Landroidx/appcompat/widget/AppCompatTextView;

    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    :goto_0
    invoke-virtual {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->y0()Z

    move-result v0

    if-eqz v0, :cond_1

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->o:Loo/d;

    iget-object v0, v0, Loo/d;->d:Landroidx/appcompat/widget/AppCompatTextView;

    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    :cond_1
    invoke-virtual {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->s0()V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->I0:Lcom/transsion/player/longvideo/helper/e;

    if-eqz v0, :cond_2

    invoke-virtual {v0, p2}, Lcom/transsion/player/longvideo/helper/e;->h(Lcom/transsion/player/longvideo/ui/LongVodUiType;)V

    :cond_2
    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->V:Lcom/transsion/player/longvideo/helper/LongVodSubtitleHelper;

    if-eqz v0, :cond_3

    invoke-virtual {v0, p2}, Lcom/transsion/player/longvideo/helper/LongVodSubtitleHelper;->z(Lcom/transsion/player/longvideo/ui/LongVodUiType;)V

    :cond_3
    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->J0:Lcom/transsion/player/longvideo/helper/a;

    if-eqz v0, :cond_4

    invoke-virtual {v0, p2}, Lcom/transsion/player/longvideo/helper/a;->c(Lcom/transsion/player/longvideo/ui/LongVodUiType;)V

    :cond_4
    invoke-virtual {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->b1()V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->S:Lcom/transsion/postdetail/layer/local/n;

    if-eqz v0, :cond_6

    sget-object v3, Lcom/transsion/player/longvideo/ui/LongVodUiType;->MIDDLE:Lcom/transsion/player/longvideo/ui/LongVodUiType;

    if-ne p2, v3, :cond_5

    sget-object p2, Lcom/transsion/postdetail/layer/local/LocalUiType;->MIDDLE:Lcom/transsion/postdetail/layer/local/LocalUiType;

    goto :goto_1

    :cond_5
    sget-object p2, Lcom/transsion/postdetail/layer/local/LocalUiType;->LAND:Lcom/transsion/postdetail/layer/local/LocalUiType;

    :goto_1
    invoke-virtual {v0, p2}, Lcom/transsion/postdetail/layer/local/n;->k(Lcom/transsion/postdetail/layer/local/LocalUiType;)V

    :cond_6
    iget-object p2, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object p2, p2, Loo/e;->k:Landroid/view/View;

    invoke-virtual {p2}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object p2

    const-string v0, "null cannot be cast to non-null type androidx.constraintlayout.widget.ConstraintLayout.LayoutParams"

    invoke-static {p2, v0}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/String;)V

    check-cast p2, Landroidx/constraintlayout/widget/ConstraintLayout$b;

    invoke-direct {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->getOrientationEventListener()Lcom/transsion/postdetail/util/g;

    move-result-object v3

    const-string v4, "null cannot be cast to non-null type android.app.Activity"

    if-eqz v3, :cond_7

    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v5

    invoke-static {v5, v4}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/String;)V

    check-cast v5, Landroid/app/Activity;

    invoke-virtual {v3, v5}, Lcom/transsion/postdetail/util/g;->b(Landroid/app/Activity;)I

    move-result v5

    invoke-virtual {v3}, Lcom/transsion/postdetail/util/g;->a()I

    move-result v6

    if-eq v6, v5, :cond_7

    invoke-virtual {v3, v5}, Lcom/transsion/postdetail/util/g;->c(I)V

    :cond_7
    iget-object v3, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v3, v3, Loo/e;->r:Landroidx/appcompat/widget/AppCompatTextView;

    invoke-virtual {v3}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object v3

    invoke-static {v3, v0}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/String;)V

    check-cast v3, Landroidx/constraintlayout/widget/ConstraintLayout$b;

    const/4 v0, -0x1

    if-eqz p1, :cond_8

    iput v2, p2, Landroidx/constraintlayout/widget/ConstraintLayout$b;->l:I

    const-string v5, "0"

    iput-object v5, p2, Landroidx/constraintlayout/widget/ConstraintLayout$b;->I:Ljava/lang/String;

    iput v0, v3, Landroidx/constraintlayout/widget/ConstraintLayout$b;->i:I

    const/16 p2, 0x50

    invoke-static {p2}, Lql/a;->b(I)I

    move-result p2

    iput p2, v3, Landroid/view/ViewGroup$MarginLayoutParams;->bottomMargin:I

    goto :goto_2

    :cond_8
    const-string v5, "h,16:9"

    iput-object v5, p2, Landroidx/constraintlayout/widget/ConstraintLayout$b;->I:Ljava/lang/String;

    iput v0, p2, Landroidx/constraintlayout/widget/ConstraintLayout$b;->l:I

    iget-object p2, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object p2, p2, Loo/e;->k:Landroid/view/View;

    invoke-virtual {p2}, Landroid/view/View;->getId()I

    move-result p2

    iput p2, v3, Landroidx/constraintlayout/widget/ConstraintLayout$b;->i:I

    iput v2, v3, Landroid/view/ViewGroup$MarginLayoutParams;->bottomMargin:I

    :goto_2
    iget-object p2, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object p2, p2, Loo/e;->m:Loo/f;

    invoke-virtual {p2}, Loo/f;->b()Landroidx/constraintlayout/widget/ConstraintLayout;

    move-result-object p2

    const-string v3, "viewBinding.layoutLand.root"

    invoke-static {p2, v3}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    if-eqz p1, :cond_9

    const/4 v3, 0x0

    goto :goto_3

    :cond_9
    const/16 v3, 0x8

    :goto_3
    invoke-virtual {p2, v3}, Landroid/view/View;->setVisibility(I)V

    iget-object p2, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object p2, p2, Loo/e;->n:Loo/g;

    invoke-virtual {p2}, Loo/g;->b()Landroidx/constraintlayout/widget/ConstraintLayout;

    move-result-object p2

    const-string v3, "viewBinding.layoutMiddle.root"

    invoke-static {p2, v3}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    xor-int/lit8 v3, p1, 0x1

    if-eqz v3, :cond_a

    const/4 v1, 0x0

    :cond_a
    invoke-virtual {p2, v1}, Landroid/view/View;->setVisibility(I)V

    invoke-virtual {p0}, Landroid/view/View;->getRotation()F

    move-result p2

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "screenUIChange  fullScreen:"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    const-string v2, "  rotation:"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, p2}, Ljava/lang/StringBuilder;->append(F)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    const-string v1, "LongVodPlayerView"

    invoke-static {v1, p2}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    if-eqz p1, :cond_e

    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->f:Landroid/view/ViewGroup;

    if-nez p1, :cond_c

    invoke-virtual {p0}, Landroid/view/View;->getParent()Landroid/view/ViewParent;

    move-result-object p1

    instance-of p2, p1, Landroid/view/ViewGroup;

    if-eqz p2, :cond_b

    check-cast p1, Landroid/view/ViewGroup;

    goto :goto_4

    :cond_b
    const/4 p1, 0x0

    :goto_4
    iput-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->f:Landroid/view/ViewGroup;

    :cond_c
    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->L:Landroid/widget/FrameLayout;

    if-eqz p1, :cond_d

    invoke-static {p1}, Lkotlin/jvm/internal/Intrinsics;->d(Ljava/lang/Object;)V

    goto :goto_5

    :cond_d
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object p1

    invoke-static {p1, v4}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/String;)V

    check-cast p1, Landroid/app/Activity;

    const p2, 0x1020002

    invoke-virtual {p1, p2}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object p1

    check-cast p1, Landroid/view/ViewGroup;

    :goto_5
    invoke-virtual {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->P0()V

    new-instance p2, Landroid/widget/FrameLayout$LayoutParams;

    invoke-direct {p2, v0, v0}, Landroid/widget/FrameLayout$LayoutParams;-><init>(II)V

    invoke-virtual {p1, p0, p2}, Landroid/view/ViewGroup;->addView(Landroid/view/View;Landroid/view/ViewGroup$LayoutParams;)V

    goto :goto_6

    :cond_e
    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->f:Landroid/view/ViewGroup;

    if-eqz p1, :cond_f

    invoke-virtual {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->P0()V

    invoke-virtual {p1, p0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    :cond_f
    :goto_6
    return-void
.end method

.method public final Z0(Lcom/transsion/baselib/db/video/VideoDetailPlayBean;)V
    .locals 2

    new-instance v0, Lcom/transsion/quickjs/QuickJSHelper$ExecuteBuilder;

    invoke-direct {v0}, Lcom/transsion/quickjs/QuickJSHelper$ExecuteBuilder;-><init>()V

    const-string v1, "watch_info"

    invoke-virtual {v0, v1}, Lcom/transsion/quickjs/QuickJSHelper$ExecuteBuilder;->l(Ljava/lang/String;)Lcom/transsion/quickjs/QuickJSHelper$ExecuteBuilder;

    move-result-object v0

    const-string v1, "onWatch"

    invoke-virtual {v0, v1}, Lcom/transsion/quickjs/QuickJSHelper$ExecuteBuilder;->m(Ljava/lang/String;)Lcom/transsion/quickjs/QuickJSHelper$ExecuteBuilder;

    move-result-object v0

    invoke-static {p1}, Lcom/blankj/utilcode/util/n;->j(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    const-string v1, "toJson(bean)"

    invoke-static {p1, v1}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {v0, p1}, Lcom/transsion/quickjs/QuickJSHelper$ExecuteBuilder;->i(Ljava/lang/String;)Lcom/transsion/quickjs/QuickJSHelper$ExecuteBuilder;

    move-result-object p1

    new-instance v0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView$i;

    invoke-direct {v0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView$i;-><init>()V

    invoke-virtual {p1, v0}, Lcom/transsion/quickjs/QuickJSHelper$ExecuteBuilder;->j(Lcom/transsion/quickjs/a;)Lcom/transsion/quickjs/QuickJSHelper$ExecuteBuilder;

    move-result-object p1

    invoke-virtual {p1}, Lcom/transsion/quickjs/QuickJSHelper$ExecuteBuilder;->n()V

    return-void
.end method

.method public final b1()V
    .locals 4

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->o:Loo/d;

    iget-object v0, v0, Loo/d;->g:Landroidx/appcompat/widget/AppCompatTextView;

    const-string v1, "viewBinding.layoutTopToolBar.vdTitle"

    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    iget-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->j:Lcom/transsion/player/longvideo/ui/LongVodUiType;

    sget-object v2, Lcom/transsion/player/longvideo/ui/LongVodUiType;->MIDDLE:Lcom/transsion/player/longvideo/ui/LongVodUiType;

    const/4 v3, 0x0

    if-eq v1, v2, :cond_0

    const/4 v1, 0x1

    goto :goto_0

    :cond_0
    const/4 v1, 0x0

    :goto_0
    if-eqz v1, :cond_1

    goto :goto_1

    :cond_1
    const/16 v3, 0x8

    :goto_1
    invoke-virtual {v0, v3}, Landroid/view/View;->setVisibility(I)V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->j:Lcom/transsion/player/longvideo/ui/LongVodUiType;

    const-string v1, "viewBinding.layoutTopToolBar.ivBack"

    if-eq v0, v2, :cond_2

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->o:Loo/d;

    iget-object v0, v0, Loo/d;->b:Landroidx/appcompat/widget/AppCompatImageView;

    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-static {v0}, Lvi/c;->k(Landroid/view/View;)V

    goto :goto_2

    :cond_2
    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->o:Loo/d;

    iget-object v0, v0, Loo/d;->b:Landroidx/appcompat/widget/AppCompatImageView;

    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-static {v0}, Lvi/c;->h(Landroid/view/View;)V

    :goto_2
    return-void
.end method

.method public currentPosition()J
    .locals 2

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e:Lcom/transsion/player/ui/longvideo/a;

    invoke-interface {v0}, Lcom/transsion/player/orplayer/f;->getCurrentPosition()J

    move-result-wide v0

    return-wide v0
.end method

.method public final e1()V
    .locals 24

    move-object/from16 v0, p0

    iget-object v1, v0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->G:Lno/a;

    if-eqz v1, :cond_4

    invoke-virtual {v1}, Lno/a;->c()Ljava/lang/String;

    move-result-object v2

    invoke-interface {v2}, Ljava/lang/CharSequence;->length()I

    move-result v2

    if-nez v2, :cond_0

    return-void

    :cond_0
    invoke-virtual {v1}, Lno/a;->i()Ljava/util/List;

    move-result-object v2

    invoke-static {v2}, Lkotlin/collections/CollectionsKt;->b0(Ljava/util/List;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lno/c;

    if-eqz v2, :cond_1

    invoke-virtual {v2}, Lno/c;->g()Z

    move-result v2

    goto :goto_0

    :cond_1
    const/4 v2, 0x0

    :goto_0
    sget-object v3, Lxi/b;->a:Lxi/b$a;

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    const-string v5, "showCoverBg, isMp3:"

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v2}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    const/4 v5, 0x1

    const-string v6, "LongVodPlayerView"

    invoke-virtual {v3, v6, v4, v5}, Lxi/b$a;->c(Ljava/lang/String;Ljava/lang/String;Z)V

    iget-object v3, v0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->g:Lcom/transsion/player/longvideo/constants/LongVodPageType;

    sget-object v4, Lcom/transsion/player/longvideo/constants/LongVodPageType;->TRAILER:Lcom/transsion/player/longvideo/constants/LongVodPageType;

    const-string v5, "viewBinding.orLongVodIvBg"

    if-eq v3, v4, :cond_3

    if-eqz v2, :cond_2

    goto :goto_1

    :cond_2
    iget-object v1, v0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v1, v1, Loo/e;->p:Landroidx/appcompat/widget/AppCompatImageView;

    invoke-static {v1, v5}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-static {v1}, Lvi/c;->g(Landroid/view/View;)V

    goto :goto_2

    :cond_3
    :goto_1
    iget-object v2, v0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v2, v2, Loo/e;->p:Landroidx/appcompat/widget/AppCompatImageView;

    invoke-static {v2, v5}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-static {v2}, Lvi/c;->k(Landroid/view/View;)V

    sget-object v6, Lcom/transsion/baseui/image/ImageHelper;->a:Lcom/transsion/baseui/image/ImageHelper$Companion;

    invoke-virtual/range {p0 .. p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v2

    move-object v7, v2

    const-string v3, "context"

    invoke-static {v2, v3}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    iget-object v2, v0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v2, v2, Loo/e;->p:Landroidx/appcompat/widget/AppCompatImageView;

    move-object v8, v2

    invoke-static {v2, v5}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {v1}, Lno/a;->c()Ljava/lang/String;

    move-result-object v9

    const/4 v10, 0x0

    const/4 v11, 0x0

    const/4 v12, 0x0

    const/4 v13, 0x0

    const/4 v14, 0x0

    invoke-virtual {v1}, Lno/a;->r()Ljava/lang/String;

    move-result-object v15

    const/16 v16, 0x0

    const/16 v17, 0x0

    const/16 v18, 0x0

    const/16 v19, 0x0

    const/16 v20, 0x0

    const/16 v21, 0x0

    const/16 v22, 0x7ef8

    const/16 v23, 0x0

    invoke-static/range {v6 .. v23}, Lcom/transsion/baseui/image/ImageHelper$Companion;->r(Lcom/transsion/baseui/image/ImageHelper$Companion;Landroid/content/Context;Landroid/widget/ImageView;Ljava/lang/String;IIIIZLjava/lang/String;ZZZZZIILjava/lang/Object;)V

    :cond_4
    :goto_2
    return-void
.end method

.method public exitFullScreen()V
    .locals 2

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->j:Lcom/transsion/player/longvideo/ui/LongVodUiType;

    sget-object v1, Lcom/transsion/player/longvideo/ui/LongVodUiType;->MIDDLE:Lcom/transsion/player/longvideo/ui/LongVodUiType;

    if-eq v0, v1, :cond_1

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->m:Loo/f;

    iget-object v0, v0, Loo/f;->l:Landroidx/appcompat/widget/AppCompatTextView;

    invoke-virtual {v0}, Landroid/view/View;->isSelected()Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->m:Loo/f;

    iget-object v0, v0, Loo/f;->l:Landroidx/appcompat/widget/AppCompatTextView;

    invoke-virtual {v0}, Landroid/view/View;->performClick()Z

    :cond_0
    const/4 v0, 0x0

    invoke-virtual {p0, v0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->A(Z)V

    :cond_1
    return-void
.end method

.method public final f1(Z)V
    .locals 4

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->I:Landroid/view/View;

    const/4 v1, 0x0

    if-nez v0, :cond_1

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->w:Landroid/view/ViewStub;

    invoke-virtual {v0}, Landroid/view/ViewStub;->inflate()Landroid/view/View;

    move-result-object v0

    iput-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->I:Landroid/view/View;

    if-eqz v0, :cond_1

    sget v2, Lcom/transsion/player/longvideo/R$id;->tv_fail_left_btn:I

    invoke-virtual {v0, v2}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    move-result-object v2

    check-cast v2, Landroid/widget/TextView;

    const-string v3, "showError$lambda$46$lambda$43"

    invoke-static {v2, v3}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    if-eqz p1, :cond_0

    const/4 v3, 0x0

    goto :goto_0

    :cond_0
    const/16 v3, 0x8

    :goto_0
    invoke-virtual {v2, v3}, Landroid/view/View;->setVisibility(I)V

    new-instance v3, Lcom/transsion/player/longvideo/ui/l;

    invoke-direct {v3}, Lcom/transsion/player/longvideo/ui/l;-><init>()V

    invoke-virtual {v2, v3}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    sget v2, Lcom/transsion/player/longvideo/R$id;->tv_fail_right_btn:I

    invoke-virtual {v0, v2}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    move-result-object v2

    check-cast v2, Landroid/widget/TextView;

    new-instance v3, Lcom/transsion/player/longvideo/ui/p;

    invoke-direct {v3, p0}, Lcom/transsion/player/longvideo/ui/p;-><init>(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)V

    invoke-virtual {v2, v3}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    sget v2, Lcom/transsion/player/longvideo/R$id;->iv_fail_back:I

    invoke-virtual {v0, v2}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/ImageView;

    new-instance v2, Lcom/transsion/player/longvideo/ui/q;

    invoke-direct {v2, p0}, Lcom/transsion/player/longvideo/ui/q;-><init>(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)V

    invoke-virtual {v0, v2}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    :cond_1
    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->I:Landroid/view/View;

    if-eqz v0, :cond_2

    sget v2, Lcom/transsion/player/longvideo/R$id;->tv_fail_title:I

    invoke-virtual {v0, v2}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/TextView;

    goto :goto_1

    :cond_2
    const/4 v0, 0x0

    :goto_1
    if-nez v0, :cond_3

    goto :goto_3

    :cond_3
    invoke-static {}, Lcom/blankj/utilcode/util/Utils;->a()Landroid/app/Application;

    move-result-object v2

    if-eqz p1, :cond_4

    sget p1, Lcom/transsion/player/longvideo/R$string;->long_vod_load_failed:I

    goto :goto_2

    :cond_4
    sget p1, Lcom/tn/lib/widget/R$string;->error_load_failed:I

    :goto_2
    invoke-virtual {v2, p1}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    :goto_3
    invoke-direct {p0, v1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->o1(Z)V

    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->J:Landroid/view/View;

    if-eqz p1, :cond_5

    invoke-static {p1}, Lvi/c;->g(Landroid/view/View;)V

    :cond_5
    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->j:Lcom/transsion/player/longvideo/ui/LongVodUiType;

    sget-object v0, Lcom/transsion/player/longvideo/ui/LongVodUiType;->MIDDLE:Lcom/transsion/player/longvideo/ui/LongVodUiType;

    if-ne p1, v0, :cond_6

    invoke-direct {p0, v1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->setPauseViewStatus(Z)V

    :cond_6
    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e:Lcom/transsion/player/ui/longvideo/a;

    invoke-interface {p1}, Lcom/transsion/player/orplayer/f;->pause()V

    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->I:Landroid/view/View;

    if-eqz p1, :cond_7

    invoke-static {p1}, Lvi/c;->k(Landroid/view/View;)V

    :cond_7
    return-void
.end method

.method public floatBack2ExitPage(Lcom/transsion/player/orplayer/f;Lcom/transsion/player/ui/ORPlayerView;)V
    .locals 1

    const-string v0, "orPlayer"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    const-string p1, "playerView"

    invoke-static {p2, p1}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    return-void
.end method

.method public final getLongVodPlayerViewAdHelper()Lcom/transsion/player/longvideo/ui/dialog/LongVodPlayerViewAdHelper;
    .locals 1

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->b:Lcom/transsion/player/longvideo/ui/dialog/LongVodPlayerViewAdHelper;

    return-object v0
.end method

.method public getPlayer()Lcom/transsion/player/orplayer/f;
    .locals 1

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e:Lcom/transsion/player/ui/longvideo/a;

    invoke-interface {v0}, Lcom/transsion/player/ui/longvideo/a;->getOrPlayer()Lcom/transsion/player/orplayer/f;

    move-result-object v0

    return-object v0
.end method

.method public getPlayerView()Lcom/transsion/player/ui/ORPlayerView;
    .locals 1

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->R:Lcom/transsion/player/ui/ORPlayerView;

    return-object v0
.end method

.method public getPlayingStream()Lno/c;
    .locals 1

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->Q:Lno/c;

    return-object v0
.end method

.method public getSubtitleView()Lcom/avery/subtitle/widget/SimpleSubtitleView;
    .locals 1

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->u:Lcom/avery/subtitle/widget/SimpleSubtitleView;

    return-object v0
.end method

.method public getSubtitleViewGroup()Landroid/view/ViewGroup;
    .locals 1

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->j:Landroidx/appcompat/widget/LinearLayoutCompat;

    return-object v0
.end method

.method public hasNextEpisode(Z)V
    .locals 1

    iput-boolean p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->A:Z

    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object p1, p1, Loo/e;->m:Loo/f;

    iget-object p1, p1, Loo/f;->d:Landroidx/constraintlayout/widget/ConstraintLayout;

    const-string v0, "viewBinding.layoutLand.clBottomControl"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {p1}, Landroid/view/View;->getVisibility()I

    move-result p1

    if-nez p1, :cond_1

    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object p1, p1, Loo/e;->m:Loo/f;

    iget-object p1, p1, Loo/f;->y:Landroidx/appcompat/widget/AppCompatTextView;

    const-string v0, "viewBinding.layoutLand.tvPlayNext"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    iget-boolean v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->A:Z

    if-eqz v0, :cond_0

    const/4 v0, 0x0

    goto :goto_0

    :cond_0
    const/16 v0, 0x8

    :goto_0
    invoke-virtual {p1, v0}, Landroid/view/View;->setVisibility(I)V

    invoke-virtual {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->R0()V

    :cond_1
    return-void
.end method

.method public hideBottomController(Z)V
    .locals 3

    invoke-static {}, Lcom/blankj/utilcode/util/c;->j()Z

    move-result v0

    if-eqz v0, :cond_1

    if-eqz p1, :cond_0

    invoke-direct {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->S()V

    goto :goto_0

    :cond_0
    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e:Lcom/transsion/player/ui/longvideo/a;

    invoke-interface {p1}, Lcom/transsion/player/orplayer/f;->isPlaying()Z

    move-result p1

    if-eqz p1, :cond_1

    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->M:Landroid/os/Handler;

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->d1:Ljava/lang/Runnable;

    invoke-virtual {p1, v0}, Landroid/os/Handler;->removeCallbacks(Ljava/lang/Runnable;)V

    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->M:Landroid/os/Handler;

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->d1:Ljava/lang/Runnable;

    const-wide/16 v1, 0xbb8

    invoke-virtual {p1, v0, v1, v2}, Landroid/os/Handler;->postDelayed(Ljava/lang/Runnable;J)Z

    :cond_1
    :goto_0
    return-void
.end method

.method public init(Ljava/lang/String;Lcom/transsion/player/longvideo/constants/LongVodPageType;Ljava/lang/String;Landroid/widget/FrameLayout;Landroid/view/ViewGroup;Ljava/util/List;)V
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Lcom/transsion/player/longvideo/constants/LongVodPageType;",
            "Ljava/lang/String;",
            "Landroid/widget/FrameLayout;",
            "Landroid/view/ViewGroup;",
            "Ljava/util/List<",
            "Lcom/transsion/moviedetailapi/bean/DubsInfo;",
            ">;)V"
        }
    .end annotation

    const-string v0, "pageName"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    const-string v0, "pageType"

    invoke-static {p2, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    const-string v0, "dubs"

    invoke-static {p6, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    sget-object v0, Lcom/transsion/player/orplayer/global/TnPlayerManager;->a:Lcom/transsion/player/orplayer/global/TnPlayerManager;

    invoke-virtual {v0}, Lcom/transsion/player/orplayer/global/TnPlayerManager;->g()Lkotlin/jvm/functions/Function0;

    move-result-object v0

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    invoke-interface {v0}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/Boolean;

    invoke-virtual {v0}, Ljava/lang/Boolean;->booleanValue()Z

    move-result v0

    const/4 v2, 0x1

    if-ne v0, v2, :cond_0

    const/4 v1, 0x1

    :cond_0
    iput-boolean v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->E:Z

    iput-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->i:Ljava/lang/String;

    iput-object p2, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->g:Lcom/transsion/player/longvideo/constants/LongVodPageType;

    iput-object p4, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->L:Landroid/widget/FrameLayout;

    iput-object p6, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->Z0:Ljava/util/List;

    iput-object p3, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->h:Ljava/lang/String;

    sget-object v2, Lxi/b;->a:Lxi/b$a;

    const-string v3, "LongVodPlayerView"

    new-instance p3, Ljava/lang/StringBuilder;

    invoke-direct {p3}, Ljava/lang/StringBuilder;-><init>()V

    const-string p4, "init,pageName:"

    invoke-virtual {p3, p4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p3, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string p1, ", pageType:"

    invoke-virtual {p3, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p3, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string p1, " "

    invoke-virtual {p3, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    const/4 v5, 0x0

    const/4 v6, 0x4

    const/4 v7, 0x0

    invoke-static/range {v2 .. v7}, Lxi/b$a;->f(Lxi/b$a;Ljava/lang/String;Ljava/lang/String;ZILjava/lang/Object;)V

    invoke-direct {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->t0()V

    invoke-direct {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->T()V

    invoke-direct {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->o0()V

    invoke-direct {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->V()V

    invoke-virtual {p0, p5}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->r0(Landroid/view/ViewGroup;)V

    invoke-direct {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->W()V

    invoke-direct {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->getOrientationEventListener()Lcom/transsion/postdetail/util/g;

    move-result-object p1

    if-eqz p1, :cond_1

    invoke-virtual {p1}, Landroid/view/OrientationEventListener;->enable()V

    :cond_1
    invoke-virtual {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->U()V

    return-void
.end method

.method public final initAdHelper(Ljava/lang/String;)V
    .locals 3

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->b:Lcom/transsion/player/longvideo/ui/dialog/LongVodPlayerViewAdHelper;

    iget-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    new-instance v2, Lcom/transsion/player/longvideo/ui/LongVodPlayerView$initAdHelper$1;

    invoke-direct {v2, p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView$initAdHelper$1;-><init>(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)V

    invoke-virtual {v0, v1, p1, v2}, Lcom/transsion/player/longvideo/ui/dialog/LongVodPlayerViewAdHelper;->L(Loo/e;Ljava/lang/String;Lkotlin/jvm/functions/Function1;)V

    return-void
.end method

.method public isPressedPause()Z
    .locals 1

    iget-boolean v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->u:Z

    return v0
.end method

.method public final k1(Z)V
    .locals 1

    const-string v0, "viewBinding.tvPressSpeed"

    if-nez p1, :cond_0

    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object p1, p1, Loo/e;->s:Landroidx/appcompat/widget/AppCompatTextView;

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    const/16 v0, 0x8

    invoke-virtual {p1, v0}, Landroid/view/View;->setVisibility(I)V

    goto :goto_0

    :cond_0
    const/4 p1, 0x1

    invoke-virtual {p0, p1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->hideBottomController(Z)V

    invoke-direct {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->S()V

    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object p1, p1, Loo/e;->s:Landroidx/appcompat/widget/AppCompatTextView;

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    const/4 v0, 0x0

    invoke-virtual {p1, v0}, Landroid/view/View;->setVisibility(I)V

    :goto_0
    return-void
.end method

.method public final m0(Lno/a;Lno/c;)V
    .locals 7

    invoke-virtual {p2}, Lno/c;->c()Ljava/lang/String;

    move-result-object v0

    invoke-interface {v0}, Ljava/lang/CharSequence;->length()I

    move-result v0

    if-nez v0, :cond_0

    return-void

    :cond_0
    sget-object v1, Lxi/b;->a:Lxi/b$a;

    const-string v2, "LongVodPlayerView"

    invoke-virtual {p2}, Lno/c;->c()Ljava/lang/String;

    move-result-object v0

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "initMp4Resolution, resolution = "

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    const/4 v4, 0x0

    const/4 v5, 0x4

    const/4 v6, 0x0

    invoke-static/range {v1 .. v6}, Lxi/b$a;->f(Lxi/b$a;Ljava/lang/String;Ljava/lang/String;ZILjava/lang/Object;)V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->S:Lcom/transsion/postdetail/layer/local/n;

    const-string v1, "P"

    if-nez v0, :cond_1

    goto :goto_0

    :cond_1
    invoke-virtual {p2}, Lno/c;->c()Ljava/lang/String;

    move-result-object v2

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Lcom/transsion/postdetail/layer/local/n;->I(Ljava/lang/String;)V

    :goto_0
    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->m:Loo/f;

    iget-object v0, v0, Loo/f;->u:Landroid/widget/TextView;

    invoke-virtual {p2}, Lno/c;->c()Ljava/lang/String;

    move-result-object v2

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->R0:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->clear()V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->R0:Ljava/util/List;

    iget-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->H0:Lcom/transsion/player/longvideo/helper/h;

    invoke-virtual {p1}, Lno/a;->i()Ljava/util/List;

    move-result-object p1

    invoke-virtual {v1, p2, p1}, Lcom/transsion/player/longvideo/helper/h;->e(Lno/c;Ljava/util/List;)Ljava/util/List;

    move-result-object p1

    check-cast p1, Ljava/util/Collection;

    invoke-interface {v0, p1}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    return-void
.end method

.method public final n0(Ljava/lang/String;Lno/a;)V
    .locals 25

    move-object/from16 v0, p0

    move-object/from16 v1, p1

    iget-object v2, v0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->R:Lcom/transsion/player/ui/ORPlayerView;

    if-eqz v2, :cond_0

    return-void

    :cond_0
    new-instance v2, Llo/a;

    new-instance v24, Llo/d;

    move-object/from16 v3, v24

    const/4 v4, 0x0

    const/4 v5, 0x0

    const/4 v6, 0x0

    const/4 v7, 0x0

    const/4 v8, 0x0

    const/4 v9, 0x0

    const/4 v10, 0x0

    const/4 v11, 0x0

    const-wide/16 v12, 0x0

    const/16 v14, 0x200

    const/4 v15, 0x0

    const/16 v16, 0x0

    const/16 v17, 0x0

    const/16 v18, 0x0

    const/16 v19, 0x0

    const/16 v20, 0x0

    const/16 v21, 0x0

    const v22, 0x1fdff

    const/16 v23, 0x0

    invoke-direct/range {v3 .. v23}, Llo/d;-><init>(Lcom/transsion/player/config/RenderType;ZIIIIIIJIIZZZZZLjava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    const/4 v5, 0x0

    sget-object v3, Lcom/transsion/baselib/helper/LocalVideoPlayerConfigMmkv;->a:Lcom/transsion/baselib/helper/LocalVideoPlayerConfigMmkv;

    invoke-virtual {v3}, Lcom/transsion/baselib/helper/LocalVideoPlayerConfigMmkv;->c()F

    move-result v8

    invoke-virtual {v3}, Lcom/transsion/baselib/helper/LocalVideoPlayerConfigMmkv;->b()Lcom/transsion/player/enum/ScaleMode;

    move-result-object v9

    const/16 v10, 0xa

    const/4 v11, 0x0

    move-object v3, v2

    move-object/from16 v4, v24

    invoke-direct/range {v3 .. v11}, Llo/a;-><init>(Llo/d;Lcom/transsion/player/config/PlayerType;ZZFLcom/transsion/player/enum/ScaleMode;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    new-instance v3, Lcom/transsion/player/ui/ORPlayerView;

    invoke-virtual/range {p0 .. p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v4

    const-string v5, "context"

    invoke-static {v4, v5}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {v2}, Llo/a;->c()Z

    move-result v5

    if-eqz v5, :cond_1

    sget-object v5, Lcom/transsion/player/config/RenderType;->SURFACE_VIEW:Lcom/transsion/player/config/RenderType;

    goto :goto_0

    :cond_1
    sget-object v5, Lcom/transsion/player/config/RenderType;->TEXTURE_VIEW:Lcom/transsion/player/config/RenderType;

    :goto_0
    invoke-direct {v3, v4, v5}, Lcom/transsion/player/ui/ORPlayerView;-><init>(Landroid/content/Context;Lcom/transsion/player/config/RenderType;)V

    iput-object v3, v0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->R:Lcom/transsion/player/ui/ORPlayerView;

    invoke-virtual/range {p2 .. p2}, Lno/a;->i()Ljava/util/List;

    move-result-object v3

    invoke-static {v3}, Lkotlin/collections/CollectionsKt;->b0(Ljava/util/List;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lno/c;

    const/4 v4, 0x0

    const/4 v5, 0x0

    const/4 v6, 0x1

    if-eqz v3, :cond_6

    invoke-virtual {v3}, Lno/c;->f()Ljava/lang/String;

    move-result-object v3

    if-eqz v3, :cond_6

    sget-object v13, Lxi/b;->a:Lxi/b$a;

    const-string v8, "LongVodPlayerView"

    invoke-static {v3}, Lcom/transsion/baselib/utils/h;->f(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v7

    invoke-virtual {v7}, Ljava/lang/String;->hashCode()I

    move-result v7

    new-instance v9, Ljava/lang/StringBuilder;

    invoke-direct {v9}, Ljava/lang/StringBuilder;-><init>()V

    const-string v10, "initPayer, id\uff1a"

    invoke-virtual {v9, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v9, v7}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v7, ","

    invoke-virtual {v9, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v9}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v9

    const/4 v10, 0x0

    const/4 v11, 0x4

    const/4 v12, 0x0

    move-object v7, v13

    invoke-static/range {v7 .. v12}, Lxi/b$a;->f(Lxi/b$a;Ljava/lang/String;Ljava/lang/String;ZILjava/lang/Object;)V

    invoke-virtual/range {p0 .. p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->y0()Z

    move-result v7

    if-nez v7, :cond_2

    goto :goto_2

    :cond_2
    sget-object v5, Lcom/transsion/baseui/music/MusicFloatManager;->g:Lcom/transsion/baseui/music/MusicFloatManager$a;

    invoke-virtual {v5}, Lcom/transsion/baseui/music/MusicFloatManager$a;->b()Lcom/transsion/baseui/music/MusicFloatManager;

    move-result-object v7

    invoke-virtual {v7}, Lcom/transsion/baseui/music/MusicFloatManager;->m()Lcom/transsion/player/orplayer/f;

    move-result-object v14

    const-string v8, "LongVodPlayerView"

    invoke-virtual {v5}, Lcom/transsion/baseui/music/MusicFloatManager$a;->b()Lcom/transsion/baseui/music/MusicFloatManager;

    move-result-object v7

    invoke-virtual {v7}, Lcom/transsion/baseui/music/MusicFloatManager;->l()Ljava/lang/String;

    move-result-object v7

    new-instance v9, Ljava/lang/StringBuilder;

    invoke-direct {v9}, Ljava/lang/StringBuilder;-><init>()V

    const-string v10, "music  mediaSourceId:"

    invoke-virtual {v9, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v9, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v9}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v9

    const/4 v10, 0x0

    const/4 v11, 0x4

    const/4 v12, 0x0

    move-object v7, v13

    invoke-static/range {v7 .. v12}, Lxi/b$a;->f(Lxi/b$a;Ljava/lang/String;Ljava/lang/String;ZILjava/lang/Object;)V

    invoke-virtual {v5}, Lcom/transsion/baseui/music/MusicFloatManager$a;->b()Lcom/transsion/baseui/music/MusicFloatManager;

    move-result-object v5

    invoke-virtual {v5}, Lcom/transsion/baseui/music/MusicFloatManager;->l()Ljava/lang/String;

    move-result-object v5

    invoke-static {v3}, Lcom/transsion/baselib/utils/h;->f(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/String;->hashCode()I

    move-result v3

    invoke-static {v3}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    move-result-object v3

    invoke-static {v5, v3}, Lkotlin/jvm/internal/Intrinsics;->b(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_3

    const-string v8, "LongVodPlayerView"

    const-string v9, "music \u662f\u540c\u4e00\u4e2a\u97f3\u4e50"

    const/4 v10, 0x0

    const/4 v11, 0x4

    const/4 v12, 0x0

    move-object v7, v13

    invoke-static/range {v7 .. v12}, Lxi/b$a;->f(Lxi/b$a;Ljava/lang/String;Ljava/lang/String;ZILjava/lang/Object;)V

    const/4 v3, 0x0

    goto :goto_1

    :cond_3
    const-string v8, "LongVodPlayerView"

    const-string v9, "music \u662f\u4e0d\u540c\u7684\u97f3\u4e50"

    const/4 v10, 0x0

    const/4 v11, 0x4

    const/4 v12, 0x0

    move-object v7, v13

    invoke-static/range {v7 .. v12}, Lxi/b$a;->f(Lxi/b$a;Ljava/lang/String;Ljava/lang/String;ZILjava/lang/Object;)V

    if-eqz v14, :cond_4

    invoke-interface {v14}, Lcom/transsion/player/orplayer/f;->stop()V

    :cond_4
    if-eqz v14, :cond_5

    invoke-interface {v14}, Lcom/transsion/player/orplayer/f;->reset()V

    :cond_5
    const/4 v3, 0x1

    :goto_1
    move-object v5, v14

    goto :goto_3

    :cond_6
    :goto_2
    const/4 v3, 0x1

    :goto_3
    sget-object v7, Lcom/transsion/videofloat/manager/w;->a:Lcom/transsion/videofloat/manager/w;

    if-nez v1, :cond_7

    const-string v8, ""

    goto :goto_4

    :cond_7
    move-object v8, v1

    :goto_4
    invoke-virtual {v7, v8}, Lcom/transsion/videofloat/manager/w;->b(Ljava/lang/String;)Lcom/transsion/player/orplayer/f;

    move-result-object v7

    if-nez v7, :cond_8

    if-eqz v3, :cond_8

    const/4 v4, 0x1

    :cond_8
    iput-boolean v4, v0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->C:Z

    sget-object v8, Lxi/b;->a:Lxi/b$a;

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v9, "initPayer, tag:"

    invoke-virtual {v3, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, ", isNewPlayer:"

    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    const-string v3, "LongVodPlayerView"

    invoke-virtual {v8, v3, v1, v6}, Lxi/b$a;->c(Ljava/lang/String;Ljava/lang/String;Z)V

    if-eqz v5, :cond_9

    const-string v9, "LongVodPlayerView"

    const-string v10, "initPayer, \u97f3\u4e50\u64ad\u653e\u5668\u590d\u7528"

    const/4 v11, 0x0

    const/4 v12, 0x4

    const/4 v13, 0x0

    invoke-static/range {v8 .. v13}, Lxi/b$a;->f(Lxi/b$a;Ljava/lang/String;Ljava/lang/String;ZILjava/lang/Object;)V

    iput-boolean v6, v0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->y:Z

    iget-object v1, v0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e:Lcom/transsion/player/ui/longvideo/a;

    iget-object v3, v0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->R:Lcom/transsion/player/ui/ORPlayerView;

    invoke-static {v3}, Lkotlin/jvm/internal/Intrinsics;->d(Ljava/lang/Object;)V

    invoke-interface {v1, v2, v5, v3}, Lcom/transsion/player/ui/longvideo/a;->setExistPlayer(Llo/a;Lcom/transsion/player/orplayer/f;Lcom/transsion/player/ui/ORPlayerView;)V

    iget-object v1, v0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e:Lcom/transsion/player/ui/longvideo/a;

    invoke-interface {v1}, Lcom/transsion/player/orplayer/f;->play()V

    goto :goto_5

    :cond_9
    if-eqz v7, :cond_a

    const-string v9, "LongVodPlayerView"

    const-string v1, "initPayer, float\u64ad\u653e\u5668\u590d\u7528"

    filled-new-array {v1}, [Ljava/lang/String;

    move-result-object v10

    const/4 v11, 0x0

    const/4 v12, 0x4

    const/4 v13, 0x0

    invoke-static/range {v8 .. v13}, Lxi/b$a;->p(Lxi/b$a;Ljava/lang/String;[Ljava/lang/String;ZILjava/lang/Object;)V

    iput-boolean v6, v0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->y:Z

    iget-object v1, v0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e:Lcom/transsion/player/ui/longvideo/a;

    iget-object v3, v0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->R:Lcom/transsion/player/ui/ORPlayerView;

    invoke-static {v3}, Lkotlin/jvm/internal/Intrinsics;->d(Ljava/lang/Object;)V

    invoke-interface {v1, v2, v7, v3}, Lcom/transsion/player/ui/longvideo/a;->setExistPlayer(Llo/a;Lcom/transsion/player/orplayer/f;Lcom/transsion/player/ui/ORPlayerView;)V

    goto :goto_5

    :cond_a
    iget-object v1, v0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e:Lcom/transsion/player/ui/longvideo/a;

    iget-object v3, v0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->R:Lcom/transsion/player/ui/ORPlayerView;

    invoke-static {v3}, Lkotlin/jvm/internal/Intrinsics;->d(Ljava/lang/Object;)V

    invoke-interface {v1, v2, v3}, Lcom/transsion/player/ui/longvideo/a;->initLongVodPlayer(Llo/a;Lcom/transsion/player/ui/ORPlayerView;)V

    :goto_5
    sget-object v1, Lcom/transsion/videofloat/VideoFloatManager;->a:Lcom/transsion/videofloat/VideoFloatManager$Companion;

    invoke-virtual {v1}, Lcom/transsion/videofloat/VideoFloatManager$Companion;->b()Lcom/transsion/videofloat/VideoFloatManager;

    move-result-object v1

    invoke-interface {v1}, Lcom/transsion/videofloat/VideoFloatManager;->f()V

    iget-object v1, v0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e:Lcom/transsion/player/ui/longvideo/a;

    iget-object v2, v0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->b1:Lcom/transsion/player/longvideo/ui/LongVodPlayerView$g;

    invoke-interface {v1, v2}, Lcom/transsion/player/orplayer/f;->addPlayerListener(Lcom/transsion/player/orplayer/e;)V

    return-void
.end method

.method public onAttachedToWindow()V
    .locals 2

    invoke-super {p0}, Landroid/view/ViewGroup;->onAttachedToWindow()V

    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    const-string v1, "context"

    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-static {v0}, Lcom/transsion/baseui/util/b;->a(Landroid/content/Context;)Z

    move-result v0

    if-nez v0, :cond_0

    invoke-direct {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->getScreenHelper()Lcom/transsion/baselib/helper/ScreenRotationHelper;

    move-result-object v0

    invoke-virtual {v0}, Lcom/transsion/baselib/helper/ScreenRotationHelper;->g()V

    :cond_0
    return-void
.end method

.method public onBackPressed()Z
    .locals 4

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->j:Lcom/transsion/player/longvideo/ui/LongVodUiType;

    sget-object v1, Lcom/transsion/player/longvideo/ui/LongVodUiType;->MIDDLE:Lcom/transsion/player/longvideo/ui/LongVodUiType;

    const/4 v2, 0x0

    const/4 v3, 0x0

    if-eq v0, v1, :cond_0

    const/4 v0, 0x1

    invoke-static {p0, v3, v0, v2}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->B(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;ZILjava/lang/Object;)V

    const/4 v3, 0x1

    goto :goto_0

    :cond_0
    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->U:Lmo/a$b;

    if-eqz v0, :cond_1

    sget-object v1, Lcom/transsion/videofloat/bean/FloatActionType;->BACK:Lcom/transsion/videofloat/bean/FloatActionType;

    invoke-interface {v0, v1}, Lmo/a$b;->c(Lcom/transsion/videofloat/bean/FloatActionType;)Z

    move-result v0

    if-nez v0, :cond_3

    :cond_1
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    instance-of v1, v0, Landroid/app/Activity;

    if-eqz v1, :cond_2

    move-object v2, v0

    check-cast v2, Landroid/app/Activity;

    :cond_2
    if-eqz v2, :cond_3

    invoke-virtual {v2}, Landroid/app/Activity;->finish()V

    :cond_3
    :goto_0
    return v3
.end method

.method public onBackgroundStatusChange(Z)V
    .locals 3

    if-eqz p1, :cond_2

    sget-object p1, Lxi/b;->a:Lxi/b$a;

    const-string v0, "LongVodPlayerView"

    const-string v1, "app\u9000\u81f3\u540e\u53f0 \u4e0a\u62a5\u57cb\u70b9"

    const/4 v2, 0x1

    invoke-virtual {p1, v0, v1, v2}, Lxi/b$a;->c(Ljava/lang/String;Ljava/lang/String;Z)V

    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e:Lcom/transsion/player/ui/longvideo/a;

    invoke-interface {p1}, Lcom/transsion/player/orplayer/f;->isPlaying()Z

    move-result p1

    if-eqz p1, :cond_1

    const/4 p1, 0x0

    iput-boolean p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->z:Z

    invoke-virtual {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->y0()Z

    move-result p1

    if-nez p1, :cond_0

    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e:Lcom/transsion/player/ui/longvideo/a;

    invoke-interface {p1}, Lcom/transsion/player/orplayer/f;->pause()V

    :cond_0
    invoke-direct {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->getOrientationEventListener()Lcom/transsion/postdetail/util/g;

    move-result-object p1

    if-eqz p1, :cond_1

    invoke-virtual {p1}, Landroid/view/OrientationEventListener;->disable()V

    :cond_1
    invoke-direct {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a1()V

    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->S:Lcom/transsion/postdetail/layer/local/n;

    if-eqz p1, :cond_2

    invoke-virtual {p1, v2}, Lcom/transsion/postdetail/layer/local/n;->b(Z)V

    :cond_2
    return-void
.end method

.method public onDetachedFromWindow()V
    .locals 2

    invoke-super {p0}, Landroid/view/ViewGroup;->onDetachedFromWindow()V

    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    const-string v1, "context"

    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-static {v0}, Lcom/transsion/baseui/util/b;->a(Landroid/content/Context;)Z

    move-result v0

    if-nez v0, :cond_0

    invoke-direct {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->getScreenHelper()Lcom/transsion/baselib/helper/ScreenRotationHelper;

    move-result-object v0

    invoke-virtual {v0}, Lcom/transsion/baselib/helper/ScreenRotationHelper;->h()V

    :cond_0
    return-void
.end method

.method public onHandlePause()V
    .locals 4

    sget-object v0, Lxi/b;->a:Lxi/b$a;

    const-string v1, "LongVodPlayerView"

    const-string v2, "onHandlePause"

    const/4 v3, 0x1

    invoke-virtual {v0, v1, v2, v3}, Lxi/b$a;->c(Ljava/lang/String;Ljava/lang/String;Z)V

    const/4 v0, 0x0

    iput-boolean v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->z:Z

    iput-boolean v3, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->u:Z

    invoke-virtual {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->y0()Z

    move-result v0

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e:Lcom/transsion/player/ui/longvideo/a;

    invoke-interface {v0}, Lcom/transsion/player/orplayer/f;->pause()V

    :cond_0
    invoke-direct {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->getOrientationEventListener()Lcom/transsion/postdetail/util/g;

    move-result-object v0

    if-eqz v0, :cond_1

    invoke-virtual {v0}, Landroid/view/OrientationEventListener;->disable()V

    :cond_1
    return-void
.end method

.method public onHandlePlay()V
    .locals 4

    sget-object v0, Lxi/b;->a:Lxi/b$a;

    const-string v1, "LongVodPlayerView"

    const-string v2, "onHandlePlay"

    const/4 v3, 0x1

    invoke-virtual {v0, v1, v2, v3}, Lxi/b$a;->c(Ljava/lang/String;Ljava/lang/String;Z)V

    iput-boolean v3, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->z:Z

    iget-boolean v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->x:Z

    if-nez v0, :cond_0

    const/4 v0, 0x0

    const/4 v1, 0x3

    const/4 v2, 0x0

    invoke-static {p0, v2, v0, v1, v2}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->w1(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Landroid/view/MotionEvent;ZILjava/lang/Object;)V

    :cond_0
    return-void
.end method

.method public onPageDestroy()V
    .locals 4

    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->Q0:Z

    invoke-direct {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a1()V

    sget-object v1, Lxi/b;->a:Lxi/b$a;

    const-string v2, "LongVodPlayerView"

    const-string v3, "\u9875\u9762\u5173\u95ed \u4e0a\u62a5\u57cb\u70b9"

    invoke-virtual {v1, v2, v3, v0}, Lxi/b$a;->c(Ljava/lang/String;Ljava/lang/String;Z)V

    iget-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->S:Lcom/transsion/postdetail/layer/local/n;

    if-eqz v1, :cond_0

    const/4 v2, 0x0

    const/4 v3, 0x0

    invoke-static {v1, v2, v0, v3}, Lcom/transsion/postdetail/layer/local/n;->c(Lcom/transsion/postdetail/layer/local/n;ZILjava/lang/Object;)V

    :cond_0
    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e:Lcom/transsion/player/ui/longvideo/a;

    iget-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->b1:Lcom/transsion/player/longvideo/ui/LongVodPlayerView$g;

    invoke-interface {v0, v1}, Lcom/transsion/player/orplayer/f;->removePlayerListener(Lcom/transsion/player/orplayer/e;)V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->V:Lcom/transsion/player/longvideo/helper/LongVodSubtitleHelper;

    if-eqz v0, :cond_1

    invoke-virtual {v0}, Lcom/transsion/player/longvideo/helper/LongVodSubtitleHelper;->u()V

    :cond_1
    invoke-virtual {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->p1()V

    invoke-direct {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->U0()V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->b:Lcom/transsion/player/longvideo/ui/dialog/LongVodPlayerViewAdHelper;

    invoke-virtual {v0}, Lcom/transsion/player/longvideo/ui/dialog/LongVodPlayerViewAdHelper;->C()V

    return-void
.end method

.method public onPipModeChanged(Z)V
    .locals 1

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->S:Lcom/transsion/postdetail/layer/local/n;

    if-nez v0, :cond_0

    goto :goto_0

    :cond_0
    invoke-virtual {v0, p1}, Lcom/transsion/postdetail/layer/local/n;->A(Z)V

    :goto_0
    return-void
.end method

.method public onSaveHistory()V
    .locals 7

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->g:Lcom/transsion/player/longvideo/constants/LongVodPageType;

    sget-object v1, Lcom/transsion/player/longvideo/constants/LongVodPageType;->TRAILER:Lcom/transsion/player/longvideo/constants/LongVodPageType;

    if-ne v0, v1, :cond_0

    return-void

    :cond_0
    iget-wide v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->o:J

    const-wide/16 v2, 0x0

    cmp-long v4, v0, v2

    if-gtz v4, :cond_1

    return-void

    :cond_1
    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->G:Lno/a;

    iget-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->X0:Lkotlinx/coroutines/k0;

    const/4 v2, 0x0

    const/4 v3, 0x0

    new-instance v4, Lcom/transsion/player/longvideo/ui/LongVodPlayerView$onSaveHistory$1;

    const/4 v5, 0x0

    invoke-direct {v4, v0, p0, v5}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView$onSaveHistory$1;-><init>(Lno/a;Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Lkotlin/coroutines/Continuation;)V

    const/4 v5, 0x3

    const/4 v6, 0x0

    invoke-static/range {v1 .. v6}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/k0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/q1;

    return-void
.end method

.method public onViewPause()V
    .locals 4

    sget-object v0, Lxi/b;->a:Lxi/b$a;

    const-string v1, "onViewPause"

    const/4 v2, 0x1

    const-string v3, "LongVodPlayerView"

    invoke-virtual {v0, v3, v1, v2}, Lxi/b$a;->c(Ljava/lang/String;Ljava/lang/String;Z)V

    const/4 v0, 0x0

    iput-boolean v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->z:Z

    invoke-virtual {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->y0()Z

    move-result v0

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e:Lcom/transsion/player/ui/longvideo/a;

    invoke-interface {v0}, Lcom/transsion/player/orplayer/f;->pause()V

    :cond_0
    invoke-direct {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->getOrientationEventListener()Lcom/transsion/postdetail/util/g;

    move-result-object v0

    if-eqz v0, :cond_1

    invoke-virtual {v0}, Landroid/view/OrientationEventListener;->disable()V

    :cond_1
    return-void
.end method

.method public onViewResume()V
    .locals 4

    sget-object v0, Lxi/b;->a:Lxi/b$a;

    const-string v1, "LongVodPlayerView"

    const-string v2, "onViewResume"

    const/4 v3, 0x1

    invoke-virtual {v0, v1, v2, v3}, Lxi/b$a;->c(Ljava/lang/String;Ljava/lang/String;Z)V

    iput-boolean v3, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->z:Z

    invoke-direct {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->getOrientationEventListener()Lcom/transsion/postdetail/util/g;

    move-result-object v0

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Landroid/view/OrientationEventListener;->enable()V

    :cond_0
    iget-boolean v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->u:Z

    if-nez v0, :cond_1

    sget-object v0, Lcom/tn/lib/util/networkinfo/f;->a:Lcom/tn/lib/util/networkinfo/f;

    invoke-virtual {v0}, Lcom/tn/lib/util/networkinfo/f;->e()Z

    move-result v0

    if-eqz v0, :cond_1

    sget-object v0, Lcom/transsion/videofloat/a;->a:Lcom/transsion/videofloat/a;

    invoke-virtual {v0}, Lcom/transsion/videofloat/a;->d()Z

    move-result v0

    if-nez v0, :cond_1

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e:Lcom/transsion/player/ui/longvideo/a;

    invoke-interface {v0}, Lcom/transsion/player/orplayer/f;->play()V

    :cond_1
    return-void
.end method

.method public final p0(Lcom/tn/lib/view/SecondariesSeekBar;)V
    .locals 2

    new-instance v0, Lkotlin/jvm/internal/Ref$LongRef;

    invoke-direct {v0}, Lkotlin/jvm/internal/Ref$LongRef;-><init>()V

    new-instance v1, Lcom/transsion/player/longvideo/ui/LongVodPlayerView$d;

    invoke-direct {v1, p0, v0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView$d;-><init>(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;Lkotlin/jvm/internal/Ref$LongRef;)V

    invoke-virtual {p1, v1}, Lcom/tn/lib/view/SecondariesSeekBar;->setOnSeekBarChangeListener(Lcom/tn/lib/view/p;)V

    return-void
.end method

.method public final p1()V
    .locals 2

    sget-object v0, Lcom/transsion/baselib/report/RoomActivityLifecycleCallbacks;->a:Lcom/transsion/baselib/report/RoomActivityLifecycleCallbacks;

    invoke-virtual {v0, p0}, Lcom/transsion/baselib/report/RoomActivityLifecycleCallbacks;->j(Lcom/transsion/baselib/report/RoomActivityLifecycleCallbacks$a;)Z

    sget-object v0, Lcom/tn/lib/util/networkinfo/f;->a:Lcom/tn/lib/util/networkinfo/f;

    iget-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e1:Lcom/transsion/player/longvideo/ui/LongVodPlayerView$f;

    invoke-virtual {v0, v1}, Lcom/tn/lib/util/networkinfo/f;->m(Lcom/tn/lib/util/networkinfo/g;)V

    return-void
.end method

.method public final q0()V
    .locals 9

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->G:Lno/a;

    const/4 v1, 0x1

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lno/a;->u()Z

    move-result v0

    if-ne v0, v1, :cond_0

    return-void

    :cond_0
    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e:Lcom/transsion/player/ui/longvideo/a;

    invoke-interface {v0}, Lcom/transsion/player/orplayer/f;->getCurrentVideoFormat()Lqo/b;

    move-result-object v0

    iput-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->P0:Lqo/b;

    if-eqz v0, :cond_1

    invoke-virtual {v0}, Lqo/b;->c()I

    move-result v2

    if-lez v2, :cond_1

    iget-object v2, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->H0:Lcom/transsion/player/longvideo/helper/h;

    invoke-virtual {v0}, Lqo/b;->c()I

    move-result v0

    invoke-static {v0}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v2, v0}, Lcom/transsion/player/longvideo/helper/h;->f(Ljava/lang/String;)V

    :cond_1
    sget-object v3, Lxi/b;->a:Lxi/b$a;

    const-string v4, "LongVodPlayerView"

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->P0:Lqo/b;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v5, "onPrepareInitBitrate, curVideoFormat = "

    invoke-virtual {v2, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v5

    const/4 v6, 0x0

    const/4 v7, 0x4

    const/4 v8, 0x0

    invoke-static/range {v3 .. v8}, Lxi/b$a;->f(Lxi/b$a;Ljava/lang/String;Ljava/lang/String;ZILjava/lang/Object;)V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->P0:Lqo/b;

    if-eqz v0, :cond_7

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->R0:Ljava/util/List;

    check-cast v0, Ljava/util/Collection;

    invoke-interface {v0}, Ljava/util/Collection;->isEmpty()Z

    move-result v0

    xor-int/2addr v0, v1

    if-eqz v0, :cond_7

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->O0:Lqo/d;

    const-string v2, "P"

    if-eqz v0, :cond_5

    invoke-virtual {v0}, Lqo/d;->b()[Lqo/b;

    move-result-object v0

    array-length v3, v0

    const/4 v4, 0x0

    :goto_0
    if-ge v4, v3, :cond_5

    aget-object v5, v0, v4

    iget-object v6, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->P0:Lqo/b;

    if-eqz v6, :cond_2

    invoke-virtual {v6}, Lqo/b;->d()Ljava/lang/String;

    move-result-object v6

    goto :goto_1

    :cond_2
    const/4 v6, 0x0

    :goto_1
    invoke-virtual {v5}, Lqo/b;->d()Ljava/lang/String;

    move-result-object v7

    invoke-static {v6, v7}, Lkotlin/jvm/internal/Intrinsics;->b(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v6

    if-eqz v6, :cond_4

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->m:Loo/f;

    iget-object v0, v0, Loo/f;->u:Landroid/widget/TextView;

    invoke-virtual {v5}, Lqo/b;->c()I

    move-result v3

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v4, v3}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v0, v3}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->S:Lcom/transsion/postdetail/layer/local/n;

    if-nez v0, :cond_3

    goto :goto_2

    :cond_3
    invoke-virtual {v5}, Lqo/b;->c()I

    move-result v3

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v4, v3}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v0, v3}, Lcom/transsion/postdetail/layer/local/n;->I(Ljava/lang/String;)V

    goto :goto_2

    :cond_4
    add-int/lit8 v4, v4, 0x1

    goto :goto_0

    :cond_5
    :goto_2
    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->R0:Ljava/util/List;

    check-cast v0, Ljava/lang/Iterable;

    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_6
    :goto_3
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_7

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lno/b;

    invoke-virtual {v3}, Lno/b;->b()Ljava/lang/String;

    move-result-object v4

    iget-object v5, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->P0:Lqo/b;

    invoke-static {v5}, Lkotlin/jvm/internal/Intrinsics;->d(Ljava/lang/Object;)V

    invoke-virtual {v5}, Lqo/b;->c()I

    move-result v5

    new-instance v6, Ljava/lang/StringBuilder;

    invoke-direct {v6}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v6, v5}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v6, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v6}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v5

    invoke-static {v4, v5}, Lkotlin/jvm/internal/Intrinsics;->b(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v4

    if-eqz v4, :cond_6

    invoke-virtual {v3, v1}, Lno/b;->d(Z)V

    goto :goto_3

    :cond_7
    return-void
.end method

.method public final r0(Landroid/view/ViewGroup;)V
    .locals 10

    invoke-virtual {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->A0()Z

    move-result v0

    if-nez v0, :cond_0

    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object p1, p1, Loo/e;->m:Loo/f;

    iget-object p1, p1, Loo/f;->h:Landroidx/constraintlayout/widget/Group;

    const-string v0, "viewBinding.layoutLand.groupControlPk"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-static {p1}, Lvi/c;->g(Landroid/view/View;)V

    return-void

    :cond_0
    new-instance v0, Lcom/transsion/player/longvideo/helper/LongVodSubtitleHelper;

    iget-object v2, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v3, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->i:Ljava/lang/String;

    iget-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->Z0:Ljava/util/List;

    if-nez v1, :cond_1

    const-string v1, "dubs"

    invoke-static {v1}, Lkotlin/jvm/internal/Intrinsics;->y(Ljava/lang/String;)V

    const/4 v1, 0x0

    :cond_1
    move-object v4, v1

    new-instance v5, Lcom/transsion/player/longvideo/ui/LongVodPlayerView$e;

    invoke-direct {v5, p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView$e;-><init>(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)V

    new-instance v6, Lcom/transsion/player/longvideo/ui/LongVodPlayerView$initSubtitle$2;

    invoke-direct {v6, p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView$initSubtitle$2;-><init>(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)V

    const/4 v7, 0x0

    const/16 v8, 0x20

    const/4 v9, 0x0

    move-object v1, v0

    invoke-direct/range {v1 .. v9}, Lcom/transsion/player/longvideo/helper/LongVodSubtitleHelper;-><init>(Loo/e;Ljava/lang/String;Ljava/util/List;Lcom/transsion/player/longvideo/helper/i;Lkotlin/jvm/functions/Function1;ZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    sget-object v1, Lcom/transsion/baselib/helper/LocalVideoPlayerConfigMmkv;->a:Lcom/transsion/baselib/helper/LocalVideoPlayerConfigMmkv;

    invoke-virtual {v1}, Lcom/transsion/baselib/helper/LocalVideoPlayerConfigMmkv;->c()F

    move-result v1

    invoke-virtual {v0, v1}, Lcom/transsion/player/longvideo/helper/LongVodSubtitleHelper;->I(F)V

    invoke-virtual {v0, p1}, Lcom/transsion/player/longvideo/helper/LongVodSubtitleHelper;->F(Landroid/view/ViewGroup;)V

    iput-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->V:Lcom/transsion/player/longvideo/helper/LongVodSubtitleHelper;

    return-void
.end method

.method public release()V
    .locals 6

    const/4 v0, 0x0

    iput-boolean v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->F:Z

    invoke-virtual {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->y0()Z

    move-result v1

    if-nez v1, :cond_3

    sget-object v1, Lxi/b;->a:Lxi/b$a;

    const-string v2, "release player"

    const-string v3, "LongVodPlayerView"

    const/4 v4, 0x1

    invoke-virtual {v1, v3, v2, v4}, Lxi/b$a;->c(Ljava/lang/String;Ljava/lang/String;Z)V

    sget-object v2, Lcom/transsion/videofloat/manager/w;->a:Lcom/transsion/videofloat/manager/w;

    invoke-virtual {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->getPlayer()Lcom/transsion/player/orplayer/f;

    move-result-object v5

    invoke-virtual {v2, v5}, Lcom/transsion/videofloat/manager/w;->c(Lcom/transsion/player/orplayer/f;)Z

    move-result v2

    if-eqz v2, :cond_0

    const-string v0, "\u64ad\u653e\u5668\u5728\u7f13\u5b58\u4e2d\uff0c\u540e\u7eed\u8981\u7528\u5230\uff0c\u4e0d\u80fdrelease"

    invoke-virtual {v1, v3, v0, v4}, Lxi/b$a;->c(Ljava/lang/String;Ljava/lang/String;Z)V

    return-void

    :cond_0
    iget-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e:Lcom/transsion/player/ui/longvideo/a;

    iget-object v2, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->Q:Lno/c;

    if-eqz v2, :cond_1

    invoke-virtual {v2}, Lno/c;->f()Ljava/lang/String;

    move-result-object v2

    goto :goto_0

    :cond_1
    const/4 v2, 0x0

    :goto_0
    if-eqz v2, :cond_2

    invoke-virtual {v2}, Ljava/lang/Object;->hashCode()I

    move-result v0

    :cond_2
    invoke-static {v0}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    move-result-object v0

    invoke-interface {v1, v0}, Lcom/transsion/player/orplayer/f;->release(Ljava/lang/String;)V

    goto :goto_1

    :cond_3
    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e:Lcom/transsion/player/ui/longvideo/a;

    invoke-interface {v0}, Lcom/transsion/player/orplayer/f;->clearSurfaceOnly()V

    :goto_1
    return-void
.end method

.method public removeNoNetError()V
    .locals 0

    invoke-virtual {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->O0()V

    return-void
.end method

.method public replay()V
    .locals 0

    invoke-direct {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->x1()V

    return-void
.end method

.method public reset()V
    .locals 4

    iget-boolean v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->C:Z

    if-nez v0, :cond_0

    return-void

    :cond_0
    sget-object v0, Lxi/b;->a:Lxi/b$a;

    const-string v1, "LongVodPlayerView"

    const-string v2, "player reset"

    const/4 v3, 0x1

    invoke-virtual {v0, v1, v2, v3}, Lxi/b$a;->c(Ljava/lang/String;Ljava/lang/String;Z)V

    const/4 v0, 0x0

    iput-boolean v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->w:Z

    iput-boolean v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->F:Z

    iput-boolean v3, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->Q0:Z

    iget-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->V:Lcom/transsion/player/longvideo/helper/LongVodSubtitleHelper;

    if-eqz v1, :cond_1

    invoke-virtual {v1}, Lcom/transsion/player/longvideo/helper/LongVodSubtitleHelper;->C()V

    :cond_1
    iget-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->W:Lcom/transsion/player/longvideo/helper/f;

    if-eqz v1, :cond_2

    invoke-virtual {v1}, Lcom/transsion/player/longvideo/helper/f;->c()V

    :cond_2
    iget-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e:Lcom/transsion/player/ui/longvideo/a;

    invoke-interface {v1}, Lcom/transsion/player/orplayer/f;->reset()V

    invoke-direct {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->U0()V

    const-wide/16 v1, 0x0

    invoke-direct {p0, v1, v2}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->t1(J)V

    invoke-direct {p0, v0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->setPauseViewStatus(Z)V

    return-void
.end method

.method public final s0()V
    .locals 4

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->P:Landroid/view/View;

    if-nez v0, :cond_0

    return-void

    :cond_0
    invoke-static {v0}, Lkotlin/jvm/internal/Intrinsics;->d(Ljava/lang/Object;)V

    invoke-virtual {v0}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object v0

    const-string v1, "null cannot be cast to non-null type androidx.constraintlayout.widget.ConstraintLayout.LayoutParams"

    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/String;)V

    check-cast v0, Landroidx/constraintlayout/widget/ConstraintLayout$b;

    iget-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->j:Lcom/transsion/player/longvideo/ui/LongVodUiType;

    sget-object v2, Lcom/transsion/player/longvideo/ui/LongVodUiType;->LAND:Lcom/transsion/player/longvideo/ui/LongVodUiType;

    const/4 v3, 0x0

    if-ne v1, v2, :cond_1

    const/high16 v1, 0x42900000    # 72.0f

    invoke-static {v1}, Lcom/blankj/utilcode/util/f0;->a(F)I

    move-result v1

    goto :goto_0

    :cond_1
    const/4 v1, 0x0

    :goto_0
    iput v1, v0, Landroid/view/ViewGroup$MarginLayoutParams;->bottomMargin:I

    iget-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->j:Lcom/transsion/player/longvideo/ui/LongVodUiType;

    if-ne v1, v2, :cond_2

    iget-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->W:Lcom/transsion/player/longvideo/helper/f;

    if-eqz v1, :cond_2

    invoke-virtual {v1}, Lcom/transsion/player/longvideo/helper/f;->a()I

    move-result v3

    :cond_2
    invoke-virtual {v0, v3}, Landroid/view/ViewGroup$MarginLayoutParams;->setMarginStart(I)V

    iget-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->P:Landroid/view/View;

    invoke-static {v1}, Lkotlin/jvm/internal/Intrinsics;->d(Ljava/lang/Object;)V

    invoke-virtual {v1, v0}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    return-void
.end method

.method public final s1()V
    .locals 2

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->V:Lcom/transsion/player/longvideo/helper/LongVodSubtitleHelper;

    if-eqz v0, :cond_1

    iget-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->Z0:Ljava/util/List;

    if-nez v1, :cond_0

    const-string v1, "dubs"

    invoke-static {v1}, Lkotlin/jvm/internal/Intrinsics;->y(Ljava/lang/String;)V

    const/4 v1, 0x0

    :cond_0
    invoke-virtual {v0, v1}, Lcom/transsion/player/longvideo/helper/LongVodSubtitleHelper;->G(Ljava/util/List;)V

    :cond_1
    return-void
.end method

.method public final setAudioSelectCallback(Lkotlin/jvm/functions/Function1;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Lcom/transsion/moviedetailapi/bean/DubsInfo;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->Y0:Lkotlin/jvm/functions/Function1;

    return-void
.end method

.method public setCallback(Lmo/a$b;)V
    .locals 1

    const-string v0, "callback"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    iput-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->U:Lmo/a$b;

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->I0:Lcom/transsion/player/longvideo/helper/e;

    if-eqz v0, :cond_0

    invoke-virtual {v0, p1}, Lcom/transsion/player/longvideo/helper/b;->d(Lmo/a$b;)V

    :cond_0
    return-void
.end method

.method public setDataSource(Lno/a;)V
    .locals 9

    const-string v0, "bean"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    const/4 v0, 0x0

    iput-boolean v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->x:Z

    iput-boolean v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->F:Z

    const-wide/16 v1, 0x0

    iput-wide v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->o:J

    iget-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->K:Landroid/view/View;

    if-eqz v1, :cond_0

    invoke-static {v1}, Lvi/c;->g(Landroid/view/View;)V

    :cond_0
    iget-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->b:Lcom/transsion/player/longvideo/ui/dialog/LongVodPlayerViewAdHelper;

    invoke-virtual {v1, p1}, Lcom/transsion/player/longvideo/ui/dialog/LongVodPlayerViewAdHelper;->a0(Lno/a;)V

    iget-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->h:Ljava/lang/String;

    invoke-virtual {p0, v1, p1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->n0(Ljava/lang/String;Lno/a;)V

    invoke-virtual {p1}, Lno/a;->a()Ljava/util/List;

    move-result-object v1

    iput-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->Z0:Ljava/util/List;

    invoke-virtual {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->s1()V

    sget-object v1, Lxi/b;->a:Lxi/b$a;

    const-string v3, "LongVodPlayerView"

    iget-boolean v2, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->C:Z

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    const-string v5, "setDataSource,  isNewPlayer\uff1a"

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v2}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    const/4 v5, 0x0

    const/4 v6, 0x4

    const/4 v7, 0x0

    move-object v2, v1

    invoke-static/range {v2 .. v7}, Lxi/b$a;->f(Lxi/b$a;Ljava/lang/String;Ljava/lang/String;ZILjava/lang/Object;)V

    iget-boolean v2, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->C:Z

    if-eqz v2, :cond_1

    invoke-virtual {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->reset()V

    :cond_1
    invoke-virtual {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->onSaveHistory()V

    iget-object v2, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->G:Lno/a;

    const/4 v8, 0x1

    if-eqz v2, :cond_3

    invoke-direct {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a1()V

    iget-object v2, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->S:Lcom/transsion/postdetail/layer/local/n;

    if-eqz v2, :cond_2

    const/4 v3, 0x0

    invoke-static {v2, v0, v8, v3}, Lcom/transsion/postdetail/layer/local/n;->c(Lcom/transsion/postdetail/layer/local/n;ZILjava/lang/Object;)V

    :cond_2
    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->S:Lcom/transsion/postdetail/layer/local/n;

    if-eqz v0, :cond_3

    invoke-virtual {v0}, Lcom/transsion/postdetail/layer/local/n;->g()V

    :cond_3
    iput-boolean v8, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->Q0:Z

    iput-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->G:Lno/a;

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->S:Lcom/transsion/postdetail/layer/local/n;

    if-nez v0, :cond_4

    goto :goto_0

    :cond_4
    invoke-virtual {p1}, Lno/a;->h()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Lcom/transsion/postdetail/layer/local/n;->G(Ljava/lang/String;)V

    :goto_0
    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->S:Lcom/transsion/postdetail/layer/local/n;

    if-eqz v0, :cond_5

    iget-boolean v2, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->g1:Z

    invoke-virtual {v0, v2}, Lcom/transsion/postdetail/layer/local/n;->D(Z)V

    :cond_5
    invoke-virtual {p1}, Lno/a;->h()Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->i:Ljava/lang/String;

    iget-object v2, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v2, v2, Loo/e;->m:Loo/f;

    iget-object v2, v2, Loo/f;->n:Lcom/transsion/postdetail/ui/view/ImmScaleView;

    iget-object v3, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->G:Lno/a;

    const-string v4, ""

    if-eqz v3, :cond_6

    invoke-virtual {v3}, Lno/a;->n()Ljava/lang/String;

    move-result-object v3

    if-nez v3, :cond_7

    :cond_6
    move-object v3, v4

    :cond_7
    invoke-virtual {v2, v0, v3}, Lcom/transsion/postdetail/ui/view/ImmScaleView;->setPageParams(Ljava/lang/String;Ljava/lang/String;)V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->m:Loo/f;

    iget-object v0, v0, Loo/f;->A:Lcom/transsion/postdetail/ui/view/ImmSpeedView;

    iget-object v2, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->i:Ljava/lang/String;

    iget-object v3, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->G:Lno/a;

    if-eqz v3, :cond_9

    invoke-virtual {v3}, Lno/a;->n()Ljava/lang/String;

    move-result-object v3

    if-nez v3, :cond_8

    goto :goto_1

    :cond_8
    move-object v4, v3

    :cond_9
    :goto_1
    invoke-virtual {v0, v2, v4}, Lcom/transsion/postdetail/ui/view/ImmSpeedView;->setPageParams(Ljava/lang/String;Ljava/lang/String;)V

    invoke-virtual {p0, p1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->u1(Lno/a;)V

    iget-boolean v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->C:Z

    if-eqz v0, :cond_a

    invoke-virtual {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e1()V

    :cond_a
    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->H0:Lcom/transsion/player/longvideo/helper/h;

    invoke-virtual {v0, p1}, Lcom/transsion/player/longvideo/helper/h;->a(Lno/a;)Lno/c;

    move-result-object v0

    if-eqz v0, :cond_e

    iget-boolean v2, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->C:Z

    invoke-direct {p0, v2}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->o1(Z)V

    iget-object v2, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->V:Lcom/transsion/player/longvideo/helper/LongVodSubtitleHelper;

    if-eqz v2, :cond_b

    invoke-virtual {v2, p1, v0}, Lcom/transsion/player/longvideo/helper/LongVodSubtitleHelper;->w(Lno/a;Lno/c;)V

    :cond_b
    invoke-virtual {p1}, Lno/a;->u()Z

    move-result v2

    if-eqz v2, :cond_c

    invoke-virtual {p0, p1, v0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->m0(Lno/a;Lno/c;)V

    :cond_c
    iput-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->H:Lno/c;

    invoke-virtual {p0, p1, v0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->T0(Lno/a;Lno/c;)V

    iget-boolean p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->C:Z

    if-eqz p1, :cond_d

    invoke-direct {p0, v0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->setPlayerDataSourceAdPrepare(Lno/c;)V

    goto :goto_2

    :cond_d
    invoke-direct {p0, v8}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->setPauseViewStatus(Z)V

    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e:Lcom/transsion/player/ui/longvideo/a;

    invoke-interface {p1}, Lcom/transsion/player/orplayer/f;->play()V

    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->V:Lcom/transsion/player/longvideo/helper/LongVodSubtitleHelper;

    if-eqz p1, :cond_e

    invoke-virtual {p1, v8}, Lcom/transsion/player/longvideo/helper/LongVodSubtitleHelper;->E(Z)V

    :cond_e
    :goto_2
    const-string v3, "LongVodPlayerView"

    const-string v4, "setDataSource, reset isNewPlayer"

    const/4 v5, 0x0

    const/4 v6, 0x4

    const/4 v7, 0x0

    move-object v2, v1

    invoke-static/range {v2 .. v7}, Lxi/b$a;->t(Lxi/b$a;Ljava/lang/String;Ljava/lang/String;ZILjava/lang/Object;)V

    iput-boolean v8, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->C:Z

    sget-object p1, Lcom/transsion/baseui/music/MusicFloatManager;->g:Lcom/transsion/baseui/music/MusicFloatManager$a;

    invoke-virtual {p1}, Lcom/transsion/baseui/music/MusicFloatManager$a;->b()Lcom/transsion/baseui/music/MusicFloatManager;

    move-result-object p1

    invoke-virtual {p1}, Lcom/transsion/baseui/music/MusicFloatManager;->k()Lcom/transsion/player/mediasession/MediaItem;

    move-result-object p1

    if-nez p1, :cond_f

    goto :goto_3

    :cond_f
    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->i:Ljava/lang/String;

    invoke-virtual {p1, v0}, Lcom/transsion/player/mediasession/MediaItem;->setPageName(Ljava/lang/String;)V

    :goto_3
    return-void
.end method

.method public final setFeedBackVisible(Z)V
    .locals 2

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->o:Loo/d;

    iget-object v0, v0, Loo/d;->c:Landroidx/appcompat/widget/AppCompatTextView;

    const-string v1, "viewBinding.layoutTopToolBar.tvFeedback"

    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-static {v0, p1}, Lvi/c;->j(Landroid/view/View;Z)V

    return-void
.end method

.method public setFloatIsShow(Z)V
    .locals 0

    iput-boolean p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->D:Z

    return-void
.end method

.method public final setLongVodPlayerViewAdHelper(Lcom/transsion/player/longvideo/ui/dialog/LongVodPlayerViewAdHelper;)V
    .locals 1

    const-string v0, "<set-?>"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    iput-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->b:Lcom/transsion/player/longvideo/ui/dialog/LongVodPlayerViewAdHelper;

    return-void
.end method

.method public final setMusicLikedFragment(Z)V
    .locals 0

    iput-boolean p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->g1:Z

    return-void
.end method

.method public setSpeed(F)V
    .locals 4

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e:Lcom/transsion/player/ui/longvideo/a;

    invoke-interface {v0, p1}, Lcom/transsion/player/orplayer/f;->setSpeed(F)V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->m:Loo/f;

    iget-object v0, v0, Loo/f;->A:Lcom/transsion/postdetail/ui/view/ImmSpeedView;

    const-string v1, "viewBinding.layoutLand.tvPlaySpeed"

    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    const/4 v1, 0x2

    const/4 v2, 0x0

    const/4 v3, 0x0

    invoke-static {v0, p1, v3, v1, v2}, Lcom/transsion/postdetail/ui/view/ImmSpeedView;->updateSpeed$default(Lcom/transsion/postdetail/ui/view/ImmSpeedView;FZILjava/lang/Object;)V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->V:Lcom/transsion/player/longvideo/helper/LongVodSubtitleHelper;

    if-eqz v0, :cond_0

    invoke-virtual {v0, p1}, Lcom/transsion/player/longvideo/helper/LongVodSubtitleHelper;->I(F)V

    :cond_0
    return-void
.end method

.method public showNoNetError()V
    .locals 3

    const/4 v0, 0x1

    const/4 v1, 0x0

    const/4 v2, 0x0

    invoke-static {p0, v2, v0, v1}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->g1(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;ZILjava/lang/Object;)V

    return-void
.end method

.method public showReplay()V
    .locals 4

    invoke-direct {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->x0()Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->j:Lcom/transsion/player/longvideo/ui/LongVodUiType;

    sget-object v1, Lcom/transsion/player/longvideo/ui/LongVodUiType;->MIDDLE:Lcom/transsion/player/longvideo/ui/LongVodUiType;

    const/4 v2, 0x0

    if-ne v0, v1, :cond_1

    const/4 v0, 0x1

    invoke-virtual {p0, v0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->hideBottomController(Z)V

    goto :goto_0

    :cond_1
    invoke-direct {p0, v2}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->c1(Z)V

    :goto_0
    invoke-direct {p0, v2}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->setPauseViewStatus(Z)V

    invoke-direct {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->Q0()V

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->J:Landroid/view/View;

    if-nez v0, :cond_2

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->y:Landroid/view/ViewStub;

    invoke-virtual {v0}, Landroid/view/ViewStub;->inflate()Landroid/view/View;

    move-result-object v0

    iput-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->J:Landroid/view/View;

    :cond_2
    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->J:Landroid/view/View;

    if-eqz v0, :cond_3

    invoke-static {v0}, Lvi/c;->k(Landroid/view/View;)V

    :cond_3
    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->J:Landroid/view/View;

    if-eqz v0, :cond_5

    iget-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->j:Lcom/transsion/player/longvideo/ui/LongVodUiType;

    sget-object v3, Lcom/transsion/player/longvideo/ui/LongVodUiType;->LAND:Lcom/transsion/player/longvideo/ui/LongVodUiType;

    if-ne v1, v3, :cond_4

    iput-boolean v2, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->K0:Z

    :cond_4
    sget v1, Lcom/transsion/player/longvideo/R$id;->tv_replay:I

    invoke-virtual {v0, v1}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/TextView;

    new-instance v1, Lcom/transsion/player/longvideo/ui/a;

    invoke-direct {v1, p0}, Lcom/transsion/player/longvideo/ui/a;-><init>(Lcom/transsion/player/longvideo/ui/LongVodPlayerView;)V

    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    :cond_5
    return-void
.end method

.method public final u1(Lno/a;)V
    .locals 4

    invoke-virtual {p1}, Lno/a;->d()I

    move-result v0

    if-lez v0, :cond_0

    invoke-virtual {p1}, Lno/a;->o()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1}, Lno/a;->d()I

    move-result v1

    invoke-virtual {p1}, Lno/a;->k()I

    move-result v2

    invoke-virtual {p1}, Lno/a;->q()Ljava/lang/Integer;

    move-result-object v3

    invoke-static {v1, v2, v3}, Lcom/transsion/baselib/utils/g;->b(IILjava/lang/Integer;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p1}, Lno/a;->e()Ljava/lang/String;

    move-result-object p1

    invoke-static {p1}, Lcom/transsion/baselib/utils/g;->d(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v0, " "

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    goto :goto_0

    :cond_0
    invoke-virtual {p1}, Lno/a;->o()Ljava/lang/String;

    move-result-object p1

    :goto_0
    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v0, v0, Loo/e;->o:Loo/d;

    iget-object v0, v0, Loo/d;->g:Landroidx/appcompat/widget/AppCompatTextView;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    invoke-virtual {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->b1()V

    return-void
.end method

.method public final v1(Landroid/view/MotionEvent;Z)V
    .locals 4

    sget-object v0, Lcom/transsion/baseui/util/c;->a:Lcom/transsion/baseui/util/c;

    iget-object v1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object v1, v1, Loo/e;->n:Loo/g;

    iget-object v1, v1, Loo/g;->d:Landroidx/appcompat/widget/AppCompatImageView;

    invoke-virtual {v1}, Landroid/view/View;->getId()I

    move-result v1

    const-wide/16 v2, 0x1f4

    invoke-virtual {v0, v1, v2, v3}, Lcom/transsion/baseui/util/c;->a(IJ)Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    sget-object v0, Lxi/b;->a:Lxi/b$a;

    const-string v1, "LongVodPlayerView"

    const-string v2, "videoPause click"

    const/4 v3, 0x1

    invoke-virtual {v0, v1, v2, v3}, Lxi/b$a;->c(Ljava/lang/String;Ljava/lang/String;Z)V

    invoke-direct {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->x0()Z

    move-result v0

    if-eqz v0, :cond_2

    sget-object p1, Lcom/tn/lib/util/networkinfo/f;->a:Lcom/tn/lib/util/networkinfo/f;

    invoke-virtual {p1}, Lcom/tn/lib/util/networkinfo/f;->e()Z

    move-result p1

    if-nez p1, :cond_1

    sget-object p1, Lcom/tn/lib/widget/toast/core/h;->a:Lcom/tn/lib/widget/toast/core/h;

    sget p2, Lcom/transsion/baseui/R$string;->base_network_fail:I

    invoke-virtual {p1, p2}, Lcom/tn/lib/widget/toast/core/h;->k(I)V

    :cond_1
    return-void

    :cond_2
    iput-boolean v3, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->y:Z

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->K:Landroid/view/View;

    if-eqz v0, :cond_3

    invoke-static {v0}, Lvi/c;->g(Landroid/view/View;)V

    :cond_3
    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e:Lcom/transsion/player/ui/longvideo/a;

    invoke-interface {v0}, Lcom/transsion/player/orplayer/f;->isComplete()Z

    move-result v0

    if-eqz v0, :cond_5

    invoke-direct {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->w0()Z

    move-result p1

    if-eqz p1, :cond_4

    return-void

    :cond_4
    invoke-direct {p0}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->x1()V

    return-void

    :cond_5
    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e:Lcom/transsion/player/ui/longvideo/a;

    invoke-interface {v0}, Lcom/transsion/player/orplayer/f;->isPlaying()Z

    move-result v0

    if-eqz v0, :cond_6

    invoke-direct {p0, p1, p2}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->Q(Landroid/view/MotionEvent;Z)V

    goto :goto_0

    :cond_6
    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->J0:Lcom/transsion/player/longvideo/helper/a;

    if-eqz p1, :cond_7

    invoke-virtual {p1}, Lcom/transsion/player/longvideo/helper/a;->b()Z

    move-result p1

    if-ne p1, v3, :cond_7

    goto :goto_0

    :cond_7
    const/4 p1, 0x0

    iput-boolean p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->u:Z

    invoke-direct {p0, v3}, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->setPauseViewStatus(Z)V

    sget-object p1, Lcom/transsion/videofloat/a;->a:Lcom/transsion/videofloat/a;

    invoke-virtual {p1}, Lcom/transsion/videofloat/a;->a()V

    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e:Lcom/transsion/player/ui/longvideo/a;

    invoke-interface {p1}, Lcom/transsion/player/orplayer/f;->play()V

    :goto_0
    return-void
.end method

.method public final y0()Z
    .locals 2

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->g:Lcom/transsion/player/longvideo/constants/LongVodPageType;

    sget-object v1, Lcom/transsion/player/longvideo/constants/LongVodPageType;->MUSIC:Lcom/transsion/player/longvideo/constants/LongVodPageType;

    if-ne v0, v1, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public final y1(Lcom/transsion/player/enum/ScaleMode;Ljava/lang/String;)V
    .locals 1

    iget-object v0, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->e:Lcom/transsion/player/ui/longvideo/a;

    invoke-interface {v0, p1}, Lcom/transsion/player/orplayer/f;->setScaleMode(Lcom/transsion/player/enum/ScaleMode;)V

    iget-object p1, p0, Lcom/transsion/player/longvideo/ui/LongVodPlayerView;->a:Loo/e;

    iget-object p1, p1, Loo/e;->m:Loo/f;

    iget-object p1, p1, Loo/f;->z:Landroid/widget/TextView;

    invoke-virtual {p1, p2}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    return-void
.end method
