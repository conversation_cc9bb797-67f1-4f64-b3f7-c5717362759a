.class public Lcom/bytedance/sdk/openadsdk/core/nativeexpress/dG;
.super Ljava/lang/Object;


# instance fields
.field public Fj:I

.field public eV:I

.field public ex:F

.field public hjc:I


# direct methods
.method public constructor <init>(IFII)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/dG;->Fj:I

    iput p2, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/dG;->ex:F

    iput p3, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/dG;->hjc:I

    iput p4, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/dG;->eV:I

    return-void
.end method
