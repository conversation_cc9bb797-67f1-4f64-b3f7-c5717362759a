.class interface abstract Lcom/bytedance/sdk/openadsdk/core/nativeexpress/ex$hjc;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/sdk/openadsdk/core/nativeexpress/ex;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "hjc"
.end annotation


# virtual methods
.method public abstract Fj()V
.end method

.method public abstract Fj(II)V
.end method

.method public abstract Fj(Ljava/lang/String;)V
.end method

.method public abstract ex(Ljava/lang/String;)V
.end method
