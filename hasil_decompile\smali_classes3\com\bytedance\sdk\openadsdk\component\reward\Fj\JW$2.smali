.class Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW$2;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/component/eV/JU;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW;->Fj()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lcom/bytedance/sdk/component/eV/JU<",
        "Landroid/graphics/Bitmap;",
        ">;"
    }
.end annotation


# instance fields
.field final synthetic Fj:Ljava/lang/String;

.field final synthetic ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW;Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW$2;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW;

    iput-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW$2;->Fj:Ljava/lang/String;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj(ILjava/lang/String;Ljava/lang/Throwable;)V
    .locals 0
    .param p3    # Ljava/lang/Throwable;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW$2;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW;)Landroid/widget/ImageView;

    move-result-object p1

    if-eqz p1, :cond_0

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW$2;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW;)Landroid/widget/ImageView;

    move-result-object p1

    const/16 p2, 0x8

    invoke-virtual {p1, p2}, Landroid/widget/ImageView;->setVisibility(I)V

    :cond_0
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW$2;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW;

    const/4 p2, -0x2

    iget-object p3, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW$2;->Fj:Ljava/lang/String;

    invoke-static {p1, p2, p3}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW;ILjava/lang/String;)V

    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/component/eV/rAx;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/bytedance/sdk/component/eV/rAx<",
            "Landroid/graphics/Bitmap;",
            ">;)V"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW$2;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW;)Landroid/widget/ImageView;

    move-result-object v0

    if-eqz v0, :cond_1

    if-eqz p1, :cond_1

    invoke-interface {p1}, Lcom/bytedance/sdk/component/eV/rAx;->ex()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Landroid/graphics/Bitmap;

    if-nez p1, :cond_0

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW$2;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW;

    const/4 v0, -0x1

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW$2;->Fj:Ljava/lang/String;

    invoke-static {p1, v0, v1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW;ILjava/lang/String;)V

    return-void

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW$2;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW;)Landroid/widget/ImageView;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/widget/ImageView;->setImageBitmap(Landroid/graphics/Bitmap;)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW$2;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW;

    const/4 v0, 0x1

    invoke-static {p1, v0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW;Z)Z

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW$2;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW;->ex(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/JW;)V

    :cond_1
    return-void
.end method
