.class Lcom/bytedance/sdk/openadsdk/component/reward/view/RFDownloadBarLayout$5;
.super Lcom/bytedance/sdk/openadsdk/core/ex/svN;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/reward/view/RFDownloadBarLayout;->ex()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/component/reward/view/RFDownloadBarLayout;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/reward/view/RFDownloadBarLayout;Ljava/lang/String;Lcom/bytedance/sdk/openadsdk/core/svN/Fj;Lcom/bytedance/sdk/openadsdk/core/ex/hjc;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/view/RFDownloadBarLayout$5;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/view/RFDownloadBarLayout;

    invoke-direct {p0, p2, p3, p4}, Lcom/bytedance/sdk/openadsdk/core/ex/svN;-><init>(Ljava/lang/String;Lcom/bytedance/sdk/openadsdk/core/svN/Fj;Lcom/bytedance/sdk/openadsdk/core/ex/hjc;)V

    return-void
.end method
