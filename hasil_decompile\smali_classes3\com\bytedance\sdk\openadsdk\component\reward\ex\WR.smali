.class public Lcom/bytedance/sdk/openadsdk/component/reward/ex/WR;
.super Ljava/lang/Object;


# direct methods
.method public static Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;)Lcom/bytedance/sdk/openadsdk/component/reward/ex/ex;
    .locals 2
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    instance-of v1, v0, Lcom/bytedance/sdk/openadsdk/core/model/rS;

    if-eqz v1, :cond_0

    move-object v1, v0

    check-cast v1, Lcom/bytedance/sdk/openadsdk/core/model/rS;

    invoke-virtual {v1}, Lcom/bytedance/sdk/openadsdk/core/model/rS;->Wr()Z

    move-result v1

    if-eqz v1, :cond_0

    new-instance v0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Ubf;

    invoke-direct {v0, p0}, Lcom/bytedance/sdk/openadsdk/component/reward/ex/Ubf;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;)V

    return-object v0

    :cond_0
    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/model/mE;->rAx(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v1

    if-eqz v1, :cond_1

    new-instance v0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/mSE;

    invoke-direct {v0, p0}, Lcom/bytedance/sdk/openadsdk/component/reward/ex/mSE;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;)V

    return-object v0

    :cond_1
    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/ex/svN;->hjc(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v1

    if-eqz v1, :cond_2

    new-instance v0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/svN;

    invoke-direct {v0, p0}, Lcom/bytedance/sdk/openadsdk/component/reward/ex/svN;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;)V

    return-object v0

    :cond_2
    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/ex/BcC;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v1

    if-eqz v1, :cond_3

    new-instance v0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/BcC;

    invoke-direct {v0, p0}, Lcom/bytedance/sdk/openadsdk/component/reward/ex/BcC;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;)V

    return-object v0

    :cond_3
    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/ex/mSE;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v1

    if-eqz v1, :cond_4

    new-instance v0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/mSE;

    invoke-direct {v0, p0}, Lcom/bytedance/sdk/openadsdk/component/reward/ex/mSE;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;)V

    return-object v0

    :cond_4
    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/ex/hjc;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v0

    if-eqz v0, :cond_5

    new-instance v0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/hjc;

    invoke-direct {v0, p0}, Lcom/bytedance/sdk/openadsdk/component/reward/ex/hjc;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;)V

    return-object v0

    :cond_5
    new-instance v0, Lcom/bytedance/sdk/openadsdk/component/reward/ex/eV;

    invoke-direct {v0, p0}, Lcom/bytedance/sdk/openadsdk/component/reward/ex/eV;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;)V

    return-object v0
.end method
