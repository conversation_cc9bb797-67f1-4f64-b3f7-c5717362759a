.class Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$1;
.super Ljava/lang/Object;

# interfaces
.implements Landroid/view/View$OnAttachStateChangeListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$1;->Fj:Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public onViewAttachedToWindow(Landroid/view/View;)V
    .locals 2

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$1;->Fj:Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;->Fj(Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;)Z

    move-result p1

    if-nez p1, :cond_0

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$1;->Fj:Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;

    iget-object v0, p1, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/core/bannerexpress/BannerExpressView;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/BannerExpressView;->getCurView()Lcom/bytedance/sdk/openadsdk/core/nativeexpress/NativeExpressView;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$1;->Fj:Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;

    iget-object v1, v1, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;->hjc:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {p1, v0, v1}, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;->Fj(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/NativeExpressView;Lcom/bytedance/sdk/openadsdk/core/model/Ql;)V

    :cond_0
    return-void
.end method

.method public onViewDetachedFromWindow(Landroid/view/View;)V
    .locals 0

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj$1;->Fj:Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/core/bannerexpress/BannerExpressView;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/core/bannerexpress/BannerExpressView;->hjc()V

    return-void
.end method
