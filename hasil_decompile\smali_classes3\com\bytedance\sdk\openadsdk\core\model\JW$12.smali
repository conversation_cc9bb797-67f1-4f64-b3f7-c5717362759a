.class Lcom/bytedance/sdk/openadsdk/core/model/JW$12;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/openadsdk/core/widget/ex;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/core/model/JW;->mSE()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/core/model/JW;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/core/model/JW;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/model/JW$12;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/JW;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj()V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/JW$12;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/JW;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/model/JW;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/JW;)Lcom/bytedance/sdk/openadsdk/core/widget/Fj/Ubf;

    move-result-object v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/model/JW$12;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/JW;

    invoke-static {v0}, Lcom/bytedance/sdk/openadsdk/core/model/JW;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/JW;)Lcom/bytedance/sdk/openadsdk/core/widget/Fj/Ubf;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/widget/Fj/Ubf;->ex()V

    :cond_0
    return-void
.end method
