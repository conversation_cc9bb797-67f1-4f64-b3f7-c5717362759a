.class public Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bytedance/sdk/openadsdk/core/act/AdActAction$BindCustomTabsServiceCallback;,
        Lcom/bytedance/sdk/openadsdk/core/act/AdActAction$PAGCustomTabsCallback;,
        Lcom/bytedance/sdk/openadsdk/core/act/AdActAction$PAGEngagementSignalsCallback;
    }
.end annotation


# instance fields
.field private BcC:Lcom/bytedance/sdk/openadsdk/core/act/ActServiceConnection;

.field public Fj:Landroidx/browser/customtabs/l;

.field private JU:Lcom/bytedance/sdk/openadsdk/core/act/AdActAction$BindCustomTabsServiceCallback;

.field private JW:Ljava/lang/Long;

.field private Ko:Z

.field private Ql:Lcom/bytedance/sdk/openadsdk/core/act/ex;

.field private Tc:J

.field private UYd:Z

.field private Ubf:Ljava/lang/String;

.field private WR:Landroidx/browser/customtabs/CustomTabsClient;

.field private dG:Z

.field private eV:Ljava/lang/String;

.field private ex:Landroid/content/Context;

.field private hjc:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

.field private mSE:Z

.field private rAx:Z

.field private rS:Landroidx/browser/customtabs/c;

.field private svN:Landroidx/browser/customtabs/j;


# direct methods
.method public constructor <init>(Landroid/content/Context;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Ljava/lang/String;Ljava/lang/String;)V
    .locals 2

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->WR:Landroidx/browser/customtabs/CustomTabsClient;

    const/4 v0, 0x0

    iput-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->mSE:Z

    iput-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->Ko:Z

    iput-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->rAx:Z

    iput-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->UYd:Z

    iput-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->dG:Z

    const-wide/16 v0, 0x0

    iput-wide v0, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->Tc:J

    new-instance v0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction$1;

    invoke-direct {v0, p0}, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction$1;-><init>(Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;)V

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->Ql:Lcom/bytedance/sdk/openadsdk/core/act/ex;

    new-instance v0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction$PAGEngagementSignalsCallback;

    invoke-direct {v0, p0}, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction$PAGEngagementSignalsCallback;-><init>(Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;)V

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->Fj:Landroidx/browser/customtabs/l;

    new-instance v0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction$PAGCustomTabsCallback;

    invoke-direct {v0, p0}, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction$PAGCustomTabsCallback;-><init>(Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;)V

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->rS:Landroidx/browser/customtabs/c;

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->ex:Landroid/content/Context;

    iput-object p2, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->hjc:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    iput-object p3, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->eV:Ljava/lang/String;

    iput-object p4, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->Ubf:Ljava/lang/String;

    return-void
.end method

.method public static synthetic BcC(Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;)Ljava/lang/Long;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->JW:Ljava/lang/Long;

    return-object p0
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;J)J
    .locals 0

    iput-wide p1, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->Tc:J

    return-wide p1
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;Landroidx/browser/customtabs/j;)Landroidx/browser/customtabs/j;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->svN:Landroidx/browser/customtabs/j;

    return-object p1
.end method

.method private Fj(I)Lcom/bytedance/sdk/openadsdk/Ko/Fj/Fj;
    .locals 2

    new-instance v0, Lcom/bytedance/sdk/openadsdk/Ko/Fj/Fj;

    invoke-direct {v0}, Lcom/bytedance/sdk/openadsdk/Ko/Fj/Fj;-><init>()V

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->eV:Ljava/lang/String;

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/Ko/Fj/Fj;->Fj(Ljava/lang/String;)V

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->hjc:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/Ko/Fj/Fj;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)V

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->hjc:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {v1}, Lcom/bytedance/sdk/openadsdk/utils/rXP;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/Ko/Fj/Fj;->ex(Ljava/lang/String;)V

    invoke-virtual {v0, p1}, Lcom/bytedance/sdk/openadsdk/Ko/Fj/Fj;->Fj(I)V

    const/4 p1, 0x0

    invoke-virtual {v0, p1}, Lcom/bytedance/sdk/openadsdk/Ko/Fj/Fj;->Fj(Z)V

    const/16 p1, 0x8

    invoke-virtual {v0, p1}, Lcom/bytedance/sdk/openadsdk/Ko/Fj/Fj;->ex(I)V

    return-object v0
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;Lcom/bytedance/sdk/openadsdk/core/act/ActServiceConnection;)Lcom/bytedance/sdk/openadsdk/core/act/ActServiceConnection;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->BcC:Lcom/bytedance/sdk/openadsdk/core/act/ActServiceConnection;

    return-object p1
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;)Lcom/bytedance/sdk/openadsdk/core/model/Ql;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->hjc:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    return-object p0
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;Ljava/lang/Long;)Ljava/lang/Long;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->JW:Ljava/lang/Long;

    return-object p1
.end method

.method private Fj()V
    .locals 2

    :try_start_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->BcC:Lcom/bytedance/sdk/openadsdk/core/act/ActServiceConnection;

    if-nez v0, :cond_0

    return-void

    :cond_0
    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->ex:Landroid/content/Context;

    invoke-virtual {v1, v0}, Landroid/content/Context;->unbindService(Landroid/content/ServiceConnection;)V

    const/4 v0, 0x0

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->WR:Landroidx/browser/customtabs/CustomTabsClient;

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->svN:Landroidx/browser/customtabs/j;

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->BcC:Lcom/bytedance/sdk/openadsdk/core/act/ActServiceConnection;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    return-void

    :catchall_0
    move-exception v0

    const-string v1, "AdActAction"

    invoke-virtual {v0}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    move-result-object v0

    invoke-static {v1, v0}, Lcom/bytedance/sdk/component/utils/dG;->Fj(Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method private Fj(Landroidx/browser/customtabs/CustomTabsClient;)V
    .locals 4

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->WR:Landroidx/browser/customtabs/CustomTabsClient;

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->rS:Landroidx/browser/customtabs/c;

    invoke-virtual {p1, v0}, Landroidx/browser/customtabs/CustomTabsClient;->c(Landroidx/browser/customtabs/c;)Landroidx/browser/customtabs/j;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->svN:Landroidx/browser/customtabs/j;

    const/16 p1, 0x9

    invoke-direct {p0, p1}, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->Fj(I)Lcom/bytedance/sdk/openadsdk/Ko/Fj/Fj;

    move-result-object p1

    :try_start_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->svN:Landroidx/browser/customtabs/j;

    sget-object v1, Landroid/os/Bundle;->EMPTY:Landroid/os/Bundle;

    invoke-virtual {v0, v1}, Landroidx/browser/customtabs/j;->h(Landroid/os/Bundle;)Z

    move-result v0

    const/4 v2, 0x0

    if-eqz v0, :cond_1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->svN:Landroidx/browser/customtabs/j;

    iget-object v3, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->Fj:Landroidx/browser/customtabs/l;

    invoke-virtual {v0, v3, v1}, Landroidx/browser/customtabs/j;->l(Landroidx/browser/customtabs/l;Landroid/os/Bundle;)Z

    move-result v0

    const/4 v1, 0x1

    invoke-virtual {p1, v1}, Lcom/bytedance/sdk/openadsdk/Ko/Fj/Fj;->hjc(I)V

    invoke-static {v1}, Lcom/bytedance/sdk/openadsdk/core/act/Fj;->Fj(I)V

    if-eqz v0, :cond_0

    invoke-virtual {p1, v1}, Lcom/bytedance/sdk/openadsdk/Ko/Fj/Fj;->eV(I)V

    invoke-static {v1}, Lcom/bytedance/sdk/openadsdk/core/act/Fj;->ex(I)V

    goto :goto_0

    :catchall_0
    move-exception p1

    goto :goto_1

    :cond_0
    invoke-static {v2}, Lcom/bytedance/sdk/openadsdk/core/act/Fj;->ex(I)V

    goto :goto_0

    :cond_1
    invoke-virtual {p1, v2}, Lcom/bytedance/sdk/openadsdk/Ko/Fj/Fj;->hjc(I)V

    invoke-static {v2}, Lcom/bytedance/sdk/openadsdk/core/act/Fj;->Fj(I)V

    :goto_0
    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/ex/hjc;->Fj(Lcom/bytedance/sdk/openadsdk/Ko/Fj/Fj;)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->JU:Lcom/bytedance/sdk/openadsdk/core/act/AdActAction$BindCustomTabsServiceCallback;

    if-eqz p1, :cond_2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->svN:Landroidx/browser/customtabs/j;

    invoke-interface {p1, v0}, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction$BindCustomTabsServiceCallback;->onBindSuccess(Landroidx/browser/customtabs/j;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :cond_2
    return-void

    :goto_1
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->JU:Lcom/bytedance/sdk/openadsdk/core/act/AdActAction$BindCustomTabsServiceCallback;

    if-eqz v0, :cond_3

    const/16 v1, 0xb

    invoke-virtual {p1}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    move-result-object p1

    invoke-interface {v0, v1, p1}, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction$BindCustomTabsServiceCallback;->onBindFail(ILjava/lang/String;)V

    :cond_3
    return-void
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;Landroidx/browser/customtabs/CustomTabsClient;)V
    .locals 0

    invoke-direct {p0, p1}, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->Fj(Landroidx/browser/customtabs/CustomTabsClient;)V

    return-void
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;Ljava/lang/String;Lorg/json/JSONObject;J)V
    .locals 0

    invoke-direct {p0, p1, p2, p3, p4}, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->Fj(Ljava/lang/String;Lorg/json/JSONObject;J)V

    return-void
.end method

.method private Fj(Ljava/lang/String;Lorg/json/JSONObject;J)V
    .locals 4

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->hjc:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    if-eqz v0, :cond_2

    invoke-static {p1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_2

    :cond_0
    const/4 v0, 0x0

    :try_start_0
    const-string v1, "is_playable"

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->hjc:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {v2}, Lcom/bytedance/sdk/openadsdk/core/model/mE;->ex(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v2

    invoke-virtual {p2, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    const-string v1, "usecache"

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/video/ex/Fj;->Fj()Lcom/bytedance/sdk/openadsdk/core/video/ex/Fj;

    move-result-object v2

    iget-object v3, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->hjc:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v2, v3}, Lcom/bytedance/sdk/openadsdk/core/video/ex/Fj;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Z

    move-result v2

    invoke-virtual {p2, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    new-instance v1, Lorg/json/JSONObject;

    invoke-direct {v1}, Lorg/json/JSONObject;-><init>()V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_1

    :try_start_1
    const-string v0, "ad_extra_data"

    invoke-virtual {p2}, Lorg/json/JSONObject;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {v1, v0, p2}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    const-wide/16 v2, 0x0

    cmp-long p2, p3, v2

    if-lez p2, :cond_1

    const-string p2, "duration"

    invoke-virtual {v1, p2, p3, p4}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    goto :goto_1

    :catchall_0
    move-exception p2

    move-object v0, v1

    goto :goto_0

    :catchall_1
    move-exception p2

    :goto_0
    const-string p3, "AdActAction"

    invoke-virtual {p2}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    move-result-object p2

    invoke-static {p3, p2}, Lcom/bytedance/sdk/component/utils/dG;->Fj(Ljava/lang/String;Ljava/lang/String;)V

    move-object v1, v0

    :cond_1
    :goto_1
    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->hjc:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-static {p2}, Lcom/bytedance/sdk/openadsdk/utils/rXP;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;)Ljava/lang/String;

    move-result-object p3

    invoke-static {p2, p3, p1, v1}, Lcom/bytedance/sdk/openadsdk/ex/hjc;->hjc(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Ljava/lang/String;Ljava/lang/String;Lorg/json/JSONObject;)V

    :cond_2
    :goto_2
    return-void
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;Z)Z
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->mSE:Z

    return p1
.end method

.method public static synthetic Ko(Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;)V
    .locals 0

    invoke-direct {p0}, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->Fj()V

    return-void
.end method

.method public static synthetic Ubf(Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;)Landroid/content/Context;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->ex:Landroid/content/Context;

    return-object p0
.end method

.method public static synthetic WR(Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;)Z
    .locals 0

    iget-boolean p0, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->UYd:Z

    return p0
.end method

.method public static synthetic eV(Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;)J
    .locals 2

    iget-wide v0, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->Tc:J

    return-wide v0
.end method

.method public static synthetic eV(Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;Z)Z
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->rAx:Z

    return p1
.end method

.method public static synthetic ex(Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;Landroidx/browser/customtabs/CustomTabsClient;)Landroidx/browser/customtabs/CustomTabsClient;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->WR:Landroidx/browser/customtabs/CustomTabsClient;

    return-object p1
.end method

.method public static synthetic ex(Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;)Z
    .locals 0

    iget-boolean p0, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->mSE:Z

    return p0
.end method

.method public static synthetic ex(Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;Z)Z
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->UYd:Z

    return p1
.end method

.method public static synthetic hjc(Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;)Ljava/lang/String;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->Ubf:Ljava/lang/String;

    return-object p0
.end method

.method public static synthetic hjc(Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;Z)Z
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->Ko:Z

    return p1
.end method

.method public static synthetic mSE(Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;)Z
    .locals 0

    iget-boolean p0, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->rAx:Z

    return p0
.end method

.method public static synthetic rAx(Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;)Z
    .locals 0

    iget-boolean p0, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->dG:Z

    return p0
.end method

.method public static synthetic svN(Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;)Z
    .locals 0

    iget-boolean p0, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->Ko:Z

    return p0
.end method


# virtual methods
.method public Fj(Lcom/bytedance/sdk/openadsdk/core/act/AdActAction$BindCustomTabsServiceCallback;)V
    .locals 2

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->JU:Lcom/bytedance/sdk/openadsdk/core/act/AdActAction$BindCustomTabsServiceCallback;

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->ex:Landroid/content/Context;

    if-eqz p1, :cond_2

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->hjc:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    if-nez p1, :cond_0

    goto :goto_0

    :cond_0
    const/16 p1, 0x8

    :try_start_0
    invoke-direct {p0, p1}, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->Fj(I)Lcom/bytedance/sdk/openadsdk/Ko/Fj/Fj;

    move-result-object p1

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/ex/hjc;->Fj(Lcom/bytedance/sdk/openadsdk/Ko/Fj/Fj;)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->ex:Landroid/content/Context;

    invoke-static {p1}, Lcom/bytedance/sdk/openadsdk/core/act/Fj;->Fj(Landroid/content/Context;)Ljava/lang/String;

    move-result-object p1

    if-nez p1, :cond_1

    return-void

    :cond_1
    new-instance v0, Lcom/bytedance/sdk/openadsdk/core/act/ActServiceConnection;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->Ql:Lcom/bytedance/sdk/openadsdk/core/act/ex;

    invoke-direct {v0, v1}, Lcom/bytedance/sdk/openadsdk/core/act/ActServiceConnection;-><init>(Lcom/bytedance/sdk/openadsdk/core/act/ex;)V

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->BcC:Lcom/bytedance/sdk/openadsdk/core/act/ActServiceConnection;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->ex:Landroid/content/Context;

    invoke-static {v1, p1, v0}, Landroidx/browser/customtabs/CustomTabsClient;->a(Landroid/content/Context;Ljava/lang/String;Landroidx/browser/customtabs/f;)Z
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    return-void

    :catchall_0
    move-exception p1

    invoke-virtual {p1}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    move-result-object p1

    const-string v0, "AdActAction"

    invoke-static {v0, p1}, Lcom/bytedance/sdk/component/utils/dG;->Fj(Ljava/lang/String;Ljava/lang/String;)V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction;->JU:Lcom/bytedance/sdk/openadsdk/core/act/AdActAction$BindCustomTabsServiceCallback;

    if-eqz v0, :cond_2

    const/16 v1, 0xa

    invoke-interface {v0, v1, p1}, Lcom/bytedance/sdk/openadsdk/core/act/AdActAction$BindCustomTabsServiceCallback;->onBindFail(ILjava/lang/String;)V

    :cond_2
    :goto_0
    return-void
.end method
