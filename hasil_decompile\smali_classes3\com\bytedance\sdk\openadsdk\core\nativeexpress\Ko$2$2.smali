.class Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ko$2$2;
.super Ljava/lang/Object;

# interfaces
.implements Landroid/os/MessageQueue$IdleHandler;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ko$2;->Fj(Landroid/os/MessageQueue;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ko$2;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ko$2;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ko$2$2;->Fj:Lcom/bytedance/sdk/openadsdk/core/nativeexpress/Ko$2;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public queueIdle()Z
    .locals 3

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/JU;->eV()Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;

    move-result-object v0

    invoke-interface {v0}, Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;->qg()I

    move-result v0

    const/4 v1, 0x0

    if-lez v0, :cond_0

    new-instance v0, Lcom/bytedance/sdk/openadsdk/core/widget/Fj/ex;

    invoke-direct {v0}, Lcom/bytedance/sdk/openadsdk/core/widget/Fj/ex;-><init>()V

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/core/widget/Fj/ex;->Fj(Z)V

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/widget/Fj/ex;->ex()V

    :cond_0
    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/JU;->eV()Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;

    move-result-object v0

    invoke-interface {v0}, Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;->Jq()I

    move-result v0

    if-lez v0, :cond_1

    new-instance v0, Lcom/bytedance/sdk/openadsdk/core/widget/Fj/ex;

    invoke-direct {v0}, Lcom/bytedance/sdk/openadsdk/core/widget/Fj/ex;-><init>()V

    const/4 v2, 0x1

    invoke-virtual {v0, v2}, Lcom/bytedance/sdk/openadsdk/core/widget/Fj/ex;->Fj(Z)V

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/widget/Fj/ex;->ex()V

    :cond_1
    return v1
.end method
