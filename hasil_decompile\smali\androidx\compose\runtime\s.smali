.class public interface abstract Landroidx/compose/runtime/s;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/compose/runtime/s$a;
    }
.end annotation

.annotation runtime L<PERSON>lin/Metadata;
.end annotation


# static fields
.field public static final a0:Landroidx/compose/runtime/s$a;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    sget-object v0, Landroidx/compose/runtime/s$a;->a:Landroidx/compose/runtime/s$a;

    sput-object v0, Landroidx/compose/runtime/s;->a0:Landroidx/compose/runtime/s$a;

    return-void
.end method


# virtual methods
.method public abstract a(Landroidx/compose/runtime/q;)Ljava/lang/Object;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Landroidx/compose/runtime/q<",
            "TT;>;)TT;"
        }
    .end annotation
.end method
