.class public Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;
.super Ljava/lang/Object;


# instance fields
.field private BcC:Ljava/lang/String;

.field final Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

.field private Ko:J

.field private UYd:Ljava/lang/String;

.field private volatile Ubf:J

.field private volatile WR:J

.field private dG:Z

.field private final eV:Ljava/util/concurrent/atomic/AtomicBoolean;

.field private final ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

.field private hjc:Landroid/widget/FrameLayout;

.field private final mSE:Ljava/util/concurrent/atomic/AtomicBoolean;

.field private rAx:Lcom/bytedance/adsdk/ugeno/component/ex;

.field private volatile svN:J


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;)V
    .locals 2

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Ljava/util/concurrent/atomic/AtomicBoolean;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Ljava/util/concurrent/atomic/AtomicBoolean;-><init>(Z)V

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->eV:Ljava/util/concurrent/atomic/AtomicBoolean;

    new-instance v0, Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-direct {v0, v1}, Ljava/util/concurrent/atomic/AtomicBoolean;-><init>(Z)V

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->mSE:Ljava/util/concurrent/atomic/AtomicBoolean;

    const-wide/16 v0, 0x0

    iput-wide v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->Ko:J

    const/4 v0, 0x0

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->UYd:Ljava/lang/String;

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->svN:Ljava/lang/String;

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->BcC:Ljava/lang/String;

    return-void
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;J)J
    .locals 0

    iput-wide p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->Ko:J

    return-wide p1
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;Lcom/bytedance/adsdk/ugeno/component/ex;)Lcom/bytedance/adsdk/ugeno/component/ex;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->rAx:Lcom/bytedance/adsdk/ugeno/component/ex;

    return-object p1
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;Ljava/lang/String;)Ljava/lang/String;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->UYd:Ljava/lang/String;

    return-object p1
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;)Ljava/util/concurrent/atomic/AtomicBoolean;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->eV:Ljava/util/concurrent/atomic/AtomicBoolean;

    return-object p0
.end method

.method public static synthetic Ubf(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;)J
    .locals 2

    iget-wide v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->WR:J

    return-wide v0
.end method

.method public static synthetic eV(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;)J
    .locals 2

    iget-wide v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->Ubf:J

    return-wide v0
.end method

.method public static synthetic ex(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;J)J
    .locals 0

    iput-wide p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->WR:J

    return-wide p1
.end method

.method public static synthetic ex(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;)Ljava/lang/String;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->BcC:Ljava/lang/String;

    return-object p0
.end method

.method public static synthetic hjc(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;)J
    .locals 2

    iget-wide v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->Ko:J

    return-wide v0
.end method


# virtual methods
.method public BcC()V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->BcC:Ljava/lang/String;

    invoke-static {v0, v1}, Lcom/bytedance/sdk/openadsdk/ex/hjc;->hjc(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Ljava/lang/String;)V

    return-void
.end method

.method public Fj()V
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->dG:Z

    if-eqz v0, :cond_0

    return-void

    :cond_0
    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->dG:Z

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->ex()V

    return-void
.end method

.method public Fj(I)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->hjc:Landroid/widget/FrameLayout;

    invoke-static {v0, p1}, Lcom/bytedance/sdk/openadsdk/utils/eh;->Fj(Landroid/view/View;I)V

    return-void
.end method

.method public Ko()Z
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->eV:Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-virtual {v0}, Ljava/util/concurrent/atomic/AtomicBoolean;->get()Z

    move-result v0

    return v0
.end method

.method public Ubf()V
    .locals 2

    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    move-result-wide v0

    iput-wide v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->svN:J

    return-void
.end method

.method public WR()V
    .locals 5

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->BcC()V

    invoke-virtual {p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->svN()V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->rAx:Lcom/bytedance/adsdk/ugeno/component/ex;

    if-eqz v0, :cond_0

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->hjc:Landroid/widget/FrameLayout;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/ugeno/component/ex;->mSE()Landroid/view/View;

    move-result-object v0

    new-instance v2, Landroid/widget/FrameLayout$LayoutParams;

    iget-object v3, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->rAx:Lcom/bytedance/adsdk/ugeno/component/ex;

    invoke-virtual {v3}, Lcom/bytedance/adsdk/ugeno/component/ex;->JW()I

    move-result v3

    iget-object v4, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->rAx:Lcom/bytedance/adsdk/ugeno/component/ex;

    invoke-virtual {v4}, Lcom/bytedance/adsdk/ugeno/component/ex;->JU()I

    move-result v4

    invoke-direct {v2, v3, v4}, Landroid/widget/FrameLayout$LayoutParams;-><init>(II)V

    invoke-virtual {v1, v0, v2}, Landroid/view/ViewGroup;->addView(Landroid/view/View;Landroid/view/ViewGroup$LayoutParams;)V

    :cond_0
    return-void
.end method

.method public eV()V
    .locals 2

    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    move-result-wide v0

    iput-wide v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->Ubf:J

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->BcC:Ljava/lang/String;

    invoke-static {v0, v1}, Lcom/bytedance/sdk/openadsdk/ex/hjc;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Ljava/lang/String;)V

    return-void
.end method

.method public ex()V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Kk:Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;

    sget v1, Lcom/bytedance/sdk/openadsdk/utils/Ko;->JW:I

    invoke-virtual {v0, v1}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/FrameLayout;

    iput-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->hjc:Landroid/widget/FrameLayout;

    return-void
.end method

.method public hjc()V
    .locals 7

    new-instance v6, Lcom/bytedance/sdk/openadsdk/core/ugen/ex/ex;

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v1, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Kk:Lcom/bytedance/sdk/openadsdk/activity/TTBaseVideoActivity;

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/core/model/Ql;->QV()Lorg/json/JSONObject;

    move-result-object v2

    iget-object v3, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    iget-object v4, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->BcC:Ljava/lang/String;

    new-instance v5, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx$1;

    invoke-direct {v5, p0}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx$1;-><init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;)V

    move-object v0, v6

    invoke-direct/range {v0 .. v5}, Lcom/bytedance/sdk/openadsdk/core/ugen/ex/ex;-><init>(Landroid/app/Activity;Lorg/json/JSONObject;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Ljava/lang/String;Lcom/bytedance/sdk/openadsdk/core/ugen/eV/Fj;)V

    invoke-virtual {v6}, Lcom/bytedance/sdk/openadsdk/core/ugen/ex/ex;->Fj()V

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->ex:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Fj;->Obv:Lcom/bytedance/sdk/openadsdk/component/reward/view/Ubf;

    invoke-virtual {v0}, Lcom/bytedance/sdk/openadsdk/component/reward/view/Ubf;->Ko()Landroid/view/View;

    move-result-object v0

    invoke-virtual {v6, v0}, Lcom/bytedance/sdk/openadsdk/core/ugen/ex/ex;->Fj(Landroid/view/View;)V

    return-void
.end method

.method public mSE()V
    .locals 4

    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    move-result-wide v0

    iget-wide v2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->svN:J

    sub-long/2addr v0, v2

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    iget-object v3, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->BcC:Ljava/lang/String;

    invoke-static {v0, v1, v2, v3}, Lcom/bytedance/sdk/openadsdk/ex/hjc;->Fj(JLcom/bytedance/sdk/openadsdk/core/model/Ql;Ljava/lang/String;)V

    return-void
.end method

.method public svN()V
    .locals 5

    iget-wide v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->svN:J

    const-wide/16 v2, 0x0

    cmp-long v4, v0, v2

    if-lez v4, :cond_0

    iget-wide v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->WR:J

    cmp-long v4, v0, v2

    if-lez v4, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->mSE:Ljava/util/concurrent/atomic/AtomicBoolean;

    const/4 v1, 0x1

    invoke-virtual {v0, v1}, Ljava/util/concurrent/atomic/AtomicBoolean;->getAndSet(Z)Z

    move-result v0

    if-nez v0, :cond_0

    iget-wide v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->WR:J

    iget-wide v2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->svN:J

    sub-long/2addr v0, v2

    iget-object v2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->Fj:Lcom/bytedance/sdk/openadsdk/core/model/Ql;

    iget-object v3, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->BcC:Ljava/lang/String;

    iget-object v4, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/rAx;->UYd:Ljava/lang/String;

    invoke-static {v0, v1, v2, v3, v4}, Lcom/bytedance/sdk/openadsdk/ex/hjc;->Fj(JLcom/bytedance/sdk/openadsdk/core/model/Ql;Ljava/lang/String;Ljava/lang/String;)V

    :cond_0
    return-void
.end method
