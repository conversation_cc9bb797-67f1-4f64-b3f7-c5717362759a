.class Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$2;
.super Lcom/bytedance/sdk/openadsdk/core/widget/Fj/Ubf;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->WR()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;Landroid/content/Context;Lcom/bytedance/sdk/openadsdk/core/Vq;Ljava/lang/String;Lcom/bytedance/sdk/openadsdk/ex/Ko;Z)V
    .locals 6

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$2;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;

    move-object v0, p0

    move-object v1, p2

    move-object v2, p3

    move-object v3, p4

    move-object v4, p5

    move v5, p6

    invoke-direct/range {v0 .. v5}, Lcom/bytedance/sdk/openadsdk/core/widget/Fj/Ubf;-><init>(Landroid/content/Context;Lcom/bytedance/sdk/openadsdk/core/Vq;Ljava/lang/String;Lcom/bytedance/sdk/openadsdk/ex/Ko;Z)V

    return-void
.end method


# virtual methods
.method public onPageFinished(Landroid/webkit/WebView;Ljava/lang/String;)V
    .locals 0

    invoke-super {p0, p1, p2}, Lcom/bytedance/sdk/openadsdk/core/widget/Fj/Ubf;->onPageFinished(Landroid/webkit/WebView;Ljava/lang/String;)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$2;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->BcC:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    if-eqz p1, :cond_0

    invoke-interface {p1}, Lcom/bytedance/sdk/openadsdk/ex/hjc/WR;->WR()V

    :cond_0
    return-void
.end method

.method public onPageStarted(Landroid/webkit/WebView;Ljava/lang/String;Landroid/graphics/Bitmap;)V
    .locals 0

    invoke-super {p0, p1, p2, p3}, Lcom/bytedance/sdk/openadsdk/core/widget/Fj/Ubf;->onPageStarted(Landroid/webkit/WebView;Ljava/lang/String;Landroid/graphics/Bitmap;)V

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$2;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->BcC:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    if-eqz p1, :cond_0

    invoke-interface {p1}, Lcom/bytedance/sdk/openadsdk/ex/hjc/WR;->Ubf()V

    :cond_0
    return-void
.end method

.method public onReceivedError(Landroid/webkit/WebView;ILjava/lang/String;Ljava/lang/String;)V
    .locals 3

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$2;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;

    invoke-static {v0, p4}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$2;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;

    const/4 v1, 0x0

    invoke-static {v0, v1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ubf(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;Z)Z

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$2;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;

    iput p2, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->WR:I

    iput-object p3, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->svN:Ljava/lang/String;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->BcC:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    if-eqz v0, :cond_2

    :try_start_0
    new-instance v0, Lorg/json/JSONObject;

    invoke-direct {v0}, Lorg/json/JSONObject;-><init>()V

    sget v1, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v2, 0x17

    if-lt v1, v2, :cond_1

    const-string v1, "code"

    invoke-virtual {v0, v1, p2}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    const-string v1, "msg"

    invoke-virtual {v0, v1, p3}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    :cond_1
    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$2;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;

    iget-object v1, v1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->BcC:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    invoke-interface {v1, v0}, Lcom/bytedance/sdk/openadsdk/ex/hjc/WR;->Fj(Lorg/json/JSONObject;)V
    :try_end_0
    .catch Lorg/json/JSONException; {:try_start_0 .. :try_end_0} :catch_0

    :catch_0
    :cond_2
    invoke-super {p0, p1, p2, p3, p4}, Lcom/bytedance/sdk/openadsdk/core/widget/Fj/Ubf;->onReceivedError(Landroid/webkit/WebView;ILjava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public onReceivedError(Landroid/webkit/WebView;Landroid/webkit/WebResourceRequest;Landroid/webkit/WebResourceError;)V
    .locals 3
    .annotation build Landroid/annotation/TargetApi;
        value = 0x17
    .end annotation

    if-eqz p2, :cond_0

    invoke-interface {p2}, Landroid/webkit/WebResourceRequest;->getUrl()Landroid/net/Uri;

    move-result-object v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$2;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;

    invoke-interface {p2}, Landroid/webkit/WebResourceRequest;->getUrl()Landroid/net/Uri;

    move-result-object v1

    invoke-virtual {v1}, Landroid/net/Uri;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Fj(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$2;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;

    const/4 v1, 0x0

    invoke-static {v0, v1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ubf(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;Z)Z

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$2;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->BcC:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    if-eqz v0, :cond_2

    :try_start_0
    new-instance v0, Lorg/json/JSONObject;

    invoke-direct {v0}, Lorg/json/JSONObject;-><init>()V

    sget v1, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v2, 0x17

    if-lt v1, v2, :cond_1

    if-eqz p3, :cond_1

    const-string v1, "code"

    invoke-static {p3}, Lcom/bytedance/sdk/openadsdk/activity/a;->a(Landroid/webkit/WebResourceError;)I

    move-result v2

    invoke-virtual {v0, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    const-string v1, "msg"

    invoke-static {p3}, Lcom/bytedance/sdk/openadsdk/activity/b;->a(Landroid/webkit/WebResourceError;)Ljava/lang/CharSequence;

    move-result-object v2

    invoke-virtual {v0, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    goto :goto_0

    :catch_0
    nop

    goto :goto_1

    :cond_1
    :goto_0
    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$2;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;

    iget-object v1, v1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->BcC:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    invoke-interface {v1, v0}, Lcom/bytedance/sdk/openadsdk/ex/hjc/WR;->Fj(Lorg/json/JSONObject;)V
    :try_end_0
    .catch Lorg/json/JSONException; {:try_start_0 .. :try_end_0} :catch_0

    :cond_2
    :goto_1
    if-eqz p3, :cond_3

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$2;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;

    invoke-static {p3}, Lcom/bytedance/sdk/openadsdk/activity/a;->a(Landroid/webkit/WebResourceError;)I

    move-result v1

    iput v1, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->WR:I

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$2;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;

    invoke-static {p3}, Lcom/bytedance/sdk/openadsdk/activity/b;->a(Landroid/webkit/WebResourceError;)Ljava/lang/CharSequence;

    move-result-object v1

    invoke-static {v1}, Ljava/lang/String;->valueOf(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v1

    iput-object v1, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->svN:Ljava/lang/String;

    :cond_3
    if-nez p2, :cond_4

    return-void

    :cond_4
    invoke-super {p0, p1, p2, p3}, Lcom/bytedance/sdk/openadsdk/core/widget/Fj/Ubf;->onReceivedError(Landroid/webkit/WebView;Landroid/webkit/WebResourceRequest;Landroid/webkit/WebResourceError;)V

    return-void
.end method

.method public onReceivedHttpError(Landroid/webkit/WebView;Landroid/webkit/WebResourceRequest;Landroid/webkit/WebResourceResponse;)V
    .locals 3
    .annotation build Landroid/annotation/TargetApi;
        value = 0x15
    .end annotation

    if-eqz p2, :cond_0

    invoke-interface {p2}, Landroid/webkit/WebResourceRequest;->isForMainFrame()Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$2;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;

    const/4 v1, 0x0

    invoke-static {v0, v1}, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->Ubf(Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;Z)Z

    if-eqz p3, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$2;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;

    invoke-virtual {p3}, Landroid/webkit/WebResourceResponse;->getStatusCode()I

    move-result v1

    iput v1, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->WR:I

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$2;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;

    const-string v1, "onReceivedHttpError"

    iput-object v1, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->svN:Ljava/lang/String;

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$2;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;

    iget-object v0, v0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->BcC:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    if-eqz v0, :cond_2

    :try_start_0
    new-instance v0, Lorg/json/JSONObject;

    invoke-direct {v0}, Lorg/json/JSONObject;-><init>()V

    if-eqz p3, :cond_1

    const-string v1, "code"

    invoke-virtual {p3}, Landroid/webkit/WebResourceResponse;->getStatusCode()I

    move-result v2

    invoke-virtual {v0, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    const-string v1, "msg"

    invoke-virtual {p3}, Landroid/webkit/WebResourceResponse;->getReasonPhrase()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    goto :goto_0

    :catch_0
    nop

    goto :goto_1

    :cond_1
    :goto_0
    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc$2;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;

    iget-object v1, v1, Lcom/bytedance/sdk/openadsdk/component/reward/Fj/Tc;->BcC:Lcom/bytedance/sdk/openadsdk/ex/hjc/Ubf;

    invoke-interface {v1, v0}, Lcom/bytedance/sdk/openadsdk/ex/hjc/WR;->Fj(Lorg/json/JSONObject;)V
    :try_end_0
    .catch Lorg/json/JSONException; {:try_start_0 .. :try_end_0} :catch_0

    :cond_2
    :goto_1
    if-eqz p2, :cond_3

    invoke-super {p0, p1, p2, p3}, Lcom/bytedance/sdk/openadsdk/core/widget/Fj/Ubf;->onReceivedHttpError(Landroid/webkit/WebView;Landroid/webkit/WebResourceRequest;Landroid/webkit/WebResourceResponse;)V

    :cond_3
    return-void
.end method
