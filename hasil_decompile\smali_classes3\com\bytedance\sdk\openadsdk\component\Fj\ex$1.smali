.class final Lcom/bytedance/sdk/openadsdk/component/Fj/ex$1;
.super Lcom/bytedance/sdk/openadsdk/core/nativeexpress/svN;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/Fj/ex;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Landroid/app/Activity;Lcom/bytedance/sdk/openadsdk/component/BcC/Fj;Lcom/bytedance/sdk/openadsdk/component/view/PAGAppOpenAdExpressView;)Lcom/bytedance/sdk/openadsdk/core/nativeexpress/svN;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/component/BcC/Fj;


# direct methods
.method public constructor <init>(Landroid/content/Context;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Ljava/lang/String;ILcom/bytedance/sdk/openadsdk/component/BcC/Fj;)V
    .locals 0

    iput-object p5, p0, Lcom/bytedance/sdk/openadsdk/component/Fj/ex$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/BcC/Fj;

    invoke-direct {p0, p1, p2, p3, p4}, Lcom/bytedance/sdk/openadsdk/core/nativeexpress/svN;-><init>(Landroid/content/Context;Lcom/bytedance/sdk/openadsdk/core/model/Ql;Ljava/lang/String;I)V

    return-void
.end method


# virtual methods
.method public Fj(Landroid/view/View;FFFFLandroid/util/SparseArray;Z)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/view/View;",
            "FFFF",
            "Landroid/util/SparseArray<",
            "Lcom/bytedance/sdk/openadsdk/core/ex/hjc$Fj;",
            ">;Z)V"
        }
    .end annotation

    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    iget-object v1, p0, Lcom/bytedance/sdk/openadsdk/component/Fj/ex$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/BcC/Fj;

    invoke-virtual {v1}, Lcom/bytedance/sdk/openadsdk/component/BcC/Fj;->ex()J

    move-result-wide v1

    invoke-static {v1, v2}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v1

    const-string v2, "duration"

    invoke-virtual {v0, v2, v1}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    invoke-virtual {p0, v0}, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->Fj(Ljava/util/Map;)V

    invoke-super/range {p0 .. p7}, Lcom/bytedance/sdk/openadsdk/core/ex/ex;->Fj(Landroid/view/View;FFFFLandroid/util/SparseArray;Z)V

    return-void
.end method
