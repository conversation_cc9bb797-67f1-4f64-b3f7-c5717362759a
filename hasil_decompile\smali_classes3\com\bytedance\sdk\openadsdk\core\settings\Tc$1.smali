.class Lcom/bytedance/sdk/openadsdk/core/settings/Tc$1;
.super Lcom/bytedance/sdk/component/svN/BcC;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/core/settings/Tc;-><init>(Ljava/lang/String;Lcom/bytedance/sdk/openadsdk/core/settings/Tc$Fj;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/core/settings/Tc;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/core/settings/Tc;Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/settings/Tc$1;->Fj:Lcom/bytedance/sdk/openadsdk/core/settings/Tc;

    invoke-direct {p0, p2}, Lcom/bytedance/sdk/component/svN/BcC;-><init>(Ljava/lang/String;)V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/settings/Tc$1;->Fj:Lcom/bytedance/sdk/openadsdk/core/settings/Tc;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/openadsdk/core/settings/Tc;->Fj(Z)V

    return-void
.end method
