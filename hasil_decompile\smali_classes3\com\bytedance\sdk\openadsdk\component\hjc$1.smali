.class Lcom/bytedance/sdk/openadsdk/component/hjc$1;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/openadsdk/component/view/PAGAppOpenHtmlLayout$Fj;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/hjc;->Fj(Landroid/view/ViewGroup;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/component/hjc;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/hjc;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/hjc$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/hjc;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj(Landroid/view/View;I)V
    .locals 0

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/hjc$1;->Fj:Lcom/bytedance/sdk/openadsdk/component/hjc;

    iget-object p1, p1, Lcom/bytedance/sdk/openadsdk/component/hjc;->Ubf:Lcom/bytedance/sdk/openadsdk/component/Fj;

    invoke-virtual {p1}, Lcom/bytedance/sdk/openadsdk/component/Fj;->Ubf()V

    return-void
.end method
