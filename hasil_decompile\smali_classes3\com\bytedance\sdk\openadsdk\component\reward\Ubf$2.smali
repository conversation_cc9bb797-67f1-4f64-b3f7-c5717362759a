.class Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$2;
.super Lcom/bykv/vk/openvk/component/video/api/Ubf/ex;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;->Fj(Lcom/bytedance/sdk/openadsdk/core/model/Ql;Lcom/bytedance/sdk/openadsdk/AdSlot;Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$Fj;Lcom/bytedance/sdk/openadsdk/utils/lv;Lcom/bytedance/sdk/openadsdk/component/reward/Tc;Z)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$Fj;

.field final synthetic eV:Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;

.field final synthetic ex:Z

.field final synthetic hjc:Lcom/bytedance/sdk/openadsdk/component/reward/Tc;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$Fj;ZLcom/bytedance/sdk/openadsdk/component/reward/Tc;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$2;->eV:Lcom/bytedance/sdk/openadsdk/component/reward/Ubf;

    iput-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$2;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$Fj;

    iput-boolean p3, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$2;->ex:Z

    iput-object p4, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$2;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/Tc;

    invoke-direct {p0}, Lcom/bykv/vk/openvk/component/video/api/Ubf/ex;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj(Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;I)V
    .locals 0

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$2;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$Fj;

    if-eqz p1, :cond_1

    iget-boolean p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$2;->ex:Z

    if-nez p1, :cond_0

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/JU;->eV()Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;

    move-result-object p1

    invoke-interface {p1}, Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;->mE()I

    move-result p1

    const/4 p2, 0x1

    if-ne p1, p2, :cond_1

    :cond_0
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$2;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$Fj;

    iget-object p2, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$2;->hjc:Lcom/bytedance/sdk/openadsdk/component/reward/Tc;

    invoke-virtual {p2}, Lcom/bytedance/sdk/openadsdk/component/reward/Tc;->Fj()Lcom/bytedance/sdk/openadsdk/component/reward/WR;

    move-result-object p2

    invoke-virtual {p1, p2}, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$Fj;->Fj(Lcom/bytedance/sdk/openadsdk/api/interstitial/PAGInterstitialAd;)V

    :cond_1
    return-void
.end method

.method public Fj(Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;ILjava/lang/String;)V
    .locals 1

    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$2;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$Fj;

    if-eqz p1, :cond_1

    iget-boolean p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$2;->ex:Z

    if-nez p1, :cond_0

    invoke-static {}, Lcom/bytedance/sdk/openadsdk/core/JU;->eV()Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;

    move-result-object p1

    invoke-interface {p1}, Lcom/bytedance/sdk/openadsdk/core/settings/Ubf;->mE()I

    move-result p1

    const/4 v0, 0x1

    if-ne p1, v0, :cond_1

    :cond_0
    iget-object p1, p0, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$2;->Fj:Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$Fj;

    invoke-virtual {p1, p2, p3}, Lcom/bytedance/sdk/openadsdk/component/reward/Ubf$Fj;->onError(ILjava/lang/String;)V

    :cond_1
    return-void
.end method
