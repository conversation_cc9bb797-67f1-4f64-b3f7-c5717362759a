.class Lcom/bytedance/sdk/openadsdk/core/eV/Fj$1;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/openadsdk/core/eV/Fj;->Fj(J)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/openadsdk/core/eV/Fj;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/openadsdk/core/eV/Fj;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/openadsdk/core/eV/Fj$1;->Fj:Lcom/bytedance/sdk/openadsdk/core/eV/Fj;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/openadsdk/core/eV/Fj$1;->Fj:Lcom/bytedance/sdk/openadsdk/core/eV/Fj;

    const/4 v1, 0x0

    invoke-static {v0, v1}, Lcom/bytedance/sdk/openadsdk/core/eV/Fj;->Fj(Lcom/bytedance/sdk/openadsdk/core/eV/Fj;Z)V

    return-void
.end method
